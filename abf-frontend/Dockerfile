# ---------- Build Stage ----------
FROM node:18-bullseye AS builder

WORKDIR /app

COPY package.json package-lock.json ./
RUN npm ci

COPY . .

# ⛔️ Fix Tailwind oxide native build error on ARM
ENV TAILWIND_DISABLE_OXIDE=1

# If using TypeScript config (next.config.ts)
RUN npm install --save-dev ts-node

RUN npm run build

# ---------- Runtime Stage ----------
FROM node:18-bullseye AS runner

WORKDIR /app
ENV NODE_ENV=production

COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/next.config.ts ./next.config.ts

EXPOSE 3000

CMD ["npm", "start"]