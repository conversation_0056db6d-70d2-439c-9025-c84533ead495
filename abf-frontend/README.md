# Contribution Guidelines


## 1. Branching Strategy
- **Main Branches:**
  - `master` → **Protected branch** (DO NOT push directly)
  - `develop` → Used for integration (optional if using feature branches directly)
- **Feature Development:**
  - Create a new branch from `develop` or `master` (if no develop branch exists):
    ```sh
    git checkout -b feature/your-feature-name
    ```
  - Work on your feature branch and commit regularly.
- **Sub-Feature Branches:**
  - If a feature requires multiple developers or multiple branches for any reason whatsoever, create sub-branches from the feature branch:
    ```sh
    git checkout -b feature/your-feature-name-subfeature
    ```
- **Bug Fixes:**
  - Create bugfix branches from the `develop` branch or feature branch if it's related:
    ```sh
    git checkout -b bugfix/fix-description
    ```

## 2. Code Commits & PRs
- Keep commits small and meaningful.
- Follow [Conventional Commits](https://www.conventionalcommits.org/):
  - `add: add new payment gateway`
  - `fix: resolve login issue`
  - 'update': Updated xyz feature
  - `docs: update README`
- Push changes to your feature branch:
  ```sh
  git push origin feature/your-feature-name
  ```
- Create a **Pull Request (PR)** before merging into the feature branch or `master`:
  - Assign reviewers.
  - Link related issues.
  - Add a meaningful PR description.
- DO NOT merge directly into `master` or `develop` without review.

## 3. Code Reviews & Merging
- At least **one** approval required before merging a PR.
- Perform a **squash merge** unless maintaining commit history is necessary.
- Resolve all conflicts before merging.

## 4. Best Practices
- Follow coding standards and linting rules.
- Write unit tests and integration tests for new features.
- Keep documentation updated.
- Use meaningful variable and function names.
- Avoid large, monolithic commits—commit in small, logical chunks.
- Write clear and concise commit messages.
- Ensure PRs remain small and focused on a single purpose.
- Always test your code before pushing.
- Use feature flags for incomplete features instead of pushing unfinished code.
- Regularly pull the latest changes from `develop` to avoid merge conflicts.
- Ensure that all new changes pass automated tests and CI/CD pipelines.
- Review other developers' PRs and provide constructive feedback.
- Use `.gitignore` properly to avoid committing unnecessary files.

Thank you for contributing!
