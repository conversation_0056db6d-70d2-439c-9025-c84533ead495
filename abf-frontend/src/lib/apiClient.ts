import axios, { InternalAxiosRequestConfig } from "axios";

const apiClient = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_BASE_URI || "http://localhost:8000",
    headers: {
        "Content-Type": "application/json",
    },
});

export const useAuth = async () => {
    try {
        console.log(localStorage);
        if (localStorage.getItem("isLoggedIn") === "true") {
            return true;
        } else {
            return false;
        }
    } catch (error) {
        return false;
    }
};
const excludedRoutes = [
    "/api/auth/v1/login",
    "/api/auth/v1/refresh",
    "/api/auth/v1/confirm-new-password"
  ];

apiClient.interceptors.request.use(
    (config) => {
        const idToken = localStorage.getItem("idToken");

        const isExcluded = excludedRoutes.some((route) => config.url?.includes(route))
        if (!isExcluded && idToken) {
            console.log("Updating headers")
            config.headers['Authorization'] = `Bearer ${idToken}`
        }
        console.log('config = ', config)
        return config;
    },
    (error) => Promise.reject(error)
);

apiClient.interceptors.response.use(
    (response) => response,  // Correct function for handling successful responses
    async (error) => {       // Function for handling errors
        const originalRequest = error.config;
        const isExcluded = excludedRoutes.some((route) => error.config.url?.includes(route))

        console.log("isExcluded =", isExcluded, "URL:", error.config?.url);

        if (isExcluded) {
            return Promise.reject(error);
        }
        if (error.response?.status === 401 && !originalRequest._retry) {

            originalRequest._retry = true;

            try {
                const refreshResponse = await axios.post("/api/auth/v1/refresh/", {
                    refresh_token: localStorage.getItem("refreshToken"),
                    id_token: localStorage.getItem("idToken"),
                });

                const authenticationResult = refreshResponse.data.AuthenticationResult;

                localStorage.setItem("accessToken", authenticationResult["AccessToken"]);
                localStorage.setItem("idToken", authenticationResult["IdToken"]);

                const idToken = localStorage.getItem('idToken')

                // Retry original request with the new token
                originalRequest.headers["Authorization"] = `Bearer ${idToken}`;
                return apiClient(originalRequest);
            } catch (refreshTokenInvalidError) {
                console.log(refreshTokenInvalidError);
                // Clear tokens and log out the user
                localStorage.removeItem("accessToken");
                localStorage.removeItem("refreshToken");
                localStorage.removeItem("idToken");
                localStorage.removeItem("isLoggedIn");
                window.location.href = "/";
                return Promise.reject(refreshTokenInvalidError);
            }
        }

        return Promise.reject(error);
    }
);

export default apiClient;
