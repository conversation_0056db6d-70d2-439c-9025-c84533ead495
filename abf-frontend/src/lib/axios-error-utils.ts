import { AxiosError } from "axios";
import { APIErrorResponse } from "@/types/profile"; // adjust path if needed

export function extractErrors(error: unknown): string[] {
  const axiosError = error as AxiosError<APIErrorResponse>;
  const data = axiosError.response?.data;

  if (!data?.errors) {
    return [data?.message || "An unknown error occurred"];
  }

  return Object.values(data.errors).flat();
}