'use client';

import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { fetchAllExpenses, fetchQuarterlyTotals } from '@/services/expenses-service';

interface ExpenseRow {
  id: number;
  particulars: string;
  main_header: string;
  sub_header: string;
  units: string;
  frequency: string;
  cost_per_unit: string;
  activity_description: string;
  budget_quarterly: Record<string, number>;
  actual_quarterly: Record<string, number>;
  total_budget: number;
  total_grant_budget: number;
  total_actual: number;
  grant: string;
  remarks: string;
}

interface QuarterlyTotals {
  'Apr-Jun': { budget: number; actual: number };
  'Jul-Sep': { budget: number; actual: number };
  'Oct-Dec': { budget: number; actual: number };
  'Jan-Mar': { budget: number; actual: number };
}

interface BudgetOverviewData {
  totalBudget: number;
  totalActual: number;
  remainingBalance: number;
  disbursedAmount: number;
}

interface ExpenseDataContextType {
  excelData: ExpenseRow[];
  backendData: ExpenseRow[];
  setExcelData: (data: ExpenseRow[]) => void;
  getQuarterlyTotals: (grantFilter?: string, quarterFilter?: string) => QuarterlyTotals;
  getQuarterlyChartData: (grantFilter?: string, quarterFilter?: string) => Array<{ quarter: string; budget: number; actual: number }>;
  getBudgetOverviewData: (grantFilter?: string, quarterFilter?: string) => BudgetOverviewData;
  refreshBackendData: () => Promise<void>;
  refreshQuarterlyData: (grantFilter?: string, quarterFilter?: string, yearFilter?: string) => Promise<void>;
  isLoading: boolean;
}

const ExpenseDataContext = createContext<ExpenseDataContextType | undefined>(undefined);

export const useExpenseData = () => {
  const context = useContext(ExpenseDataContext);
  if (context === undefined) {
    throw new Error('useExpenseData must be used within an ExpenseDataProvider');
  }
  return context;
};

interface ExpenseDataProviderProps {
  children: ReactNode;
}

export const ExpenseDataProvider: React.FC<ExpenseDataProviderProps> = ({ children }) => {
  const [excelData, setExcelData] = useState<ExpenseRow[]>([]);
  const [backendData, setBackendData] = useState<ExpenseRow[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Cache for API responses to improve performance
  const [quarterlyDataCache, setQuarterlyDataCache] = useState<Record<string, any>>({});

  // Sample data for testing when backend is not available
  const getSampleData = (): ExpenseRow[] => [
    {
      id: 1,
      particulars: 'Staff Salaries',
      main_header: 'Program Cost',
      sub_header: 'Personnel',
      units: '12',
      frequency: '1',
      cost_per_unit: '50000',
      activity_description: 'Monthly salaries for program staff',
      budget_quarterly: {
        'Apr-Jun': 150000,
        'Jul-Sep': 150000,
        'Oct-Dec': 150000,
        'Jan-Mar': 150000
      },
      actual_quarterly: {
        'Apr-Jun': 145000,
        'Jul-Sep': 148000,
        'Oct-Dec': 152000,
        'Jan-Mar': 149000
      },
      total_budget: 600000,
      total_grant_budget: 600000,
      total_actual: 594000,
      grant: '1',
      remarks: 'Quarterly salary payments'
    },
    {
      id: 2,
      particulars: 'Training Materials',
      main_header: 'Program Cost',
      sub_header: 'Materials',
      units: '4',
      frequency: '1',
      cost_per_unit: '25000',
      activity_description: 'Training materials and resources',
      budget_quarterly: {
        'Apr-Jun': 25000,
        'Jul-Sep': 25000,
        'Oct-Dec': 25000,
        'Jan-Mar': 25000
      },
      actual_quarterly: {
        'Apr-Jun': 23000,
        'Jul-Sep': 26000,
        'Oct-Dec': 24000,
        'Jan-Mar': 25000
      },
      total_budget: 100000,
      total_grant_budget: 100000,
      total_actual: 98000,
      grant: '1',
      remarks: 'Quarterly training material procurement'
    }
  ];

  // Helper function to safely convert to number
  const safeNumber = (value: any): number => {
    const num = Number(value);
    return isNaN(num) || !isFinite(num) ? 0 : num;
  };

  // Helper function to convert quarter formats
  const normalizeQuarter = (quarter: string): string => {
    const quarterMap: Record<string, string> = {
      'Q1': 'Apr-Jun',
      'Q2': 'Jul-Sep',
      'Q3': 'Oct-Dec',
      'Q4': 'Jan-Mar'
    };
    return quarterMap[quarter] || quarter;
  };

  // Helper function to filter data based on grant and quarter
  const filterData = (data: ExpenseRow[], grantFilter?: string, quarterFilter?: string): ExpenseRow[] => {
    let filteredData = data;

    // Filter by grant
    if (grantFilter && grantFilter !== 'all' && grantFilter !== 'All') {
      filteredData = filteredData.filter(row => row.grant === grantFilter);
    }

    // Note: Quarter filtering is handled at the calculation level since we need to filter quarterly data within each row
    return filteredData;
  };

  // Fetch backend expense data
  const refreshBackendData = async () => {
    try {
      setIsLoading(true);
      console.log('Fetching backend expense data...');
      const response = await fetchAllExpenses();

      if (response && Array.isArray(response) && response.length > 0) {
        console.log('Backend expense data received:', response.length, 'records');
        // Transform backend data to match our ExpenseRow interface
        const transformedData: ExpenseRow[] = response.map((expense: any) => ({
          id: expense.id,
          particulars: expense.particulars || '',
          main_header: expense.main_header || '',
          sub_header: expense.sub_header || '',
          units: String(expense.units || '0'),
          frequency: String(expense.frequency || '1'),
          cost_per_unit: String(expense.cost_per_unit || '0'),
          activity_description: expense.activity_description || '',
          budget_quarterly: {
            'Apr-Jun': safeNumber(expense.budget_q1),
            'Jul-Sep': safeNumber(expense.budget_q2),
            'Oct-Dec': safeNumber(expense.budget_q3),
            'Jan-Mar': safeNumber(expense.budget_q4)
          },
          actual_quarterly: {
            'Apr-Jun': safeNumber(expense.actual_q1),
            'Jul-Sep': safeNumber(expense.actual_q2),
            'Oct-Dec': safeNumber(expense.actual_q3),
            'Jan-Mar': safeNumber(expense.actual_q4)
          },
          total_budget: safeNumber(expense.total_budget),
          total_grant_budget: safeNumber(expense.total_grant_budget),
          total_actual: safeNumber(expense.total_actual),
          grant: String(expense.grant || ''),
          remarks: expense.remarks || ''
        }));

        setBackendData(transformedData);
        console.log('Backend data transformed and stored:', transformedData.length, 'records');
      } else {
        console.log('No backend expense data available, will use sample data when needed');
        setBackendData([]);
      }
    } catch (error) {
      console.error('Error fetching backend expense data:', error);
      console.log('Backend API not available, will use sample data for demonstration');
      // Set empty array on error, sample data will be used in calculations
      setBackendData([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh quarterly data using the new API endpoint
  const refreshQuarterlyData = async (grantFilter?: string, quarterFilter?: string, yearFilter?: string) => {
    try {
      setIsLoading(true);

      // Create cache key
      const cacheKey = `${grantFilter || 'all'}-${quarterFilter || 'All'}-${yearFilter || '2025'}`;

      // Check cache first
      if (quarterlyDataCache[cacheKey]) {
        console.log('Using cached quarterly data for:', cacheKey);
        return;
      }

      console.log('Fetching quarterly data with filters:', { grantFilter, quarterFilter, yearFilter });
      const response = await fetchQuarterlyTotals(grantFilter, quarterFilter, yearFilter);

      if (response && response.status === 'SUCCESS') {
        // Cache the response
        setQuarterlyDataCache(prev => ({
          ...prev,
          [cacheKey]: response.data
        }));

        console.log('Quarterly data fetched and cached:', response.data);
      } else {
        console.log('No quarterly data received from API, will use local calculation');
      }
    } catch (error) {
      console.error('Error fetching quarterly data from API:', error);
      console.log('Will fall back to local calculation from available data');
      // Don't throw error, let the component fall back to local calculation
    } finally {
      setIsLoading(false);
    }
  };

  // Load backend data on mount
  useEffect(() => {
    refreshBackendData();
    refreshQuarterlyData(); // Also load quarterly data
  }, []);

  const getQuarterlyTotals = (grantFilter?: string, quarterFilter?: string): QuarterlyTotals => {
    // Create cache key for this specific request
    const cacheKey = `${grantFilter || 'all'}-${quarterFilter || 'All'}-2025`;

    // Check if we have cached API data for this filter combination
    if (quarterlyDataCache[cacheKey] && quarterlyDataCache[cacheKey].quarterly_totals) {
      console.log('Using cached quarterly totals for:', cacheKey);
      return quarterlyDataCache[cacheKey].quarterly_totals;
    }

    // Fallback to local calculation
    const totals: QuarterlyTotals = {
      'Apr-Jun': { budget: 0, actual: 0 },
      'Jul-Sep': { budget: 0, actual: 0 },
      'Oct-Dec': { budget: 0, actual: 0 },
      'Jan-Mar': { budget: 0, actual: 0 }
    };

    // Use backend data if available, otherwise fall back to excel data, or sample data for demo
    let dataToUse = backendData.length > 0 ? backendData : excelData;

    // If no data available, use sample data for demonstration
    if (dataToUse.length === 0) {
      console.log('No real data available, using sample data for demonstration');
      dataToUse = getSampleData();
    }

    const filteredData = filterData(dataToUse, grantFilter);

    console.log('Calculating quarterly totals from local data:', {
      backendDataCount: backendData.length,
      excelDataCount: excelData.length,
      filteredDataCount: filteredData.length,
      grantFilter,
      quarterFilter
    });

    // Normalize quarter filter (convert Q1, Q2, etc. to Apr-Jun, Jul-Sep, etc.)
    const normalizedQuarterFilter = quarterFilter ? normalizeQuarter(quarterFilter) : quarterFilter;

    filteredData.forEach(row => {
      const quarterKeys = ['Apr-Jun', 'Jul-Sep', 'Oct-Dec', 'Jan-Mar'];

      quarterKeys.forEach(quarterKey => {
        // Skip if quarter filter is specified and doesn't match
        if (normalizedQuarterFilter && normalizedQuarterFilter !== 'All' && normalizedQuarterFilter !== quarterKey) {
          return;
        }

        const budget = row.budget_quarterly?.[quarterKey] || 0;
        const actual = row.actual_quarterly?.[quarterKey] || 0;

        totals[quarterKey as keyof QuarterlyTotals].budget += safeNumber(budget);
        totals[quarterKey as keyof QuarterlyTotals].actual += safeNumber(actual);
      });
    });

    console.log('Local quarterly totals calculated:', totals);
    return totals;
  };

  const getQuarterlyChartData = (grantFilter?: string, quarterFilter?: string) => {
    const totals = getQuarterlyTotals(grantFilter, quarterFilter);

    return [
      { quarter: 'Apr-Jun', budget: totals['Apr-Jun'].budget, actual: totals['Apr-Jun'].actual },
      { quarter: 'Jul-Sep', budget: totals['Jul-Sep'].budget, actual: totals['Jul-Sep'].actual },
      { quarter: 'Oct-Dec', budget: totals['Oct-Dec'].budget, actual: totals['Oct-Dec'].actual },
      { quarter: 'Jan-Mar', budget: totals['Jan-Mar'].budget, actual: totals['Jan-Mar'].actual }
    ];
  };

  const getBudgetOverviewData = (grantFilter?: string, quarterFilter?: string): BudgetOverviewData => {
    // Create cache key for this specific request
    const cacheKey = `${grantFilter || 'all'}-${quarterFilter || 'All'}-2025`;

    // Check if we have cached API data for budget overview
    if (quarterlyDataCache[cacheKey] && quarterlyDataCache[cacheKey].budget_overview) {
      console.log('Using cached budget overview for:', cacheKey);
      const cached = quarterlyDataCache[cacheKey].budget_overview;
      return {
        totalBudget: cached.total_budget,
        totalActual: cached.total_actual,
        remainingBalance: cached.remaining_balance,
        disbursedAmount: cached.disbursed_amount
      };
    }

    // Fallback to calculation from quarterly totals
    const totals = getQuarterlyTotals(grantFilter, quarterFilter);

    // Calculate total budget and actual from quarterly totals
    const totalBudget = Object.values(totals).reduce((sum, quarter) => sum + quarter.budget, 0);
    const totalActual = Object.values(totals).reduce((sum, quarter) => sum + quarter.actual, 0);
    const remainingBalance = totalBudget - totalActual;

    // Disbursed amount is sample data (80% of budget) since disbursement is not yet setup
    const disbursedAmount = totalBudget * 0.8;

    return {
      totalBudget,
      totalActual,
      remainingBalance,
      disbursedAmount
    };
  };

  const value: ExpenseDataContextType = {
    excelData,
    backendData,
    setExcelData,
    getQuarterlyTotals,
    getQuarterlyChartData,
    getBudgetOverviewData,
    refreshBackendData,
    refreshQuarterlyData,
    isLoading
  };

  return (
    <ExpenseDataContext.Provider value={value}>
      {children}
    </ExpenseDataContext.Provider>
  );
};
