"use client";

import { useRouter } from "next/navigation";
import React, { createContext, useContext, useState, useEffect } from "react";

export type UserType = "GRANTEE" | "GRANT_MAKER";

export type UserRole = "ADMIN";

type User = {
    firstName: string;
    lastName: string;
    email: string;
    type: UserType;
    role: UserRole;
};

type AuthContextType = {
    user: User | null;
    isLoading: boolean;
    token: string | null;
    login: (user: User, id_token: string) => void;
    logout: () => void;
};
const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
    const [user, setUser] = useState<User | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [token, setToken] = useState<string | null>("");

    const router = useRouter();
    useEffect(() => {
        const isLoggedIn = localStorage.getItem("isLoggedIn");
        const storedToken = localStorage.getItem("idToken");

        if (isLoggedIn === "true" && storedToken) {
            const storedUser: User = {
                email: localStorage.getItem("email") || "",
                firstName: localStorage.getItem("firstName") || "",
                lastName: localStorage.getItem("lastName") || "",
                role: localStorage.getItem("userRole") as UserRole,
                type: localStorage.getItem("userType") as UserType,
            };

            // ✅ If any of the user fields are missing, force logout
            if (!storedUser.email || !storedUser.role || !storedUser.type) {
                logout(); // force logout
                return;
            }

            setUser(storedUser);
            setToken(storedToken);
        } else {
            logout(); // no token or not logged in
        }

        const timeout = setTimeout(() => {
            setIsLoading(false);
        }, 50);

        return () => clearTimeout(timeout);
    }, []);

    const login = (user: User, token: string) => {
        setUser(user);
        console.log("USER set");
        console.log("User = " + JSON.stringify(user));
        setToken(token);
        localStorage.setItem("isLoggedIn", "true");
        localStorage.setItem("idToken", token);
        localStorage.setItem("email", user.email);
        localStorage.setItem("firstName", user.firstName);
        localStorage.setItem("lastName", user.lastName);
        localStorage.setItem("userRole", user.role);
        localStorage.setItem("userType", user.type);
    };

    const logout = () => {
        localStorage.clear();
        setUser(null);
        setToken(null);
        router.push("/login");
    };

    return (
        <AuthContext.Provider value={{ user, isLoading, login, token, logout }}>
            {children}
        </AuthContext.Provider>
    );
};
export const useAuthContext = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error("useAuthContext must be used inside an AuthProvider");
    }
    return context;
};
