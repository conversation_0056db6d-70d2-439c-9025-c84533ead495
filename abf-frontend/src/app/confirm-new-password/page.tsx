"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PrivacyPolicy } from "@/components/PrivacyPolicy";
import apiClient from "@/lib/apiClient";
import classNames from "classnames";
import { AxiosError } from "axios";
import { useAuthContext } from "@/contexts/AuthContext";
import { Eye, EyeOff, Shield, CheckCircle2 } from "lucide-react";

export default function ConfirmNewPassword() {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [confirm_password, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [privacyAccepted, setPrivacyAccepted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { login } = useAuthContext();

  const passwordRules = [
    {
      label: "Minimum 8 characters",
      test: (pw: string) => pw.length >= 8,
    },
    {
      label: "One uppercase letter",
      test: (pw: string) => /[A-Z]/.test(pw),
    },
    {
      label: "One lowercase letter",
      test: (pw: string) => /[a-z]/.test(pw),
    },
    {
      label: "One number",
      test: (pw: string) => /[0-9]/.test(pw),
    },
    {
      label: "One special character",
      test: (pw: string) => /[!@#$%^&*(),.?":{}|<>]/.test(pw),
    },
  ];

  useEffect(() => {
    if (localStorage.getItem("isLoggedIn")) {
      router.push('/');
    }
    const username: string = sessionStorage.getItem('username') || '';
    setEmail(username);
  }, [router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    if (password !== confirm_password) {
      setError("Both the passwords should match");
      setIsLoading(false);
      return;
    }
    if (!email || !password) {
      setError("Please enter both email and password");
      setIsLoading(false);
      return;
    }
    if (!email.includes("@")) {
      setError("Please enter a valid email address");
      setIsLoading(false);
      return;
    }
    if (!privacyAccepted) {
      setError("Please accept the Privacy Policy to continue");
      setIsLoading(false);
      return;
    }

    // Check if password meets all requirements
    const allRulesPassed = passwordRules.every(rule => rule.test(password));
    if (!allRulesPassed) {
      setError("Password must meet all the requirements listed below");
      setIsLoading(false);
      return;
    }

    try {
      const session = sessionStorage.getItem('session');
      const response = await apiClient.post("/api/auth/v1/confirm-new-password/", {
        username: email,
        new_password: password,
        session: session
      });


      if (response.status == 200) {
        const authResult = response.data.AuthenticationResult;

        const user = {
          firstName: "",
          lastName: "",
          email: email,
          role: authResult.User.Role,
          type: authResult.User.Type
        }
        console.log("authResult.IdToken = " + authResult.IdToken);

        login(user, authResult.IdToken)
        // Login successfull go to home screen (right now it's for grantee only)
        setTimeout(() => {
          router.push('/');
        }, 50);
      }
      try {
        // Should never fail, if failed we would have to manually add the row in the users_user table
        await apiClient.post('/api/users/v1/user/');

        router.push('/');

      } catch (error) {
        const err = error as AxiosError<{ message: string }>;
        if (err?.response?.status) {
          setError(err.response.data.message);
        }
        console.log('Unable to create user, please look into it immediately')

      }

    } catch (error) {
      const err = error as AxiosError<{ message: string }>
      if (err.response?.status) {
        setError(err.response.data.message);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Sophisticated Multi-Layer Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-teal-50/90 via-white/60 to-purple-50/80"></div>
      <div className="absolute inset-0 bg-gradient-to-bl from-cyan-50/70 via-transparent to-emerald-50/60"></div>
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-50/30 to-transparent"></div>

      {/* Professional Background Pattern */}
      <div className="absolute inset-0" style={{
        backgroundImage: `
          radial-gradient(circle at 25% 25%, rgba(20, 184, 166, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),
          radial-gradient(circle at 75% 25%, rgba(59, 130, 246, 0.06) 0%, transparent 50%),
          radial-gradient(circle at 25% 75%, rgba(6, 182, 212, 0.09) 0%, transparent 50%)
        `
      }}></div>

      {/* Static Blur Elements for Depth */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Large Static Orbs */}
        <div className="absolute -top-60 -right-60 w-[500px] h-[500px] bg-gradient-to-br from-blue-200/35 via-purple-200/25 to-teal-200/30 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-60 -left-60 w-[450px] h-[450px] bg-gradient-to-tr from-teal-200/40 via-cyan-200/30 to-indigo-200/25 rounded-full blur-3xl"></div>
        <div className="absolute top-10 right-10 w-80 h-80 bg-gradient-to-r from-purple-200/30 via-blue-200/25 to-teal-200/35 rounded-full blur-2xl"></div>
        <div className="absolute bottom-10 left-10 w-96 h-96 bg-gradient-to-l from-indigo-200/35 via-teal-200/30 to-cyan-200/25 rounded-full blur-2xl"></div>

        {/* Medium Static Elements */}
        <div className="absolute top-1/4 right-1/3 w-48 h-48 bg-gradient-to-br from-cyan-200/30 via-teal-200/25 to-blue-200/20 rounded-full blur-xl"></div>
        <div className="absolute bottom-1/3 left-1/4 w-56 h-56 bg-gradient-to-tr from-purple-200/25 via-indigo-200/30 to-teal-200/25 rounded-full blur-xl"></div>
        <div className="absolute top-1/2 left-1/6 w-40 h-40 bg-gradient-to-r from-teal-200/35 via-cyan-200/25 to-emerald-200/30 rounded-full blur-lg"></div>

        {/* Additional Depth Elements */}
        <div className="absolute top-1/6 left-1/2 w-32 h-32 bg-gradient-to-br from-indigo-200/20 via-blue-200/25 to-teal-200/20 rounded-full blur-lg"></div>
        <div className="absolute bottom-1/6 right-1/2 w-36 h-36 bg-gradient-to-tl from-purple-200/20 via-cyan-200/25 to-emerald-200/20 rounded-full blur-lg"></div>
      </div>

      {/* Professional Geometric Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Static Geometric Shapes */}
        <div className="absolute inset-0 opacity-8">
          <div className="absolute top-20 left-20 w-32 h-32 border border-teal-300/30 rotate-45"></div>
          <div className="absolute top-40 right-32 w-24 h-24 border border-purple-300/25 rotate-12"></div>
          <div className="absolute bottom-32 left-40 w-28 h-28 border border-cyan-300/30 -rotate-12"></div>
          <div className="absolute bottom-20 right-20 w-36 h-36 border border-indigo-300/25 rotate-45"></div>
          <div className="absolute top-1/2 left-1/3 w-20 h-20 border border-blue-300/20 rotate-30"></div>
          <div className="absolute top-1/3 right-1/3 w-16 h-16 border border-emerald-300/25 -rotate-30"></div>
        </div>

        {/* Subtle Dot Pattern */}
        <div className="absolute inset-0 opacity-15">
          <div className="absolute top-1/6 left-1/5 w-2 h-2 bg-teal-400/40 rounded-full"></div>
          <div className="absolute top-2/3 right-1/5 w-1.5 h-1.5 bg-purple-400/35 rounded-full"></div>
          <div className="absolute bottom-1/4 left-1/3 w-2 h-2 bg-blue-400/40 rounded-full"></div>
          <div className="absolute top-1/2 right-1/6 w-1.5 h-1.5 bg-cyan-400/35 rounded-full"></div>
          <div className="absolute top-1/3 left-2/3 w-1 h-1 bg-indigo-400/30 rounded-full"></div>
          <div className="absolute bottom-1/6 right-1/3 w-1 h-1 bg-emerald-400/35 rounded-full"></div>
          <div className="absolute top-3/4 left-1/6 w-1.5 h-1.5 bg-teal-400/30 rounded-full"></div>
        </div>

        {/* Enhanced Line Patterns */}
        <div className="absolute inset-0 opacity-12">
          <svg className="w-full h-full">
            <defs>
              <linearGradient id="lineGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#14b8a6" stopOpacity="0.3" />
                <stop offset="50%" stopColor="#8b5cf6" stopOpacity="0.2" />
                <stop offset="100%" stopColor="#06b6d4" stopOpacity="0.3" />
              </linearGradient>
              <linearGradient id="lineGradient2" x1="0%" y1="100%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#06b6d4" stopOpacity="0.25" />
                <stop offset="50%" stopColor="#6366f1" stopOpacity="0.15" />
                <stop offset="100%" stopColor="#10b981" stopOpacity="0.25" />
              </linearGradient>
            </defs>
            <line x1="10%" y1="20%" x2="90%" y2="80%" stroke="url(#lineGradient1)" strokeWidth="1.5" />
            <line x1="20%" y1="90%" x2="80%" y2="10%" stroke="url(#lineGradient2)" strokeWidth="1.5" />
            <line x1="5%" y1="50%" x2="95%" y2="50%" stroke="url(#lineGradient1)" strokeWidth="0.8" />
            <line x1="50%" y1="5%" x2="50%" y2="95%" stroke="url(#lineGradient2)" strokeWidth="0.6" />
          </svg>
        </div>
      </div>

      <div className="relative w-full max-w-md p-6">
        {/* Header Section */}
        <div className="text-center mb-6">
          {/* Company Logos */}
          <div className="flex items-center justify-center gap-8 mb-6">
            {/* OAB Logo */}
            <div className="flex flex-col items-center">
              <img
                src="/OAB-logo.svg"
                alt="OAB Logo"
                className="w-30 h-26 object-contain drop-shadow-lg"
                onError={(e) => {
                  // Show fallback text logo
                  const target = e.currentTarget as HTMLImageElement;
                  target.style.display = 'none';
                  const fallback = target.nextElementSibling as HTMLElement;
                  if (fallback) fallback.style.display = 'block';
                }}
              />
              <span className="text-lg font-bold text-teal-700 hidden">OAB</span>
            </div>

            {/* Sophius Logo */}
            <div className="flex flex-col items-center">
              <img
                src="/Sophius-logo.svg"
                alt="Sophius Logo"
                className="w-30 h-46 object-contain drop-shadow-lg"
                onError={(e) => {
                  // Show fallback text logo
                  const target = e.currentTarget as HTMLImageElement;
                  target.style.display = 'none';
                  const fallback = target.nextElementSibling as HTMLElement;
                  if (fallback) fallback.style.display = 'block';
                }}
              />
              <span className="text-lg font-bold text-teal-700 hidden">Sophius</span>
            </div>
          </div>

          <h1 className="text-xl font-bold text-gray-900 mb-2 drop-shadow-sm">
            Set New Password
          </h1>
          <p className="text-sm text-gray-700">
            Create a secure password for your account
          </p>
        </div>

        {/* Main Card */}
        <Card className="relative overflow-hidden border-0 bg-white/90 backdrop-blur-md">
          {/* Multiple shadow layers for depth */}
          <div className="absolute inset-0 bg-white/95 rounded-xl shadow-2xl"></div>
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-teal-500/5 rounded-xl"></div>
          <div className="absolute -inset-1 bg-gradient-to-r from-purple-300/20 via-blue-300/20 to-teal-300/20 rounded-xl blur-sm"></div>
          <div className="absolute -inset-2 bg-gradient-to-br from-indigo-200/15 via-purple-200/15 to-cyan-200/15 rounded-xl blur-md"></div>

          {/* Glowing border effect */}
          <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-400/20 via-blue-400/20 to-teal-400/20 p-[1px]">
            <div className="h-full w-full rounded-xl bg-white/95"></div>
          </div>

          <CardContent className="relative p-6 z-10">
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <Alert variant="destructive" className="bg-red-50 border border-red-200 rounded-lg py-2">
                    <AlertDescription className="text-red-700 font-medium text-sm">{error}</AlertDescription>
                  </Alert>
                )}

                <div className="space-y-4">
                  {/* Email Field */}
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-700">Email Address</label>
                    <Input
                      type="email"
                      placeholder="Enter your email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      disabled
                      className="h-10 rounded-lg border-gray-300 bg-gray-50 text-gray-600 focus:border-teal-500 focus:ring-1 focus:ring-teal-200 transition-all duration-200"
                    />
                  </div>

                  {/* Password Field */}
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-700">New Password</label>
                    <div className="relative">
                      <Input
                        type={showPassword ? "text" : "password"}
                        placeholder="Enter new password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="h-10 rounded-lg border-gray-300 focus:border-teal-500 focus:ring-1 focus:ring-teal-200 transition-all duration-200 pr-10"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-teal-600 transition-colors"
                      >
                        {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>
                  </div>

                  {/* Confirm Password Field */}
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-700">Confirm Password</label>
                    <div className="relative">
                      <Input
                        type={showConfirmPassword ? "text" : "password"}
                        placeholder="Confirm new password"
                        value={confirm_password}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        className="h-10 rounded-lg border-gray-300 focus:border-teal-500 focus:ring-1 focus:ring-teal-200 transition-all duration-200 pr-10"
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-teal-600 transition-colors"
                      >
                        {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>
                  </div>

                  {/* Password Validation */}
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle2 className="w-4 h-4 text-teal-600" />
                      <h3 className="font-medium text-gray-800 text-sm">Password Requirements</h3>
                    </div>
                    <div className="grid grid-cols-2 gap-1">
                      {passwordRules.map((rule, i) => {
                        const passed = rule.test(password);
                        return (
                          <div key={i} className="flex items-center gap-2">
                            <div className={classNames(
                              "w-3 h-3 rounded-full flex items-center justify-center text-xs",
                              passed
                                ? "bg-green-500 text-white"
                                : "bg-gray-300"
                            )}>
                              {passed ? "✓" : ""}
                            </div>
                            <span className={classNames(
                              "text-xs",
                              passed ? "text-green-700 font-medium" : "text-gray-600"
                            )}>
                              {rule.label}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Privacy Policy Section */}
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-gray-800 text-sm">Privacy Policy</h3>
                      <PrivacyPolicy
                        onAccept={() => setPrivacyAccepted(true)}
                        onReject={() => setPrivacyAccepted(false)}
                      >
                        <button
                          type="button"
                          className="text-xs text-teal-600 hover:text-teal-700 font-medium underline underline-offset-2"
                        >
                          Read Policy
                        </button>
                      </PrivacyPolicy>
                    </div>

                    <div className="flex items-center gap-2">
                      <div className={classNames(
                        "w-3 h-3 rounded-full flex items-center justify-center text-xs",
                        privacyAccepted === true
                          ? "bg-green-500 text-white"
                          : privacyAccepted === false
                          ? "bg-orange-500 text-white"
                          : "bg-gray-300"
                      )}>
                        {privacyAccepted === true ? "✓" : privacyAccepted === false ? "!" : ""}
                      </div>
                      <span className={classNames(
                        "text-xs",
                        privacyAccepted === true
                          ? "text-green-700 font-medium"
                          : privacyAccepted === false
                          ? "text-orange-700 font-medium"
                          : "text-gray-600"
                      )}>
                        {privacyAccepted === true
                          ? "Privacy policy accepted"
                          : privacyAccepted === false
                          ? "Please read and accept Privacy Policy to continue"
                          : "Privacy policy status pending"}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={isLoading || privacyAccepted !== true}
                  className="w-full h-11 bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold transition-all duration-300 transform hover:scale-[1.02] disabled:scale-100 rounded-lg shadow-lg hover:shadow-xl disabled:shadow-md"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Setting Password...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Shield className="w-4 h-4" />
                      Set New Password
                    </div>
                  )}
                </Button>
              </form>
            </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-6">
          <p className="text-xs text-gray-600 max-w-sm mx-auto">
            By setting a new password, you agree to our Terms of Service and Privacy Policy.
          </p>
          <div className="mt-3 flex items-center justify-center gap-2 text-xs text-gray-500">
            <div className="w-1 h-1 bg-teal-500 rounded-full shadow-sm shadow-teal-500/30"></div>
            <span>Secure • Encrypted • Protected</span>
            <div className="w-1 h-1 bg-teal-500 rounded-full shadow-sm shadow-teal-500/30"></div>
          </div>
        </div>
      </div>
    </div>
  );
}