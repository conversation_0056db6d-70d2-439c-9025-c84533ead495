"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { Layout } from "@/components/Layout";
import { useAuthContext } from "@/contexts/AuthContext";
import GranteeDashboard from "@/components/grantee/DashboardHome";
import GrantMakerDashboard from "@/components/grantmaker/DashboardHome";
import { useAuth } from "@/lib/apiClient";
import { Spinner } from "@/components/Spinner";

const options = [
  { title: "Profile", link: "/profile" },
  // { title: "Reports", link: "/reports" },
  { title: "Funding", link: "/funding" },
  // { title: "Milestones", link: "/milestones" },
  { title: "Help Center", link: "/support-ticket/my-tickets" },
  // { title: "About", link: "/about" },
];

export default function Home() {
  const router = useRouter();

  useEffect(() => {

    const isAuthenticated = useAuth()
    if (!isAuthenticated) {
      router.push('/login')
    }

  })
  const { user, isLoading, logout } = useAuthContext();

  if (isLoading) return <Spinner />
  if (!user) return null;

  if (user.type === 'GRANTEE') return <GranteeDashboard />;
  if (user.type === 'GRANT_MAKER') return <GrantMakerDashboard />;

  return (
    <Layout title="Dashboard">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-6xl mx-auto">
        {options.map((option, index) => (
          <Link key={index} href={option.link}>
            <Card className="cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-[1.02] bg-white border-transparent hover:border-orange-100">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800">{option.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Explore {option.title.toLowerCase()} section</p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </Layout>
  );
}
