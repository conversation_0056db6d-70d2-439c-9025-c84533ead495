import AndFundingEntityModal from "@/components/grantmaker/AddFundingEntityModal";
import Layout from "@/components/grantmaker/Layout"
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useEffect, useState } from "react";
import { Save, X, Building2, IndianRupee, TrendingUp, BarChart3, Wallet, Plus, Edit3, Trash2, Pie<PERSON>hart } from "lucide-react";
import { motion } from "framer-motion";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
} from "chart.js";
import { Pie } from "react-chartjs-2";
import { addFundingEntity, addFundingRecord, deleteFundingEntity, deleteFundingRecord, editFundingEntity, editFundingRecord, getAllFundingEntities, getAllFundingRecords, getGrants, transformFundingAllocationAPIResponseToFundingAllocation, transformFundingAllocationListAPIResponse, transformFundingEntityAPIResponseToFundingEntity, transformFundingEntityListAPIResponse, transformFundingRecordAPIResponseToFundingRecord, transformFundingRecordListAPIResponse } from "@/services/grantmaker/grantmaker-service";
import { FundingEntity, FundingRecord, FundingRecordUpdateRequest } from "@/types/finance";
import { FundingAllocation } from '@/types/finance';
import { fetchFundingAllocations, addFundingAllocation, deleteFundingAllocation } from '@/services/grantmaker/grantmaker-service';
import { toast } from "sonner";
import { AddFundingRecordModal } from "@/components/AddFundingRecordModal";
import { AddFundingAllocationModal } from "@/components/grantmaker/AddFundingAllocationModal";
import { OrganizationGrant } from "@/types/profile";
import { transformGrantsAPIResponseToOrganizationGrants } from "@/services/profile-service";
import { extractErrors } from "@/lib/axios-error-utils";

ChartJS.register(ArcElement, Tooltip, Legend);

export default function GrantMakerFinancePage() {

  const [fundingEntities, setFundingEntities] = useState<FundingEntity[]>([]);

  const [fundingRecords, setFundingRecords] = useState<FundingRecord[]>([]);

  // Funding Allocations state
  const [fundingAllocations, setFundingAllocations] = useState<FundingAllocation[]>([]);
  const [isAddFundingAllocationModalOpen, setIsAddFundingAllocationModalOpen] = useState(false);

  const [grants, setGrants] = useState<OrganizationGrant[]>([]);
  const getAllGrantsProvidedByGrantMaker = async () => {

    try {
      const response = await getGrants();
      const frontendData = transformGrantsAPIResponseToOrganizationGrants(response!)
      console.log("fronted data = ", JSON.stringify(frontendData));
      setGrants(frontendData);

    } catch(error) {
      console.log("Error: ", error);
    }

  }
  const getAllFundingAllocations = async () => {
    const response = await fetchFundingAllocations();

    if (response.status === "SUCCESS") {
      const frontendData = transformFundingAllocationListAPIResponse(response.data);
      setFundingAllocations(frontendData);
    }
  }
  const fetchFundingEntities = async () => {

    try {
      const response = await getAllFundingEntities();
      console.log("response = ", JSON.stringify(response));

      if (response.status === "SUCCESS") {
        const frontendData = transformFundingEntityListAPIResponse(response.data);
        console.log("Frontend data = ", JSON.stringify(frontendData));
        setFundingEntities(frontendData);
      }

    } catch (error) {
      toast.error("Unable to fetch funding entities!");
    }
  }
  useEffect(() => {

    getAllFundingAllocations();
    fetchFundingEntities();
    getAllGrantsProvidedByGrantMaker();

  }, []);

  useEffect(() => {

    const fetchFundingRecords = async () => {

      try {
        const response = await getAllFundingRecords();

        if (response.status == 'SUCCESS') {
          const frontendData = transformFundingRecordListAPIResponse(response.data);
          setFundingRecords(frontendData);
        }

      } catch (error) {

      }


    }

    fetchFundingRecords();

  }, []);


  const [isAddFundingEntityModalOpen, setIsAddFundingEntityModalOpen] = useState(false);
  const [isAddFundingRecordModalOpen, setIsAddFundingRecordModalOpen] = useState(false);

  // Editing state for Funding Entities table
  const [editingFundingEntityId, setEditingFundingEntityId] = useState<number | null>(null);
  const [fundingEntityEditValues, setFundingEntityEditValues] = useState<{ organizationName: string; panNumber: string }>({ organizationName: "", panNumber: "" });

  // Editing state for Funding Records table
  const [editingFundingRecordId, setEditingFundingRecordId] = useState<number | null>(null);
  const [fundingRecordEditValues, setFundingRecordEditValues] = useState<{ amount: string; dateReceived: string; notes: string }>({ amount: "", dateReceived: "", notes: "" });
  const [datePopoverOpen, setDatePopoverOpen] = useState(false);

  async function handleAddFundingEntity(entityName: string, panNumber: string) {

    try {

      const backendData = {
        organization_name: entityName,
        pan_number: panNumber
      }

      const response = await addFundingEntity(backendData);

      if (response.status === 'SUCCESS') {

        const frontendData = transformFundingEntityAPIResponseToFundingEntity(response.data);

        setFundingEntities((prev) => [frontendData, ...(prev || [])]);
        toast.success("Funding entity added successfully")
      }

    } catch (error) {

    }
    setIsAddFundingEntityModalOpen(false);
  }
  async function handleEditFundingEntity(id: number, entityName: string, panNumber: string) {

    const backendData = {
      'organization_name': entityName,
      'pan_number': panNumber
    }

    try {
      const response = await editFundingEntity(id, backendData);
      console.log("Response = ", JSON.stringify(response));
      if (response.status == 'SUCCESS') {
        // Update the local state with the new entity data
        setFundingEntities(prev =>
          prev.map(entity =>
            entity.id === id
              ? { ...entity, organizationName: entityName, panNumber }
              : entity
          )
        );
        toast.success("Funding entity updated successfully");
        setEditingFundingEntityId(null);
      }

    } catch (error) {
      console.log("Unable to edit!")
    }
  }
  async function handleDeleteFundingEntity(id: number) {
    try {
      const response = await deleteFundingEntity(id);
      if (response.status === 204) {
        toast.success("Successfully deleted funding entity");
        setFundingEntities((prev) => prev.filter(entity => entity.id !== id));
      }

    } catch (error) {
      console.log("Unable to edit!")
    }

  }
  async function handleAddFundingRecord(values: FundingRecordUpdateRequest): Promise<void> {
    try {
      const response = await addFundingRecord(values);
      if (response.status === 'SUCCESS') {
        const newRecord = transformFundingRecordAPIResponseToFundingRecord(response.data);
        setFundingRecords(prev => [newRecord, ...(prev || [])]);
      } else {
        console.error(response.message);
      }
    } catch (error) {
      console.error('Failed to add funding record:', error);
    }
  }
  async function handleEditFundingRecord(id: number, values: FundingRecordUpdateRequest) {
    try {
      const response = await editFundingRecord(id, values);
      if (response.status === 'SUCCESS') {
        const updatedRecord = transformFundingRecordAPIResponseToFundingRecord(response.data);
        setFundingRecords(prev =>
          prev.map(record => (record.id === id ? updatedRecord : record))
        );
        toast.success("Funding record updated successfully");
        setEditingFundingRecordId(null);
      } else {
        console.error(response.message);
      }
    } catch (error) {
      console.error('Failed to edit funding record:', error);
    }
  }
  async function handleDeleteFundingRecord(id: number) {
    try {
      const response = await deleteFundingRecord(id);

      if (response.status === 204) {
        setFundingRecords(prev => prev.filter(record => record.id !== id));
        toast.success("Funding record deleted successfully");
      }

    } catch (error) {
      console.error('Failed to delete funding record:', error);
      toast.error("Unable to delete funding record");
    }
  }
  async function handleAddFundingAllocation(values: {fundingEntityId: number,  grantId: number}) {
    console.log("ADding ")
    try {
      const payload = {
        grant: values.grantId,
        funding_entity: values.fundingEntityId
      }
      const response = await addFundingAllocation(payload);
      if (response.status === 'SUCCESS') {
        const frontendData = transformFundingAllocationAPIResponseToFundingAllocation(response.data);
        setFundingAllocations(prev => [frontendData, ...(prev || [])]);
        toast.success("Funding allocation added successfully");
      }
    } catch (error) {
      const flatErrors = extractErrors(error)

      flatErrors.map((err) => toast.error(err))
    }
    setIsAddFundingAllocationModalOpen(false);
  }

  async function handleDeleteFundingAllocation(id: number) {
    try {
      const response = await deleteFundingAllocation(id);
      if (response.status === 204) {
        setFundingAllocations(prev => prev.filter(allocation => allocation.id !== id));
        toast.success("Funding allocation deleted successfully");
      }
    } catch (error) {
      toast.error("Unable to delete funding allocation");
    }
  }

  // Calculate summary metrics
  const totalFundingAmount = fundingRecords.reduce((sum, record) => {
    const amount = Number(record.amount) || 0;
    return sum + amount;
  }, 0);

  const totalAllocatedAmount = fundingAllocations.reduce((sum, allocation) => {
    const amount = Number(allocation.amountAllocated) || 0;
    return sum + amount;
  }, 0);

  const totalEntities = fundingEntities.length;
  const totalAllocations = fundingAllocations.length;



  // Format number in lakhs
  const formatInLakhs = (amount: number) => {
    if (isNaN(amount) || amount === null || amount === undefined) {
      return '₹0L';
    }
    const lakhs = amount / 100000;
    return `₹${lakhs.toFixed(1).replace(/\.0$/, '')}L`;
  };

  return (
    <Layout title="Finance">
      <div className="w-full max-w-7xl mx-auto space-y-8">
        {/* Enhanced Summary Cards Section with Visualization */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-5 gap-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, staggerChildren: 0.1 }}
        >
          {/* Total Funding Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card className="bg-gradient-to-br from-white via-white to-teal-50/30 shadow-xl rounded-xl overflow-hidden border-0 h-[200px]">
              <div className="p-6 relative">
                <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-500/10 to-transparent rounded-bl-full"></div>
                <div className="flex items-start mb-3">
                  <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-3 shadow-lg">
                    <Wallet className="h-6 w-6 text-[#00998F]" />
                  </div>
                  <h3 className="text-gray-800 font-bold text-sm pt-1">Total Funding</h3>
                </div>
                <div className="mb-3">
                  <p className="text-3xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                    {formatInLakhs(totalFundingAmount)}
                  </p>
                </div>
                <div className="flex items-center text-xs text-gray-500">
                  <span className="font-medium">Received from entities</span>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Total Allocated Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="bg-gradient-to-br from-white via-white to-teal-50/30 shadow-xl rounded-xl overflow-hidden border-0 h-[200px]">
              <div className="p-6 relative">
                <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-500/10 to-transparent rounded-bl-full"></div>
                <div className="flex items-start mb-3">
                  <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-3 shadow-lg">
                    <TrendingUp className="h-6 w-6 text-[#00998F]" />
                  </div>
                  <h3 className="text-gray-800 font-bold text-sm pt-1">Total Allocated</h3>
                </div>
                <div className="mb-3">
                  <p className="text-3xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                    {formatInLakhs(totalAllocatedAmount)}
                  </p>
                </div>
                <div className="flex items-center text-xs text-gray-500">
                  <span className="font-medium">To grant partners</span>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Available Funds Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="bg-gradient-to-br from-white via-white to-teal-50/30 shadow-xl rounded-xl overflow-hidden border-0 h-[200px]">
              <div className="p-6 relative">
                <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-500/10 to-transparent rounded-bl-full"></div>
                <div className="flex items-start mb-3">
                  <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-3 shadow-lg">
                    <IndianRupee className="h-6 w-6 text-[#00998F]" />
                  </div>
                  <h3 className="text-gray-800 font-bold text-sm pt-1">Available Funds</h3>
                </div>
                <div className="mb-3">
                  <p className="text-3xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                    {formatInLakhs(totalFundingAmount - totalAllocatedAmount)}
                  </p>
                </div>
                <div className="flex items-center text-xs text-gray-500">
                  <span className="font-medium">Unallocated funds</span>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Funding Entities Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="bg-gradient-to-br from-white via-white to-teal-50/30 shadow-xl rounded-xl overflow-hidden border-0 h-[200px]">
              <div className="p-6 relative">
                <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-500/10 to-transparent rounded-bl-full"></div>
                <div className="flex items-start mb-3">
                  <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-3 shadow-lg">
                    <Building2 className="h-6 w-6 text-[#00998F]" />
                  </div>
                  <h3 className="text-gray-800 font-bold text-sm pt-1">Funding Entities</h3>
                </div>
                <div className="mb-3">
                  <p className="text-3xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                    {totalEntities}
                  </p>
                </div>
                <div className="flex items-center text-xs text-gray-500">
                  <span className="font-medium">Active partners</span>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Active Allocations Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Card className="bg-gradient-to-br from-white via-white to-teal-50/30 shadow-xl rounded-xl overflow-hidden border-0 h-[200px]">
              <div className="p-6 relative">
                <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-500/10 to-transparent rounded-bl-full"></div>
                <div className="flex items-start mb-3">
                  <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-3 shadow-lg">
                    <BarChart3 className="h-6 w-6 text-[#00998F]" />
                  </div>
                  <h3 className="text-gray-800 font-bold text-sm pt-1">Active Allocations</h3>
                </div>
                <div className="mb-3">
                  <p className="text-3xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                    {totalAllocations}
                  </p>
                </div>
                <div className="flex items-center text-xs text-gray-500">
                  <span className="font-medium">Grant allocations</span>
                </div>
              </div>
            </Card>
          </motion.div>
        </motion.div>

        {/* Enhanced Funding Distribution Pie Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card className="bg-gradient-to-br from-white via-white to-teal-50/30 shadow-xl rounded-xl overflow-hidden border-0">
            <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>

            <CardHeader className="pb-4 relative">
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-500/10 to-transparent rounded-bl-full"></div>
              <div className="flex items-center relative z-10">
                <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-lg">
                  <PieChart className="h-6 w-6 text-[#00998F]" />
                </div>
                <div>
                  <CardTitle className="text-2xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-600 bg-clip-text text-transparent">
                    Funding Distribution 
                  </CardTitle>
                  <CardDescription className="text-gray-600 mt-1 font-medium">Entity-wise funding breakdown</CardDescription>
                </div>
              </div>
            </CardHeader>

            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Pie Chart */}
                <div className="flex flex-col items-center">
                  <div className="w-full h-80 flex items-center justify-center relative">
                    <Pie
                      data={{
                        labels: fundingEntities.map(entity => entity.organizationName),
                        datasets: [{
                          data: fundingEntities.map(entity => {
                            const entityRecords = fundingRecords.filter(record => record.fundingEntityName === entity.organizationName);
                            return entityRecords.reduce((sum, record) => {
                              const amount = Number(record.amount) || 0;
                              return sum + amount;
                            }, 0);
                          }),
                          backgroundColor: [
                            '#00998F',
                            '#14B8A6',
                            '#10B981',
                            '#059669',
                            '#047857',
                            '#065F46',
                            '#064E3B',
                            '#022C22'
                          ],
                          borderWidth: 3,
                          borderColor: '#ffffff',
                          hoverBorderWidth: 4,
                          hoverBorderColor: '#ffffff',
                        }]
                      }}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                          legend: {
                            position: 'bottom' as const,
                            labels: {
                              padding: 20,
                              usePointStyle: true,
                              pointStyle: 'circle',
                              font: {
                                size: 12,
                                weight: 'bold'
                              },
                              color: '#374151',
                              generateLabels: function(chart: any) {
                                const data = chart.data;
                                if (data.labels.length && data.datasets.length) {
                                  return data.labels.map((label: string, i: number) => {
                                    const value = data.datasets[0].data[i];
                                    const total = data.datasets[0].data.reduce((a: number, b: number) => a + b, 0);
                                    const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0';
                                    return {
                                      text: `${label} (${percentage}%)`,
                                      fillStyle: data.datasets[0].backgroundColor[i],
                                      strokeStyle: '#ffffff',
                                      lineWidth: 2,
                                      hidden: false,
                                      index: i
                                    };
                                  });
                                }
                                return [];
                              }
                            }
                          },
                          tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.9)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#00998F',
                            borderWidth: 2,
                            cornerRadius: 12,
                            displayColors: true,
                            padding: 12,
                            titleFont: {
                              size: 14,
                              weight: 'bold'
                            },
                            bodyFont: {
                              size: 13,
                              weight: 'normal'
                            },
                            callbacks: {
                              title: function(context: any) {
                                return context[0].label;
                              },
                              label: function(context: any) {
                                const value = context.parsed || 0;
                                const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0';
                                return [
                                  `Amount: ₹${value.toLocaleString('en-IN')}`,
                                  `Percentage: ${percentage}%`
                                ];
                              }
                            }
                          }
                        },
                        elements: {
                          arc: {
                            borderWidth: 3,
                            borderColor: '#ffffff',
                            hoverBorderWidth: 4,
                            hoverBorderColor: '#ffffff'
                          }
                        }
                      }}
                    />
                  </div>
                </div>

                {/* Summary Statistics */}
                <div className="bg-gradient-to-r from-teal-50/50 to-emerald-50/50 rounded-xl p-6 border border-teal-100">
                  <h3 className="text-lg font-bold text-gray-800 mb-6 flex items-center">
                    <div className="p-2 bg-teal-100 rounded-lg mr-3">
                      <BarChart3 className="h-4 w-4 text-[#00998F]" />
                    </div>
                    Distribution Summary
                  </h3>

                  <div className="space-y-4">
                    {fundingEntities.map((entity, index) => {
                      const entityRecords = fundingRecords.filter(record => record.fundingEntityName === entity.organizationName);
                      const entityTotal = entityRecords.reduce((sum, record) => {
                        const amount = Number(record.amount) || 0;
                        return sum + amount;
                      }, 0);
                      const percentage = totalFundingAmount > 0 ? (entityTotal / totalFundingAmount) * 100 : 0;

                      return (
                        <div key={entity.id} className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm border border-gray-100">
                          <div className="flex items-center">
                            <div
                              className="w-4 h-4 rounded-full mr-3"
                              style={{
                                backgroundColor: [
                                  '#00998F', '#14B8A6', '#10B981', '#059669',
                                  '#047857', '#065F46', '#064E3B', '#022C22'
                                ][index % 8]
                              }}
                            ></div>
                            <div>
                              <p className="font-semibold text-gray-800 text-sm">{entity.organizationName}</p>
                              <p className="text-xs text-gray-500">{entity.panNumber}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-[#00998F] text-sm">
                              ₹{(entityTotal || 0).toLocaleString('en-IN', {
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 0
                              })}
                            </p>
                            <p className="text-xs text-gray-500">{percentage.toFixed(1)}%</p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <AndFundingEntityModal
          isOpen={isAddFundingEntityModalOpen}
          onClose={() => setIsAddFundingEntityModalOpen(false)}
          handleAdd={handleAddFundingEntity}
        />

        {/* Enhanced Funding Entities Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card className="bg-gradient-to-br from-white via-white to-teal-50/30 shadow-xl rounded-xl overflow-hidden border-0">
            <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>

            <CardHeader className="pb-4 relative">
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-500/10 to-transparent rounded-bl-full"></div>
              <div className="flex items-center justify-between relative z-10">
                <div className="flex items-center">
                  <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-lg">
                    <Building2 className="h-6 w-6 text-[#00998F]" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-600 bg-clip-text text-transparent">
                      Funding Entities
                    </CardTitle>
                    <CardDescription className="text-gray-600 mt-1 font-medium">Manage your funding partners</CardDescription>
                  </div>
                </div>
                <Button
                  onClick={() => setIsAddFundingEntityModalOpen(true)}
                  className="bg-gradient-to-r from-[#00998F] to-teal-600 hover:from-teal-700 hover:to-teal-800 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-lg px-6 py-2.5 font-medium flex items-center gap-2 transform hover:scale-105"
                >
                  <Plus className="h-4 w-4" />
                  Add Entity
                </Button>
              </div>
            </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="min-w-full">
                    <thead className="bg-gradient-to-r from-teal-50/50 via-emerald-50/30 to-teal-50/50">
                      <tr className="border-b border-teal-100">
                        <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">Sr. No</th>
                        <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">Organization Name</th>
                        <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">PAN Number</th>
                        <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-50">
                      {fundingEntities.map((entity, index) => (
                        <motion.tr
                          key={entity.id}
                          className="hover:bg-gray-50/50 transition-colors duration-200"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                        >
                          <td className="px-6 py-4">
                            <div className="w-8 h-8 bg-gradient-to-br from-teal-100 to-emerald-100 rounded-full flex items-center justify-center">
                              <span className="text-sm font-bold text-[#00998F]">{index + 1}</span>
                            </div>
                          </td>
                          <td className="py-4 px-4">
                            {editingFundingEntityId === entity.id ? (
                              <Input
                                value={fundingEntityEditValues.organizationName}
                                onChange={e =>
                                  setFundingEntityEditValues(vals => ({ ...vals, organizationName: e.target.value }))
                                }
                                className="border-teal-200 focus:border-teal-500 focus:ring-teal-500"
                              />
                            ) : (
                              <div className="font-medium text-gray-900">{entity.organizationName}</div>
                            )}
                          </td>
                          <td className="py-4 px-4">
                            {editingFundingEntityId === entity.id ? (
                              <Input
                                value={fundingEntityEditValues.panNumber}
                                onChange={e =>
                                  setFundingEntityEditValues(vals => ({ ...vals, panNumber: e.target.value }))
                                }
                                className="border-teal-200 focus:border-teal-500 focus:ring-teal-500"
                              />
                            ) : (
                              <div className="text-gray-700 font-mono text-sm">{entity.panNumber}</div>
                            )}
                          </td>
                          <td className="py-4 px-4">
                            <div className="flex items-center gap-2">
                              {editingFundingEntityId === entity.id ? (
                                <>
                                  <Button
                                    size="sm"
                                    onClick={() => {
                                      handleEditFundingEntity(
                                        entity.id,
                                        fundingEntityEditValues.organizationName,
                                        fundingEntityEditValues.panNumber
                                      );
                                    }}
                                    className="bg-green-600 hover:bg-green-700 text-white shadow-sm"
                                  >
                                    <Save className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => setEditingFundingEntityId(null)}
                                    className="border-gray-300 hover:bg-gray-50"
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </>
                              ) : (
                                <>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      setEditingFundingEntityId(entity.id);
                                      setFundingEntityEditValues({
                                        organizationName: entity.organizationName,
                                        panNumber: entity.panNumber,
                                      });
                                    }}
                                    className="border-teal-200 text-teal-700 hover:bg-teal-50 hover:border-teal-300"
                                  >
                                    <Edit3 className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleDeleteFundingEntity(entity.id)}
                                    className="border-red-200 text-red-700 hover:bg-red-50 hover:border-red-300"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </>
                              )}
                            </div>
                          </td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
        </motion.div>



        {/* Enhanced Funding Records Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <AddFundingRecordModal
            isOpen={isAddFundingRecordModalOpen}
            onClose={() => setIsAddFundingRecordModalOpen(false)}
            handleAdd={handleAddFundingRecord}
            fundingEntities={fundingEntities}
          />

          <Card className="bg-gradient-to-br from-white via-white to-teal-50/30 shadow-xl rounded-xl overflow-hidden border-0">
            <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>


            <CardHeader className="pb-4 relative">
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-500/10 to-transparent rounded-bl-full"></div>
              <div className="flex items-center justify-between relative z-10">
                <div className="flex items-center">
                  <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-lg">
                    <IndianRupee className="h-6 w-6 text-[#00998F]" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-600 bg-clip-text text-transparent">
                      Funding Records
                    </CardTitle>
                    <CardDescription className="text-gray-600 mt-1 font-medium">Track all funding transactions</CardDescription>
                  </div>
                </div>
                <Button
                  onClick={() => setIsAddFundingRecordModalOpen(true)}
                  className="bg-gradient-to-r from-[#00998F] to-teal-600 hover:from-teal-700 hover:to-teal-800 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-lg px-6 py-2.5 font-medium flex items-center gap-2 transform hover:scale-105"
                >
                  <Plus className="h-4 w-4" />
                  Add Record
                </Button>
              </div>
            </CardHeader>

            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead className="bg-gradient-to-r from-teal-50/50 via-emerald-50/30 to-teal-50/50">
                    <tr className="border-b border-teal-100">
                      <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">Sr. No</th>
                      <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">Funding Entity</th>
                      <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">Amount</th>
                      <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">Date Received</th>
                      <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">Notes</th>
                      <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-teal-50">
                    {fundingRecords.map((record, index) => (
                      <motion.tr
                        key={record.id}
                        className="hover:bg-gradient-to-r hover:from-teal-50/30 hover:via-emerald-50/20 hover:to-teal-50/30 transition-all duration-300 group"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <td className="px-6 py-4">
                          <div className="w-8 h-8 bg-gradient-to-br from-teal-100 to-emerald-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-bold text-[#00998F]">{index + 1}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="font-semibold text-gray-900 group-hover:text-teal-700 transition-colors">{record.fundingEntityName}</div>
                        </td>
                        <td className="px-6 py-4">
                          {editingFundingRecordId === record.id ? (
                            <Input
                              value={fundingRecordEditValues.amount}
                              type="number"
                              min={0}
                              onChange={e =>
                                setFundingRecordEditValues(vals => ({ ...vals, amount: e.target.value }))
                              }
                              className="border-teal-200 focus:border-teal-500 focus:ring-teal-500"
                            />
                          ) : (
                            <div className="font-bold bg-gradient-to-r from-[#00998F] to-emerald-600 bg-clip-text text-transparent text-lg">₹{record.amount.toLocaleString()}</div>
                          )}
                        </td>
                        <td className="px-6 py-4">
                          {editingFundingRecordId === record.id ? (
                            <Popover open={datePopoverOpen} onOpenChange={setDatePopoverOpen}>
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className="w-[140px] justify-start text-left font-normal border-teal-200 hover:border-teal-300"
                                >
                                  {fundingRecordEditValues.dateReceived
                                    ? fundingRecordEditValues.dateReceived
                                    : "Pick date"}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent align="start" className="w-auto p-0">
                                <Calendar
                                  mode="single"
                                  selected={
                                    fundingRecordEditValues.dateReceived
                                      ? new Date(fundingRecordEditValues.dateReceived)
                                      : undefined
                                  }
                                  onSelect={date => {
                                    setFundingRecordEditValues(vals => ({
                                      ...vals,
                                      dateReceived: date
                                        ? date.toISOString().slice(0, 10)
                                        : "",
                                    }));
                                    setDatePopoverOpen(false);
                                  }}
                                  initialFocus
                                />
                              </PopoverContent>
                            </Popover>
                          ) : (
                            <div className="text-gray-700">{record.dateReceived}</div>
                          )}
                        </td>
                        <td className="px-6 py-4">
                          {editingFundingRecordId === record.id ? (
                            <Textarea
                              value={fundingRecordEditValues.notes}
                              rows={2}
                              onChange={e =>
                                setFundingRecordEditValues(vals => ({ ...vals, notes: e.target.value }))
                              }
                              className="min-h-[60px] resize-none border-teal-200 focus:border-teal-500 focus:ring-teal-500"
                            />
                          ) : (
                            <div className="text-gray-700 text-sm max-w-xs truncate">{record.notes}</div>
                          )}
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-2">
                            {editingFundingRecordId === record.id ? (
                              <>
                                <Button
                                  size="sm"
                                  onClick={() => {
                                    handleEditFundingRecord(record.id, {
                                      funding_entity: record.fundingEntity,
                                      amount: Number(fundingRecordEditValues.amount),
                                      date_received: fundingRecordEditValues.dateReceived,
                                      notes: fundingRecordEditValues.notes,
                                    });
                                  }}
                                  className="bg-green-600 hover:bg-green-700 text-white shadow-sm"
                                >
                                  <Save className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => setEditingFundingRecordId(null)}
                                  className="border-gray-300 hover:bg-gray-50"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </>
                            ) : (
                              <>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => {
                                    setEditingFundingRecordId(record.id);
                                    setFundingRecordEditValues({
                                      amount: record.amount.toString(),
                                      dateReceived: record.dateReceived,
                                      notes: record.notes ?? "",
                                    });
                                  }}
                                  className="border-teal-200 text-teal-700 hover:bg-teal-50 hover:border-teal-300"
                                >
                                  <Edit3 className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleDeleteFundingRecord(record.id)}
                                  className="border-red-200 text-red-700 hover:bg-red-50 hover:border-red-300"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </>
                            )}
                          </div>
                        </td>
                      </motion.tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Enhanced Funding Allocations Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9 }}
        >
          <AddFundingAllocationModal
            open={isAddFundingAllocationModalOpen}
            onClose={() => setIsAddFundingAllocationModalOpen(false)}
            onSave={handleAddFundingAllocation}
            fundingEntities={fundingEntities}
            grants={grants}
          />

          <Card className="bg-gradient-to-br from-white via-white to-teal-50/30 shadow-xl rounded-xl overflow-hidden border-0">
            <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>

            
            <CardHeader className="pb-4 relative">
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-500/10 to-transparent rounded-bl-full"></div>
              <div className="flex items-center justify-between relative z-10">
                <div className="flex items-center">
                  <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-lg">
                    <BarChart3 className="h-6 w-6 text-[#00998F]" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-600 bg-clip-text text-transparent">
                      Funding Allocations
                    </CardTitle>
                    <CardDescription className="text-gray-600 mt-1 font-medium">Allocate funds to grant partners</CardDescription>
                  </div>
                </div>
                <Button
                  onClick={() => setIsAddFundingAllocationModalOpen(true)}
                  className="bg-gradient-to-r from-[#00998F] to-teal-600 hover:from-teal-700 hover:to-teal-800 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-lg px-6 py-2.5 font-medium flex items-center gap-2 transform hover:scale-105"
                >
                  <Plus className="h-4 w-4" />
                  Add Allocation
                </Button>
              </div>
            </CardHeader>

            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead className="bg-gradient-to-r from-teal-50/50 via-emerald-50/30 to-teal-50/50">
                    <tr className="border-b border-teal-100">
                      <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">Sr. No</th>
                      <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">Funding Entity</th>
                      <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">Recipient Organization</th>
                      <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">Grant Name</th>
                      <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">Amount Allocated</th>
                      <th className="text-left px-6 py-4 text-sm font-bold text-[#00998F]">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-teal-50">
                    {fundingAllocations.map((allocation, index) => (
                      <motion.tr
                        key={allocation.id}
                        className="hover:bg-gradient-to-r hover:from-teal-50/30 hover:via-emerald-50/20 hover:to-teal-50/30 transition-all duration-300 group"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <td className="px-6 py-4 text-sm text-[#00998F] font-bold">{index + 1}</td>
                        <td className="px-6 py-4">
                          <div className="font-semibold text-gray-900 group-hover:text-teal-700 transition-colors">
                            {allocation.fundingEntityName}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="font-medium text-gray-800 group-hover:text-teal-600 transition-colors">
                            {allocation.grantRecipientOrganizationName}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-gray-700 group-hover:text-teal-600 transition-colors">
                            {allocation.grantName}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="font-bold bg-gradient-to-r from-[#00998F] to-emerald-600 bg-clip-text text-transparent text-lg">
                            ₹{Number(allocation.amountAllocated).toLocaleString()}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDeleteFundingAllocation(allocation.id)}
                            className="border-red-200 text-red-700 hover:bg-red-50 hover:border-red-300 hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                          >
                            <Trash2 className="h-3 w-3 mr-1" />
                            Delete
                          </Button>
                        </td>
                      </motion.tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </Layout>
  );
}