"use client";
import { Spinner } from "@/components/Spinner";
import { useAuthContext } from "@/contexts/AuthContext"
import GrantMakerFinancePage from "./GrantMakerFinancePage";
import  UserTypeBasedRenderer from "@/components/UserTypeBasedRenderer";
import { ProtectedRoute } from "@/components/ProtectedRoute";

export default function FinancePage() {
    return (
        <>
        <ProtectedRoute allowedTypes={['GRANT_MAKER']} fallback={null}>
            <GrantMakerFinancePage />
        </ProtectedRoute>

        </>
    )

}