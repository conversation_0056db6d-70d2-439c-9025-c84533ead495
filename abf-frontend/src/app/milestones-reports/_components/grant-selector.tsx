"use client";

import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandList,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { CheckIcon, ChevronsUpDownIcon } from "lucide-react";
import { useState } from "react";
import { z } from "zod";

export const NarrativeReportGrantSchema = z.object({
  id: z.number(),
  grant_name: z.string(),
  start_date: z.string().transform((val) => new Date(val)),
  end_date: z.string().transform((val) => new Date(val)),
  grant_purpose: z.string(),
  annual_budget: z.string().transform(parseFloat),
  funding_sources: z.string(),
  created_at: z.string().transform(Date),
  updated_at: z.string().transform(Date),
  organization: z.number(),
  grant_maker_organization: z.number(),
});

export type NarrativeReportGrantType = z.infer<
  typeof NarrativeReportGrantSchema
>;

export function ReportGrantSelector({
  selectedGrant,
  setSelectedGrant,
  grants,
}: {
  selectedGrant?: number;
  setSelectedGrant: (grant?: number) => void;
  grants: NarrativeReportGrantType[];
}) {
  const [open, setOpen] = useState<boolean>();

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="justify-between w-full border-teal-200 bg-teal-50 duration-200 transition-colors text-teal-700 hover:bg-teal-100 hover:text-teal-700"
        >
          {selectedGrant
            ? grants.find((g) => selectedGrant === g.id)?.grant_name
            : "Select Grant..."}
          <ChevronsUpDownIcon className="opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="p-0 popover-content-width-full"
        style={{
          width: "var(--radix-popover-trigger-width)",
          maxHeight: "var(--radix-popover-content-available-height)",
        }}
      >
        <Command>
          <CommandInput placeholder="Search Grant..." className="h-9" />
          <CommandList>
            <CommandEmpty>No Grants found.</CommandEmpty>
            <CommandGroup>
              {grants.map((grant) => (
                <CommandItem
                  key={grant.id}
                  value={grant.grant_name}
                  onSelect={(grant_name: string) => {
                    const grant = grants.find(
                      (g) => g.grant_name === grant_name,
                    );

                    if (!grant) return;

                    setSelectedGrant(grant.id);
                    setOpen(false);
                  }}
                >
                  {grant.grant_name}
                  <CheckIcon
                    className={cn(
                      "ml-auto",
                      grants.find((g) => g.id === selectedGrant)?.grant_name ===
                        grant.grant_name
                        ? "opacity-100"
                        : "opacity-0",
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
