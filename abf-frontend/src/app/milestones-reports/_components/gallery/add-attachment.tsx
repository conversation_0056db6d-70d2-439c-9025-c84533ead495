import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { PaperclipIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { editAttachmentSchema } from "./edit-attachment";
import z from "zod";

const MAX_FILE_SIZE_MB = 50;

export const uploadAttachmentSchema = editAttachmentSchema.extend({
  file: z
    .instanceof(File, { message: "Please select a file" })
    .refine(
      (file) => file.size <= MAX_FILE_SIZE_MB * 1024 * 1024,
      `File must be less than ${MAX_FILE_SIZE_MB}MB`,
    ),
});

export type UploadAttachmentFormInputs = z.infer<typeof uploadAttachmentSchema>;

export function AddAttachmentDialog() {
  const form = useForm<UploadAttachmentFormInputs>({
    resolver: zodResolver(uploadAttachmentSchema),
    defaultValues: {
      title: "",
      description: "",
    },
  });

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="w-fit bg-teal-500 hover:bg-teal-500/80 transition-all duration-300">
          <PaperclipIcon /> Add Attachments
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogTitle>Attachment</DialogTitle>

        <Form {...form}>
          <form
            autoFocus
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-3"
          >
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input autoComplete="off" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea autoComplete="off" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="file"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>File</FormLabel>
                  <FormControl>
                    <Input
                      type="file"
                      onChange={(event) => {
                        const file = event.target.files?.[0];
                        field.onChange(file);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.formState.errors.root && (
              <FormItem>
                <FormMessage className="text-red-500">
                  {form.formState.errors.root.message}
                </FormMessage>
              </FormItem>
            )}

            <Button type="submit" disabled={uploading}>
              {uploading ? "Uploading..." : "Upload"}
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
