"use client";

import type React from "react";

import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { GalleryItem } from "./attachment-card";
import { format } from "date-fns";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

export const editAttachmentSchema = z.object({
  title: z
    .string()
    .min(1, "Title is required")
    .max(100, { message: "Cannot be more than 100 characters" }),
  description: z
    .string()
    .max(500, { message: "Cannot be more than 500 characters" })
    .optional(),
});

export type EditAttachmentFormInputs = z.infer<typeof editAttachmentSchema>;

interface EditAttachmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  item?: GalleryItem;
  onSave: (data: GalleryItem, updatedData: EditAttachmentFormInputs) => void;
}

export default function EditAttachmentDialog({
  isOpen,
  onClose,
  onSave,
  item,
}: EditAttachmentDialogProps) {
  const form = useForm({
    resolver: zodResolver(editAttachmentSchema),
    defaultValues: {
      title: item && item.title,
      description: item && item.description,
    },
  });

  const onSubmit = async (values: EditAttachmentFormInputs) => {
    if (!item) return;

    console.log(values);

    onSave(item, values);
  };

  useEffect(() => {
    form.reset(
      item && {
        title: item.title,
        description: item?.description,
      },
    );
  }, [item]);

  return (
    item && (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Attachment</DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="space-y-4 py-4"
            >
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input autoFocus autoComplete="off" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea autoComplete="off" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-2">
                <Label>File Information</Label>
                <div className="rounded-md bg-gray-50 p-3 text-sm">
                  <p className="text-gray-500">
                    Filename:{" "}
                    <span className="text-gray-700 font-mono">
                      {item.file_name}
                    </span>
                  </p>
                  <p className="text-gray-500">
                    Type:{" "}
                    <span className="text-gray-700">{item.file_type}</span>
                  </p>
                  <p className="text-gray-500">
                    Uploaded:{" "}
                    <span className="text-gray-700">
                      {format(item.uploaded_at, "PPP")}
                    </span>
                  </p>
                </div>
              </div>

              <DialogFooter className="pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={form.formState.isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting ? "Saving..." : "Save Changes"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    )
  );
}
