"use client";

import Image from "next/image";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Download,
  Trash2,
  FileText,
  Film,
  Music,
  Archive,
  File,
  EyeIcon,
  PencilIcon,
} from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { format } from "date-fns";

export interface GalleryItem {
  id: number;
  title: string;
  description: string;
  file_name: string;
  file_type: string;
  attachment_url: string;
  uploaded_at: Date;
}

interface AttachmentCardProps {
  item: GalleryItem;
  onDelete?: (item: GalleryItem) => void;
  onEdit?: (item: GalleryItem) => void;
}

const getFileExtension = (filename: string) => {
  return filename.split(".").pop()?.toUpperCase() || "FILE";
};

const getFileIcon = (fileType: string) => {
  if (fileType.startsWith("image/")) return null;
  if (fileType.startsWith("video/"))
    return <Film className="h-16 w-16 text-blue-500" />;
  if (fileType.startsWith("audio/"))
    return <Music className="h-16 w-16 text-green-500" />;
  if (fileType.includes("pdf") || fileType.includes("document"))
    return <FileText className="h-16 w-16 text-red-500" />;
  if (fileType.includes("zip") || fileType.includes("archive"))
    return <Archive className="h-16 w-16 text-yellow-500" />;
  return <File className="h-16 w-16 text-gray-500" />;
};

const getExtensionColor = (fileType: string) => {
  if (fileType.startsWith("image/")) return "bg-blue-500 text-white";
  if (fileType.startsWith("video/")) return "bg-purple-500 text-white";
  if (fileType.startsWith("audio/")) return "bg-green-500 text-white";
  if (fileType.includes("pdf")) return "bg-red-500 text-white";
  return "bg-gray-500 text-white";
};

export function AttachmentCard({
  item,
  onDelete,
  onEdit,
}: AttachmentCardProps) {
  return (
    <Card className="overflow-hidden hover:shadow-lg transition-all duration-200 group p-0 gap-0">
      <div className="relative">
        <AspectRatio ratio={16 / 9} className="bg-gray-50">
          {item.file_type.startsWith("image/") ? (
            <Image
              fill
              src={item.attachment_url || "/placeholder.svg"}
              alt={item.file_name}
              className="object-cover"
            />
          ) : item.file_type.startsWith("video/") ? (
            <video src={item.attachment_url}></video>
          ) : (
            <div className="flex items-center justify-center h-full bg-gray-50">
              {getFileIcon(item.file_type)}
            </div>
          )}
        </AspectRatio>

        {/* Extension Badge */}
        <div className="absolute top-3 right-3">
          <Badge
            className={`${getExtensionColor(item.file_type)} font-semibold px-2 py-1`}
          >
            {getFileExtension(item.file_name)}
          </Badge>
        </div>
      </div>

      <CardContent className="p-4">
        {/* Title and Filename */}
        <div className="space-y-1 mb-4">
          <h3 className="font-semibold line-clamp-1" title={item.title}>
            {item.title}
          </h3>
          <p
            className="text-sm text-muted-foreground line-clamp-1 font-mono"
            title={item.file_name}
          >
            {item.file_name}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button asChild variant="outline" size="sm" className="flex-1">
            <a
              href={item.attachment_url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download
            </a>
          </Button>

          <ViewAttachment item={item} />

          {onEdit && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(item)}
              className="px-3"
            >
              <PencilIcon className="size-4" />
            </Button>
          )}

          {onDelete && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onDelete(item)}
              className="px-3"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function ViewAttachment({ item }: { item: GalleryItem }) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button size="sm" variant="outline" className="px-3">
          <EyeIcon className="size-4" />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{item.title}</DialogTitle>
          <p className="text-muted-foreground text-sm">
            Uploaded on {format(item.uploaded_at, "PPP")}
          </p>
        </DialogHeader>

        <div className="relative rounded-xl overflow-hidden">
          <AspectRatio ratio={16 / 9} className="bg-gray-50">
            {item.file_type.startsWith("image/") ? (
              <Image
                fill
                src={item.attachment_url || "/placeholder.svg"}
                alt={item.file_name}
                className="object-cover"
              />
            ) : item.file_type.startsWith("video/") ? (
              <video controls>
                <source src={item.attachment_url} type={item.file_type} />
              </video>
            ) : (
              <div className="flex items-center justify-center h-full bg-gray-50">
                {getFileIcon(item.file_type)}
              </div>
            )}
          </AspectRatio>

          {/* Extension Badge */}
          <div className="absolute top-3 right-3">
            <Badge
              className={`${getExtensionColor(item.file_type)} font-semibold px-2 py-1`}
            >
              {getFileExtension(item.file_name)}
            </Badge>
          </div>
        </div>

        <p
          className="text-sm text-gray-500 line-clamp-1 font-mono"
          title={item.file_name}
        >
          {item.file_name}
        </p>

        <p className="prose text-sm">{item.description}</p>
      </DialogContent>
    </Dialog>
  );
}
