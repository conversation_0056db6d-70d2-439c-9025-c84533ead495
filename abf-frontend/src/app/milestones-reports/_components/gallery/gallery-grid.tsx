import { UseQueryResult } from "@tanstack/react-query";
import { FileIcon } from "lucide-react";
import { AttachmentCard, GalleryItem } from "./attachment-card";
import { Skeleton } from "@/components/ui/skeleton";

interface GalleryGridProps {
  title: string;
  filter?: (item: GalleryItem) => boolean;
  gallery: UseQueryResult<GalleryItem[]>;
  onEdit?: (file: GalleryItem) => void;
  onDelete?: (item: GalleryItem) => void;
}

export default function GalleryGrid({
  title,
  filter = () => true,
  gallery,
  onEdit,
  onDelete,
}: GalleryGridProps) {
  const isEmpty = gallery.data && gallery.data.filter(filter).length === 0;

  return (
    <div className="space-y-6 py-6">
      <h2 className="text-2xl font-semibold tracking-tight">{title}</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {gallery.isLoading &&
          Array.from({ length: 3 }).map((_, i) => (
            <Skeleton key={i} className="w-full h-[300px]"></Skeleton>
          ))}

        {isEmpty && (
          <div className="flex flex-col items-center justify-center py-6 text-center col-span-full">
            <div className="rounded-full bg-gray-100 p-6 mb-4">
              <FileIcon className="h-12 w-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No {title} found
            </h3>
            <p className="text-gray-500 max-w-sm">
              Upload some files to see them displayed in this gallery.
            </p>
          </div>
        )}

        {gallery.data
          ?.filter(filter)
          .map((item) => (
            <AttachmentCard
              key={item.id}
              item={item}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))}
      </div>
    </div>
  );
}
