"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    <PERSON><PERSON>Title,
    Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import apiClient from "@/lib/apiClient";
import { zodResolver } from "@hookform/resolvers/zod";
import { PaperclipIcon } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { GalleryItem } from "../_components/gallery/attachment-card";
import EditAttachmentDialog, {
    EditAttachmentFormInputs,
    editAttachmentSchema,
} from "../_components/gallery/edit-attachment";
import { useQuery } from "@tanstack/react-query";
import GalleryGrid from "../_components/gallery/gallery-grid";
import { toast } from "sonner";

const MAX_FILE_SIZE_MB = 50;

let uploadSchema: z.ZodType<any>;

if (typeof window !== "undefined") {
  uploadSchema = editAttachmentSchema.extend({
    file: z
      .instanceof(File, { message: "Please select a file" })
      .refine(
        (file) => file.size <= MAX_FILE_SIZE_MB * 1024 * 1024,
        `File must be less than ${MAX_FILE_SIZE_MB}MB`
      ),
  });
} else {
  uploadSchema = z.any(); // fallback for SSR or static build
}
type UploadFormInputs = z.infer<typeof uploadSchema>;

export default function Page() {
    const gallery = useQuery({
        queryKey: ["gallery"],
        queryFn: () =>
            apiClient.get("/api/reports/gallery/").then((res) => res.data),
    });

    const [uploading, setUploading] = useState(false);
    const [uploadError, setUploadError] = useState<string | null>(null);
    const [editingItem, setEditingItem] = useState<GalleryItem>();
    const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);

    const form = useForm<UploadFormInputs>({
        resolver: zodResolver(uploadSchema),
        defaultValues: {
            title: "",
            description: "",
        },
    });

    const onSubmit = async (data: UploadFormInputs) => {
        const file = data.file;
        setUploading(true); 
        setUploadError(null); 

        try {
            const presignRes = await apiClient.post("/api/reports/gallery/upload/", {
                file_name: file.name,
                file_type: file.type,
            });

            const { upload_url, object_key } = presignRes.data.data;

            const uploadRes = await fetch(upload_url, {
                method: "PUT",
                headers: {
                    "Content-Type": file.type,
                },
                body: file,
            });

            if (!uploadRes.ok) {
                throw new Error("Upload to S3 failed");
            }

            await apiClient.post("/api/reports/gallery/", {
                title: data.title.trim(),
                description: data.description?.trim() || "",
                s3_key: object_key,
                file_name: file.name,
                file_type: file.type,
            });

            form.reset();
            gallery.refetch();
            setIsUploadDialogOpen(false);
        } catch (err) {
            console.error(err);
            form.setError("root", {
                type: "custom",
                message: "Failed to upload",
            });
            setUploadError("Upload failed. Please try again.");
        } finally {
            setUploading(false); 
        }
    };

    async function handleSaveAttachment(
        item: GalleryItem,
        updatedValues: EditAttachmentFormInputs,
    ) {
        console.log("Saving Attachment");
        try {
            const data = await apiClient.patch(
                `/api/reports/gallery/${item.id}/`,
                updatedValues,
            );
            console.log(data);
            setEditingItem(undefined);
            gallery.refetch();
            return item;
        } catch (error) {
            throw error;
        }
    }

    async function handleDeleteGalleryItem(file: GalleryItem) {
        toast.promise(apiClient.delete(`/api/reports/gallery/${file.id}/`), {
            loading: "Deleting file...",
            success: (data) => {
                gallery.refetch();
                return "Deleted file";
            },
            error: "Failed to delete file",
        });
    }

    return (
        <div className="space-y-5">
            <div className="w-full flex justify-end">
                <Dialog
                    open={isUploadDialogOpen}
                    onOpenChange={setIsUploadDialogOpen}
                >
                    <DialogTrigger asChild>
                        <Button className="w-fit bg-teal-500 hover:bg-teal-500/80 transition-all duration-300">
                            <PaperclipIcon /> Add Attachments
                        </Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogTitle>Attachment</DialogTitle>

                        <Form {...form}>
                            <form
                                autoFocus
                                onSubmit={form.handleSubmit(onSubmit)}
                                className="space-y-3"
                            >
                                <FormField
                                    control={form.control}
                                    name="title"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Title</FormLabel>
                                            <FormControl>
                                                <Input autoComplete="off" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="description"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Description</FormLabel>
                                            <FormControl>
                                                <Textarea autoComplete="off" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="file"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>File</FormLabel>
                                            <FormControl>
                                                <Input
                                                    type="file"
                                                    onChange={(event) => {
                                                        const file = event.target.files?.[0];
                                                        field.onChange(file);
                                                    }}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {uploadError && <p style={{ color: "red" }}>{uploadError}</p>}

                                <Button type="submit" disabled={uploading}>
                                    {uploading ? "Uploading..." : "Upload"}
                                </Button>
                            </form>
                        </Form>
                    </DialogContent>
                </Dialog>

                <EditAttachmentDialog
                    isOpen={!!editingItem}
                    item={editingItem}
                    onClose={() => setEditingItem(undefined)}
                    onSave={handleSaveAttachment}
                />
            </div>

            <GalleryGrid
                title={"Images"}
                filter={(item: GalleryItem) => item.file_type.startsWith("image/")}
                gallery={gallery}
                onEdit={(file: GalleryItem) => setEditingItem(file)}
                onDelete={handleDeleteGalleryItem}
            />

            <GalleryGrid
                title={"Videos"}
                gallery={gallery}
                filter={(item: GalleryItem) => item.file_type.startsWith("video/")}
                onEdit={(file: GalleryItem) => setEditingItem(file)}
                onDelete={handleDeleteGalleryItem}
            />

            <GalleryGrid
                title={"Files"}
                gallery={gallery}
                filter={(item: GalleryItem) =>
                    !(
                        item.file_type.startsWith("video/") ||
                        item.file_type.startsWith("image/")
                    )
                }
                onEdit={(file: GalleryItem) => setEditingItem(file)}
                onDelete={handleDeleteGalleryItem}
            />
        </div>
    );
}