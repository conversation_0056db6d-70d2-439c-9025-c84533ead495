"use client";

import { useState, useMemo, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import {
  Plus,
  RefreshCw,
  FileX,
  AlertTriangleIcon,
  Loader2
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { GrantSelector } from "@/components/funding/GrantSelector";
import { QuarterlyDateSelector } from "@/components/funding/QuarterlyDateSelector";
import { ReportGridCard } from "@/components/reports/ReportGridCard";
import { ReportListItem } from "@/components/reports/ReportListItem";
import { RemarksModal } from "@/components/reports/RemarksModal";
import { ViewModeToggle } from "@/components/reports/ViewModeToggle";
import apiClient from "@/lib/apiClient";

// Types
interface Grant {
  id: string;
  grant_name: string;
  annual_budget?: number;
}

type ReportStatus = 'PENDING' | 'APPROVED' | 'REJECTED';
interface Report {
  id: number;
  quarter: number;
  year: string;
  created_at: Date;
  updated_at: Date;
  grant_id: number;
  remarks?: string;
  status: ReportStatus;
  grant?: Grant;
}


const GRANTS = [
  { id: "all", name: "All Grants" },
  { id: "1", name: "Education Fund 2024" },
  { id: "2", name: "Healthcare Initiative" },
  { id: "3", name: "Community Development" },
];


const fetchReports = async (
  grant: string,
  quarter: string,
  year: string,
): Promise<Report[]> => {
  const params = new URLSearchParams();
  if (grant !== "all") params.append("grant", grant);
  if (quarter !== "All") params.append("quarter", quarter[1]);
  if (year !== "All") params.append("year", year);

  const response = await apiClient.get(
    `/api/reports/narrative?${params.toString()}`,
  );
  return response.data;
};

const EmptyState = ({ selectedTab }: { selectedTab: string }) => (
  <div className="flex flex-col items-center justify-center py-12 text-gray-500">
    <FileX className="h-12 w-12 mb-4 text-gray-300" />
    <p className="font-medium text-lg">No Reports Found</p>
    <p className="text-sm text-gray-400 mt-1">
      {selectedTab === 'all' 
        ? 'Try adjusting your search criteria or create a new report to get started.' 
        : `No ${selectedTab} reports found`
      }
    </p>
  </div>
);

const ErrorState = ({ onRetry }: { onRetry: () => void }) => (
  <Alert variant="destructive">
    <AlertTriangleIcon className="h-4 w-4" />
    <AlertDescription className="flex justify-between items-center">
      <span>Failed to load reports. Please try again.</span>
      <Button variant="outline" size="sm" onClick={onRetry} className="ml-4">
        <RefreshCw className="h-4 w-4 mr-2" />
        Retry
      </Button>
    </AlertDescription>
  </Alert>
);

// Main Component
export default function NarrativeReports() {
  const [selectedGrant, setSelectedGrant] = useState("all");
  const [selectedQuarter, setSelectedQuarter] = useState("All");
  const [selectedYear, setSelectedYear] = useState("All");
  const [viewMode, setViewMode] = useState<"quarterly" | "yearly">("quarterly");
  const [showRemarksModal, setShowRemarksModal] = useState(false);
  const [selectedRemarks, setSelectedRemarks] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [displayMode, setDisplayMode] = useState<"grid" | "list">("grid");

  // Query
  const { data: reports = [], isLoading, isError, refetch } = useQuery({
    queryKey: ["narrativeReports", selectedGrant, selectedQuarter, selectedYear],
    queryFn: () => fetchReports(selectedGrant, selectedQuarter, selectedYear),
  });

  // Filter reports by status
  const filteredReports = useMemo(() => {
    if (statusFilter === "all") return reports;
    return reports.filter(report => report.status.toLowerCase() === statusFilter.toLowerCase());
  }, [reports, statusFilter]);

  // Handlers
  const handleRemarksClick = useCallback((remarks: string) => {
    setSelectedRemarks(remarks);
    setShowRemarksModal(true);
  }, []);

  const handleResetFilters = () => {
    setSelectedGrant("all");
    setSelectedQuarter("All");
    setSelectedYear("All");
    setStatusFilter("all");
  };

  if (isError) {
    return (
      <div className="space-y-6">
        <Card className="shadow-sm border border-gray-100">
          <CardContent className="p-6">
            <ErrorState onRetry={refetch} />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <motion.div 
        className="flex justify-between flex-wrap gap-4 items-center"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex gap-4 items-center">
          <GrantSelector
            selectedGrant={selectedGrant}
            setSelectedGrant={setSelectedGrant}
            grants={GRANTS}
          />

          <QuarterlyDateSelector
            selectedQuarter={selectedQuarter}
            setSelectedQuarter={setSelectedQuarter}
            selectedYear={selectedYear}
            setSelectedYear={setSelectedYear}
            viewMode={viewMode}
            setViewMode={setViewMode}
          />
          <Button
            onClick={handleResetFilters}
            variant="outline"
            size="sm"
            className="flex items-center gap-2 hover:bg-slate-50 transition-all duration-300 group"
          >
            <RefreshCw className="h-4 w-4 group-hover:rotate-180 transition-transform duration-500" />
            Reset
          </Button>
        </div>

        <ViewModeToggle
          viewMode={displayMode}
          setViewMode={setDisplayMode}
        />
      </motion.div>

      {/* Tabs and Content */}
      <Card className="shadow-sm border border-gray-100">
        <CardContent className="p-6">
          <Tabs value={statusFilter} onValueChange={setStatusFilter} className="w-full">
            <div className="flex items-center justify-between mb-6">
              <TabsList>
                <TabsTrigger value="all">All Reports ({reports.length})</TabsTrigger>
                <TabsTrigger value="approved">Approved</TabsTrigger>
                <TabsTrigger value="pending">Pending</TabsTrigger>
                <TabsTrigger value="rejected">Rejected</TabsTrigger>
              </TabsList>
              
              <Link
                href="/milestones-reports/narrative/create"
                className="inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium h-10 px-4 py-2 bg-gradient-to-r from-teal-500 to-emerald-500 hover:from-teal-600 hover:to-emerald-600 transition-all duration-300 text-white shadow-lg hover:shadow-xl group"
              >
                <Plus className="mr-2 h-4 w-4 group-hover:scale-110 transition-transform duration-300" />
                New Report
              </Link>
            </div>
            
            <TabsContent value={statusFilter} className="mt-0">
              {isLoading ? (
                <div className="py-12 text-center text-gray-500">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p>Loading reports...</p>
                </div>
              ) : filteredReports.length === 0 ? (
                <EmptyState selectedTab={statusFilter} />
              ) : (
                <AnimatePresence mode="wait">
                  <motion.div
                    key={`${statusFilter}-${filteredReports.length}-${displayMode}`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className={
                      displayMode === "grid" 
                        ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6" 
                        : "space-y-4"
                    }
                  >
                    {filteredReports.map((report, index) => 
                      displayMode === "grid" ? (
                        <ReportGridCard
                          key={report.id}
                          report={report}
                          index={index}
                          onRemarksClick={handleRemarksClick}
                        />
                      ) : (
                        <ReportListItem
                          key={report.id}
                          report={report}
                          index={index}
                          onRemarksClick={handleRemarksClick}
                        />
                      )
                    )}
                  </motion.div>
                </AnimatePresence>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Modals */}
      <RemarksModal
        isOpen={showRemarksModal}
        onClose={() => setShowRemarksModal(false)}
        remarks={selectedRemarks}
      />
    </div>
  );
}