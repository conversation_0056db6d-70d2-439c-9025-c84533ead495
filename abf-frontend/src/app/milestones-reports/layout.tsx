"use client";
import { ReactNode } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { Layout } from "@/components/Layout";

const tabs = [
  { name: "Narrative Reports", href: "/milestones-reports" },
  { name: "Gallery", href: "/milestones-reports/gallery" },
];

export default function MilestonesReportsLayout({ children }: { children: ReactNode }) {
  const pathname = usePathname();

  return (
    <Layout>
      <div className="px-4 md:px-6 pt-4">
        <div className="flex space-x-4 border-b border-gray-200 mb-6">
          {tabs.map((tab) => (
            <Link
              key={tab.name}
              href={tab.href}
              className={`pb-2 px-1 border-b-2 text-sm font-medium ${pathname === tab.href
                ? "border-emerald-500 text-emerald-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
            >
              {tab.name}
            </Link>
          ))}
        </div>
        {children}
      </div>
    </Layout>
  );
}