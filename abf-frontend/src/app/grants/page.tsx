"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Layout from "@/components/grantmaker/Layout";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Search, Filter, ArrowUpDown, Download, Grid, List } from "lucide-react";
import { Grant, getGrants, formatCurrency} from "@/services/grantmaker/grantmaker-service";
import { Badge } from "@/components/ui/badge";

export default function GrantsPage() {
  const router = useRouter();
  const [grants, setGrants] = useState<Grant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState<keyof Grant>("grant_name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [viewMode, setViewMode] = useState<"list" | "grid">("list");

  useEffect(() => {
    const fetchGrants = async () => {
      try {
        setLoading(true);
        const response = await getGrants();
        console.log("Response = " + JSON.stringify(response));
        setGrants(response ?? []);
      } catch (err) {
        console.error("Error fetching grants:", err);
        setError("Failed to load grants");
      } finally {
        setLoading(false);
      }
    };

    fetchGrants();
  }, []);

  const handleSort = (field: keyof Grant) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const filteredGrants = grants.filter((grant) =>
    grant.grant_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedGrants = [...filteredGrants].sort((a, b) => {
    const aValue = a[sortField]?.toString().toLowerCase() || "";
    const bValue = b[sortField]?.toString().toLowerCase() || "";

    if (sortDirection === "asc") {
      return aValue.localeCompare(bValue);
    } else {
      return bValue.localeCompare(aValue);
    }
  });

  const handleViewDetails = (grantId: number) => {
    router.push(`/grantmaker/grants/${grantId}`);
  };

  if (loading) {
    return (
      <Layout title="Grants">
        <div className="flex justify-center items-center h-[calc(100vh-6rem)]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout title="Grants">
        <Card className="bg-white shadow-md hover:shadow-lg transition-shadow mb-8 max-w-4xl mx-auto">
          <CardContent className="p-8 text-center">
            <div className="text-red-500 text-xl mb-4">{error}</div>
            <Button
              onClick={() => window.location.reload()}
              className="bg-gradient-to-r from-orange-500 via-amber-500 to-orange-400 hover:from-orange-600 hover:via-amber-600 hover:to-orange-500 text-white"
            >
              Retry Loading
            </Button>
          </CardContent>
        </Card>
      </Layout>
    );
  }

  return (
    <Layout title="Grants">
      <div className="w-full max-w-7xl mx-auto">
        <Card className="bg-white shadow-md hover:shadow-lg transition-shadow mb-8">
          <CardHeader>
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div>
                <CardTitle className="text-2xl text-gray-800">All Grants</CardTitle>
                <CardDescription>Manage and view all your submitted grants</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === "list" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className={viewMode === "list" ? "bg-orange-500 hover:bg-orange-600" : ""}
                >
                  <List className="h-4 w-4 mr-1" />
                  List
                </Button>
                <Button
                  variant={viewMode === "grid" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className={viewMode === "grid" ? "bg-orange-500 hover:bg-orange-600" : ""}
                >
                  <Grid className="h-4 w-4 mr-1" />
                  Grid
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-1" />
                  Export
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between mb-6">
              <div className="relative w-full md:w-64">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search grants..."
                  className="h-10 w-full rounded-md border border-gray-300 bg-white pl-10 pr-4 text-sm focus:border-orange-500 focus:outline-none focus:ring-1 focus:ring-orange-500"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Filter className="h-4 w-4" />
                <span>Filter</span>
              </Button>
            </div>

            {viewMode === "list" ? (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="p-3 text-left font-medium text-gray-600 border-b">
                        <button className="flex items-center gap-1" onClick={() => handleSort("grant_name")}>
                          Grant Name
                          {sortField === "grant_name" && <ArrowUpDown className="h-4 w-4" />}
                        </button>
                      </th>
                      <th className="p-3 text-left font-medium text-gray-600 border-b">Duration</th>
                      <th className="p-3 text-left font-medium text-gray-600 border-b">Purpose</th>
                      <th className="p-3 text-left font-medium text-gray-600 border-b">Amount</th>
                      <th className="p-3 text-left font-medium text-gray-600 border-b">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {sortedGrants.map((grant) => (
                      <tr
                        key={grant.id}
                        className="hover:bg-gray-50 cursor-pointer transition-colors"
                        onClick={() => handleViewDetails(grant.id)}
                      >
                        <td className="p-3 border-b">{grant.grant_name}</td>
                        <td className="p-3 border-b">{grant.grant_duration}</td>
                        <td className="p-3 border-b text-sm text-gray-600">{grant.grant_purpose}</td>
                        <td className="p-3 border-b font-medium text-gray-900">{formatCurrency(Number(grant.annual_budget))}</td>
                        <td className="p-3 border-b">
                          <Button variant="outline" size="sm" onClick={(e) => {
                            e.stopPropagation();
                            handleViewDetails(grant.id);
                          }}>
                            View Details
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {sortedGrants.map((grant) => (
                  <Card
                    key={grant.id}
                    className="cursor-pointer hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02]"
                    onClick={() => handleViewDetails(grant.id)}
                  >
                    <div className="h-1.5 bg-gradient-to-r from-orange-500 via-amber-500 to-orange-400"></div>
                    <CardContent className="p-6 space-y-3">
                      <h3 className="font-semibold text-lg text-gray-900">{grant.grant_name}</h3>
                      <p className="text-sm text-gray-600">{grant.grant_duration}</p>
                      <p className="text-sm text-gray-700">{grant.grant_purpose}</p>
                      <p className="text-sm text-gray-900 font-medium">
                        {formatCurrency(Number(grant.annual_budget))}
                      </p>
                      <Button variant="outline" size="sm" className="w-full mt-4">
                        View Details
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}