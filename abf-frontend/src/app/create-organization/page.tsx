"use client";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  SelectContent,
  SelectTrigger,
  SelectValue,
  SelectItem,
  Select,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useEffect, useState } from "react";
import { createProfile, getProfile, prepareOrganizationPayload, transformBackendToFrontend, transformFrontendToBackend } from "@/services/profile-service";
import { AxiosError, isAxiosError } from "axios";
import { organizationDetailsFormSchema } from "@/schemas/OrganizationDetailsSchema";
import { useRouter } from "next/navigation";
import { Spinner } from "@/components/Spinner";
import { Building2, FileText, MapPin, ChevronRight, ChevronLeft, Check } from "lucide-react";
import classNames from "classnames";

export default function CreateOrganization() {
	const [isProfileLoading, setIsProfileLoading] = useState(true);
	const router = useRouter();

	useEffect(() => {
		const fetchProfileData = async () => {
			try {
				const fetchProfileResponse = await getProfile();
				if (fetchProfileResponse.status == 'SUCCESS') {
					router.replace('/')
				}
			} catch (error: unknown) {
				if (isAxiosError(error)) {
					const err = error as AxiosError<{message: string}>;
					setIsProfileLoading(false);
					console.log("Profile loading: " + isProfileLoading);
					console.error('Axios error: ', err.message);
					console.error('Response: ', err.response);
				} else if (error instanceof Error) {
					console.error('General error: ', error.message);
				} else {
					console.error('Unexpected error: ', error);
				}
				console.log(error)
			}
		}
		fetchProfileData();
	}, [])

  return (
    <ProtectedRoute allowedTypes={["GRANTEE"]} fallback={null}>
      <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
        {/* Refined Multi-Layer Background System */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50/90 to-indigo-50"></div>
        <div className="absolute inset-0 bg-gradient-to-tr from-teal-50/80 via-white/60 to-purple-50/70"></div>
        <div className="absolute inset-0 bg-gradient-to-bl from-cyan-50/60 via-transparent to-emerald-50/50"></div>

        {/* Sophisticated Professional Pattern */}
        <div className="absolute inset-0" style={{
          backgroundImage: `
            radial-gradient(ellipse 1000px 700px at 15% 15%, rgba(20, 184, 166, 0.08) 0%, rgba(20, 184, 166, 0.02) 50%, transparent 80%),
            radial-gradient(ellipse 900px 600px at 85% 85%, rgba(139, 92, 246, 0.06) 0%, rgba(139, 92, 246, 0.015) 50%, transparent 80%),
            radial-gradient(ellipse 800px 900px at 85% 15%, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.01) 50%, transparent 80%),
            radial-gradient(ellipse 850px 650px at 15% 85%, rgba(6, 182, 212, 0.07) 0%, rgba(6, 182, 212, 0.02) 50%, transparent 80%)
          `
        }}></div>

        {/* Refined Static Blur Elements for Elegant Depth */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Primary Depth Orbs */}
          <div className="absolute -top-40 -right-40 w-[400px] h-[400px] bg-gradient-to-br from-blue-200/25 via-teal-200/20 to-cyan-200/15 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-[380px] h-[380px] bg-gradient-to-tr from-teal-200/30 via-emerald-200/20 to-indigo-200/15 rounded-full blur-3xl"></div>

          {/* Secondary Depth Elements */}
          <div className="absolute top-10 right-10 w-72 h-72 bg-gradient-to-r from-purple-200/20 via-blue-200/15 to-teal-200/25 rounded-full blur-2xl"></div>
          <div className="absolute bottom-10 left-10 w-80 h-80 bg-gradient-to-l from-indigo-200/25 via-teal-200/20 to-cyan-200/15 rounded-full blur-2xl"></div>

          {/* Accent Elements */}
          <div className="absolute top-1/4 right-1/3 w-40 h-40 bg-gradient-to-br from-cyan-200/20 via-teal-200/15 to-blue-200/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-1/3 left-1/4 w-48 h-48 bg-gradient-to-tr from-purple-200/15 via-indigo-200/20 to-teal-200/15 rounded-full blur-xl"></div>
        </div>

        {/* Refined Professional Accents */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Subtle Geometric Elements */}
          <div className="absolute inset-0 opacity-8">
            <div className="absolute top-24 left-24 w-24 h-24 border border-teal-300/25 rotate-45 rounded-lg"></div>
            <div className="absolute bottom-24 right-24 w-28 h-28 border border-indigo-300/20 rotate-45 rounded-xl"></div>
            <div className="absolute top-1/3 right-1/4 w-20 h-20 border border-cyan-300/25 -rotate-12 rounded-md"></div>
            <div className="absolute bottom-1/3 left-1/4 w-16 h-16 border border-purple-300/20 rotate-30 rounded"></div>
          </div>

          {/* Elegant Accent Dots */}
          <div className="absolute inset-0 opacity-12">
            <div className="absolute top-1/5 left-1/6 w-2 h-2 bg-teal-400/40 rounded-full"></div>
            <div className="absolute top-3/5 right-1/6 w-1.5 h-1.5 bg-indigo-400/35 rounded-full"></div>
            <div className="absolute bottom-1/5 left-1/3 w-2 h-2 bg-cyan-400/40 rounded-full"></div>
            <div className="absolute top-1/2 right-1/5 w-1.5 h-1.5 bg-purple-400/35 rounded-full"></div>
          </div>

          {/* Refined Line Patterns */}
          <div className="absolute inset-0 opacity-6">
            <svg className="w-full h-full">
              <defs>
                <linearGradient id="lineGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#14b8a6" stopOpacity="0.3" />
                  <stop offset="50%" stopColor="#8b5cf6" stopOpacity="0.15" />
                  <stop offset="100%" stopColor="#06b6d4" stopOpacity="0.25" />
                </linearGradient>
              </defs>
              <line x1="10%" y1="20%" x2="90%" y2="80%" stroke="url(#lineGradient1)" strokeWidth="1" strokeLinecap="round" />
              <line x1="20%" y1="90%" x2="80%" y2="10%" stroke="url(#lineGradient1)" strokeWidth="1" strokeLinecap="round" />
            </svg>
          </div>
        </div>

        {isProfileLoading ? (
          <div className="relative z-10">
            <Spinner />
          </div>
        ) : (
          <OrganizationForm />
        )}
      </div>
    </ProtectedRoute>
  );
}

function OrganizationForm() {
	const router = useRouter();
	const [currentStep, setCurrentStep] = useState(0);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [error, setError] = useState<string>("");

	const steps = [
		{
			id: 0,
			title: "Organization Details",
			description: "Basic information about your organization",
			icon: Building2,
		},
		{
			id: 1,
			title: "Tax Details",
			description: "Registration numbers and tax information",
			icon: FileText,
		},
		{
			id: 2,
			title: "Address Information",
			description: "Location and contact details",
			icon: MapPin,
		},
	];

  const form = useForm<z.infer<typeof organizationDetailsFormSchema>>({
    resolver: zodResolver(organizationDetailsFormSchema),
		mode: "onChange", // Enable real-time validation
		defaultValues: {
			organizationName: "",
			panNumber: "",
			phoneNumber: "",
			emailAddress: "",
			websiteUrl: "",
			numberOfTeamMembers: "",
			missionVision: "",
			backgroundHistory: "",
			csrRegistrationNumber: "",
			taxRegistrationNumber: "",
			taxRegistrationNumberUnder12A: "",
			fcraRegistrationNumber: "",
			trustRegistrationNumber: "",
			darpanId: "",
			organizationLegalType: "",
			addressLine1: "",
			addressLine2: "",
			postalCode: "",
			locality: "",
			state: "",
			city: "",
			country: ""
		}
  });
	const {watch, setValue} = form;

	// Watch all form values for real-time validation
	const watchedValues = watch();

	const postalCode = watch('postalCode');

	// Validation for each step
	const validateCurrentStep = () => {
		const values = watchedValues; // Use watched values for real-time updates

		if (currentStep === 0) {
			// Organization Details validation
			const isValid = !!(
				values.organizationName &&
				values.phoneNumber &&
				values.emailAddress &&
				values.organizationLegalType &&
				values.websiteUrl &&
				values.numberOfTeamMembers &&
				values.backgroundHistory &&
				values.missionVision
			);
			console.log("Step 0 validation:", {
				organizationName: !!values.organizationName,
				phoneNumber: !!values.phoneNumber,
				emailAddress: !!values.emailAddress,
				organizationLegalType: !!values.organizationLegalType,
				websiteUrl: !!values.websiteUrl,
				numberOfTeamMembers: !!values.numberOfTeamMembers,
				backgroundHistory: !!values.backgroundHistory,
				missionVision: !!values.missionVision,
				isValid
			});
			return isValid;
		} else if (currentStep === 1) {
			// Tax Details validation
			const isValid = !!(
				values.panNumber &&
				values.csrRegistrationNumber &&
				values.taxRegistrationNumber &&
				values.taxRegistrationNumberUnder12A &&
				values.fcraRegistrationNumber &&
				values.trustRegistrationNumber &&
				values.darpanId
			);
			console.log("Step 1 validation:", {
				panNumber: !!values.panNumber,
				csrRegistrationNumber: !!values.csrRegistrationNumber,
				taxRegistrationNumber: !!values.taxRegistrationNumber,
				taxRegistrationNumberUnder12A: !!values.taxRegistrationNumberUnder12A,
				fcraRegistrationNumber: !!values.fcraRegistrationNumber,
				trustRegistrationNumber: !!values.trustRegistrationNumber,
				darpanId: !!values.darpanId,
				isValid
			});
			return isValid;
		} else if (currentStep === 2) {
			// Address Information validation
			const isValid = !!(
				values.addressLine1 &&
				values.postalCode &&
				values.locality &&
				values.city &&
				values.state &&
				values.country
			);
			console.log("Step 2 validation:", {
				addressLine1: !!values.addressLine1,
				postalCode: !!values.postalCode,
				locality: !!values.locality,
				city: !!values.city,
				state: !!values.state,
				country: !!values.country,
				isValid
			});
			return isValid;
		}
		return false;
	};

	const nextStep = () => {
		if (currentStep < steps.length - 1 && validateCurrentStep()) {
			setCurrentStep(currentStep + 1);
		}
	};

	const prevStep = () => {
		if (currentStep > 0) {
			setCurrentStep(currentStep - 1);
		}
	};

  async function onSubmit(values: z.infer<typeof organizationDetailsFormSchema>) {
		setIsSubmitting(true);
		setError("");

		const payload = prepareOrganizationPayload(values);
		try {
			const response = await createProfile(payload);
			router.replace('/');
		} catch(error) {
			if (isAxiosError<{message: string}>(error)) {
				const err = error as AxiosError<{message: string}>;
				setError(err.response?.data?.message || "Failed to create organization profile");
			} else {
				setError("An unexpected error occurred");
			}
		} finally {
			setIsSubmitting(false);
		}
  }



	useEffect(() => {
		console.log("❌ Form errors:", form.formState.errors);

		const fetchPincodeDetails = async () => {
			if (postalCode && postalCode.length === 6) {
				try {
					console.log("Postal code = " + postalCode)
					const res = await fetch(`https://api.postalpincode.in/pincode/${postalCode}`);
					const data = await res.json();
					const postOffices = data[0].PostOffice;
					const postOffice = postOffices[0];

					setValue("city", postOffice.Block || "")
					setValue("state", postOffice.Circle || "")
					setValue("country", postOffice.Country || "")
					console.log(data)
				} catch(error: any) {
					console.log("ERROR: " + error);
				}
			}
		}

		fetchPincodeDetails();
	}, [postalCode, setValue, form.formState.errors])

  return (
    <div className="min-h-screen bg-white">
      {/* Main Content Container */}
      <div className="relative w-full max-w-6xl mx-auto px-6 py-8">
        {/* Header Text - Centered */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2 tracking-tight">
            Create Organization Profile
          </h1>
          <p className="text-gray-600 text-base mb-6">
            Complete your organization registration in a few simple steps
          </p>
        </div>

        {/* Progress Indicator */}
        <div className="flex items-center justify-center mb-6">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className="flex flex-col items-center">
                <div className={classNames(
                  "w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300",
                  index <= currentStep
                    ? "bg-teal-600 border-teal-600 text-white"
                    : "bg-white border-gray-300 text-gray-400"
                )}>
                  {index < currentStep ? (
                    <Check className="w-6 h-6" />
                  ) : (
                    <step.icon className="w-6 h-6" />
                  )}
                </div>
                <div className="mt-3 text-center w-32">
                  <div className={classNames(
                    "text-sm font-semibold transition-colors duration-300 mb-1",
                    index <= currentStep ? "text-teal-700" : "text-gray-500"
                  )}>
                    {step.title}
                  </div>
                  <div className="text-xs text-gray-500 leading-tight px-1">
                    {step.description}
                  </div>
                </div>
              </div>
              {index < steps.length - 1 && (
                <div className={classNames(
                  "w-16 h-0.5 mx-4 transition-colors duration-300",
                  index < currentStep ? "bg-teal-600" : "bg-gray-300"
                )} />
              )}
            </div>
          ))}
        </div>

        {/* Clean Main Card */}
        <Card className="bg-white border border-gray-200 shadow-lg rounded-xl">

        <CardContent className="p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
              {error && (
                <Alert variant="destructive" className="bg-red-50 border border-red-200 rounded-lg">
                  <AlertDescription className="text-red-700">{error}</AlertDescription>
                </Alert>
              )}

              {/* Step Content */}
              <div className="min-h-[450px]">
                {currentStep === 0 && (
                  <div className="space-y-5">
                    <div className="text-center mb-5">
                      <h2 className="text-xl font-bold text-gray-900 mb-2">Organization Details</h2>
                      <p className="text-gray-600">Tell us about your organization</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                      <FormField
                        control={form.control}
                        name="organizationName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Organization Name
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="XYZ NGO"
                                {...field}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="phoneNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Organization Phone Number
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="+91-9876543210"
                                {...field}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="emailAddress"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Organization Email
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="<EMAIL>"
                                {...field}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="organizationLegalType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Organization Legal Type
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm">
                                  <SelectValue placeholder="Select organization type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="NON_PROFIT">Non Profit</SelectItem>
                                <SelectItem value="TRUST">Trust</SelectItem>
                                <SelectItem value="SOCIETY">Society</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="websiteUrl"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Website URL
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="https://www.abc.com"
                                {...field}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="numberOfTeamMembers"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Number of Team Members
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="0"
                                type="number"
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 gap-5">
                      <FormField
                        control={form.control}
                        name="backgroundHistory"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Background History
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Tell us about your organization's background and history"
                                rows={4}
                                {...field}
                                className="rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm resize-none"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="missionVision"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Mission & Vision
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Describe your organization's mission and vision"
                                {...field}
                                rows={4}
                                className="rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm resize-none"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                )}

                {currentStep === 1 && (
                  <div className="space-y-5">
                    <div className="text-center mb-5">
                      <h2 className="text-xl font-bold text-gray-900 mb-2">Tax Details</h2>
                      <p className="text-gray-600">Registration numbers and tax information</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                      <FormField
                        control={form.control}
                        name="panNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              PAN Number
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="**********"
                                {...field}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="csrRegistrationNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              CSR Registration Number
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="CSR123456"
                                {...field}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="taxRegistrationNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Tax Registration Number
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="TAX123456"
                                {...field}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="taxRegistrationNumberUnder12A"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Tax Registration Number Under 12A
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="12A123456"
                                {...field}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="fcraRegistrationNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              FCRA Registration Number
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="FCRA123456"
                                {...field}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="trustRegistrationNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Trust Registration Number
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="TRUST123456"
                                {...field}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="darpanId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Darpan ID
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="DARPAN123456"
                                {...field}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                )}

                {currentStep === 2 && (
                  <div className="space-y-5">
                    <div className="text-center mb-5">
                      <h2 className="text-xl font-bold text-gray-900 mb-2">Address Information</h2>
                      <p className="text-gray-600">Location and contact details</p>
                    </div>

                    <div className="grid grid-cols-1 gap-5">
                      <FormField
                        control={form.control}
                        name="addressLine1"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Address Line 1
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="123, MG Road"
                                {...field}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="addressLine2"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Address Line 2
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Landmark (Optional)"
                                {...field}
                                value={field.value ?? ""}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                      <FormField
                        control={form.control}
                        name="postalCode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Pincode
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="411001"
                                {...field}
                                value={field.value ?? ""}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="locality"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Locality/Area
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Dadar"
                                {...field}
                                value={field.value ?? ""}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="city"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              City
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Mumbai"
                                {...field}
                                value={field.value ?? ""}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="state"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              State
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Maharashtra"
                                {...field}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="country"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-gray-800">
                              Country
                              <span className="text-red-600 ml-1">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="India"
                                {...field}
                                className="h-12 rounded-xl border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 focus:bg-teal-50/30 transition-all duration-300 shadow-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Navigation Buttons */}
              <div className="border-t border-gray-200 pt-6 mt-6">
                <div className="flex justify-between items-center">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={prevStep}
                    disabled={currentStep === 0}
                    className={classNames(
                      "h-12 px-6 rounded-lg font-medium transition-colors duration-200 min-w-[120px]",
                      currentStep === 0
                        ? "opacity-50 cursor-not-allowed"
                        : "hover:bg-gray-50"
                    )}
                  >
                    <ChevronLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>

                  {currentStep < steps.length - 1 ? (
                    <Button
                      type="button"
                      onClick={nextStep}
                      disabled={!validateCurrentStep()}
                      className={classNames(
                        "h-12 px-6 rounded-lg font-medium transition-colors duration-200 min-w-[120px]",
                        validateCurrentStep()
                          ? "bg-teal-600 hover:bg-teal-700 text-white"
                          : "bg-gray-300 text-gray-500 cursor-not-allowed"
                      )}
                    >
                      Next
                      <ChevronRight className="w-4 h-4 ml-2" />
                    </Button>
                  ) : (
                    <Button
                      type="submit"
                      disabled={isSubmitting || !validateCurrentStep()}
                      className={classNames(
                        "h-12 px-8 rounded-lg font-medium transition-colors duration-200 min-w-[160px]",
                        !isSubmitting && validateCurrentStep()
                          ? "bg-teal-600 hover:bg-teal-700 text-white"
                          : "bg-gray-300 text-gray-500 cursor-not-allowed"
                      )}
                    >
                      {isSubmitting ? (
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          Creating Profile...
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Building2 className="w-4 h-4" />
                          Create Organization
                        </div>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
      </div>
    </div>
  );
}
