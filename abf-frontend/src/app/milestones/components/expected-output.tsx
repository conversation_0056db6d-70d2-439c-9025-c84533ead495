"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MilestoneTable } from "./MilestoneTable";
import { useRouter } from "next/navigation";

interface MilestoneRow {
  sr_no: number;
  activity: string;
  description: string;
  target_group: string;
  defined_quarterly: { Q1: string; Q2: string; Q3: string; Q4: string };
  actuals_quarterly: { Q1: string; Q2: string; Q3: string; Q4: string };
  defined_attachments?: { Q1?: File; Q2?: File; Q3?: File; Q4?: File };
  actuals_attachments?: { Q1?: File; Q2?: File; Q3?: File; Q4?: File };
}

export function ExpectedOutputPage() {
  const router = useRouter();
  const [milestoneRows, setMilestoneRows] = useState<MilestoneRow[]>([{
    sr_no: 1,
    activity: "",
    description: "",
    target_group: "",
    defined_quarterly: { Q1: "", Q2: "", Q3: "", Q4: "" },
    actuals_quarterly: { Q1: "", Q2: "", Q3: "", Q4: "" },
    defined_attachments: { Q1: undefined, Q2: undefined, Q3: undefined, Q4: undefined },
    actuals_attachments: { Q1: undefined, Q2: undefined, Q3: undefined, Q4: undefined }
  }]);

  const handleAddRow = () => {
    const newRow: MilestoneRow = {
      sr_no: milestoneRows.length + 1,
      activity: "",
      description: "",
      target_group: "",
      defined_quarterly: { Q1: "", Q2: "", Q3: "", Q4: "" },
      actuals_quarterly: { Q1: "", Q2: "", Q3: "", Q4: "" },
      defined_attachments: { Q1: undefined, Q2: undefined, Q3: undefined, Q4: undefined },
      actuals_attachments: { Q1: undefined, Q2: undefined, Q3: undefined, Q4: undefined }
    };
    setMilestoneRows([...milestoneRows, newRow]);
  };

  const handleInputChange = (index: number, field: string, value: string | number | File) => {
    const updatedRows = [...milestoneRows];
    if (field.includes(".")) {
      const [parent, child] = field.split(".");
      if (parent === 'defined_attachments' || parent === 'actuals_attachments') {
        // Handle file attachments
        if (!updatedRows[index][parent]) {
          (updatedRows[index] as any)[parent] = {};
        }
        (updatedRows[index] as any)[parent][child] = value;
      } else if (parent === 'defined_quarterly' || parent === 'actuals_quarterly') {
        // Handle text values for quarterly fields
        (updatedRows[index] as any)[parent][child] = value.toString();
      } else {
        (updatedRows[index] as any)[parent][child] = value;
      }
    } else {
      (updatedRows[index] as any)[field] = value;
    }
    setMilestoneRows(updatedRows);
  };

  return (
    <div className="min-h-screen p-8 bg-gradient-to-br from-gray-50 to-gray-100">
      <Card className="max-w-7xl mx-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-2xl font-bold">Expected Output Management</CardTitle>
          <Button 
            onClick={() => router.push('/milestones')} 
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Back to Milestones
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <Card className="p-4">
              <CardTitle className="text-xl mb-4">Expected Output</CardTitle>
              <p className="text-gray-600 mb-4">
                Add the expected outputs for your project milestones. These are the tangible deliverables that will be produced as a result of your project activities.
              </p>
              <MilestoneTable
                milestoneRows={milestoneRows}
                onAddRow={handleAddRow}
                onInputChange={handleInputChange}
                tableTitle="Expected Output"
              />
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}