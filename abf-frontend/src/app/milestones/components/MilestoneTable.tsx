"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus as PlusIcon } from "lucide-react";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";

interface MilestoneRow {
  sr_no: number;
  activity: string;
  description: string;
  target_group: string;
  defined_quarterly: { Q1: string; Q2: string; Q3: string; Q4: string };
  actuals_quarterly: { Q1: string; Q2: string; Q3: string; Q4: string };
  defined_attachments?: { Q1?: File; Q2?: File; Q3?: File; Q4?: File };
  actuals_attachments?: { Q1?: File; Q2?: File; Q3?: File; Q4?: File };
}

interface MilestoneTableProps {
  milestoneRows: MilestoneRow[];
  onAddRow: () => void;
  onInputChange: (index: number, field: string, value: string | number) => void;
  tableTitle: string;
}

export function MilestoneTable({ milestoneRows, onAddRow, onInputChange, tableTitle }: MilestoneTableProps) {
  const [submittedRows, setSubmittedRows] = useState<MilestoneRow[]>([]);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const validateRow = (row: MilestoneRow) => {
    const rowErrors: {[key: string]: string} = {};
    
    // Validate required fields
    if (!row.activity.trim()) rowErrors.activity = 'This field is required';
    if (!row.description.trim()) rowErrors.description = 'This field is required';
    if (!row.target_group.trim()) rowErrors.target_group = 'This field is required';
    
    return rowErrors;
  };

  const calculateTotal = (field: string) => {
    return milestoneRows.reduce((total, row) => {
      if (field.includes(".")) {
        const [parent, child] = field.split(".");
        return total + ((row as any)[parent][child] || 0);
      }
      return total + ((row as any)[field] || 0);
    }, 0);
  };

  const handleSubmit = async () => {
    console.log('Starting milestone submission...');
    setErrors({});

    let hasValidationErrors = false;
    const allErrors: {[key: string]: string} = {};
  
    if (milestoneRows.length === 0) {
      console.log('No milestone rows found');
      toast.error('Please add at least one milestone row');
      return;
    }
  
    console.log('Validating milestone rows:', milestoneRows);
    milestoneRows.forEach((row, index) => {
      const rowErrors = validateRow(row);
      
      if (Object.keys(rowErrors).length > 0) {
        hasValidationErrors = true;
        Object.entries(rowErrors).forEach(([field, message]) => {
          allErrors[`${index}-${field}`] = message;
        });
      }
    });
  
    if (hasValidationErrors) {
      console.log('Validation errors found:', allErrors);
      setErrors(allErrors);
      const errorMessages = Object.values(allErrors);
      const uniqueErrors = [...new Set(errorMessages)];
      uniqueErrors.forEach(error => toast.error(error));
      return;
    }
  
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URI || 'http://localhost:8000';
    const updatedRows = [...milestoneRows];
    const results = [];
    
    try {
      console.log('Submitting milestone data to API...');
      for (let i = 0; i < milestoneRows.length; i++) {
        const row = milestoneRows[i];
        
        const formData = new FormData();
        formData.append('grant', '1');
        formData.append('activity', row.activity);
        formData.append('description', row.description);
        formData.append('target_group', row.target_group);
        formData.append('defined_q1', row.defined_quarterly.Q1.toString());
        formData.append('defined_q2', row.defined_quarterly.Q2.toString());
        formData.append('defined_q3', row.defined_quarterly.Q3.toString());
        formData.append('defined_q4', row.defined_quarterly.Q4.toString());
        formData.append('actual_q1', row.actuals_quarterly.Q1.toString());
        formData.append('actual_q2', row.actuals_quarterly.Q2.toString());
        formData.append('actual_q3', row.actuals_quarterly.Q3.toString());
        formData.append('actual_q4', row.actuals_quarterly.Q4.toString());
        formData.append('milestone_date', new Date().toISOString().split('T')[0]);
        formData.append('milestone_type', tableTitle.toLowerCase());
        
        // Append file attachments for defined quarterly if they exist
        if (row.defined_attachments?.Q1) {
          formData.append('defined_q1_attachment', row.defined_attachments.Q1);
        }
        if (row.defined_attachments?.Q2) {
          formData.append('defined_q2_attachment', row.defined_attachments.Q2);
        }
        if (row.defined_attachments?.Q3) {
          formData.append('defined_q3_attachment', row.defined_attachments.Q3);
        }
        if (row.defined_attachments?.Q4) {
          formData.append('defined_q4_attachment', row.defined_attachments.Q4);
        }
        
        // Append file attachments for actuals quarterly if they exist
        if (row.actuals_attachments?.Q1) {
          formData.append('actual_q1_attachment', row.actuals_attachments.Q1);
        }
        if (row.actuals_attachments?.Q2) {
          formData.append('actual_q2_attachment', row.actuals_attachments.Q2);
        }
        if (row.actuals_attachments?.Q3) {
          formData.append('actual_q3_attachment', row.actuals_attachments.Q3);
        }
        if (row.actuals_attachments?.Q4) {
          formData.append('actual_q4_attachment', row.actuals_attachments.Q4);
        }
  
        console.log(`Submitting row ${i + 1}:`);
        // In a real implementation, you would send this to the backend
        // For now, we'll just simulate a successful response
        
        // Mock successful response
        const mockResponse = {
          id: i + 1,
          ...row
        };
        
        console.log(`Success data for row ${i + 1}:`, mockResponse);
        results.push(mockResponse);
        
        updatedRows[i] = {
          ...row
        };
      }
      
      setSubmittedRows(updatedRows);
      console.log('All rows submitted successfully');
      
      // Reset the table after successful submission
      const emptyRow = {
        sr_no: 1,
        activity: "",
        description: "",
        target_group: "",
        defined_quarterly: { Q1: "", Q2: "", Q3: "", Q4: "" },
        actuals_quarterly: { Q1: "", Q2: "", Q3: "", Q4: "" },
        defined_attachments: { Q1: undefined, Q2: undefined, Q3: undefined, Q4: undefined },
        actuals_attachments: { Q1: undefined, Q2: undefined, Q3: undefined, Q4: undefined }
      };
      
      // Reset all fields for the first row
      Object.keys(emptyRow).forEach(field => {
        if (field === 'defined_quarterly' || field === 'actuals_quarterly') {
          Object.keys(emptyRow[field as keyof typeof emptyRow]).forEach(subField => {
            onInputChange(0, `${field}.${subField}`, "");
          });
        } else if (field === 'defined_attachments' || field === 'actuals_attachments') {
          // Skip resetting attachments as they'll be cleared when the component resets
        } else {
          onInputChange(0, field, emptyRow[field as keyof typeof emptyRow]);
        }
      });
      
      // Remove all additional rows
      while (milestoneRows.length > 1) {
        milestoneRows.pop();
      }
      
      setErrors({});
      setSubmittedRows([]);
      
      toast.success(
        <div className="font-semibold">
          <div className="text-xl mb-2">Success! 🎉</div>
          <div>Your {tableTitle.toLowerCase()} data has been successfully submitted</div>
        </div>,
        {
          duration: 6000,
          position: 'top-center',
          style: {
            background: '#4CAF50',
            color: '#fff',
            fontSize: '16px',
            padding: '16px',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
            maxWidth: '400px',
            width: '100%'
          }
        }
      );
    } catch (error) {
      console.error(`Error submitting ${tableTitle.toLowerCase()} data:`, error);
      toast.error(error instanceof Error ? error.message : `Failed to submit ${tableTitle.toLowerCase()} data`);
    }
  };

  // Use submitted rows if available, otherwise use the original rows
  const displayRows = submittedRows.length > 0 ? submittedRows : milestoneRows;

  return (
    <div className="overflow-x-auto">
      <div className="flex justify-between mb-4">
        <Button onClick={onAddRow} variant="outline" className="flex items-center gap-2">
          <PlusIcon className="w-4 h-4" /> Add Row
        </Button>
      </div>
      <div className="rounded-lg border shadow-sm overflow-hidden">
        <table className="w-full border-collapse bg-white text-base">
          <thead>
            <tr className="bg-gray-100 text-base">
              <th className="p-4 border text-left font-semibold text-base">Sr no.</th>
              <th className="p-4 border text-left font-semibold text-base">Activity</th>
              <th className="p-4 border text-left font-semibold text-base">Description</th>
              <th className="p-4 border text-left font-semibold text-base">Target Group</th>
              <th className="p-4 border text-center font-semibold text-base" colSpan={8}>Defined quarterly breakup</th>
              <th className="p-4 border text-center font-semibold text-base" colSpan={8}>Actuals quarterly breakup</th>
            </tr>
            <tr className="bg-gray-50 text-base">
              <th className="p-4 border text-base" colSpan={4}></th>
              <th className="p-4 border text-center text-base" colSpan={2}>Q1</th>
              <th className="p-4 border text-center text-base" colSpan={2}>Q2</th>
              <th className="p-4 border text-center text-base" colSpan={2}>Q3</th>
              <th className="p-4 border text-center text-base" colSpan={2}>Q4</th>
              <th className="p-4 border text-center text-base" colSpan={2}>Q1</th>
              <th className="p-4 border text-center text-base" colSpan={2}>Q2</th>
              <th className="p-4 border text-center text-base" colSpan={2}>Q3</th>
              <th className="p-4 border text-center text-base" colSpan={2}>Q4</th>
            </tr>
            <tr className="bg-gray-50 text-base">
              <th className="p-4 border text-base" colSpan={4}></th>
              <th className="p-4 border text-center text-base">Value</th>
              <th className="p-4 border text-center text-base">File</th>
              <th className="p-4 border text-center text-base">Value</th>
              <th className="p-4 border text-center text-base">File</th>
              <th className="p-4 border text-center text-base">Value</th>
              <th className="p-4 border text-center text-base">File</th>
              <th className="p-4 border text-center text-base">Value</th>
              <th className="p-4 border text-center text-base">File</th>
              <th className="p-4 border text-center text-base">Value</th>
              <th className="p-4 border text-center text-base">File</th>
              <th className="p-4 border text-center text-base">Value</th>
              <th className="p-4 border text-center text-base">File</th>
              <th className="p-4 border text-center text-base">Value</th>
              <th className="p-4 border text-center text-base">File</th>
              <th className="p-4 border text-center text-base">Value</th>
              <th className="p-4 border text-center text-base">File</th>
            </tr>
          </thead>
          <tbody>
            {displayRows.map((row, index) => (
              <tr key={index} className="hover:bg-gray-50">
                <td className="p-3 border min-w-[120px]">{row.sr_no}</td>
                <td className="p-3 border min-w-[120px]">
                  <div className="relative">
                    <Input
                      type="text"
                      value={row.activity}
                      onChange={(e) => onInputChange(index, 'activity', e.target.value)}
                      className={`w-full min-h-[40px] text-base px-3 py-2 ${errors[`${index}-activity`] ? 'border-red-500' : ''}`}
                    />
                    {errors[`${index}-activity`] && (
                      <div className="text-red-500 text-xs mt-1">{errors[`${index}-activity`]}</div>
                    )}
                  </div>
                </td>
                <td className="p-3 border min-w-[120px]">
                  <div className="relative">
                    <Input
                      type="text"
                      value={row.description}
                      onChange={(e) => onInputChange(index, 'description', e.target.value)}
                      className={`w-full min-h-[40px] text-base px-3 py-2 ${errors[`${index}-description`] ? 'border-red-500' : ''}`}
                    />
                    {errors[`${index}-description`] && (
                      <div className="text-red-500 text-xs mt-1">{errors[`${index}-description`]}</div>
                    )}
                  </div>
                </td>
                <td className="p-3 border min-w-[120px]">
                  <div className="relative">
                    <Input
                      type="text"
                      value={row.target_group}
                      onChange={(e) => onInputChange(index, 'target_group', e.target.value)}
                      className={`w-full min-h-[40px] text-base px-3 py-2 ${errors[`${index}-target_group`] ? 'border-red-500' : ''}`}
                    />
                    {errors[`${index}-target_group`] && (
                      <div className="text-red-500 text-xs mt-1">{errors[`${index}-target_group`]}</div>
                    )}
                  </div>
                </td>
                {/* Defined quarterly values and file uploads */}
                {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                  <React.Fragment key={`defined_${quarter}`}>
                    <td className="p-3 border min-w-[120px]">
                      <Input
                        type="text"
                        value={row.defined_quarterly[quarter as keyof typeof row.defined_quarterly]}
                        onChange={(e) => onInputChange(index, `defined_quarterly.${quarter}`, e.target.value)}
                        className="w-full min-h-[40px] text-base px-3 py-2"
                      />
                    </td>
                    <td className="p-3 border min-w-[120px]">
                      <Input
                        type="file"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            onInputChange(index, `defined_attachments.${quarter}`, file);
                          }
                        }}
                        className="w-full min-h-[40px] text-sm py-1"
                        accept=".pdf,.jpg,.jpeg,.png"
                      />
                      {row.defined_attachments?.[quarter as keyof typeof row.defined_attachments] && (
                        <div className="text-sm text-gray-600 mt-1">
                          {(row.defined_attachments[quarter as keyof typeof row.defined_attachments] as File).name}
                        </div>
                      )}
                    </td>
                  </React.Fragment>
                ))}
                
                {/* Actuals quarterly values and file uploads */}
                {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                  <React.Fragment key={`actual_${quarter}`}>
                    <td className="p-3 border min-w-[120px]">
                      <Input
                        type="text"
                        value={row.actuals_quarterly[quarter as keyof typeof row.actuals_quarterly]}
                        onChange={(e) => onInputChange(index, `actuals_quarterly.${quarter}`, e.target.value)}
                        className="w-full min-h-[40px] text-base px-3 py-2"
                      />
                    </td>
                    <td className="p-3 border min-w-[120px]">
                      <Input
                        type="file"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            onInputChange(index, `actuals_attachments.${quarter}`, file);
                          }
                        }}
                        className="w-full min-h-[40px] text-sm py-1"
                        accept=".pdf,.jpg,.jpeg,.png"
                      />
                      {row.actuals_attachments?.[quarter as keyof typeof row.actuals_attachments] && (
                        <div className="text-sm text-gray-600 mt-1">
                          {(row.actuals_attachments[quarter as keyof typeof row.actuals_attachments] as File).name}
                        </div>
                      )}
                    </td>
                  </React.Fragment>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="flex justify-end mt-4">
        <Button
          onClick={handleSubmit}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          Submit {tableTitle}
        </Button>
      </div>
    </div>
  );
}