"use client";

import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function MilestonePage() {
  const router = useRouter();

  return (
    <div className="min-h-screen p-8 bg-gradient-to-br from-gray-50 to-gray-100">
      <Card className="max-w-7xl mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">Milestone Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => router.push('/milestones/expected-output')}>
              <CardTitle className="text-xl mb-4">Expected Output</CardTitle>
              <p className="text-gray-600">You can add expected output for the milestone</p>
            </Card>
            <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => router.push('/milestones/expected-outcomes')}>
              <CardTitle className="text-xl mb-4">Expected Outcomes</CardTitle>
              <p className="text-gray-600">You can add expected outcomes for the milestone</p>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}