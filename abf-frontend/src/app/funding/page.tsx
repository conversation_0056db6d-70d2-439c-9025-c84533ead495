"use client";

import { useState, useEffect } from 'react';
import { useFunding } from '@/hooks/useFunding';
import { useGrantFinancial } from '@/hooks/useGrantFinancial';
import { motion, AnimatePresence } from 'framer-motion';
import { FundingSummaryCard } from '@/components/funding/FundingSummaryCard';
import { QuarterlyBudgetVsActualsChart } from '@/components/funding/QuarterlyBudgetVsActualsChart';
import { useExpenseData } from '@/contexts/ExpenseDataContext';
import { DisbursementTable } from '@/components/funding/DisbursementTable';
import { CurrencyDollarIcon, ChartBarIcon, WalletIcon, IndianRupeeIcon } from '@/components/ui/icons';
import { QuarterlyDateSelector } from '@/components/funding/QuarterlyDateSelector';
import { GrantSelector } from '@/components/funding/GrantSelector';
import { SkeletonCard, SkeletonChartCard, SkeletonTable } from '@/components/funding/SkeletonCard';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Download } from 'lucide-react';

export default function FundingPage() {
  const [selectedGrant, setSelectedGrant] = useState('all');
  const [selectedYear, setSelectedYear] = useState('2025'); // Default to current year
  const [selectedQuarter, setSelectedQuarter] = useState('All');
  const [viewMode, setViewMode] = useState<'quarterly' | 'yearly'>('quarterly');
  const [statusFilter, setStatusFilter] = useState('All');

  const {
    isLoading,
    disbursementHistory,
    formatCurrency,
    refreshData
  } = useFunding();

  const {
    grants,
    isLoading: isGrantDataLoading,
    refreshData: refreshGrantData
  } = useGrantFinancial();

  // Get quarterly data from context (uploaded expense data and backend data)
  const {
    getQuarterlyChartData,
    getBudgetOverviewData,
    refreshBackendData,
    refreshQuarterlyData,
    isLoading: isExpenseDataLoading
  } = useExpenseData();

  // Get budget overview data from expense context (connected to quarterly totals)
  const budgetOverviewData = getBudgetOverviewData(selectedGrant, selectedQuarter);

  // Get quarterly chart data from expense context
  const quarterlyExpenseData = getQuarterlyChartData(selectedGrant, selectedQuarter);

  // Use expense data for budget overview (connected to quarterly totals)
  const displayData = {
    allocated_amount: budgetOverviewData.totalBudget,
    disbursed_amount: budgetOverviewData.disbursedAmount,
    utilized_amount: budgetOverviewData.totalActual,
    remaining_balance: budgetOverviewData.remainingBalance,
    quarterly_data: quarterlyExpenseData
  };

  // Debug: Log the quarterly data
  console.log('Selected grant:', selectedGrant);
  console.log('Selected year:', selectedYear);
  console.log('Selected quarter:', selectedQuarter);
  console.log('Budget overview data:', budgetOverviewData);
  console.log('Display data:', displayData);
  console.log('Quarterly expense data for chart:', quarterlyExpenseData);

  // Filter disbursement history based on status
  const filteredHistory = statusFilter === 'All'
    ? disbursementHistory
    : disbursementHistory.filter(item => item.status === statusFilter);

  // Refresh data on date or grant change
  useEffect(() => {
    refreshData();
    refreshGrantData();
    refreshBackendData(); // Refresh expense data from backend
  }, [selectedYear, selectedGrant]);

  // Separate effect for quarterly data to ensure it refreshes when quarter filter changes
  useEffect(() => {
    refreshQuarterlyData(selectedGrant, selectedQuarter, selectedYear); // Refresh quarterly data with filters
  }, [selectedYear, selectedQuarter, selectedGrant]);

  if (isLoading || isGrantDataLoading || isExpenseDataLoading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <div className="h-8 w-48 bg-gray-200 rounded-md animate-pulse"></div>
            <div className="h-4 w-64 bg-gray-200 rounded-md animate-pulse mt-2"></div>
          </div>
        </div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6 bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <div className="h-10 w-48 bg-gray-200 rounded-md animate-pulse"></div>
          <div className="h-10 w-64 bg-gray-200 rounded-md animate-pulse"></div>
        </div>

        {/* Skeleton Summary Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <SkeletonCard />
            </motion.div>
          ))}
        </div>

        {/* Skeleton Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
          >
            <SkeletonChartCard />
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.5 }}
          >
            <SkeletonChartCard />
          </motion.div>
        </div>

        {/* Skeleton Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.6 }}
          className="mt-6"
        >
          <SkeletonTable />
        </motion.div>
      </div>
    );
  }

  return (
      <div className="space-y-8 pb-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-700 bg-clip-text text-transparent">Budget Overview</h1>
            <p className="text-sm text-gray-500 mt-1">Track and manage your grant budget</p>
          </div>
        </div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6 bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <GrantSelector
            selectedGrant={selectedGrant}
            setSelectedGrant={setSelectedGrant}
            grants={grants}
            isLoading={isGrantDataLoading}
          />

          <QuarterlyDateSelector
            selectedQuarter={selectedQuarter}
            setSelectedQuarter={setSelectedQuarter}
            selectedYear={selectedYear}
            setSelectedYear={setSelectedYear}
            viewMode={viewMode}
            setViewMode={setViewMode}
          />
        </div>

        <AnimatePresence>
          <motion.div
            key="funding-summary-cards"
            className="grid grid-cols-1 md:grid-cols-4 gap-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ staggerChildren: 0.1 }}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <FundingSummaryCard
                title="Grant Allocated"
                amount={formatCurrency(displayData.allocated_amount)}
                icon={<IndianRupeeIcon className="w-5 h-5" />}
                accentColor="sage"
                description="Total funding allocated to your organization"
              />
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <FundingSummaryCard
                title="Disbursed Amount"
                amount={formatCurrency(displayData.disbursed_amount)}
                icon={<ChartBarIcon className="w-5 h-5" />}
                accentColor="mint"
                description="Budget disbursed for current period"
              />
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.15 }}
            >
              <FundingSummaryCard
                title="Remaining Balance"
                amount={formatCurrency(displayData.remaining_balance)}
                icon={<CurrencyDollarIcon className="w-5 h-5" />}
                accentColor="cyan"
                description="Available funds remaining in the grant"
              />
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <FundingSummaryCard
                title="Utilized"
                amount={formatCurrency(displayData.utilized_amount)}
                icon={<WalletIcon className="w-5 h-5" />}
                accentColor="forest"
                description="Total amount utilized from the grant"
              />
            </motion.div>
          </motion.div>
        </AnimatePresence>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <QuarterlyBudgetVsActualsChart
            data={quarterlyExpenseData}
            formatCurrency={formatCurrency}
          />
        </motion.div>

        {/* Disbursement History Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.4 }}
        >
          <Card className="shadow-md hover:shadow-xl transition-all duration-300 border-0 rounded-xl overflow-hidden">
            <div className="h-1.5 bg-gradient-to-r from-emerald-600 via-emerald-500 to-emerald-400"></div>
            <CardContent className="p-6 relative">
              <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-gray-100/80 to-transparent rounded-bl-full"></div>
              <div className="flex justify-between items-center mb-6 relative z-10">
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">Disbursement History</h3>
                  <p className="text-sm text-gray-500 mt-1">View and manage your disbursement records</p>
                </div>
                <div className="flex items-center gap-3">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[150px] border-emerald-200 focus:ring-emerald-500">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="All">All Status</SelectItem>
                      <SelectItem value="Disbursed">Disbursed</SelectItem>
                      <SelectItem value="Pending">Pending</SelectItem>
                      <SelectItem value="Failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" size="sm" className="flex items-center gap-1 border-emerald-200 text-emerald-700 hover:bg-emerald-50 hover:text-emerald-800">
                    <Download className="h-4 w-4" />
                    <span>Export</span>
                  </Button>
                </div>
              </div>

              <div className="overflow-hidden rounded-xl border border-gray-200 shadow-sm">
                <DisbursementTable data={filteredHistory} formatCurrency={formatCurrency} />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
  );
}