"use client";

import React from 'react';
import { FundingSummaryCard } from '@/components/funding/FundingSummaryCard';
import { CurrencyDollarIcon, ChartBarIcon, WalletIcon, IndianRupeeIcon } from '@/components/ui/icons';
import { useFunding } from '@/hooks/useFunding';
import { motion } from 'framer-motion';

export default function FundingSummaryPage() {
  const {
    grantSummary,
    formatCurrency,
    isLoading
  } = useFunding();

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-pulse text-teal-500 font-medium">Loading funding data...</div>
      </div>
    );
  }

  return (
    <div className="space-y-8 pb-8">
      <div className="bg-gradient-to-r from-teal-50 to-blue-50 p-6 rounded-lg shadow-sm mb-8">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-2xl font-bold text-gray-800">Funding Summary</h1>
          <p className="text-gray-600 mt-1">Overview of your grant funding status</p>
        </motion.div>
      </div>

      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ staggerChildren: 0.1 }}
      >
        <FundingSummaryCard
          title="Grant Allocated"
          amount={formatCurrency(grantSummary?.totalGrantAmount || 0)}
          icon={<IndianRupeeIcon className="w-5 h-5" />}
          accentColor="teal"
          description="Total funding allocated to your organization"
        />

        <FundingSummaryCard
          title="Received Amount"
          amount={formatCurrency(grantSummary?.allocatedBudget || 0)}
          icon={<ChartBarIcon className="w-5 h-5" />}
          accentColor="blue"
          description="Budget allocated for current period"
        />

        <FundingSummaryCard
          title="Spent to Date"
          amount={formatCurrency(grantSummary?.spentToDate || 0)}
          icon={<WalletIcon className="w-5 h-5" />}
          accentColor="amber"
          description="Total amount spent from the grant"
        />

        <FundingSummaryCard
          title="Remaining Balance"
          amount={formatCurrency(grantSummary?.remainingBalance || 0)}
          icon={<CurrencyDollarIcon className="w-5 h-5" />}
          accentColor="emerald"

          description="Available funds remaining in the grant"
        />
      </motion.div>

      <div className="mt-8 p-6 bg-white rounded-lg shadow-md border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Grant Timeline</h2>
        <div className="flex justify-between items-center">
          <div className="text-center">
            <p className="text-sm text-gray-500">Start Date</p>
            <p className="font-medium text-gray-800">{grantSummary?.grantStartDate || 'N/A'}</p>
          </div>

          <div className="grow mx-4 h-1 bg-gradient-to-r from-teal-500 to-emerald-500 rounded-full"></div>

          <div className="text-center">
            <p className="text-sm text-gray-500">End Date</p>
            <p className="font-medium text-gray-800">{grantSummary?.grantEndDate || 'N/A'}</p>
          </div>
        </div>
      </div>
    </div>
  );
}