'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { fetchExpenseDetail } from '@/services/expenses-service';
import { Download, Edit, ArrowLeft, AlertCircle } from 'lucide-react';
import { motion } from 'framer-motion';

interface EditHistory {
  field: string;
  row_id: number;
  new_value: string;
  old_value: string;
  timestamp: string;
}

interface Expense {
  id: string;
  particulars: string;
  main_header: string;
  sub_header: string;
  units: string;
  frequency: string;
  cost_per_unit: number;
  activity_description: string;
  budget_q1: number;
  budget_q2: number;
  budget_q3: number;
  budget_q4: number;
  actual_q1: number;
  actual_q2: number;
  actual_q3: number;
  actual_q4: number;
  total_budget: number;
  total_grant_budget: number;
  total_actual: number;
  grant_name: string;
  remarks: string | null;
  rejection_notes?: string | null;
  expense_date: string;
  source_type: string;
  status: string;
  is_frozen: boolean;
  metadata: { edit_history?: EditHistory[] };
  created_at?: string;
  updated_at?: string;
}

const formatCurrency = (value: number | null | undefined) => {
  if (value == null) return 'N/A';
  return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(value);
};

const formatDateTime = (date: string | null | undefined) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleString('en-IN', { dateStyle: 'medium', timeStyle: 'short' });
};

const getStatusStyles = (status: string) => {
  switch (status.toLowerCase()) {
    case 'pending':
      return 'bg-amber-100 text-amber-800';
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export default function ExpenseDetailPage() {
  const router = useRouter();
  const { id } = useParams();
  const [expense, setExpense] = useState<Expense | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function loadExpenseDetail() {
      setIsLoading(true);
      try {
        const data = await fetchExpenseDetail(id as string);
        // Handle rejection_notes from either direct field or metadata
        if (data.metadata?.rejection_notes && !data.rejection_notes) {
          data.rejection_notes = data.metadata.rejection_notes;
        }
        setExpense(data);
      } catch (error) {
        console.error('Failed to fetch expense details:', error);
      } finally {
        setIsLoading(false);
      }
    }

    if (id) {
      loadExpenseDetail();
    }
  }, [id]);

  const handleDownloadPDF = () => {
    // Placeholder for PDF download logic
    alert('PDF download functionality to be implemented');
  };

  if (isLoading) {
    return (
      <div className="p-6 space-y-6 max-w-5xl mx-auto">
        <div className="h-8 w-48 bg-gray-200 rounded animate-pulse"></div>
        <Card className="p-4">
          <CardContent className="space-y-4">
            <div className="h-6 w-64 bg-gray-200 rounded animate-pulse"></div>
            <div className="grid grid-cols-2 gap-4">
              {[...Array(16)].map((_, i) => (
                <div key={i} className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!expense) {
    return (
      <div className="flex items-center justify-center py-12 text-red-600">
        <p className="text-lg font-semibold">Expense not found.</p>
      </div>
    );
  }

  const editHistory = expense.metadata?.edit_history || [];

  return (
    <div className="p-6 space-y-6 max-w-5xl mx-auto">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-800">Expense Details</h1>
        <div className="flex gap-2">

          <Button
            onClick={() => router.push('/funding/expenses')}
            className="bg-gradient-to-r from-teal-600 to-teal-500 text-white flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Expenses List
          </Button>
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        <Card className="shadow-md rounded-xl border-0 overflow-hidden">
          <div className="h-1 bg-teal-600"></div>
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-gray-800">
              Expense ID: {expense.id}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-8">
            {/* General Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-700 mb-4">General Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 bg-gray-50 p-4 rounded-lg">
                <div>
                  <strong className="text-gray-600">Particulars:</strong>
                  <p className="text-gray-800">{expense.particulars || 'N/A'}</p>
                </div>
                <div>
                  <strong className="text-gray-600">Main Header:</strong>
                  <p className="text-gray-800">{expense.main_header || 'N/A'}</p>
                </div>
                <div>
                  <strong className="text-gray-600">Sub Header:</strong>
                  <p className="text-gray-800">{expense.sub_header || 'N/A'}</p>
                </div>
                <div>
                  <strong className="text-gray-600">Grant:</strong>
                  <p className="text-gray-800">{expense.grant_name || 'N/A'}</p>
                </div>
                <div>
                  <strong className="text-gray-600">Status:</strong>
                  <span
                    className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusStyles(expense.status)}`}
                  >
                    {expense.status.charAt(0).toUpperCase() + expense.status.slice(1).toLowerCase()}
                  </span>
                </div>
                <div>
                  <strong className="text-gray-600">Expense Date:</strong>
                  <p className="text-gray-800">{expense.expense_date || 'N/A'}</p>
                </div>
                <div>
                  <strong className="text-gray-600">Units:</strong>
                  <p className="text-gray-800">{expense.units || 'N/A'}</p>
                </div>
                <div>
                  <strong className="text-gray-600">Frequency:</strong>
                  <p className="text-gray-800">{expense.frequency || 'N/A'}</p>
                </div>
                <div>
                  <strong className="text-gray-600">Source Type:</strong>
                  <p className="text-gray-800">{expense.source_type || 'N/A'}</p>
                </div>
                <div>
                  <strong className="text-gray-600">Frozen:</strong>
                  <p className="text-gray-800">{expense.is_frozen ? 'Yes' : 'No'}</p>
                </div>
              </div>
            </div>

            {/* Financial Details */}
            <div>
              <h3 className="text-lg font-semibold text-gray-700 mb-4">Financial Details</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-4 mb-4">
                  <div>
                    <strong className="text-gray-600">Cost per Unit:</strong>
                    <p className="text-gray-800">{formatCurrency(expense.cost_per_unit)}</p>
                  </div>
                  <div>
                    <strong className="text-gray-600">Total Budget:</strong>
                    <p className="text-gray-800">{formatCurrency(expense.total_budget)}</p>
                  </div>
                  <div>
                    <strong className="text-gray-600">Total Actual:</strong>
                    <p className="text-gray-800">{formatCurrency(expense.total_actual)}</p>
                  </div>
                  <div>
                    <strong className="text-gray-600">Total Grant Budget:</strong>
                    <p className="text-gray-800">{formatCurrency(expense.total_grant_budget)}</p>
                  </div>
                </div>
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="text-left p-3 text-gray-600">Quarter</th>
                      <th className="text-left p-3 text-gray-600">Budget</th>
                      <th className="text-left p-3 text-gray-600">Actual</th>
                      <th className="text-left p-3 text-gray-600">Variance</th>
                    </tr>
                  </thead>
                  <tbody>
                    {['Q1', 'Q2', 'Q3', 'Q4'].map((quarter, index) => {
                      const budget = expense[`budget_q${index + 1}` as keyof Expense];
                      const actual = expense[`actual_q${index + 1}` as keyof Expense];
                      const variance = (budget || 0) - (actual || 0);
                      return (
                        <tr key={quarter} className="border-b hover:bg-gray-50">
                          <td className="p-3 text-gray-700">{quarter}</td>
                          <td className="p-3 text-gray-700">{formatCurrency(budget)}</td>
                          <td className="p-3 text-gray-700">{formatCurrency(actual)}</td>
                          <td className="p-3 text-gray-700">
                            <span
                              className={variance < 0 ? 'text-red-600' : 'text-green-600'}
                            >
                              {formatCurrency(variance)}
                            </span>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Activity Description */}
            <div>
              <h3 className="text-lg font-semibold text-gray-700 mb-4">Activity Description</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-800">{expense.activity_description || 'None'}</p>
              </div>
            </div>

            {/* Notes */}
            <div>
              <h3 className="text-lg font-semibold text-gray-700 mb-4">Notes</h3>
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <strong className="text-gray-600">Remarks:</strong>
                  <p className="text-gray-800 mt-1">{expense.remarks || 'None'}</p>
                </div>

                {/* Rejection Notes - Only show if expense is rejected and has rejection notes */}
                {expense.status.toLowerCase() === 'rejected' && expense.rejection_notes && (
                  <div className="border-l-4 border-red-500 pl-4 bg-red-50 p-4 rounded-r-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertCircle className="h-4 w-4 text-red-600" />
                      <strong className="text-red-600 font-semibold uppercase tracking-wide text-sm">
                        Rejection Notes
                      </strong>
                    </div>
                    <p className="text-red-700 font-medium leading-relaxed">{expense.rejection_notes}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Edit History */}
            <div>
              <h3 className="text-lg font-semibold text-gray-700 mb-4">Edit History</h3>
              {editHistory.length > 0 ? (
                <div className="overflow-x-auto bg-gray-50 p-4 rounded-lg max-h-96">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="text-left p-3 text-gray-600">Timestamp</th>
                        <th className="text-left p-3 text-gray-600">Field</th>
                        <th className="text-left p-3 text-gray-600">Old Value</th>
                        <th className="text-left p-3 text-gray-600">New Value</th>
                      </tr>
                    </thead>
                    <tbody>
                      {editHistory.map((edit, index) => (
                        <motion.tr
                          key={index}
                          className="border-b hover:bg-gray-50"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: index * 0.05 }}
                        >
                          <td className="p-3 text-gray-700">
                            {formatDateTime(edit.timestamp)}
                          </td>
                          <td className="p-3 text-gray-700">
                            {edit.field.charAt(0).toUpperCase() + edit.field.slice(1)}
                          </td>
                          <td className="p-3 text-gray-700">{edit.old_value || 'N/A'}</td>
                          <td className="p-3 text-gray-700">{edit.new_value || 'N/A'}</td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-gray-500 bg-gray-50 p-4 rounded-lg">
                  No edit history available.
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}