'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { ArrowLeft, Download, FileSpreadsheet, FileText } from 'lucide-react';
import { ExcelAttachmentViewer } from '@/components/funding/expense/ExcelAttachmentViewer';
import { useRouter } from 'next/navigation';
import { fundingService } from '@/services/funding-service';
import * as granteeExpenseService from '@/services/grantee-expense-service';

interface ExpenseDetail {
  id: string;
  particulars: string;
  main_header: string;
  sub_headers: string;
  units?: string;
  frequency?: string;
  cost_per_unit?: number;
  budget_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  actuals_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  total_budget: number;
  total_actual: number;
  expense_date: string;
  source_type: 'excel' | 'manual' | string;
  attachment?: string;
  receipt?: File | string | null;
  excel_data?: any[];
  status?: 'pending' | 'approved' | 'rejected';
  remarks?: string;
  rejection_notes?: string;
}

export default function ExpenseDetailClient({ expenseId }: { expenseId: string }) {
  const router = useRouter();
  const [expense, setExpense] = useState<ExpenseDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [showExcelViewer, setShowExcelViewer] = useState(false);

  useEffect(() => {
    fetchExpenseDetail();
  }, [expenseId]);

  const fetchExpenseDetail = async () => {
    setLoading(true);
    try {
      // In a real implementation, this would fetch from the API
      // const response = await fetch(`${API_BASE_URL}/api/funding/v1/expenses/${expenseId}`);
      // const data = await response.json();

      // For now, use mock data
      const mockExpenses = [
        {
          id: 'EXP-2024-001',
          particulars: 'Staff Salaries',
          main_header: 'Human Resources',
          sub_headers: 'Permanent Staff',
          units: 'Months',
          frequency: '12',
          cost_per_unit: 50000,
          budget_quarterly: { Q1: 150000, Q2: 150000, Q3: 150000, Q4: 150000 },
          actuals_quarterly: { Q1: 145000, Q2: 150000, Q3: 148000, Q4: 0 },
          total_budget: 600000,
          total_actual: 443000,
          expense_date: '2024-01-15',
          source_type: 'manual',
          attachment: 'Manual Entry',
          receipt: 'salary-receipt-jan2024.pdf'
        },
        {
          id: 'EXP-2024-002',
          particulars: 'Office Rent',
          main_header: 'Administrative',
          sub_headers: 'Facilities',
          units: 'Months',
          frequency: '12',
          cost_per_unit: 25000,
          budget_quarterly: { Q1: 75000, Q2: 75000, Q3: 75000, Q4: 75000 },
          actuals_quarterly: { Q1: 75000, Q2: 75000, Q3: 75000, Q4: 0 },
          total_budget: 300000,
          total_actual: 225000,
          expense_date: '2024-02-01',
          source_type: 'excel',
          attachment: 'expense-data.xlsx',
          receipt: undefined,
          excel_data: [
            {
              particulars: 'Office Rent',
              main_header: 'Administrative',
              sub_headers: 'Facilities',
              units: 'Months',
              frequency: '12',
              cost_per_unit: 25000,
              budget_quarterly: { Q1: 75000, Q2: 75000, Q3: 75000, Q4: 75000 },
              actuals_quarterly: { Q1: 75000, Q2: 75000, Q3: 75000, Q4: 0 }
            }
          ]
        },
        // Add more mock expenses to match the ones in the expense history
        {
          id: 'EXP-2024-003',
          particulars: 'Program Supplies',
          main_header: 'Programs',
          sub_headers: 'Materials',
          units: 'Items',
          frequency: '4',
          cost_per_unit: 15000,
          budget_quarterly: { Q1: 15000, Q2: 15000, Q3: 15000, Q4: 15000 },
          actuals_quarterly: { Q1: 14500, Q2: 14800, Q3: 14700, Q4: 14000 },
          total_budget: 60000,
          total_actual: 58000,
          expense_date: '2024-02-15',
          source_type: 'excel',
          attachment: 'expense-data.xlsx',
          excel_data: [
            {
              particulars: 'Program Supplies',
              main_header: 'Programs',
              sub_headers: 'Materials',
              budget_quarterly: { Q1: 15000, Q2: 15000, Q3: 15000, Q4: 15000 },
              actuals_quarterly: { Q1: 14500, Q2: 14800, Q3: 14700, Q4: 14000 }
            }
          ]
        },
        {
          id: 'EXP-2024-004',
          particulars: 'Personnel Costs',
          main_header: 'Personnel',
          sub_headers: 'Salaries',
          units: 'Months',
          frequency: '12',
          cost_per_unit: 4583,
          budget_quarterly: { Q1: 13750, Q2: 13750, Q3: 13750, Q4: 13750 },
          actuals_quarterly: { Q1: 13000, Q2: 13000, Q3: 13000, Q4: 13000 },
          total_budget: 55000,
          total_actual: 52000,
          expense_date: '2024-03-01',
          source_type: 'manual',
          attachment: 'expense-4.pdf',
          receipt: 'personnel-receipt-mar2024.pdf'
        },
        {
          id: 'EXP-2024-005',
          particulars: 'Miscellaneous',
          main_header: 'Other',
          sub_headers: 'General',
          units: 'Items',
          frequency: '4',
          cost_per_unit: 10000,
          budget_quarterly: { Q1: 10000, Q2: 10000, Q3: 10000, Q4: 10000 },
          actuals_quarterly: { Q1: 9500, Q2: 9500, Q3: 9500, Q4: 9500 },
          total_budget: 40000,
          total_actual: 38000,
          expense_date: '2024-03-15',
          source_type: 'manual',
          attachment: 'Manual Entry',
          receipt: null
        }
      ];

      // Try to find the expense by ID
      let foundExpense = mockExpenses.find(exp => exp.id === expenseId);

      // If not found by ID, try to match with the expense history from the grantee expense service
      if (!foundExpense) {
        try {
          const expenseHistory = await granteeExpenseService.getExpenseHistory();
          const historyItem = expenseHistory.find(item => item.id === expenseId);

          if (historyItem) {
            // Create a compatible expense object from the history item
            foundExpense = {
              id: historyItem.id,
              particulars: historyItem.description || 'Expense Item',
              main_header: historyItem.category || 'Uncategorized',
              sub_headers: 'General',
              units: historyItem.units || '',
              frequency: historyItem.frequency || '',
              cost_per_unit: historyItem.cost_per_unit || 0,
              budget_quarterly: {
                Q1: historyItem.budget_q1 || 0,
                Q2: historyItem.budget_q2 || 0,
                Q3: historyItem.budget_q3 || 0,
                Q4: historyItem.budget_q4 || 0
              },
              actuals_quarterly: {
                Q1: historyItem.actual_q1 || 0,
                Q2: historyItem.actual_q2 || 0,
                Q3: historyItem.actual_q3 || 0,
                Q4: historyItem.actual_q4 || 0
              },
              total_budget: historyItem.totalBudget,
              total_actual: historyItem.totalActualSpent,
              expense_date: historyItem.loggedDate,
              source_type: historyItem.source_type || 'manual',
              attachment: historyItem.attachment,
              receipt: typeof historyItem.receipt === 'string' ? historyItem.receipt : null,
              status: historyItem.status || 'pending',
              remarks: historyItem.remarks || '',
              rejection_notes: historyItem.rejection_notes || ''
            };

            // Log the expense object for debugging
            console.log('Created expense object from history item:', foundExpense);
          }
        } catch (error) {
          console.error('Error fetching expense history:', error);
        }
      }

      if (foundExpense) {
        setExpense(foundExpense);
      } else {
        toast.error('Expense not found');
        router.push('/funding/expenses');
      }
    } catch (error) {
      console.error('Error fetching expense details:', error);
      toast.error('Failed to load expense details');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = () => {
    if (expense?.receipt && expense.source_type === 'manual') {
      // In a real implementation, this would download the receipt file
      const receiptName = typeof expense.receipt === 'string' ? expense.receipt : expense.receipt.name;
      toast.success(`Downloading receipt: ${receiptName}`);
      // Simulate file download
      const link = document.createElement('a');
      link.href = '#'; // In a real implementation, this would be the file URL
      link.download = receiptName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  if (loading) {
    return (
      <div className="p-6 flex justify-center items-center min-h-[400px]">
        <div className="animate-pulse text-orange-500 font-medium">Loading expense details...</div>
      </div>
    );
  }

  if (!expense) {
    return (
      <div className="p-6 flex justify-center items-center min-h-[400px]">
        <div className="text-red-500 font-medium">Expense not found</div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {showExcelViewer && expense.source_type === 'excel' && expense.attachment && (
        <ExcelAttachmentViewer
          attachment={expense.attachment}
          data={expense.excel_data}
          onClose={() => setShowExcelViewer(false)}
        />
      )}

      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/funding/expenses')}
            className="flex items-center gap-1"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </Button>
          <h1 className="text-xl font-semibold text-gray-800">Expense Details</h1>
        </div>

        {expense.source_type === 'excel' && expense.attachment && (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={() => setShowExcelViewer(true)}
            >
              <FileSpreadsheet className="h-4 w-4" />
              <span>View Excel</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={handleDownload}
            >
              <Download className="h-4 w-4" />
              <span>Download</span>
            </Button>
          </div>
        )}
      </div>

      <Card className="shadow-md">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-xl">
              Expense Details
            </CardTitle>
            <div>
              {expense.status === 'approved' && (
                <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                  Approved
                </span>
              )}
              {expense.status === 'rejected' && (
                <span className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                  Rejected
                </span>
              )}
              {expense.status === 'pending' && (
                <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                  Pending Review
                </span>
              )}
            </div>
          </div>
          <div className="text-sm text-gray-500">
            ID: {expense.id} | Source: {expense.source_type === 'excel' ? 'Excel Upload' : 'Manual Entry'}
            {expense.expense_date && ` | Date: ${new Date(expense.expense_date).toLocaleDateString()}`}
          </div>
        </CardHeader>
        <CardContent>
          {/* Display in the same format as the manual entry table */}
          <div className="overflow-x-auto">
            <table className="w-full border-collapse bg-white text-sm">
              <thead>
                <tr className="bg-gray-100">
                  <th className="p-3 border text-left font-semibold">Sr no.</th>
                  <th className="p-3 border text-left font-semibold">Particulars</th>
                  <th className="p-3 border text-left font-semibold">Header</th>
                  <th className="p-3 border text-left font-semibold">Sub-headers</th>
                  <th className="p-3 border text-left font-semibold">Units</th>
                  <th className="p-3 border text-left font-semibold">Frequency</th>
                  <th className="p-3 border text-left font-semibold">Cost per unit</th>
                  <th className="p-3 border text-center font-semibold" colSpan={4}>Budget quarterly breakup</th>
                  <th className="p-3 border text-center font-semibold">Total Budget</th>
                  <th className="p-3 border text-center font-semibold" colSpan={4}>Actuals quarterly breakup</th>
                  <th className="p-3 border text-center font-semibold">Total Actual</th>
                  <th className="p-3 border text-center font-semibold">Attachment</th>
                </tr>
                <tr className="bg-gray-50">
                  <th className="p-3 border" colSpan={7}></th>
                  <th className="p-3 border text-center">Q1</th>
                  <th className="p-3 border text-center">Q2</th>
                  <th className="p-3 border text-center">Q3</th>
                  <th className="p-3 border text-center">Q4</th>
                  <th className="p-3 border"></th>
                  <th className="p-3 border text-center">Q1</th>
                  <th className="p-3 border text-center">Q2</th>
                  <th className="p-3 border text-center">Q3</th>
                  <th className="p-3 border text-center">Q4</th>
                  <th className="p-3 border"></th>
                  <th className="p-3 border"></th>
                </tr>
              </thead>
              <tbody>
                <tr className="hover:bg-gray-50">
                  <td className="p-3 border">1</td>
                  <td className="p-3 border">{expense.particulars}</td>
                  <td className="p-3 border">{expense.main_header}</td>
                  <td className="p-3 border">{expense.sub_headers}</td>
                  <td className="p-3 border">{expense.units || '-'}</td>
                  <td className="p-3 border">{expense.frequency || '-'}</td>
                  <td className="p-3 border text-right">{expense.cost_per_unit ? fundingService.formatCurrency(expense.cost_per_unit) : '-'}</td>
                  <td className="p-3 border text-right">{fundingService.formatCurrency(expense.budget_quarterly.Q1)}</td>
                  <td className="p-3 border text-right">{fundingService.formatCurrency(expense.budget_quarterly.Q2)}</td>
                  <td className="p-3 border text-right">{fundingService.formatCurrency(expense.budget_quarterly.Q3)}</td>
                  <td className="p-3 border text-right">{fundingService.formatCurrency(expense.budget_quarterly.Q4)}</td>
                  <td className="p-3 border text-right font-medium">{fundingService.formatCurrency(expense.total_budget)}</td>
                  <td className="p-3 border text-right">{fundingService.formatCurrency(expense.actuals_quarterly.Q1)}</td>
                  <td className="p-3 border text-right">{fundingService.formatCurrency(expense.actuals_quarterly.Q2)}</td>
                  <td className="p-3 border text-right">{fundingService.formatCurrency(expense.actuals_quarterly.Q3)}</td>
                  <td className="p-3 border text-right">{fundingService.formatCurrency(expense.actuals_quarterly.Q4)}</td>
                  <td className="p-3 border text-right font-medium">{fundingService.formatCurrency(expense.total_actual)}</td>
                  <td className="p-3 border text-center">
                    {expense.source_type === 'excel' && expense.attachment ? (
                      <Button
                        variant="link"
                        size="sm"
                        className="p-0 h-auto text-blue-600 hover:text-blue-800 flex items-center gap-1 mx-auto"
                        onClick={() => setShowExcelViewer(true)}
                      >
                        <FileSpreadsheet className="h-4 w-4 mr-1" />
                        <span>View</span>
                      </Button>
                    ) : expense.source_type === 'manual' && expense.receipt ? (
                      <Button
                        variant="link"
                        size="sm"
                        className="p-0 h-auto text-blue-600 hover:text-blue-800 flex items-center gap-1 mx-auto"
                        onClick={handleDownload}
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        <span>Download Receipt</span>
                      </Button>
                    ) : (
                      <span>Manual Entry</span>
                    )}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Remarks and Rejection Notes */}
      {(expense.remarks || expense.rejection_notes) && (
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-xl">
              Remarks
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {expense.remarks && (
              <div>
                <h3 className="font-medium text-gray-800 mb-1">Your Remarks:</h3>
                <p className="text-gray-700 bg-gray-50 p-3 rounded-md">{expense.remarks}</p>
              </div>
            )}

            {expense.rejection_notes && (
              <div>
                <h3 className="font-medium text-red-800 mb-1">Rejection Notes:</h3>
                <p className="text-gray-700 bg-red-50 p-3 rounded-md border border-red-100">{expense.rejection_notes}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
