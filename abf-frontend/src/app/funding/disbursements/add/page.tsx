'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Upload } from 'lucide-react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { ExcelDataTable } from '@/components/funding/disbursement/ExcelDataTable';
import { ManualEntryTable } from '@/components/funding/disbursement/ManualEntryTable';
import { useRouter } from 'next/navigation';
import { useFunding } from '@/hooks/useFunding';

interface DisbursementRow {
  sr_no: number;
  particulars: string;
  scheduled_payment_date: string;
  scheduled_amount: number;
  received_amount?: number;
  pending_amount?: number;
  payment_received_date?: string;
  remarks?: string;
}

export default function AddDisbursementPage() {
  const router = useRouter();
  const { refreshDisbursementData } = useFunding();
  const [activeTab, setActiveTab] = useState<'manual' | 'excel'>('manual');
  const [file, setFile] = useState<File | null>(null);
  const [excelData, setExcelData] = useState<DisbursementRow[]>([]);
  const [manualRows, setManualRows] = useState<DisbursementRow[]>([{
    sr_no: 1,
    particulars: '',
    scheduled_payment_date: '',
    scheduled_amount: 0,
    received_amount: undefined,
    pending_amount: undefined,
    payment_received_date: undefined,
    remarks: ''
  }]);
  const [isUploading, setIsUploading] = useState(false);

  // Handle file change for Excel upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Check file type
      const fileType = selectedFile.name.split('.').pop()?.toLowerCase();
      if (fileType !== 'xlsx' && fileType !== 'xls' && fileType !== 'csv') {
        toast.error('Please upload a valid Excel or CSV file');
        return;
      }
      setFile(selectedFile);
    }
  };

  // Handle Excel file upload
  const handleUpload = () => {
    if (!file) {
      toast.error('Please select a file to upload');
      return;
    }

    setIsUploading(true);

    // Simulate file processing
    setTimeout(() => {
      // Mock data that would come from parsing the Excel file
      const mockUploadedDisbursements: DisbursementRow[] = [
        {
          sr_no: 1,
          particulars: 'Q1 Operational Funding',
          scheduled_payment_date: '2024-01-15',
          scheduled_amount: 100000,
          received_amount: 100000,
          pending_amount: 0,
          payment_received_date: '2024-01-15',
          remarks: 'On time payment'
        },
        {
          sr_no: 2,
          particulars: 'Q2 Operational Funding',
          scheduled_payment_date: '2024-04-15',
          scheduled_amount: 100000,
          received_amount: 100000,
          pending_amount: 0,
          payment_received_date: '2024-04-20',
          remarks: 'Delayed by 5 days'
        }
      ];
      
      setExcelData(mockUploadedDisbursements);
      setIsUploading(false);
      setActiveTab('excel');
    }, 1500);
  };

  // Add a new row to the manual entry table
  const handleAddRow = () => {
    const newRow: DisbursementRow = {
      sr_no: manualRows.length + 1,
      particulars: "",
      scheduled_payment_date: "",
      scheduled_amount: 0,
      received_amount: undefined,
      pending_amount: undefined,
      payment_received_date: undefined,
      remarks: ""
    };
    setManualRows([...manualRows, newRow]);
  };

  // Handle input changes in the manual entry table
  const handleInputChange = (index: number, field: string, value: string | number) => {
    const updatedRows = [...manualRows];
    (updatedRows[index] as any)[field] = value;
    
    // Update pending amount when scheduled or received amount changes
    if (field === 'scheduled_amount' || field === 'received_amount') {
      const row = updatedRows[index];
      row.pending_amount = row.scheduled_amount - (row.received_amount || 0);
    }
    
    setManualRows(updatedRows);
  };

  // Handle input changes in the Excel data table
  const handleExcelInputChange = (index: number, field: string, value: string | number) => {
    const updatedRows = [...excelData];
    (updatedRows[index] as any)[field] = value;
    
    // Update pending amount when scheduled or received amount changes
    if (field === 'scheduled_amount' || field === 'received_amount') {
      const row = updatedRows[index];
      row.pending_amount = row.scheduled_amount - (row.received_amount || 0);
    }
    
    setExcelData(updatedRows);
  };

  // Handle successful submission
  const handleSuccess = () => {
    toast.success(
      <div className="font-semibold">
        <div className="text-xl mb-2">Success! 🎉</div>
        <div>Your disbursement data has been processed successfully</div>
      </div>,
      {
        duration: 6000,
        position: 'top-center',
        style: {
          background: '#4CAF50',
          color: '#fff',
          fontSize: '16px',
          padding: '16px',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
          maxWidth: '400px',
          width: '100%'
        }
      }
    );
    refreshDisbursementData();
    router.push('/funding/disbursements');
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => router.push('/funding/disbursements')}
            className="flex items-center gap-1"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </Button>
          <h1 className="text-xl font-semibold text-gray-800">Add Disbursement</h1>
        </div>
      </div>

      <Card className="border border-gray-200 shadow-sm rounded-md">
        <CardContent className="p-6">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'manual' | 'excel')}>
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="manual">Manual Entry</TabsTrigger>
              <TabsTrigger value="excel">Excel Upload</TabsTrigger>
            </TabsList>
            <TabsContent value="manual" className="mt-4">
              <ManualEntryTable
                disbursementRows={manualRows}
                onAddRow={handleAddRow}
                onInputChange={handleInputChange}
              />
            </TabsContent>
            <TabsContent value="excel" className="mt-4">
              <div className="space-y-4">
                <Card className="p-4">
                  <div className="flex flex-col space-y-4">
                    <h3 className="text-lg font-medium">Upload Excel File</h3>
                    <p className="text-sm text-gray-500">Upload an Excel file (.xlsx, .xls) or CSV file (.csv) with disbursement data.</p>
                    <div className="flex items-center space-x-4">
                      <input
                        id="excel-upload"
                        type="file"
                        accept=".xlsx,.xls,.csv"
                        onChange={handleFileChange}
                        className="hidden"
                      />
                      <Button
                        disabled={isUploading}
                        onClick={() => document.getElementById("excel-upload")?.click()}
                        className="bg-orange-500 hover:bg-orange-600 text-white"
                      >
                        {isUploading ? 'Uploading...' : 'Select File'}
                      </Button>
                      <Button
                        disabled={!file || isUploading}
                        onClick={handleUpload}
                        className="bg-orange-500 hover:bg-orange-600 text-white"
                      >
                        {isUploading ? (
                          <span className="flex items-center gap-2">
                            <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
                            Processing...
                          </span>
                        ) : (
                          <span className="flex items-center gap-2">
                            <Upload className="h-4 w-4" />
                            Upload
                          </span>
                        )}
                      </Button>
                      <p className="text-sm text-gray-500">
                        {file ? file.name : "Only .xlsx, .xls, or .csv files are allowed"}
                      </p>
                    </div>
                  </div>
                </Card>

                {excelData.length > 0 && (
                  <Card className="p-4">
                    <CardTitle className="text-xl mb-4">Excel Data</CardTitle>
                    <ExcelDataTable 
                      data={excelData} 
                      onInputChange={handleExcelInputChange}
                      editable={true}
                    />
                    <div className="flex justify-end mt-4">
                      <Button 
                        onClick={handleSuccess} 
                        className="bg-orange-500 hover:bg-orange-600 text-white"
                      >
                        Submit Excel Data
                      </Button>
                    </div>
                  </Card>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
