import { Input } from "@/components/ui/input";

interface ExpenseRow {
  sr_no: number;
  particulars: string;
  main_header: string;
  sub_headers: string;
  units?: string;
  frequency?: string;
  cost_per_unit?: number;
  budget_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  actuals_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  total_budget?: number;
  total_actual?: number;
  remarks?: string;
  is_frozen?: boolean;
  source_type?: string;
}

interface ExcelDataTableProps {
  data: ExpenseRow[];
  onInputChange?: (index: number, field: string, value: string | number) => void;
  editable?: boolean;
  showRemarks?: boolean;
}

export function ExcelDataTable({ data, onInputChange, editable = false, showRemarks = false }: ExcelDataTableProps) {
  const isActualExceedingBudget = (row: ExpenseRow) => {
    const budgetSum = Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0);
    const actualsSum = Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0);
    return actualsSum > budgetSum;
  };

  const calculateTotal = (field: string) => {
    return data.reduce((total, row) => {
      if (field.startsWith("budget_quarterly.")) {
        const quarter = field.split(".")[1] as keyof typeof row.budget_quarterly;
        return total + (row.budget_quarterly[quarter] || 0);
      }
  
      if (field.startsWith("actuals_quarterly.")) {
        const quarter = field.split(".")[1] as keyof typeof row.actuals_quarterly;
        return total + (row.actuals_quarterly[quarter] || 0);
      }
  
      // @ts-expect-error: field is dynamic but known, safe fallback for flat fields
      return total + (row[field] || 0);
    }, 0);
  };


  return (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse bg-white">
        <thead>
          <tr className="bg-gray-100">
            <th className="p-3 border text-left font-semibold">Sr no.</th>
            <th className="p-3 border text-left font-semibold">Particulars</th>
            <th className="p-3 border text-left font-semibold">Main header</th>
            <th className="p-3 border text-left font-semibold">Sub-headers</th>
            <th className="p-3 border text-left font-semibold">Units</th>
            <th className="p-3 border text-left font-semibold">Frequency</th>
            <th className="p-3 border text-left font-semibold">Cost per unit</th>
            <th className="p-3 border text-center font-semibold" colSpan={4}>Budget quarterly breakup</th>
            <th className="p-3 border text-center font-semibold">Total Budget</th>
            <th className="p-3 border text-center font-semibold" colSpan={4}>Actuals quarterly breakup</th>
            <th className="p-3 border text-center font-semibold">Total Actual</th>
            {showRemarks && <th className="p-3 border text-left font-semibold">Remarks</th>}
          </tr>
          <tr className="bg-gray-50">
            <th className="p-3 border" colSpan={7}></th>
            <th className="p-3 border text-center">Q1</th>
            <th className="p-3 border text-center">Q2</th>
            <th className="p-3 border text-center">Q3</th>
            <th className="p-3 border text-center">Q4</th>
            <th className="p-3 border"></th>
            <th className="p-3 border text-center">Q1</th>
            <th className="p-3 border text-center">Q2</th>
            <th className="p-3 border text-center">Q3</th>
            <th className="p-3 border text-center">Q4</th>
            <th className="p-3 border"></th>
          </tr>
        </thead>
            <tbody>
              {data.map((row, index) => (
                <tr key={index} className={`hover:bg-gray-50 ${row.is_frozen ? 'bg-gray-50' : ''}`}>
                  <td className="p-3 border">{row.sr_no}</td>
                  <td className="p-3 border">
                    {editable ? (
                      <Input
                        type="text"
                        value={row.particulars}
                        onChange={(e) => onInputChange && onInputChange(index, 'particulars', e.target.value)}
                        className="w-full"
                      />
                    ) : (
                      row.particulars
                    )}
                  </td>
                  <td className="p-3 border">
                    {editable ? (
                      <Input
                        type="text"
                        value={row.main_header}
                        onChange={(e) => onInputChange && onInputChange(index, 'main_header', e.target.value)}
                        className="w-full"
                      />
                    ) : (
                      row.main_header
                    )}
                  </td>
                  <td className="p-3 border">
                    {editable ? (
                      <Input
                        type="text"
                        value={row.sub_headers}
                        onChange={(e) => onInputChange && onInputChange(index, 'sub_headers', e.target.value)}
                        className="w-full"
                      />
                    ) : (
                      row.sub_headers
                    )}
                  </td>
                  <td className="p-3 border">
                    {editable ? (
                      <Input
                        type="text"
                        value={row.units || ''}
                        onChange={(e) => onInputChange && onInputChange(index, 'units', e.target.value)}
                        className="w-full"
                      />
                    ) : (
                      row.units || ''
                    )}
                  </td>
                  <td className="p-3 border">
                    {editable ? (
                      <Input
                        type="text"
                        value={row.frequency || ''}
                        onChange={(e) => onInputChange && onInputChange(index, 'frequency', e.target.value)}
                        className="w-full"
                      />
                    ) : (
                      row.frequency || ''
                    )}
                  </td>
                  <td className="p-3 border">
                    {editable ? (
                      <Input
                        type="number"
                        value={row.cost_per_unit || ''}
                        onChange={(e) => onInputChange && onInputChange(index, 'cost_per_unit', parseFloat(e.target.value))}
                        className="text-right"
                      />
                    ) : (
                      row.cost_per_unit?.toFixed(2) || ''
                    )}
                  </td>
                  {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                    <td key={`budget_${quarter}`} className="p-3 border">
                      {editable ? (
                        <Input
                          type="number"
                          value={row.budget_quarterly[quarter as keyof typeof row.budget_quarterly]}
                          onChange={(e) => onInputChange && onInputChange(index, `budget_quarterly.${quarter}`, e.target.value)}
                          className="text-right"
                        />
                      ) : (
                        parseFloat(row.budget_quarterly[quarter as keyof typeof row.budget_quarterly].toString()).toFixed(2)
                      )}
                    </td>
                  ))}
                  <td className="p-3 border text-center font-medium">
                    {(row.total_budget || Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0)).toFixed(2)}
                  </td>
                  {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                    <td key={`actual_${quarter}`} className="p-3 border">
                      {editable ? (
                        <Input
                          type="number"
                          value={row.actuals_quarterly[quarter as keyof typeof row.actuals_quarterly]}
                          onChange={(e) => onInputChange && onInputChange(index, `actuals_quarterly.${quarter}`, e.target.value)}
                          className="text-right"
                        />
                      ) : (
                        parseFloat(row.actuals_quarterly[quarter as keyof typeof row.actuals_quarterly].toString()).toFixed(2)
                      )}
                    </td>
                  ))}
                  <td className="p-3 border text-center font-medium">
                    {(row.total_actual || Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0)).toFixed(2)}
                  </td>
                  {showRemarks && (
                    <td className="p-3 border">
                      {editable ? (
                        <div className="relative">
                          <Input
                            type="text"
                            value={row.remarks || ''}
                            onChange={(e) => onInputChange && onInputChange(index, 'remarks', e.target.value)}
                            className={`w-full ${isActualExceedingBudget(row) && !row.remarks ? 'border-amber-300' : ''}`}
                            placeholder={isActualExceedingBudget(row) ? "Required: explain why actual exceeds budget" : "Optional remarks"}
                          />
                          {isActualExceedingBudget(row) && !row.remarks && (
                            <div className="text-amber-500 text-xs mt-1">Required when actual exceeds budget</div>
                          )}
                        </div>
                      ) : (
                        <>
                          {row.remarks ? (
                            <div className="text-sm">
                              {row.remarks}
                              {row.is_frozen && (
                                <div className="mt-1 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  Submitted by Grantmaker
                                </div>
                              )}
                            </div>
                          ) : row.is_frozen ? (
                            <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              Submitted by Grantmaker
                            </div>
                          ) : null}
                        </>
                      )}
                    </td>
                  )}
                </tr>
              ))}
              <tr className="bg-gray-100 font-bold">
                <td className="p-3 border" colSpan={7}>Total</td>
                {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                  <td key={`total_budget_${quarter}`} className="p-3 border text-right">
                    {calculateTotal(`budget_quarterly.${quarter}`).toFixed(2)}
                  </td>
                ))}
                <td className="p-3 border text-right">
                  {data.reduce((total, row) => total + (row.total_budget || Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0)), 0).toFixed(2)}
                </td>
                {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                  <td key={`total_actual_${quarter}`} className="p-3 border text-right">
                    {calculateTotal(`actuals_quarterly.${quarter}`).toFixed(2)}
                  </td>
                ))}
                <td className="p-3 border text-right">
                  {data.reduce((total, row) => total + (row.total_actual || Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0)), 0).toFixed(2)}
                </td>
                {showRemarks && <td className="p-3 border"></td>}
              </tr>
            </tbody>
          </table>
    </div>
  );
}