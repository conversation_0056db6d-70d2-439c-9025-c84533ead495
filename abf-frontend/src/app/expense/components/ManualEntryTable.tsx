import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Plus as PlusIcon } from "lucide-react";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { object } from "zod";

interface ExpenseRow {
  sr_no: number;
  particulars: string;
  main_header: string;
  sub_headers: string;
  units?: string;
  frequency?: string;
  cost_per_unit?: number;
  budget_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  actuals_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  total_budget?: number;
  total_actual?: number;
  receipt?: File;
  remarks?: string;
  is_frozen?: boolean;
  source_type?: string;
}

interface ManualEntryTableProps {
  expenseRows: ExpenseRow[];
  onAddRow: () => void;
  onInputChange: (index: number, field: string, value: string | number) => void;
  showFrozenRows?: boolean;
}

export function ManualEntryTable({ expenseRows, onAddRow, onInputChange, showFrozenRows = false }: ManualEntryTableProps) {
  const [submittedRows, setSubmittedRows] = useState<ExpenseRow[]>([]);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const isActualExceedingBudget = (row: ExpenseRow) => {
    const budgetSum = Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0);
    const actualsSum = Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0);
    return actualsSum > budgetSum;
  };

  const validateRow = (row: ExpenseRow) => {
    const rowErrors: {[key: string]: string} = {};

    // Only validate required fields during form submission
    if (!row.particulars.trim()) rowErrors.particulars = 'This field is required';
    if (!row.main_header.trim()) rowErrors.main_header = 'This field is required';
    if (!row.sub_headers.trim()) rowErrors.sub_headers = 'This field is required';

    // Validate budget quarterly values only for negative numbers
    const budgetSum = Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0);
    if (budgetSum < 0) {
      rowErrors.budget = 'Budget values cannot be negative';
    }

    // Validate actuals quarterly values only for negative numbers
    const actualsSum = Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0);

    Object.entries(row.actuals_quarterly).forEach(([quarter, value]) => {
      if (row.actuals_quarterly[quarter] > (row.budget_quarterly[quarter] * 0.1)) {
        rowErrors.actuals = 'Overspent detected'
        console.log("Overspent detected")
      }

    })
    if (actualsSum < 0) {
      rowErrors.actuals = 'Actual values cannot be negative';
    }

    // Validate that remarks are provided when actual exceeds budget
    if (actualsSum > budgetSum && (!row.remarks || row.remarks.trim() === '')) {
      rowErrors.remarks = 'Remarks are required when actual expenses exceed budget';
    }

    return rowErrors;
  };

  const calculateTotal = (field: string) => {
    return expenseRows.reduce((total, row) => {
      if (field.startsWith("budget_quarterly.")) {
        const quarter = field.split(".")[1] as keyof typeof row.budget_quarterly;
        return total + (row.budget_quarterly[quarter] || 0);
      }
  
      if (field.startsWith("actuals_quarterly.")) {
        const quarter = field.split(".")[1] as keyof typeof row.actuals_quarterly;
        return total + (row.actuals_quarterly[quarter] || 0);
      }
  
      // @ts-expect-error: field is dynamic but known, safe fallback for flat fields
      return total + (row[field] || 0);
    }, 0);
  };

  const handleSubmit = async () => {
    console.log('Starting form submission...');
    setErrors({});

    let hasValidationErrors = false;
    const allErrors: {[key: string]: string} = {};

    if (expenseRows.length === 0) {
      console.log('No expense rows found');
      toast.error('Please add at least one expense row');
      return;
    }

    console.log('Validating expense rows:', expenseRows);
    expenseRows.forEach((row, index) => {
      const rowErrors = validateRow(row);
      const budgetSum = Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0);

      if (row.total_budget && row.total_budget !== budgetSum) {
        rowErrors.budget = `Total budget (${row.total_budget}) does not match quarterly sum (${budgetSum})`;
      }

      if (Object.keys(rowErrors).length > 0) {
        hasValidationErrors = true;
        Object.entries(rowErrors).forEach(([field, message]) => {
          allErrors[`${index}-${field}`] = message;
        });
      }
    });

    if (hasValidationErrors) {
      console.log('Validation errors found:', allErrors);
      setErrors(allErrors);
      const errorMessages = Object.values(allErrors);
      const uniqueErrors = [...new Set(errorMessages)];
      uniqueErrors.forEach(error => toast.error(error));
      return;
    }

    // Validation for actual > budget is now handled in validateRow


    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URI || 'http://localhost:8000';
    const updatedRows = [...expenseRows];
    const results = [];

    try {
      console.log('Submitting expense data to API...');
      for (let i = 0; i < expenseRows.length; i++) {
        const row = expenseRows[i];
        const budgetTotal = Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0);
        const actualTotal = Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0);

        const formData = new FormData();
        formData.append('grant', '1');  // Using grant ID 1 which should exist in the database
        formData.append('particulars', row.particulars);
        formData.append('main_headers', row.main_header);
        formData.append('sub_headers', row.sub_headers);
        formData.append('source_type', 'manual');
        formData.append('units', row.units || '');
        formData.append('frequency', row.frequency || '');
        formData.append('cost_per_unit', row.cost_per_unit?.toString() || '0');
        formData.append('budget_q1', row.budget_quarterly.Q1.toString());
        formData.append('budget_q2', row.budget_quarterly.Q2.toString());
        formData.append('budget_q3', row.budget_quarterly.Q3.toString());
        formData.append('budget_q4', row.budget_quarterly.Q4.toString());
        formData.append('actual_q1', row.actuals_quarterly.Q1.toString());
        formData.append('actual_q2', row.actuals_quarterly.Q2.toString());
        formData.append('actual_q3', row.actuals_quarterly.Q3.toString());
        formData.append('actual_q4', row.actuals_quarterly.Q4.toString());
        formData.append('expense_date', new Date().toISOString().split('T')[0]);
        formData.append('total_budget', budgetTotal.toString());
        formData.append('total_actual', actualTotal.toString());

        if (row.receipt) {
          formData.append('receipt', row.receipt);
        }

        // Add remarks if actual > budget
        if (actualTotal > budgetTotal && row.remarks) {
          formData.append('remarks', row.remarks);
        }

        console.log(`Submitting row ${i + 1}:`);
        // Get the JWT token from localStorage
        const idToken = localStorage.getItem("idToken");

        // Use the correct API endpoint path with the /api prefix
        const response = await fetch(`${API_BASE_URL}/api/funding/v1/expenses/`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${idToken}`
          },
          body: formData
        });

        if (!response.ok) {
          // Clone the response before reading its body to avoid the 'body stream already read' error
          const responseClone = response.clone();
          let errorData;
          try {
            errorData = await responseClone.json();
          } catch (error) {
            // If the response is not valid JSON (e.g., HTML), get the text instead
            // Fix: Use the cloned response to avoid 'body stream already read' error

            console.log("Error: " + error);
            const textClone = response.clone();
            const text = await textClone.text();
            console.error(`Non-JSON error response for row ${i + 1}:`, text);
            throw new Error(`Server returned an invalid response. Status: ${response.status}`);
          }
          console.error(`Error data for row ${i + 1}:`, errorData);

          // Handle different types of error responses
          if (errorData.non_field_errors && Array.isArray(errorData.non_field_errors)) {
            throw new Error(errorData.non_field_errors.join(', '));
          } else if (typeof errorData === 'object' && errorData !== null) {
            // Format field-specific errors
            const errorMessages = Object.entries(errorData)
              .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
              .join('\n');
            throw new Error(errorMessages || 'Failed to submit expense data');
          } else {
            throw new Error(errorData.message || 'Failed to submit expense data');
          }
        }

        const data = await response.json();
        console.log(`Success data for row ${i + 1}:`, data);
        results.push(data);

        updatedRows[i] = {
          ...row,
          total_budget: data.total_budget,
          total_actual: data.total_actual
        };
      }

      setSubmittedRows(updatedRows);
      console.log('All rows submitted successfully');

      // Reset the table after successful submission
      const emptyRow = {
        sr_no: 1,
        particulars: "",
        main_header: "",
        sub_headers: "",
        units: "",
        frequency: "",
        cost_per_unit: 0,
        total_grant_budget: 0,
        budget_quarterly: { Q1: 0, Q2: 0, Q3: 0, Q4: 0 },
        actuals_quarterly: { Q1: 0, Q2: 0, Q3: 0, Q4: 0 },
        receipt: undefined
      };

      // Reset all fields for the first row
      Object.keys(emptyRow).forEach(field => {
        if (typeof emptyRow[field] === 'object') {
          Object.keys(emptyRow[field]).forEach(subField => {
            onInputChange(0, `${field}.${subField}`, 0);
          });
        } else {
          onInputChange(0, field, emptyRow[field]);
        }
      });

      // Remove all additional rows
      while (expenseRows.length > 1) {
        expenseRows.pop();
      }

      setErrors({}); // Clear any validation errors
      setSubmittedRows([]); // Clear submitted rows state
      expenseRows.splice(1); // Remove all rows except the first one
      setErrors({}); // Clear any validation errors

      toast.success(
        <div className="font-semibold">
          <div className="text-xl mb-2">Hurray! 🎉</div>
          <div>Your expense data has been successfully submitted</div>
        </div>,
        {
          duration: 6000,
          position: 'top-center',
          style: {
            background: '#4CAF50',
            color: '#fff',
            fontSize: '16px',
            padding: '16px',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
            maxWidth: '400px',
            width: '100%'
          }
        }
      );

      // Navigate to expense history page after a short delay
      setTimeout(() => {
        window.location.href = '/expense/history';
      }, 2000);
    } catch (error) {
      console.error('Error submitting expense data:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to submit expense data');
    }
  };

  // Use submitted rows if available, otherwise use the original rows
  const displayRows = submittedRows.length > 0 ? submittedRows : expenseRows;

  return (
    <div className="overflow-x-auto">
      <div className="flex justify-between mb-4">
        <Button onClick={onAddRow} variant="outline" className="flex items-center gap-2">
          <PlusIcon className="w-4 h-4" /> Add Row
        </Button>
      </div>
      <div className="rounded-lg border shadow-sm overflow-hidden">
        <table className="w-full border-collapse bg-white">
          <thead>
            <tr className="bg-gray-100">
              <th className="p-3 border text-left font-semibold">Sr no.</th>
              <th className="p-3 border text-left font-semibold">Particulars</th>
              <th className="p-3 border text-left font-semibold">Main header</th>
              <th className="p-3 border text-left font-semibold">Sub-headers</th>
              <th className="p-3 border text-left font-semibold">Units</th>
              <th className="p-3 border text-left font-semibold">Frequency</th>
              <th className="p-3 border text-left font-semibold">Cost per unit</th>
              <th className="p-3 border text-center font-semibold" colSpan={4}>Budget quarterly breakup</th>
              <th className="p-3 border text-center font-semibold">Total Budget</th>
              <th className="p-3 border text-center font-semibold" colSpan={4}>Actuals quarterly breakup</th>
              <th className="p-3 border text-center font-semibold">Total Actual</th>
              <th className="p-3 border text-center font-semibold">Remarks</th>
              <th className="p-3 border text-center font-semibold">Attachment</th>
            </tr>
            <tr className="bg-gray-50">
              <th className="p-3 border" colSpan={7}></th>
              <th className="p-3 border text-center">Q1</th>
              <th className="p-3 border text-center">Q2</th>
              <th className="p-3 border text-center">Q3</th>
              <th className="p-3 border text-center">Q4</th>
              <th className="p-3 border"></th>
              <th className="p-3 border text-center">Q1</th>
              <th className="p-3 border text-center">Q2</th>
              <th className="p-3 border text-center">Q3</th>
              <th className="p-3 border text-center">Q4</th>
              <th className="p-3 border"></th>
            </tr>
          </thead>
          <tbody>
            {displayRows.map((row, index) => (
              <tr key={index} className={`hover:bg-gray-50 ${row.is_frozen ? 'bg-gray-50' : ''}`}>
                <td className="p-3 border">{row.sr_no}</td>
                <td className="p-3 border">
                  <div className="relative">
                    {row.is_frozen ? (
                      <div className="p-2">
                        {row.particulars}
                        {row.is_frozen && (
                          <div className="mt-1 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Submitted by Grantmaker
                          </div>
                        )}
                      </div>
                    ) : (
                      <>
                        <Input
                          type="text"
                          value={row.particulars}
                          onChange={(e) => onInputChange(index, 'particulars', e.target.value)}
                          className={`w-full ${errors[`${index}-particulars`] ? 'border-red-500' : ''}`}
                        />
                        {errors[`${index}-particulars`] && (
                          <div className="text-red-500 text-xs mt-1">{errors[`${index}-particulars`]}</div>
                        )}
                      </>
                    )}
                  </div>
                </td>
                <td className="p-3 border">
                  <div className="relative">
                    {row.is_frozen ? (
                      <div className="p-2">{row.main_header}</div>
                    ) : (
                      <>
                        <Input
                          type="text"
                          value={row.main_header}
                          onChange={(e) => onInputChange(index, 'main_header', e.target.value)}
                          className={`w-full ${errors[`${index}-main_header`] ? 'border-red-500' : ''}`}
                        />
                        {errors[`${index}-main_header`] && (
                          <div className="text-red-500 text-xs mt-1">{errors[`${index}-main_header`]}</div>
                        )}
                      </>
                    )}
                  </div>
                </td>
                <td className="p-3 border">
                  <div className="relative">
                    {row.is_frozen ? (
                      <div className="p-2">{row.sub_headers}</div>
                    ) : (
                      <>
                        <Input
                          type="text"
                          value={row.sub_headers}
                          onChange={(e) => onInputChange(index, 'sub_headers', e.target.value)}
                          className={`w-full ${errors[`${index}-sub_headers`] ? 'border-red-500' : ''}`}
                        />
                        {errors[`${index}-sub_headers`] && (
                          <div className="text-red-500 text-xs mt-1">{errors[`${index}-sub_headers`]}</div>
                        )}
                      </>
                    )}
                  </div>
                </td>
                <td className="p-3 border">
                  <div className="relative">
                    {row.is_frozen ? (
                      <div className="p-2">{row.units || ''}</div>
                    ) : (
                      <Input
                        type="text"
                        value={row.units || ''}
                        onChange={(e) => onInputChange(index, 'units', e.target.value)}
                        className="w-full"
                      />
                    )}
                  </div>
                </td>
                <td className="p-3 border">
                  <div className="relative">
                    {row.is_frozen ? (
                      <div className="p-2">{row.frequency || ''}</div>
                    ) : (
                      <Input
                        type="text"
                        value={row.frequency || ''}
                        onChange={(e) => onInputChange(index, 'frequency', e.target.value)}
                        className="w-full"
                      />
                    )}
                  </div>
                </td>
                <td className="p-3 border">
                  <div className="relative">
                    {row.is_frozen ? (
                      <div className="p-2 text-right">{row.cost_per_unit?.toFixed(2) || ''}</div>
                    ) : (
                      <Input
                        type="number"
                        value={row.cost_per_unit || ''}
                        onChange={(e) => onInputChange(index, 'cost_per_unit', parseFloat(e.target.value))}
                        className="text-right"
                      />
                    )}
                  </div>
                </td>
                {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                  <td key={`budget_${quarter}`} className="p-3 border">
                    {row.is_frozen ? (
                      <div className="p-2 text-right">
                        {parseFloat(row.budget_quarterly[quarter as keyof typeof row.budget_quarterly].toString()).toFixed(2)}
                      </div>
                    ) : (
                      <Input
                        type="number"
                        value={row.budget_quarterly[quarter as keyof typeof row.budget_quarterly]}
                        onChange={(e) => onInputChange(index, `budget_quarterly.${quarter}`, e.target.value)}
                        className="text-right"
                      />
                    )}
                  </td>
                ))}
                <td className="p-3 border text-center font-medium">
                  {row.total_budget || Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0)}
                </td>
                {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                  <td key={`actual_${quarter}`} className="p-3 border">
                    {row.is_frozen ? (
                      <div className="p-2 text-right">
                        {parseFloat(row.actuals_quarterly[quarter as keyof typeof row.actuals_quarterly].toString()).toFixed(2)}
                      </div>
                    ) : (
                      <Input
                        type="number"
                        value={row.actuals_quarterly[quarter as keyof typeof row.actuals_quarterly]}
                        onChange={(e) => onInputChange(index, `actuals_quarterly.${quarter}`, e.target.value)}
                        className="text-right"
                      />
                    )}
                  </td>
                ))}
                <td className="p-3 border text-center font-medium">
                  {row.total_actual || Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0)}
                </td>
                <td className="p-3 border">
                  <div className="relative">
                    {row.is_frozen ? (
                      <div className="p-2">{row.remarks || ''}</div>
                    ) : (
                      <Input
                        type="text"
                        value={row.remarks || ''}
                        onChange={(e) => onInputChange(index, 'remarks', e.target.value)}
                        className={`w-full ${errors[`${index}-remarks`] ? 'border-red-500' : ''}`}
                        placeholder={isActualExceedingBudget(row) ? "Required: explain why actual exceeds budget" : "Optional remarks"}
                      />
                    )}
                    {errors[`${index}-remarks`] && (
                      <div className="text-red-500 text-xs mt-1">{errors[`${index}-remarks`]}</div>
                    )}
                    {isActualExceedingBudget(row) && !row.remarks && !errors[`${index}-remarks`] && (
                      <div className="text-amber-500 text-xs mt-1">Required when actual exceeds budget</div>
                    )}
                  </div>
                </td>
                <td className="p-3 border text-center">
                  {row.is_frozen ? (
                    <div className="p-2">
                      {row.receipt ? (
                        <div className="text-sm text-blue-600 cursor-pointer">
                          View Attachment
                        </div>
                      ) : (
                        <div className="text-sm text-gray-500">No attachment</div>
                      )}
                    </div>
                  ) : (
                    <>
                      <Input
                        type="file"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            onInputChange(index, 'receipt', file);
                          }
                        }}
                        className="w-full"
                        accept=".pdf,.jpg,.jpeg,.png"
                      />
                      {row.receipt && (
                        <div className="text-sm text-gray-600 mt-1">
                          {row.receipt.name}
                        </div>
                      )}
                    </>
                  )}
                </td>
              </tr>
            ))}
            <tr className="bg-gray-100 font-bold">
              <td className="p-3 border" colSpan={7}>Total</td>
              {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                <td key={`total_budget_${quarter}`} className="p-3 border text-right">
                  {calculateTotal(`budget_quarterly.${quarter}`)}
                </td>
              ))}
              <td className="p-3 border text-right">
                {displayRows.reduce((total, row) => total + (row.total_budget || Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0)), 0)}
              </td>
              {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                <td key={`total_actual_${quarter}`} className="p-3 border text-right">
                  {calculateTotal(`actuals_quarterly.${quarter}`)}
                </td>
              ))}
              <td className="p-3 border text-right">
                {displayRows.reduce((total, row) => total + (row.total_actual || Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0)), 0)}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div className="flex justify-end mt-4">
        <Button onClick={handleSubmit} className="bg-blue-600 hover:bg-blue-700 text-white">
          Submit
        </Button>
      </div>
    </div>
  );
}