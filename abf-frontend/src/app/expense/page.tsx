"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { ExcelDataTable } from "./components/ExcelDataTable";
import { ManualEntryTable } from "./components/ManualEntryTable";
import { useRouter } from "next/navigation";

interface ExpenseRow {
  sr_no: number;
  particulars: string;
  main_header: string;
  sub_headers: string;
  units?: string;
  frequency?: string;
  cost_per_unit?: number;
  budget_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  actuals_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  total_budget?: number;
  total_actual?: number;
  remarks?: string;
}

export default function ExpensePage() {
  const router = useRouter();
  const [file, setFile] = useState<File | null>(null);
  const [excelData, setExcelData] = useState<ExpenseRow[]>([]);
  const [manualRows, setManualRows] = useState<ExpenseRow[]>([{
    sr_no: 1,
    particulars: "",
    main_header: "",
    sub_headers: "",
    units: "",
    frequency: "",
    cost_per_unit: 0,
    budget_quarterly: { Q1: 0, Q2: 0, Q3: 0, Q4: 0 },
    actuals_quarterly: { Q1: 0, Q2: 0, Q3: 0, Q4: 0 }
  }]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    console.log("File selected:", file);

    if (file) {
      if (!file.name.match(/\.(xlsx|xls|csv)$/)) {
        toast.error("Please upload only Excel or CSV files (.xlsx, .xls, or .csv)");
        return;
      }
      setFile(file);
      console.log("File set in state:", file.name);

      // Mock data for testing without backend
      // In a real implementation, you would use a library like xlsx or papaparse to parse the file
      console.log("Generating mock data for testing");
      const mockData = [
        {
          particulars: "Office Rent",
          main_header: "Administrative Expenses",
          sub_headers: "Rent",
          units: "Months",
          frequency: "Monthly",
          cost_per_unit: 5000,
          budget_quarterly: { Q1: 5000, Q2: 5000, Q3: 5000, Q4: 5000 },
          actuals_quarterly: { Q1: 5000, Q2: 5000, Q3: 4800, Q4: 0 }
        },
        {
          particulars: "Salaries",
          main_header: "Personnel",
          sub_headers: "Staff",
          units: "People",
          frequency: "Monthly",
          cost_per_unit: 4000,
          budget_quarterly: { Q1: 12000, Q2: 12000, Q3: 15000, Q4: 15000 },
          actuals_quarterly: { Q1: 12000, Q2: 12000, Q3: 14500, Q4: 0 }
        },
        {
          particulars: "Equipment",
          main_header: "Capital Expenses",
          sub_headers: "Hardware",
          units: "Items",
          frequency: "One-time",
          cost_per_unit: 1000,
          budget_quarterly: { Q1: 8000, Q2: 2000, Q3: 1000, Q4: 1000 },
          actuals_quarterly: { Q1: 7500, Q2: 1800, Q3: 900, Q4: 0 }
        }
      ];

      try {
        // Process the mock data to ensure all quarterly values are properly formatted
        console.log("Processing mock data");
        const processedData = mockData.map((row, index) => ({
          ...row,
          sr_no: index + 1,
          budget_quarterly: {
            Q1: parseFloat(row.budget_quarterly?.Q1?.toString() || "0"),
            Q2: parseFloat(row.budget_quarterly?.Q2?.toString() || "0"),
            Q3: parseFloat(row.budget_quarterly?.Q3?.toString() || "0"),
            Q4: parseFloat(row.budget_quarterly?.Q4?.toString() || "0")
          },
          actuals_quarterly: {
            Q1: parseFloat(row.actuals_quarterly?.Q1?.toString() || "0"),
            Q2: parseFloat(row.actuals_quarterly?.Q2?.toString() || "0"),
            Q3: parseFloat(row.actuals_quarterly?.Q3?.toString() || "0"),
            Q4: parseFloat(row.actuals_quarterly?.Q4?.toString() || "0")
          },
          total_budget: parseFloat(
            Object.values(row.budget_quarterly || {}).reduce((acc: number, val: any) => acc + parseFloat(val?.toString() || "0"), 0).toFixed(2)
          ),
          total_actual: parseFloat(
            Object.values(row.actuals_quarterly || {}).reduce((acc: number, val: any) => acc + parseFloat(val?.toString() || "0"), 0).toFixed(2)
          )
        }));

        console.log("Processed data:", processedData);
        setExcelData(processedData);
        toast.success("File uploaded successfully. Data is now displayed in the table below.");
        event.target.value = "";
        setFile(null);
      } catch (error) {
        console.error("Error processing file:", error);
        toast.error("Error processing file");
      }
    }
  };

  const handleAddRow = () => {
    const newRow: ExpenseRow = {
      sr_no: manualRows.length + 1,
      particulars: "",
      main_header: "",
      sub_headers: "",
      units: "",
      frequency: "",
      cost_per_unit: 0,
      budget_quarterly: { Q1: 0, Q2: 0, Q3: 0, Q4: 0 },
      actuals_quarterly: { Q1: 0, Q2: 0, Q3: 0, Q4: 0 }
    };
    setManualRows([...manualRows, newRow]);
  };

  const handleInputChange = (index: number, field: string, value: string | number) => {
    const updatedRows = [...manualRows];
    if (field.includes(".")) {
      const [parent, child] = field.split(".");
      (updatedRows[index] as any)[parent][child] = Number(value);

      // Remove the immediate validation to prevent errors while typing
      // Budget validation will now only happen during form submission
    } else if (field === "total_budget" || field === "total_actual") {
      (updatedRows[index] as any)[field] = Number(value);
      // Remove the immediate validation to prevent errors while typing
    } else {
      (updatedRows[index] as any)[field] = value;
    }
    setManualRows(updatedRows);
  };

  // Function to handle input changes for Excel data
  const handleExcelInputChange = (index: number, field: string, value: string | number) => {
    const updatedRows = [...excelData];
    if (field.includes(".")) {
      const [parent, child] = field.split(".");
      (updatedRows[index] as any)[parent][child] = Number(value);
    } else if (field === "total_budget" || field === "total_actual") {
      (updatedRows[index] as any)[field] = Number(value);
    } else {
      (updatedRows[index] as any)[field] = value;
    }
    setExcelData(updatedRows);
  };

  return (
    <div className="min-h-screen p-8 bg-gradient-to-br from-gray-50 to-gray-100">
      <Card className="max-w-7xl mx-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-2xl font-bold">Expense Management</CardTitle>
          <Button
            onClick={() => router.push('/expense/history')}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            View Submitted Expenses
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <Card className="p-4">
              <CardTitle className="text-xl mb-4">Excel Upload</CardTitle>
              <div className="flex items-center space-x-4">
                <input
                  type="file"
                  accept=".xlsx,.xls,.csv"
                  onChange={handleFileUpload}
                  className="hidden"
                  id="excel-upload"
                />
                <Button
                  variant="default"
                  onClick={() => document.getElementById("excel-upload")?.click()}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Upload File
                </Button>
                <p className="text-sm text-gray-500">
                  {file ? file.name : "Only .xlsx, .xls, or .csv files are allowed"}
                </p>
              </div>
            </Card>

                        {excelData.length > 0 && (
              <Card className="p-4">
                <CardTitle className="text-xl mb-4">Excel Data</CardTitle>
                <ExcelDataTable
                  data={excelData}
                  onInputChange={handleExcelInputChange}
                  editable={true}
                  showRemarks={true}
                />
                <div className="flex justify-end mt-4">
                  <Button
                    onClick={() => {
                      // Validate that remarks are provided when actual exceeds budget
                      const rowsWithErrors = excelData.filter(row => {
                        const budgetSum = Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0);
                        const actualsSum = Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0);
                        return actualsSum > budgetSum && (!row.remarks || row.remarks.trim() === '');
                      });

                      if (rowsWithErrors.length > 0) {
                        toast.error('Please provide remarks for all items where actual expenses exceed budget');
                        return;
                      }

                      // Skip backend validation and just display the data
                      console.log("Excel data submitted (frontend only):", excelData);
                      toast.success(
                        <div className="font-semibold">
                          <div className="text-xl mb-2">Success! 🎉</div>
                          <div>Your Excel data has been processed successfully</div>
                        </div>,
                        {
                          duration: 6000,
                          position: 'top-center',
                          style: {
                            background: '#4CAF50',
                            color: '#fff',
                            fontSize: '16px',
                            padding: '16px',
                            borderRadius: '8px',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
                            maxWidth: '400px',
                            width: '100%'
                          }
                        }
                      );
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    Submit Excel Data
                  </Button>
                </div>
              </Card>
            )}

            <Card className="p-4">
              <CardTitle className="text-xl mb-4">Manual Entry</CardTitle>
              <ManualEntryTable
                expenseRows={manualRows}
                onAddRow={handleAddRow}
                onInputChange={handleInputChange}
              />
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}