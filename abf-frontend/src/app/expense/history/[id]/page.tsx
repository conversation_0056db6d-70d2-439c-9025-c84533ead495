"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON>le } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { ArrowLeft, Download, FileSpreadsheet, AlertCircle } from "lucide-react";
import { ExcelDataTable } from "../../components/ExcelDataTable";
import { ExcelAttachmentViewer } from "@/components/funding/expense/ExcelAttachmentViewer";
import { useRouter } from "next/navigation";
import * as granteeExpenseService from "@/services/grantee-expense-service";

interface ExpenseRow {
  id?: number;
  sr_no: number;
  particulars: string;
  main_header: string;
  sub_headers: string;
  budget_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  actuals_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  total_budget?: number;
  total_actual?: number;
  expense_date?: string;
  source_type?: string; // 'excel' or 'manual' or 'grantmaker_excel'
  attachment?: string;
  is_frozen?: boolean;
  remarks?: string;
  rejection_notes?: string | null;
  status?: string;
  units?: string;
  frequency?: string;
  cost_per_unit?: number;
  receipt?: string | null;
}

export default function ExpenseDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const [showExcelViewer, setShowExcelViewer] = useState<boolean>(false);
  const [expense, setExpense] = useState<ExpenseRow | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const expenseId = params.id;

  useEffect(() => {
    fetchExpenseDetail();
  }, [expenseId]);

  const fetchExpenseDetail = async () => {
    setLoading(true);
    setError(null);

    try {
      // Use the grantee expense service to fetch expense details
      const item = await granteeExpenseService.getExpenseById(expenseId);

      // Transform the API data to match our ExpenseRow interface
      console.log('Expense detail API response:', item);
      const transformedData: ExpenseRow = {
        id: parseInt(item.id.toString()),
        sr_no: 1,
        particulars: item.particulars || item.description || '',
        main_header: item.main_headers || item.category || '',
        sub_headers: item.sub_headers || '',
        units: item.units || '',
        frequency: item.frequency || '',
        cost_per_unit: item.cost_per_unit || 0,
        budget_quarterly: {
          Q1: item.budget_q1 || (item.budget_quarterly?.Q1) || 0,
          Q2: item.budget_q2 || (item.budget_quarterly?.Q2) || 0,
          Q3: item.budget_q3 || (item.budget_quarterly?.Q3) || 0,
          Q4: item.budget_q4 || (item.budget_quarterly?.Q4) || 0
        },
        actuals_quarterly: {
          Q1: item.actual_q1 || (item.actuals_quarterly?.Q1) || 0,
          Q2: item.actual_q2 || (item.actuals_quarterly?.Q2) || 0,
          Q3: item.actual_q3 || (item.actuals_quarterly?.Q3) || 0,
          Q4: item.actual_q4 || (item.actuals_quarterly?.Q4) || 0
        },
        total_budget: item.total_budget || item.totalBudget || 0,
        total_actual: item.total_actual || item.totalActualSpent || 0,
        expense_date: item.expense_date || item.loggedDate,
        source_type: item.source_type || 'manual', // Default to manual if not specified
        attachment: item.receipt || item.attachment || (item.source_type === 'excel' ? 'Excel Upload' : 'Manual Entry'),
        receipt: item.receipt || null,
        is_frozen: item.is_frozen || false,
        remarks: item.remarks || '',
        rejection_notes: item.rejection_notes || item.metadata?.rejection_notes || null,
        status: item.status || 'pending'
      };

      console.log('Transformed expense data:', transformedData);

      setExpense(transformedData);
    } catch (err) {
      console.error('Error fetching expense details:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      toast.error('Failed to load expense details');

      // For demo purposes, load mock data if API fails
      loadMockData();
    } finally {
      setLoading(false);
    }
  };

  const loadMockData = () => {
    // Mock data for testing without backend
    const mockData = {
      id: parseInt(expenseId),
      sr_no: 1,
      particulars: "Office Rent",
      main_header: "Administrative Expenses",
      sub_headers: "Rent",
      budget_quarterly: { Q1: 5000, Q2: 5000, Q3: 5000, Q4: 5000 },
      actuals_quarterly: { Q1: 5000, Q2: 5000, Q3: 4800, Q4: 0 },
      total_budget: 20000,
      total_actual: 14800,
      expense_date: "2023-10-15",
      source_type: "excel"
    };

    setExpense(mockData);
    toast.info('Using mock data for demonstration');
  };

  return (
    <div className="min-h-screen p-8 bg-gradient-to-br from-gray-50 to-gray-100">
      {showExcelViewer && expense?.attachment && (
        <ExcelAttachmentViewer
          attachment={expense.attachment}
          onClose={() => setShowExcelViewer(false)}
        />
      )}
      <Card className="max-w-7xl mx-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <Button
              variant="outline"
              size="sm"
              className="mb-2"
              onClick={() => router.push('/funding/expenses')}
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Expense History
            </Button>
            <CardTitle className="text-2xl font-bold">
              Expense Details
            </CardTitle>
            <p className="text-sm text-gray-500 mt-1">
              ID: {expense?.id} | Source: {
                expense?.source_type === 'excel'
                  ? 'Excel Upload'
                  : expense?.source_type === 'grantmaker_excel'
                    ? 'Grantmaker Upload'
                    : 'Manual Entry'
              }
              {expense?.expense_date && ` | Date: ${new Date(expense.expense_date).toLocaleDateString()}`}
              {expense?.is_frozen && ' | Status: Frozen'}
            </p>
          </div>
          {expense?.attachment && (
            <Button
              variant="outline"
              className="text-blue-600 hover:text-blue-800"
              onClick={() => {
                if (expense.source_type === "excel") {
                  setShowExcelViewer(true);
                } else {
                  // For non-Excel attachments, this would download the file
                  alert("Downloading attachment: " + expense.attachment);
                }
              }}
            >
              {expense.source_type === "excel" ? (
                <>
                  <FileSpreadsheet className="h-4 w-4 mr-1" />
                  View Excel
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-1" />
                  Download Attachment
                </>
              )}
            </Button>
          )}
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
            </div>
          ) : error ? (
            <div className="text-center text-red-500 p-4">
              <p>{error}</p>
              <Button
                onClick={fetchExpenseDetail}
                className="mt-4 bg-blue-600 hover:bg-blue-700 text-white"
              >
                Retry
              </Button>
            </div>
          ) : expense ? (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {expense.is_frozen && (
                  <div className="md:col-span-3 bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <div className="flex items-center gap-2 mb-2">
                      <FileSpreadsheet className="h-5 w-5 text-blue-600" />
                      <p className="font-medium text-blue-800">Submitted by Grantmaker</p>
                    </div>
                    {expense.remarks && (
                      <div className="mt-2">
                        <p className="text-sm font-medium text-gray-700">Remarks:</p>
                        <p className="text-sm text-gray-600">{expense.remarks}</p>
                      </div>
                    )}
                  </div>
                )}

                {/* Rejection Notes - Only show if expense is rejected and has rejection notes */}
                {expense.status?.toLowerCase() === 'rejected' && expense.rejection_notes && (
                  <div className="md:col-span-3 border-l-4 border-red-500 pl-4 bg-red-50 p-4 rounded-r-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertCircle className="h-4 w-4 text-red-600" />
                      <p className="font-semibold text-red-600 uppercase tracking-wide text-sm">
                        Rejection Notes
                      </p>
                    </div>
                    <p className="text-red-700 font-medium leading-relaxed">{expense.rejection_notes}</p>
                  </div>
                )}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <p className="text-sm text-gray-500">Date</p>
                  <p className="text-lg font-medium">
                    {expense.expense_date || "N/A"}
                  </p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <p className="text-sm text-gray-500">Total Budget</p>
                  <p className="text-lg font-medium">
                    ₹{expense.total_budget?.toFixed(2) || "0.00"}
                  </p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <p className="text-sm text-gray-500">Total Actual</p>
                  <p className="text-lg font-medium">
                    ₹{expense.total_actual?.toFixed(2) || "0.00"}
                  </p>
                </div>
              </div>

              <div className="mt-8">
                <h3 className="text-xl font-semibold mb-4">
                  {expense.source_type === "excel"
                    ? "Excel Upload Data"
                    : "Manual Entry Data"}
                </h3>
                <ExcelDataTable
                  data={[expense]}
                  editable={false}
                  showRemarks={true}
                />
              </div>

              <div className="flex justify-between">
                <Button
                  onClick={() => router.push('/funding/expenses')}
                  variant="outline"
                  className="border-blue-600 text-blue-600 hover:bg-blue-50"
                >
                  Back to List
                </Button>

                <Button
                  onClick={() => router.push('/expense')}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Add New Expense
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center p-8">
              <p className="text-gray-500 mb-4">Expense not found</p>
              <Button
                onClick={() => router.push('/funding/expenses')}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Back to List
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
