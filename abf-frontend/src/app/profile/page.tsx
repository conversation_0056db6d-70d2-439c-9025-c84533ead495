"use client";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Layout } from "@/components/Layout";
import { TabButton } from "@/components/TabButton";
import {
  BasicDetails,
  OrganizationDetails,
  LegalDocuments,
  GrantsList,
} from "@/components/profile";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import { getProfile, transformOrganizationAPIResponseToOrganization, transformProfileData } from "@/services/profile-service";
import { Organization, ProfileData } from "@/types/profile";
import { HistoricalGrant } from "@/types/profile";

// Form validation schema
const formSchema = z.object({
  organization_name: z.string().min(1, "Organization name is required"),
  organization_type: z.string().min(1, "Organization type is required"),
  tax_registration_no: z.string().min(1, "Tax registration number is required"),
  pan: z
    .string()
    .min(1, "PAN is required")
    .regex(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, "Invalid PAN"),
  mailing_address: z.string().min(1, "Mailing address is required"),
  phone_number: z
    .string()
    .min(1, "Phone number is required")
    .regex(/^\+\d{2}-\d{10}$/, "Invalid phone"),
  email_address: z.string().min(1, "Email is required").email("Invalid email"),
  website_url: z.string().url("Invalid URL").optional().or(z.literal("")),
  number_of_team_members: z
    .string()
    .min(1, "Required")
    .refine((val) => parseInt(val) > 0, "Invalid number"),
  mission_vision: z.string().min(1, "Mission/vision is required"),
  background_history: z.string().optional(),
  previous_grants_info: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState("profile");
  const [activeSubTab, setActiveSubTab] = useState("applicant");
  const [isEditing, setIsEditing] = useState(false);
  const [editingTab, setEditingTab] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [profileId, setProfileId] = useState<number | null>(null);
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [profileData, setProfileData] = useState<ProfileData>({
    organization: {
      organizationName: "",
      organizationLegalType: "",
      organizationFunctionType: "",
      taxRegistrationNumber: "",
      panNumber: "",
    },
    // TODO: TO be removed
    contact: {
      fullName: "",
      phone: "",
      email: "",
      address: "",
    },
    details: {
      teamMembers: "",
      website: "",
      mission: "",
      history: "",
    },
    identificationDetails: {
      csrRegistrationNumber: "",
      taxRegistrationNumber: "",
      taxRegistrationNumberUnder12A: "",
      trustRegistrationNumber: "",
      darpanId: "",
      fcraRegistrationNumber: "",
    },
    previousGrants: [] as HistoricalGrant[],
    keyPersonnel: [],
  });

  // Fetch profile data when component mounts
  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        setIsLoading(true);
        // Fetch organisation profile based on cognito id instead of just plain id
        const response = await getProfile();
        if (response) {
          const frontedData = transformOrganizationAPIResponseToOrganization(response.data);
          setOrganization(frontedData);
          console.log("Transformed organization data = " + JSON.stringify(frontedData));

          response.data.organization_function_type =
            response.data.organization_function_type_name;
          response.data.organization_legal_type =
            response.data.organization_legal_type_name;
          const transformedData: ProfileData = transformProfileData(
            response.data
          );
          setProfileData(transformedData);
          console.log("new profile data = " + JSON.stringify(transformedData));
        }
      } catch (error) {
        console.error("Error fetching profile data:", error);
        toast.error("Failed to load profile data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfileData();
  }, []);

  const toggleEditMode = (tabName = "") => {
    const targetTab = tabName || activeSubTab;
    if (isEditing && editingTab === targetTab) {
      setIsEditing(false);
      setEditingTab("");
    } else {
      setIsEditing(true);
      setEditingTab(targetTab);
    }
  };

  return (
    <Layout title="Profile">
      <div className="max-w-5xl mx-auto">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          </div>
        ) : (
          <>
            <div className="mb-8 border-b border-gray-200">
              <div className="flex">
                <TabButton
                  active={activeTab === "profile"}
                  onClick={() => setActiveTab("profile")}
                >
                  Profile Details
                </TabButton>
                <TabButton
                  active={activeTab === "grant"}
                  onClick={() => setActiveTab("grant")}
                >
                  Grant Details
                </TabButton>
              </div>
            </div>

            {showSuccessMessage && (
              <Alert className="mb-4 bg-green-50 text-green-700 border-green-200">
                <AlertDescription>
                  Profile updated successfully!
                </AlertDescription>
              </Alert>
            )}

            {/* Profile Tab */}
            {activeTab === "profile" && (
              <div className="space-y-6">
                <h1 className="text-2xl font-semibold text-gray-800">
                  Organization Profile
                </h1>

                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    className={activeSubTab === "applicant" ? "bg-teal-500 text-white" : ""}
                    onClick={() => setActiveSubTab("applicant")}
                  >
                    Basic Details
                  </Button>
                  <Button
                    variant="outline"
                    className={activeSubTab === "documents" ? "bg-teal-500 text-white" : ""}
                    onClick={() => setActiveSubTab("documents")}
                  >
                    Legal Documents
                  </Button>
                </div>

                <div className="space-y-8">
                  {activeSubTab === "applicant" && (
                    <BasicDetails
                      organization={organization}
                    />
                  )}

                  {activeSubTab === "documents" && (
                    <LegalDocuments
                      organization={organization}
                    />
                  )}
                </div>
              </div>
            )}

            {/* Grant Tab */}
            {activeTab === "grant" && (
              <GrantsList
              organization={organization}/>
            )}
          </>
        )}
      </div>
    </Layout>
  );
}
