// src/app/grantmaker/resources/page.tsx
"use client";

import Head from "next/head";
import { useEffect, useState, useRef } from "react";
import {
  FileText,
  Download,
  DownloadCloud,
  X,
  Upload,
  Trash2Icon,
} from "lucide-react";
import {
  getResources,
  createResources,
  deleteResources,
} from "@/services/resources-service";
import SupportTabsLayout from "../support/SupportTabsLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface Resource {
  id: number;
  title: string;
  file_description?: string;
  created_at: string;
  attachments: string[];
  file_names?: string[];
}

interface ResourceInput {
  title: string;
  description: string;
  file: File | null;
}

export default function SupportResources() {
  const [resources, setResources] = useState<Resource[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredResources, setFilteredResources] = useState<Resource[]>([]);
  const [resourceInputs, setResourceInputs] = useState<ResourceInput[]>([
    { title: "", description: "", file: null },
  ]);
  const fileInputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const fetchResources = async () => {
    try {
      const data = await getResources();
      const mappedData = data.map((resource: Resource) => ({
        ...resource,
        file_names:
          resource.file_names ||
          resource.attachments.map(
            (url, i) => url.split("/").pop() || `File ${i + 1}`,
          ),
      }));
      setResources(mappedData);
      setFilteredResources(mappedData);
      setLoading(false);
    } catch (err: any) {
      setError(err.message);
      setLoading(false);
    }
  };

  // Fetch resources logic remains unchanged
  useEffect(() => {
    fetchResources();
  }, []);

  // Search functionality remains unchanged
  useEffect(() => {
    if (!searchTerm) {
      setFilteredResources(resources);
      return;
    }
    const lowerSearch = searchTerm.toLowerCase();
    const filtered = resources.filter(
      (resource) =>
        resource.title.toLowerCase().includes(lowerSearch) ||
        (resource.file_description &&
          resource.file_description.toLowerCase().includes(lowerSearch)),
    );
    setFilteredResources(filtered);
  }, [searchTerm, resources]);

  // Handle download functions remain unchanged
  const handleDownload = (fileUrl: string, fileName: string) => {
    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = fileName || "resource";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDownloadAll = async () => {
    const JSZip = (await import("jszip")).default;
    const FileSaver = (await import("file-saver")).default;
    const zip = new JSZip();
    try {
      for (const resource of resources) {
        for (let i = 0; i < resource.attachments.length; i++) {
          const fileUrl = resource.attachments[i];
          const fileName = resource.file_names?.[i] || `File ${i + 1}`;
          const response = await fetch(fileUrl);
          if (!response.ok) throw new Error(`Failed to fetch ${fileName}`);
          const blob = await response.blob();
          zip.file(fileName, blob);
        }
      }
      const content = await zip.generateAsync({ type: "blob" });
      FileSaver.saveAs(content, "resources.zip");
    } catch (err: any) {
      console.error("Download all error:", err);
      setError("Failed to download all files");
    }
  };

  // Upload form handlers remain unchanged
  const addNewFileInput = () => {
    if (resourceInputs.length >= 100) {
      setError("Cannot add more than 100 files");
      return;
    }
    setResourceInputs([
      ...resourceInputs,
      { title: "", description: "", file: null },
    ]);
  };

  const handleInputChange = (
    index: number,
    field: keyof ResourceInput,
    value: string | File | null,
  ) => {
    const updatedInputs = [...resourceInputs];
    updatedInputs[index] = { ...updatedInputs[index], [field]: value };
    setResourceInputs(updatedInputs);
  };

  const handleFileChange = (
    index: number,
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      if (file.size > 50 * 1024 * 1024) {
        setError(`File ${file.name} is too large (max 5MB)`);
        return;
      }
      handleInputChange(index, "file", file);
    } else {
      handleInputChange(index, "file", null);
    }
  };

  const removeFileInput = (index: number) => {
    if (resourceInputs.length === 1) {
      setError("At least one file input is required");
      return;
    }
    const updatedInputs = resourceInputs.filter((_, i) => i !== index);
    setResourceInputs(updatedInputs);
    fileInputRefs.current = fileInputRefs.current.filter((_, i) => i !== index);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    for (const [index, input] of resourceInputs.entries()) {
      if (!input.title) {
        setError(`Title is required for file ${index + 1}`);
        setLoading(false);
        return;
      }
      if (!input.file) {
        setError(`File is required for file ${index + 1}`);
        setLoading(false);
        return;
      }
    }
    try {
      const resourcesData = resourceInputs.map((input) => ({
        title: input.title,
        description: input.description,
        file: input.file,
      }));
      await createResources(resourcesData);
      setResourceInputs([{ title: "", description: "", file: null }]);
      fileInputRefs.current.forEach((ref) => {
        if (ref) ref.value = "";
      });
      fileInputRefs.current = [null];
      setIsModalOpen(false);
      const data = await getResources();
      const mappedData = data.map((resource: Resource) => ({
        ...resource,
        file_names:
          resource.file_names ||
          resource.attachments.map(
            (url, i) => url.split("/").pop() || `File ${i + 1}`,
          ),
      }));
      setResources(mappedData);
      setFilteredResources(mappedData);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  function handleDeleteResource(resource: Resource): void {
    toast.promise(deleteResources(resource.id), {
      loading: `Deleting ${resource.title}`,
      success: (data: any) => {
        setResources([]);
        fetchResources();
        return `${data.message}`;
      },
      error: `Failed to delete ${resource.title}`,
    });
  }

  return (
    <SupportTabsLayout>
      <Head>
        <title>Resources - Grant Management System</title>
      </Head>
      <div className="min-h-screen p-6 bg-gradient-to-b from-gray-50 to-gray-100">
        <div className="w-full max-w-6xl mx-auto space-y-6">
          {/* Header */}

          {/* Search Bar */}
          <div className="relative mb-5">
            <input
              type="text"
              placeholder="Search resources..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full p-3 pr-10 rounded-lg border border-gray-200 shadow-sm focus:ring-2 focus:ring-teal-300 focus:border-teal-500 text-sm bg-white transition-all duration-300"
            />
            <svg
              className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 1116.65 16.65z"
              />
            </svg>
          </div>

          {/* Resources List */}
          <Card className="bg-white/90 backdrop-blur-sm shadow-lg rounded-lg border border-gray-100">
            <CardHeader className="flex flex-row items-center justify-between p-6 border-b border-gray-100">
              <div className="flex items-center space-x-4 mb-4">
                <FileText className="w-7 h-7 text-teal-600" />
                <h2 className="text-2xl font-bold text-gray-900 tracking-tight">
                  Resources
                </h2>
              </div>
              <div className="flex space-x-3">
                <Button
                  onClick={() => setIsModalOpen(true)}
                  className="flex items-center space-x-2 bg-teal-600 hover:bg-teal-700 text-white text-sm px-4 py-2 rounded-lg transition-all duration-300 shadow-sm"
                  aria-label="Upload documents"
                >
                  <Upload className="w-4 h-4" />
                  <span>Upload</span>
                </Button>
                {filteredResources.length > 0 && (
                  <Button
                    onClick={handleDownloadAll}
                    className="flex items-center space-x-2 bg-teal-600 hover:bg-teal-700 text-white text-sm px-4 py-2 rounded-lg transition-all duration-300 shadow-sm"
                    aria-label="Download all resources as ZIP"
                  >
                    <DownloadCloud className="w-4 h-4" />
                    <span>Download All</span>
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent className="p-6">
              {loading && (
                <p className="text-gray-500 text-center text-sm font-medium animate-pulse">
                  Loading resources...
                </p>
              )}
              {error && (
                <p className="text-red-500 text-center text-sm font-medium bg-red-50 p-3 rounded-lg">
                  {error}
                </p>
              )}
              {!loading && !error && filteredResources.length === 0 && (
                <p className="text-gray-500 text-center text-sm italic">
                  No resources available.
                </p>
              )}
              <div className="space-y-4">
                {filteredResources.map((resource) => (
                  <div
                    key={resource.id}
                    className="group bg-white border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
                  >
                    <div className="p-5 flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <FileText className="w-6 h-6 text-teal-500" />
                          <h3 className="text-base font-semibold text-gray-800  group-hover:text-teal-600 transition-colors duration-300">
                            {resource.title}
                          </h3>
                        </div>
                        <p className="text-sm text-gray-500 mt-2 ml-9">
                          Created:{" "}
                          {new Date(resource.created_at).toLocaleDateString()}
                        </p>
                        {resource.file_description && (
                          <p className="text-sm text-gray-600 mt-2 ml-9 line-clamp-2">
                            {resource.file_description}
                          </p>
                        )}
                      </div>
                      <div className="flex flex-col space-y-2 items-end">
                        <Button
                          size="icon"
                          variant="destructive"
                          onClick={() => handleDeleteResource(resource)}
                        >
                          <Trash2Icon />
                        </Button>
                        {resource.attachments?.length > 0 &&
                          resource.attachments.map((attachment, index) => (
                            <Button
                              key={index}
                              onClick={() =>
                                handleDownload(
                                  attachment,
                                  resource.file_names?.[index] ||
                                  `File ${index + 1}`,
                                )
                              }
                              className="flex items-center space-x-2 bg-teal-100 text-teal-700 hover:bg-teal-200 text-sm px-3 py-1.5 rounded-lg transition-all duration-300"
                              aria-label={`Download ${resource.file_names?.[index] || `File ${index + 1}`}`}
                            >
                              <Download className="w-4 h-4" />
                              <span>
                                {resource.file_names?.[index] ||
                                  `File ${index + 1}`}
                              </span>
                            </Button>
                          ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Upload Modal */}
          {isModalOpen && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <Card className="w-full max-w-2xl max-h-[85vh] bg-white/95 backdrop-blur-md rounded-xl shadow-2xl flex flex-col animate-in fade-in-50 duration-300">
                <CardHeader className="flex flex-row items-center justify-between p-5 border-b border-gray-100">
                  <CardTitle className="text-xl font-semibold text-gray-800 ">
                    Upload Documents
                  </CardTitle>
                  <Button
                    variant="ghost"
                    onClick={() => setIsModalOpen(false)}
                    className="text-gray-500 hover:text-gray-700 p-2 rounded-full hover:bg-teal-100 transition-all duration-300"
                    aria-label="Close modal"
                  >
                    <X className="w-5 h-5" />
                  </Button>
                </CardHeader>
                <div className="flex-1 overflow-y-auto p-6">
                  <form onSubmit={handleSubmit} className="space-y-5">
                    {resourceInputs.map((input, index) => (
                      <Card
                        key={index}
                        className="p-4 border border-gray-100 bg-white/50 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
                      >
                        <div className="flex justify-between items-center mb-3">
                          <h4 className="text-sm font-medium text-gray-700 ">
                            File {index + 1}
                          </h4>
                          {resourceInputs.length > 1 && (
                            <Button
                              variant="ghost"
                              onClick={() => removeFileInput(index)}
                              className="text-red-500 hover:text-red-600 text-xs p-1 rounded-full hover:bg-red-50 transition-all duration-300"
                              aria-label={`Remove file input ${index + 1}`}
                            >
                              Remove
                            </Button>
                          )}
                        </div>
                        <div className="space-y-4">
                          <div>
                            <label
                              htmlFor={`title-${index}`}
                              className="block text-sm font-medium text-gray-600"
                            >
                              Resource Title
                            </label>
                            <input
                              type="text"
                              id={`title-${index}`}
                              value={input.title}
                              onChange={(e) =>
                                handleInputChange(
                                  index,
                                  "title",
                                  e.target.value,
                                )
                              }
                              className="mt-1 block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 focus:ring-opacity-50 text-sm py-2.5 px-4 transition-all duration-300 bg-white hover:bg-gray-50"
                              required
                            />
                          </div>
                          <div>
                            <label
                              htmlFor={`description-${index}`}
                              className="block text-sm font-medium text-gray-600"
                            >
                              Description (Optional)
                            </label>
                            <textarea
                              id={`description-${index}`}
                              value={input.description}
                              onChange={(e) =>
                                handleInputChange(
                                  index,
                                  "description",
                                  e.target.value,
                                )
                              }
                              className="mt-1 block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 focus:ring-opacity-50 text-sm py-2.5 px-4 transition-all duration-300 bg-white hover:bg-gray-50"
                              rows={3}
                            />
                          </div>
                          <div>
                            <label
                              htmlFor={`file-${index}`}
                              className="block text-sm font-medium text-gray-600"
                            >
                              Upload File (Max 5MB)
                            </label>
                            <div className="mt-1 relative">
                              <input
                                type="file"
                                id={`file-${index}`}
                                onChange={(e) => handleFileChange(index, e)}
                                className="block w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-teal-100 file:text-teal-700 file:hover:bg-teal-200 file:transition-all file:duration-300 file:cursor-pointer file:shadow-sm"
                                ref={(el) =>
                                  (fileInputRefs.current[index] = el)
                                }
                                required
                              />
                              {input.file && (
                                <p className="mt-2 text-xs text-gray-500">
                                  Selected: {input.file.name} (
                                  {(input.file.size / 1024 / 1024).toFixed(2)}{" "}
                                  MB)
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                    {error && (
                      <p className="text-red-500 text-sm font-medium bg-red-50 p-3 rounded-lg">
                        {error}
                      </p>
                    )}
                  </form>
                </div>
                {/* Modal Footer */}
                <div className="p-5 border-t border-gray-100 bg-white/50 shadow-sm">
                  <div className="flex justify-between items-center">
                    <Button
                      onClick={addNewFileInput}
                      className="bg-teal-600 hover:bg-teal-700 text-white text-sm px-4 py-2 rounded-lg transition-all duration-300 shadow-sm"
                    >
                      Add Another File
                    </Button>
                    <div className="flex space-x-3">
                      <Button
                        onClick={() => setIsModalOpen(false)}
                        className="bg-teal-200 hover:bg-teal-300 text-teal-800 text-sm px-4 py-2 rounded-lg transition-all duration-300 shadow-sm"
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        disabled={loading}
                        onClick={handleSubmit}
                        className="bg-teal-600 hover:bg-teal-700 text-white text-sm px-4 py-2 rounded-lg disabled:bg-teal-400 disabled:cursor-not-allowed transition-all duration-300 shadow-sm"
                      >
                        {loading ? "Uploading..." : "Upload"}
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          )}
        </div>
      </div>
    </SupportTabsLayout>
  );
}
