"use client";

import { useEffect, useMemo, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { format, formatDistanceToNow } from "date-fns";
import {
  MessageSquare,
  Filter,
  ArrowDown,
  Paperclip,
  Eye,
  Lock,
  Unlock,
  Search,
  AlertCircle,
} from "lucide-react";
import { toast } from "sonner";
import SupportTabsLayout from "../../SupportTabsLayout";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  fetchAllTickets,
  getTicketUpdates,
  sendTicketUpdate,
  updateTicketStatus,
  getTicketDetails,
} from "@/services/supportTicket.service";

const STATUSES = ["open", "under review", "resolved"];

export default function GrantMakerChatPage() {
  const router = useRouter();
  const [tickets, setTickets] = useState<any[]>([]);
  const [selected, setSelected] = useState<any | null>(null);
  const [timelineMap, setTimelineMap] = useState<Record<number, any[]>>({});
  const [newMessage, setNewMessage] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [isSending, setIsSending] = useState(false);
  const [newMsgAlert, setNewMsgAlert] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [updatingStatusId, setUpdatingStatusId] = useState<number | null>(null);

  const chatRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateIdRef = useRef<Record<number, number>>({});
  const lastStatusCountRef = useRef<Record<number, number>>({});

  const scrollToBottom = () => {
    setTimeout(() => {
      chatRef.current?.scrollTo({ top: chatRef.current.scrollHeight, behavior: "smooth" });
      setNewMsgAlert(false);
    }, 100);
  };

  const buildTimeline = (ticket: any, updates: any[]) => {
    const timeline = [];

    if (ticket?.description) {
      timeline.push({
        id: "desc",
        type: "message",
        update_text: ticket.description,
        updated_at: ticket.created_at,
        user: {
          type: { code: "GRANTEE" },
          first_name: ticket.created_by_first_name,
          last_name: ticket.created_by_last_name,
          email: ticket.created_by_email,
        },
        attachments: ticket.attachments || [],
      });
    }

    (ticket.status_history || []).forEach((entry: any, idx: number) => {
      timeline.push({
        type: "status",
        id: `status-${entry.id}-${idx}`,
        updated_at: entry.changed_at,
        to_status: entry.to_status,
        user: entry.changed_by,
      });
    });

    updates.forEach((u: any) => timeline.push({ type: "message", ...u }));

    return timeline.sort((a, b) => new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime());
  };

  useEffect(() => {
    (async () => {
      const all = await fetchAllTickets();
      setTickets(all);
      if (all.length > 0) setSelected(all[0]);

      const map: Record<number, any[]> = {};
      for (const t of all) {
        const [updates, latestTicket] = await Promise.all([
          getTicketUpdates(t.id),
          getTicketDetails(t.id),
        ]);
        map[t.id] = buildTimeline(latestTicket, updates);
        lastUpdateIdRef.current[t.id] = updates.at(-1)?.id || 0;
        lastStatusCountRef.current[t.id] = latestTicket.status_history?.length || 0;
      }
      setTimelineMap(map);
    })();
  }, []);

  useEffect(() => {
    if (!selected?.id) return;

    pollingRef.current = setInterval(async () => {
      const [updates, latestTicket] = await Promise.all([
        getTicketUpdates(selected.id),
        getTicketDetails(selected.id),
      ]);

      const lastUpdateId = lastUpdateIdRef.current[selected.id] || 0;
      const nowUpdateId = updates.at(-1)?.id || 0;

      const lastStatusCount = lastStatusCountRef.current[selected.id] || 0;
      const nowStatusCount = latestTicket.status_history?.length || 0;

      const hasNewUpdate = nowUpdateId !== lastUpdateId;
      const hasNewStatus = nowStatusCount !== lastStatusCount;

      if (hasNewUpdate || hasNewStatus) {
        lastUpdateIdRef.current[selected.id] = nowUpdateId;
        lastStatusCountRef.current[selected.id] = nowStatusCount;

        const newTimeline = buildTimeline(latestTicket, updates);
        setTimelineMap((prev) => ({ ...prev, [selected.id]: newTimeline }));

        const isAtBottom =
          chatRef.current &&
          chatRef.current.scrollHeight - chatRef.current.scrollTop - chatRef.current.clientHeight < 100;

        if (isAtBottom) scrollToBottom();
        else setNewMsgAlert(true);

        if (hasNewUpdate && !hasNewStatus) {
          toast.success(
            latestTicket.status === "resolved"
              ? "You have received a new message. But this ticket is resolved."
              : "You have received a new message. Your ticket is open."
          );
        }

        if (hasNewStatus && !hasNewUpdate) {
          const latestStatus = latestTicket.status_history?.at(-1)?.to_status;
          toast.success(`Ticket status changed to ${latestStatus}.`);
        }

        if (hasNewUpdate && hasNewStatus) {
          const latestStatus = latestTicket.status_history?.at(-1)?.to_status;
          toast.success(
            latestTicket.status === "resolved"
              ? `You have received a new message and ticket is now "${latestStatus}".`
              : `You have received a new message. Ticket status changed to "${latestStatus}".`
          );
        }
      }
    }, 2000);

    return () => clearInterval(pollingRef.current!);
  }, [selected?.id]);

  useEffect(() => {
    const el = chatRef.current;
    if (!el) return;
    const onScroll = () => {
      const isBottom = el.scrollHeight - el.scrollTop - el.clientHeight < 100;
      setShowScrollButton(!isBottom);
      if (isBottom) setNewMsgAlert(false);
    };
    el.addEventListener("scroll", onScroll);
    return () => el.removeEventListener("scroll", onScroll);
  }, []);

  const handleSend = async () => {
    if (!newMessage.trim() && !file) return;
    try {
      setIsSending(true);
      const update = await sendTicketUpdate(selected.id, newMessage, file);
      const [updates, updatedTicket] = await Promise.all([
        getTicketUpdates(selected.id),
        getTicketDetails(selected.id),
      ]);
      const updatedTimeline = buildTimeline(updatedTicket, updates);
      setTimelineMap((prev) => ({ ...prev, [selected.id]: updatedTimeline }));
      lastUpdateIdRef.current[selected.id] = update.id;
      setNewMessage("");
      setFile(null);
      fileInputRef.current!.value = "";
      scrollToBottom();
    } catch {
      toast.error("Failed to send.");
    } finally {
      setIsSending(false);
    }
  };

  const handleStatusChange = async () => {
    try {
      const newStatus = selected.status === "resolved" ? "open" : "resolved";
      setUpdatingStatusId(selected.id);
      const updated = await updateTicketStatus(selected.id, newStatus);
      const newTicket = { ...selected, status: updated.status };
      setTickets((prev) => prev.map((t) => (t.id === selected.id ? newTicket : t)));
      setSelected(newTicket);
    } catch {
      toast.error("Failed to update ticket status");
    } finally {
      setUpdatingStatusId(null);
    }
  };

  const filteredTickets = useMemo(() => {
    return tickets.filter((t) =>
      (t.title?.toLowerCase().includes(search.toLowerCase()) ||
        t.grant_name?.toLowerCase().includes(search.toLowerCase())) &&
      (statusFilter.length === 0 || statusFilter.includes(t.status))
    );
  }, [tickets, search, statusFilter]);

  const getStatusBadgeStyle = (status: string) => {
    switch (status?.toLowerCase()) {
      case "open":
        return "bg-teal-100 text-teal-800 border-teal-50 border-opacity-50";
      case "under review":
        return "bg-amber-100 text-amber-800 border-amber-50 border-opacity-50";
      case "resolved":
        return "bg-teal-100 text-teal-800 border-teal-50 border-opacity-50";
      default:
        return "bg-gray-100 text-gray-700 border-gray-50 border-opacity-50";
    }
  };

  const renderPriorityBadge = (priority: string) => {
    const styles: Record<string, string> = {
      low: "bg-gray-100 text-gray-700 border-gray-50 border-opacity-50",
      medium: "bg-amber-100 text-amber-800 border-amber-50 border-opacity-50",
      high: "bg-orange-100 text-orange-800 border-orange-50 border-opacity-50",
      urgent: "bg-red-100 text-red-800 border-red-50 border-opacity-50",
    };
    return (
      <span
        className={`text-xs font-medium px-2 py-1 rounded-lg ${
          styles[priority?.toLowerCase()] || "bg-gray-100 text-gray-600 border-gray-50 border-opacity-50"
        }`}
      >
        {priority?.replace(/\b\w/g, (l: string) => l.toUpperCase())}
      </span>
    );
  };

  return (
    <SupportTabsLayout>
      <div className="flex h-[calc(100vh-112px)] bg-gray-50 shadow-lg rounded-2xl border border-gray-200">
        <aside className="w-1/3 p-6 space-y-6 bg-white rounded-l-2xl border-r border-gray-200 flex flex-col">
          <header className="flex justify-between items-center">
            <h2 className="text-xl font-bold text-gray-800 flex items-center gap-3">
              <MessageSquare className="w-6 h-6 text-teal-600" />
              Conversations
            </h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push("/grantmaker/support/my-tickets/support-ticket")}
              className="text-gray-500 hover:bg-gray-100"
            >
              Back to Tickets
            </Button>
          </header>

          <div className="flex gap-3">
            <div className="relative flex-grow">
              <Search className="absolute left-3.5 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
              <input
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                placeholder="Search by title or grant..."
                className="w-full pl-10 pr-4 py-2.5 border border-gray-300 bg-white rounded-full text-sm text-gray-700 placeholder:text-gray-400 focus:outline-none focus:border-teal-500 focus:ring-2 focus:ring-teal-200 transition-all"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className="bg-white border-gray-300 text-gray-600 hover:bg-gray-100 rounded-full"
                >
                  <Filter className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-48 bg-white rounded-lg shadow-lg border-gray-200">
                <DropdownMenuLabel className="text-gray-700 font-semibold">Status Filter</DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-gray-200" />
                {STATUSES.map((status) => (
                  <DropdownMenuCheckboxItem
                    key={status}
                    checked={statusFilter.includes(status)}
                    onCheckedChange={(checked) =>
                      setStatusFilter((prev) =>
                        checked ? [...prev, status] : prev.filter((s) => s !== status)
                      )
                    }
                    className="text-sm text-gray-700 hover:!bg-teal-50 focus:bg-teal-100"
                  >
                    {status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className="overflow-y-auto space-y-3 flex-1 -mr-3 pr-3">
            {filteredTickets.map((t, idx) => (
              <div
                key={t.id}
                className={`cursor-pointer p-4 rounded-xl border-2 transition-all duration-300 ${
                  selected?.id === t.id ? "bg-teal-50 border-teal-500 shadow-md" : "hover:bg-gray-100 border-transparent"
                }`}
                onClick={() => setSelected(t)}
              >
                <div className="flex justify-between items-start">
                  <p className="text-sm font-semibold text-gray-800">{t.title}</p>
                  <span
                    className={`inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-full ${getStatusBadgeStyle(t.status)}`}
                  >
                    {t.status?.replace("-", " ").replace(/\b\w/g, (l: string) => l.toUpperCase())}
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-1">{t.grant_name}</p>
                <div className="flex items-center justify-between mt-2">
                  {t.priority && renderPriorityBadge(t.priority)}
                  <p className="text-xs text-gray-400">
                    {formatDistanceToNow(new Date(t.updated_at), { addSuffix: true })}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </aside>
        <main className="w-2/3 flex flex-col bg-white rounded-r-2xl">
          {selected ? (
            <>
              <header className="px-6 py-4 flex justify-between items-center border-b border-gray-200">
                <div className="flex items-center gap-4">
                  <div className="border-l-4 border-teal-500 pl-3">
                    <h3 className="text-lg font-bold text-gray-800">Ticket #{selected.id}</h3>
                    <p className="text-sm text-gray-500">{selected.grant_name}</p>
                  </div>
                  {selected.priority && renderPriorityBadge(selected.priority)}
                  <span
                    className={`inline-flex items-center px-3 py-1 text-xs font-medium rounded-full ${getStatusBadgeStyle(selected.status)}`}
                  >
                    {selected.status?.replace("-", " ").replace(/\b\w/g, (l: string) => l.toUpperCase())}
                  </span>
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() =>
                      router.push(`/grantmaker/support/my-tickets/support-ticket/support-ticket-detail?id=${selected.id}`)
                    }
                    className="border-gray-300 text-gray-600 hover:bg-gray-100 rounded-lg"
                  >
                    <Eye className="w-4 h-4 mr-2" /> View Details
                  </Button>
                  <Button
                    size="sm"
                    variant={selected.status === "resolved" ? "default" : "outline"}
                    onClick={handleStatusChange}
                    disabled={updatingStatusId === selected.id}
                    className={`${
                      selected.status === "resolved"
                        ? "bg-teal-600 hover:bg-teal-700 text-white"
                        : "border-gray-300 text-gray-600 hover:bg-gray-100"
                    } rounded-lg`}
                  >
                    {selected.status === "resolved" ? (
                      <>
                        <Unlock className="w-4 h-4 mr-2" /> Reopen
                      </>
                    ) : (
                      <>
                        <Lock className="w-4 h-4 mr-2" /> Close
                      </>
                    )}
                  </Button>
                </div>
              </header>

              <div className="flex-1 overflow-y-auto p-6 space-y-6 bg-gray-50" ref={chatRef}>
                {timelineMap[selected?.id || 0]?.length > 0 ? (
                  timelineMap[selected.id].map((item, idx) => {
                    if (item.type === "status") {
                      return (
                        <div key={item.id} className="text-center text-xs text-gray-500 my-4">
                          Status changed to{" "}
                          <span
                            className={`font-semibold mx-1 ${getStatusBadgeStyle(item.to_status)} px-2 py-0.5 rounded-full`}
                          >
                            {item.to_status.replace("-", " ").replace(/\b\w/g, (l: string) => l.toUpperCase())}
                          </span>{" "}
                          on {format(new Date(item.updated_at), "dd MMM yyyy, HH:mm")}
                        </div>
                      );
                    }

                    const isGM = item.user?.type?.code === "GRANT_MAKER";
                    const name = item.user?.first_name
                      ? `${item.user.first_name} ${item.user.last_name}`
                      : item.user?.email;
                    return (
                      <div key={item.id} className={`flex gap-3 ${isGM ? "justify-end" : "justify-start"}`}>
                        <div className={`rounded-2xl px-4 py-3 max-w-lg shadow-sm ${
                            isGM
                              ? "bg-teal-600 text-white rounded-br-none"
                              : "bg-white text-gray-800 border border-gray-200 rounded-bl-none"
                          }`}
                        >
                          <p className={`text-xs font-semibold mb-1 ${isGM ? 'text-teal-100' : 'text-gray-600'}`}>
                            {name} • {formatDistanceToNow(new Date(item.updated_at), { addSuffix: true })}
                          </p>
                          <p className="whitespace-pre-wrap">{item.update_text}</p>
                          {item.attachments?.map((url: string, i: number) => (
                            <a
                              key={i}
                              href={url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className={`flex items-center gap-2 text-xs mt-2 ${isGM ? 'text-teal-200 hover:text-white' : 'text-teal-600 hover:text-teal-800'}`}
                            >
                              <Paperclip className="w-4 h-4" />
                              {new URL(url).pathname.split("/").pop()?.split("?")[0]}
                            </a>
                          ))}
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <AlertCircle className="w-10 h-10 mb-4 text-gray-400" />
                    <p className="font-semibold">No messages yet.</p>
                    <p className="text-sm">Start the conversation by sending a message.</p>
                  </div>
                )}
                {newMsgAlert && showScrollButton && (
                  <div className="absolute bottom-24 right-10">
                    <Button
                      onClick={scrollToBottom}
                      size="sm"
                      className="bg-teal-600 hover:bg-teal-700 text-white rounded-full px-4 py-2 shadow-lg animate-bounce"
                    >
                      <ArrowDown className="w-4 h-4 mr-2" /> New Messages
                    </Button>
                  </div>
                )}
              </div>

              <footer className="p-4 bg-white border-t border-gray-200">
                {selected.status === "resolved" ? (
                  <p className="text-sm text-center text-gray-500">This ticket is resolved. Reopen to send messages.</p>
                ) : (
                  <div className="flex items-start gap-4">
                    <input
                      ref={fileInputRef}
                      type="file"
                      onChange={(e) => setFile(e.target.files?.[0] || null)}
                      className="hidden"
                      id="file-input"
                    />
                    <Button
                      onClick={() => fileInputRef.current?.click()}
                      variant="outline"
                      size="icon"
                      className="border-gray-300 text-gray-600 hover:bg-gray-100 rounded-full"
                      title="Attach file"
                    >
                      <Paperclip className="w-5 h-5" />
                    </Button>
                    <div className="flex-1">
                      <textarea
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        placeholder="Type your message here..."
                        className="w-full border-gray-300 rounded-lg p-3 text-sm text-gray-800 placeholder:text-gray-400 bg-white focus:outline-none focus:border-teal-500 focus:ring-2 focus:ring-teal-200 resize-none"
                        rows={3}
                      />
                      {file && (
                        <div className="mt-2 text-xs text-gray-600 bg-gray-100 px-3 py-1.5 rounded-lg border border-gray-200 flex items-center justify-between">
                          <span>{file.name}</span>
                          <button onClick={() => setFile(null)} className="text-red-500 hover:text-red-700">X</button>
                        </div>
                      )}
                    </div>
                    <Button
                      disabled={isSending || (!newMessage.trim() && !file)}
                      onClick={handleSend}
                      className="bg-teal-600 hover:bg-teal-700 text-white px-6 py-3 text-sm font-semibold rounded-lg shadow-md hover:shadow-lg transition-all disabled:bg-gray-300 disabled:shadow-none"
                    >
                      {isSending ? "Sending..." : "Send"}
                    </Button>
                  </div>
                )}
              </footer>
            </>
          ) : (
            <div className="flex flex-1 items-center justify-center flex-col gap-4 text-center text-gray-500 p-8">
              <MessageSquare className="w-16 h-16 text-gray-300" />
              <p className="text-xl font-semibold text-gray-700">Select a Conversation</p>
              <p className="text-sm max-w-xs">
                Choose a ticket from the list on the left to view the chat history and respond.
              </p>
            </div>
          )}
        </main>
      </div>
  </SupportTabsLayout>
);
}