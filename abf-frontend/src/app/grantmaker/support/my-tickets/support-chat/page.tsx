"use client";

import { useEffect, useMemo, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { format, formatDistanceToNow } from "date-fns";
import {
  MessageSquare,
  Filter,
  ArrowDown,
  Paperclip,
  Eye,
  Lock,
  Unlock,
  Search,
  AlertCircle,
} from "lucide-react";
import { toast } from "sonner";
import SupportTabsLayout from "../../SupportTabsLayout";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  fetchAllTickets,
  getTicketUpdates,
  sendTicketUpdate,
  updateTicketStatus,
  getTicketDetails,
} from "@/services/supportTicket.service";

const STATUSES = ["open", "under review", "resolved"];

export default function GrantMakerChatPage() {
  const router = useRouter();
  const [tickets, setTickets] = useState<any[]>([]);
  const [selected, setSelected] = useState<any | null>(null);
  const [timelineMap, setTimelineMap] = useState<Record<number, any[]>>({});
  const [newMessage, setNewMessage] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [isSending, setIsSending] = useState(false);
  const [newMsgAlert, setNewMsgAlert] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [updatingStatusId, setUpdatingStatusId] = useState<number | null>(null);

  const chatRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateIdRef = useRef<Record<number, number>>({});
  const lastStatusCountRef = useRef<Record<number, number>>({});

  const scrollToBottom = () => {
    setTimeout(() => {
      chatRef.current?.scrollTo({ top: chatRef.current.scrollHeight, behavior: "smooth" });
      setNewMsgAlert(false);
    }, 100);
  };

  const buildTimeline = (ticket: any, updates: any[]) => {
    const timeline = [];

    if (ticket?.description) {
      timeline.push({
        id: "desc",
        type: "message",
        update_text: ticket.description,
        updated_at: ticket.created_at,
        user: {
          type: { code: "GRANTEE" },
          first_name: ticket.created_by_first_name,
          last_name: ticket.created_by_last_name,
          email: ticket.created_by_email,
        },
        attachments: ticket.attachments || [],
      });
    }

    (ticket.status_history || []).forEach((entry: any, idx: number) => {
      timeline.push({
        type: "status",
        id: `status-${entry.id}-${idx}`,
        updated_at: entry.changed_at,
        to_status: entry.to_status,
        user: entry.changed_by,
      });
    });

    updates.forEach((u: any) => timeline.push({ type: "message", ...u }));

    return timeline.sort((a, b) => new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime());
  };

  useEffect(() => {
    (async () => {
      const all = await fetchAllTickets();
      setTickets(all);
      if (all.length > 0) setSelected(all[0]);

      const map: Record<number, any[]> = {};
      for (const t of all) {
        const [updates, latestTicket] = await Promise.all([
          getTicketUpdates(t.id),
          getTicketDetails(t.id),
        ]);
        map[t.id] = buildTimeline(latestTicket, updates);
        lastUpdateIdRef.current[t.id] = updates.at(-1)?.id || 0;
        lastStatusCountRef.current[t.id] = latestTicket.status_history?.length || 0;
      }
      setTimelineMap(map);
    })();
  }, []);

  useEffect(() => {
    if (!selected?.id) return;

    pollingRef.current = setInterval(async () => {
      const [updates, latestTicket] = await Promise.all([
        getTicketUpdates(selected.id),
        getTicketDetails(selected.id),
      ]);

      const lastUpdateId = lastUpdateIdRef.current[selected.id] || 0;
      const nowUpdateId = updates.at(-1)?.id || 0;

      const lastStatusCount = lastStatusCountRef.current[selected.id] || 0;
      const nowStatusCount = latestTicket.status_history?.length || 0;

      const hasNewUpdate = nowUpdateId !== lastUpdateId;
      const hasNewStatus = nowStatusCount !== lastStatusCount;

      if (hasNewUpdate || hasNewStatus) {
        lastUpdateIdRef.current[selected.id] = nowUpdateId;
        lastStatusCountRef.current[selected.id] = nowStatusCount;

        const newTimeline = buildTimeline(latestTicket, updates);
        setTimelineMap((prev) => ({ ...prev, [selected.id]: newTimeline }));

        const isAtBottom =
          chatRef.current &&
          chatRef.current.scrollHeight - chatRef.current.scrollTop - chatRef.current.clientHeight < 100;

        if (isAtBottom) scrollToBottom();
        else setNewMsgAlert(true);

        if (hasNewUpdate && !hasNewStatus) {
          toast.success(
            latestTicket.status === "resolved"
              ? "You have received a new message. But this ticket is resolved."
              : "You have received a new message. Your ticket is open."
          );
        }

        if (hasNewStatus && !hasNewUpdate) {
          const latestStatus = latestTicket.status_history?.at(-1)?.to_status;
          toast.success(`Ticket status changed to ${latestStatus}.`);
        }

        if (hasNewUpdate && hasNewStatus) {
          const latestStatus = latestTicket.status_history?.at(-1)?.to_status;
          toast.success(
            latestTicket.status === "resolved"
              ? `You have received a new message and ticket is now "${latestStatus}".`
              : `You have received a new message. Ticket status changed to "${latestStatus}".`
          );
        }
      }
    }, 2000);

    return () => clearInterval(pollingRef.current!);
  }, [selected?.id]);

  useEffect(() => {
    const el = chatRef.current;
    if (!el) return;
    const onScroll = () => {
      const isBottom = el.scrollHeight - el.scrollTop - el.clientHeight < 100;
      setShowScrollButton(!isBottom);
      if (isBottom) setNewMsgAlert(false);
    };
    el.addEventListener("scroll", onScroll);
    return () => el.removeEventListener("scroll", onScroll);
  }, []);

  const handleSend = async () => {
    if (!newMessage.trim() && !file) return;
    try {
      setIsSending(true);
      const update = await sendTicketUpdate(selected.id, newMessage, file);
      const [updates, updatedTicket] = await Promise.all([
        getTicketUpdates(selected.id),
        getTicketDetails(selected.id),
      ]);
      const updatedTimeline = buildTimeline(updatedTicket, updates);
      setTimelineMap((prev) => ({ ...prev, [selected.id]: updatedTimeline }));
      lastUpdateIdRef.current[selected.id] = update.id;
      setNewMessage("");
      setFile(null);
      fileInputRef.current!.value = "";
      scrollToBottom();
    } catch {
      toast.error("Failed to send.");
    } finally {
      setIsSending(false);
    }
  };

  const handleStatusChange = async () => {
    try {
      const newStatus = selected.status === "resolved" ? "open" : "resolved";
      setUpdatingStatusId(selected.id);
      const updated = await updateTicketStatus(selected.id, newStatus);
      const newTicket = { ...selected, status: updated.status };
      setTickets((prev) => prev.map((t) => (t.id === selected.id ? newTicket : t)));
      setSelected(newTicket);
    } catch {
      toast.error("Failed to update ticket status");
    } finally {
      setUpdatingStatusId(null);
    }
  };

  const filteredTickets = useMemo(() => {
    return tickets.filter((t) =>
      (t.title?.toLowerCase().includes(search.toLowerCase()) ||
        t.grant_name?.toLowerCase().includes(search.toLowerCase())) &&
      (statusFilter.length === 0 || statusFilter.includes(t.status))
    );
  }, [tickets, search, statusFilter]);

  const getStatusBadgeStyle = (status: string) => {
    switch (status?.toLowerCase()) {
      case "open":
        return "professional-badge-open";
      case "under review":
      case "under-review":
        return "professional-badge-review";
      case "resolved":
        return "professional-badge-resolved";
      default:
        return "bg-gray-500 text-white px-3 py-1 rounded-full text-xs font-semibold";
    }
  };

  const renderPriorityBadge = (priority: string) => {
    const styles: Record<string, string> = {
      low: "professional-priority-low",
      medium: "professional-priority-medium",
      high: "professional-priority-high",
      urgent: "professional-priority-high",
    };
    return (
      <span
        className={styles[priority?.toLowerCase()] || "bg-gray-500 text-white px-2 py-1 rounded-lg text-xs font-bold"}
      >
        {priority?.replace(/\b\w/g, (l: string) => l.toUpperCase())}
      </span>
    );
  };

  return (
    <SupportTabsLayout>
      <div className="flex h-[calc(100vh-112px)] bg-gray-50 shadow-lg rounded-2xl border border-gray-200">
        {/* Enhanced Professional Sidebar */}
        <aside className="w-1/3 bg-gradient-to-b from-gray-50 to-white rounded-l-2xl border-r border-gray-200 flex flex-col shadow-lg">
          <header className="professional-header-gradient p-6 text-white rounded-tl-2xl">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                  <MessageSquare className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold">Live Conversations</h2>
                  <p className="text-gray-300 text-sm">Support chat interface</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push("/grantmaker/support/my-tickets/support-ticket")}
                className="text-white hover:bg-white/20 rounded-lg"
              >
                ← Back
              </Button>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-300">{filteredTickets.length} Active Chats</span>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-gray-300">Online</span>
              </div>
            </div>
          </header>

          <div className="p-6 space-y-4">
            <div className="flex gap-3">
              <div className="relative flex-grow">
                <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                <input
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  placeholder="Search conversations..."
                  className="professional-input w-full pl-12 pr-4 py-3"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    className="professional-button-secondary"
                    size="icon"
                  >
                    <Filter className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56 bg-white rounded-xl shadow-xl border-gray-200">
                  <DropdownMenuLabel className="text-gray-800 font-bold px-4 py-3">Filter by Status</DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-gray-200" />
                  {STATUSES.map((status) => (
                    <DropdownMenuCheckboxItem
                      key={status}
                      checked={statusFilter.includes(status)}
                      onCheckedChange={(checked) =>
                        setStatusFilter((prev) =>
                          checked ? [...prev, status] : prev.filter((s) => s !== status)
                        )
                      }
                      className="text-sm text-gray-700 hover:!bg-teal-50 focus:bg-teal-100 px-4 py-2"
                    >
                      {status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto px-6 pb-6 space-y-4">
            {filteredTickets.map((t, idx) => (
              <div
                key={t.id}
                className={`professional-card cursor-pointer p-5 transition-all duration-300 group ${
                  selected?.id === t.id
                    ? "ring-2 ring-teal-500 bg-gradient-to-r from-teal-50 to-blue-50 transform scale-105"
                    : "hover:shadow-lg hover:scale-102"
                }`}
                onClick={() => setSelected(t)}
              >
                <div className="flex justify-between items-start mb-3">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      t.status === 'open' ? 'bg-green-400' :
                      t.status === 'under-review' ? 'bg-amber-400' : 'bg-teal-400'
                    }`}></div>
                    <h3 className="font-bold text-gray-900 text-sm group-hover:text-teal-700 transition-colors">
                      Ticket #{t.id}
                    </h3>
                  </div>
                  <span className={`inline-flex items-center ${getStatusBadgeStyle(t.status)}`}>
                    {t.status?.replace("-", " ").replace(/\b\w/g, (l: string) => l.toUpperCase())}
                  </span>
                </div>

                <h4 className="font-semibold text-gray-800 text-base mb-2 line-clamp-2 group-hover:text-teal-800 transition-colors">
                  {t.title}
                </h4>

                <div className="space-y-2">
                  <p className="text-sm text-gray-600 truncate">
                    <span className="font-medium">Grant:</span> {t.grant_name}
                  </p>
                  <div className="flex items-center justify-between">
                    {t.priority && renderPriorityBadge(t.priority)}
                    <div className="text-right">
                      <p className="text-xs text-gray-500">
                        {formatDistanceToNow(new Date(t.updated_at), { addSuffix: true })}
                      </p>
                      {timelineMap[t.id]?.length > 0 && (
                        <p className="text-xs text-teal-600 font-medium">
                          {timelineMap[t.id].length} messages
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </aside>
        {/* Enhanced Professional Chat Area */}
        <main className="w-2/3 flex flex-col bg-white rounded-r-2xl shadow-lg">
          {selected ? (
            <>
              {/* Enhanced Chat Header */}
              <header className="professional-header-gradient px-8 py-6 text-white rounded-tr-2xl">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                        <MessageSquare className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold">Ticket #{selected.id}</h3>
                        <p className="text-gray-300">{selected.grant_name}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      {selected.priority && (
                        <div className="bg-white/20 px-3 py-1 rounded-lg">
                          <span className="text-white text-sm font-semibold">
                            {selected.priority.toUpperCase()} Priority
                          </span>
                        </div>
                      )}
                      <span className={`${getStatusBadgeStyle(selected.status)}`}>
                        {selected.status?.replace("-", " ").replace(/\b\w/g, (l: string) => l.toUpperCase())}
                      </span>
                    </div>
                  </div>
                  <div className="flex gap-3">
                    <Button
                      size="sm"
                      onClick={() =>
                        router.push(`/grantmaker/support/my-tickets/support-ticket/support-ticket-detail?id=${selected.id}`)
                      }
                      className="bg-white/20 hover:bg-white/30 text-white border-white/30 rounded-lg"
                    >
                      <Eye className="w-4 h-4 mr-2" /> View Details
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleStatusChange}
                      disabled={updatingStatusId === selected.id}
                      className={`${
                        selected.status === "resolved"
                          ? "bg-white text-gray-900 hover:bg-gray-100"
                          : "bg-white/20 hover:bg-white/30 text-white border-white/30"
                      } rounded-lg`}
                    >
                      {selected.status === "resolved" ? (
                        <>
                          <Unlock className="w-4 h-4 mr-2" /> Reopen
                        </>
                      ) : (
                        <>
                          <Lock className="w-4 h-4 mr-2" /> Close
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </header>

              {/* Enhanced Chat Messages Area */}
              <div className="flex-1 overflow-y-auto p-8 space-y-6 bg-gradient-to-br from-gray-50 to-white relative" ref={chatRef}>
                {timelineMap[selected?.id || 0]?.length > 0 ? (
                  timelineMap[selected.id].map((item, idx) => {
                    if (item.type === "status") {
                      return (
                        <div key={item.id} className="flex justify-center my-8">
                          <div className="bg-white px-6 py-3 rounded-full shadow-md border border-gray-200">
                            <p className="text-sm text-gray-600">
                              Status changed to{" "}
                              <span className={`font-semibold mx-1 ${getStatusBadgeStyle(item.to_status)}`}>
                                {item.to_status.replace("-", " ").replace(/\b\w/g, (l: string) => l.toUpperCase())}
                              </span>{" "}
                              on {format(new Date(item.updated_at), "dd MMM yyyy, HH:mm")}
                            </p>
                          </div>
                        </div>
                      );
                    }

                    const isGM = item.user?.type?.code === "GRANT_MAKER";
                    const name = item.user?.first_name
                      ? `${item.user.first_name} ${item.user.last_name}`
                      : item.user?.email;
                    return (
                      <div key={item.id} className={`flex gap-4 ${isGM ? "justify-end" : "justify-start"}`}>
                        {!isGM && (
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-50 rounded-full flex items-center justify-center flex-shrink-0">
                            <span className="text-blue-600 font-bold text-sm">
                              {name?.charAt(0)?.toUpperCase() || 'U'}
                            </span>
                          </div>
                        )}
                        <div className={`max-w-lg ${isGM ? 'order-1' : ''}`}>
                          <div className={`rounded-2xl px-6 py-4 shadow-lg ${
                              isGM
                                ? "bg-gradient-to-br from-teal-600 to-teal-700 text-white rounded-br-md"
                                : "bg-white text-gray-800 border border-gray-200 rounded-bl-md"
                            }`}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <p className={`text-sm font-bold ${isGM ? 'text-teal-100' : 'text-gray-700'}`}>
                                {name}
                              </p>
                              <p className={`text-xs ${isGM ? 'text-teal-200' : 'text-gray-500'}`}>
                                {formatDistanceToNow(new Date(item.updated_at), { addSuffix: true })}
                              </p>
                            </div>
                            <p className="whitespace-pre-wrap leading-relaxed">{item.update_text}</p>
                            {item.attachments?.map((url: string, i: number) => (
                              <a
                                key={i}
                                href={url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className={`flex items-center gap-2 text-sm mt-3 p-2 rounded-lg transition-all ${
                                  isGM
                                    ? 'bg-white/20 text-teal-100 hover:bg-white/30'
                                    : 'bg-gray-50 text-teal-600 hover:bg-gray-100'
                                }`}
                              >
                                <Paperclip className="w-4 h-4" />
                                <span className="truncate">{new URL(url).pathname.split("/").pop()?.split("?")[0]}</span>
                              </a>
                            ))}
                          </div>
                        </div>
                        {isGM && (
                          <div className="w-10 h-10 bg-gradient-to-br from-teal-100 to-teal-50 rounded-full flex items-center justify-center flex-shrink-0 order-2">
                            <span className="text-teal-600 font-bold text-sm">
                              {name?.charAt(0)?.toUpperCase() || 'G'}
                            </span>
                          </div>
                        )}
                      </div>
                    );
                  })
                ) : (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-50 rounded-full flex items-center justify-center mb-6">
                      <MessageSquare className="w-10 h-10 text-gray-400" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-700 mb-2">No messages yet</h3>
                    <p className="text-gray-500 text-center max-w-sm">
                      Start the conversation by sending a message below. Your message will be delivered instantly.
                    </p>
                  </div>
                )}
                {newMsgAlert && showScrollButton && (
                  <div className="absolute bottom-8 right-8">
                    <Button
                      onClick={scrollToBottom}
                      size="sm"
                      className="professional-button-primary rounded-full px-6 py-3 shadow-xl animate-bounce"
                    >
                      <ArrowDown className="w-4 h-4 mr-2" /> New Messages
                    </Button>
                  </div>
                )}
              </div>

              {/* Enhanced Chat Input Footer */}
              <footer className="p-6 bg-white border-t border-gray-200 rounded-br-2xl">
                {selected.status === "resolved" ? (
                  <div className="text-center py-6">
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full mb-3">
                      <Lock className="w-6 h-6 text-gray-400" />
                    </div>
                    <p className="text-gray-600 font-medium">This ticket is resolved</p>
                    <p className="text-sm text-gray-500">Reopen the ticket to continue the conversation</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-start gap-4">
                      <input
                        ref={fileInputRef}
                        type="file"
                        onChange={(e) => setFile(e.target.files?.[0] || null)}
                        className="hidden"
                        id="file-input"
                      />
                      <Button
                        onClick={() => fileInputRef.current?.click()}
                        className="professional-button-secondary"
                        size="icon"
                        title="Attach file"
                      >
                        <Paperclip className="w-5 h-5" />
                      </Button>
                      <div className="flex-1">
                        <textarea
                          value={newMessage}
                          onChange={(e) => setNewMessage(e.target.value)}
                          placeholder="Type your message here... Press Ctrl+Enter to send"
                          className="professional-input w-full resize-none text-base"
                          rows={3}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && e.ctrlKey && !isSending && (newMessage.trim() || file)) {
                              handleSend();
                            }
                          }}
                        />
                        {file && (
                          <div className="mt-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <Paperclip className="w-4 h-4 text-blue-600" />
                              </div>
                              <div>
                                <p className="text-sm font-medium text-gray-800">{file.name}</p>
                                <p className="text-xs text-gray-500">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                              </div>
                            </div>
                            <button
                              onClick={() => setFile(null)}
                              className="text-red-500 hover:text-red-700 p-1 rounded-lg hover:bg-red-50 transition-all"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        )}
                      </div>
                      <Button
                        disabled={isSending || (!newMessage.trim() && !file)}
                        onClick={handleSend}
                        className="professional-button-primary px-8 py-3 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSending ? (
                          <>
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                            Sending...
                          </>
                        ) : (
                          <>
                            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                            </svg>
                            Send Message
                          </>
                        )}
                      </Button>
                    </div>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>💡 Tip: Use Ctrl+Enter to send quickly</span>
                      <span>Powered by Professional Support</span>
                    </div>
                  </div>
                )}
              </footer>
            </>
          ) : (
            <div className="flex flex-1 items-center justify-center flex-col gap-6 text-center p-12">
              <div className="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-50 rounded-full flex items-center justify-center mb-4">
                <MessageSquare className="w-12 h-12 text-gray-400" />
              </div>
              <div className="space-y-3">
                <h3 className="text-2xl font-bold text-gray-700">Select a Conversation</h3>
                <p className="text-gray-500 max-w-md leading-relaxed">
                  Choose a support ticket from the sidebar to start or continue a conversation.
                  You can view chat history, send messages, and manage ticket status.
                </p>
              </div>
              <div className="flex items-center space-x-4 text-sm text-gray-400">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>Real-time messaging</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <span>File attachments</span>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
  </SupportTabsLayout>
);
}