"use client";

import { useEffect, useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { format, formatDistanceToNow } from "date-fns";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import {
  Search,
  Filter,
  Download,
  AlertCircle,
  Ticket,
  Clock,
  MessageSquare,
  CheckCircle,
  X,
} from "lucide-react";
import { fetchAllTickets } from "@/services/supportTicket.service";

const STATUSES = ["open", "under-review", "resolved"];
const CATEGORIES = ["financial", "reporting", "documents", "technical support", "applications", "account management"];

export default function GrantMakerTicketsPage() {
  const router = useRouter();
  const [tickets, setTickets] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState([]);
  const [categoryFilter, setCategoryFilter] = useState([]);
  const [grantFilter, setGrantFilter] = useState([]);

  useEffect(() => {
    const loadTickets = async () => {
      try {
        const data = await fetchAllTickets();
        const formatted = data.map((t) => ({
          id: t.id,
          title: t.title || `Ticket #${t.id}`,
          grant_name: t.grant_name || "Unknown Grant",
          category: t.category,
          status: t.status?.toLowerCase().replace(/\s+/g, "-"),
          created_at: t.created_at,
          updated_at: t.updated_at,
        }));
        setTickets(formatted);
      } catch (error) {
        console.error("Failed to fetch tickets:", error);
      }
    };
    loadTickets();
  }, []);

  const normalize = (text) =>
    text?.toLowerCase().replace(/[^a-z0-9 ]/gi, "") || "";

  const filteredTickets = useMemo(() => {
    const search = normalize(searchTerm);
    const tokens = search.split(" ").filter(Boolean);

    return tickets.filter((t) => {
      const target = normalize(
        `${t.title} ${t.grant_name} ${t.status} ${t.category}`
      );
      const matchesSearch =
        search.length === 0 || tokens.every((token) => target.includes(token));
      const matchesStatus =
        statusFilter.length === 0 || statusFilter.includes(t.status);
      const matchesCategory =
        categoryFilter.length === 0 || categoryFilter.includes(t.category);
      const matchesGrant =
        grantFilter.length === 0 || grantFilter.includes(t.grant_name);

      return matchesSearch && matchesStatus && matchesCategory && matchesGrant;
    });
  }, [tickets, searchTerm, statusFilter, categoryFilter, grantFilter]);

  const getStatusBadgeStyle = (status) => {
    switch (status) {
      case "open":
        return "professional-badge-open";
      case "under-review":
        return "professional-badge-review";
      case "resolved":
        return "professional-badge-resolved";
      default:
        return "bg-gray-500 text-white px-3 py-1 rounded-full text-sm font-semibold";
    }
  };

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter([]);
    setCategoryFilter([]);
    setGrantFilter([]);
  };

  const quickStats = useMemo(
    () => [
      {
        label: "Total Tickets",
        value: filteredTickets.length,
        icon: <Ticket className="w-6 h-6 text-teal-600" />,
        bg: "bg-gradient-to-br from-teal-50 to-teal-100",
        border: "border-teal-200",
        color: "text-teal-700"
      },
      {
        label: "Open",
        value: filteredTickets.filter((t) => t.status === "open").length,
        icon: <MessageSquare className="w-6 h-6 text-blue-600" />,
        bg: "bg-gradient-to-br from-blue-50 to-blue-100",
        border: "border-blue-200",
        color: "text-blue-700"
      },
      {
        label: "Under Review",
        value: filteredTickets.filter((t) => t.status === "under-review").length,
        icon: <Clock className="w-6 h-6 text-amber-600" />,
        bg: "bg-gradient-to-br from-amber-50 to-amber-100",
        border: "border-amber-200",
        color: "text-amber-700"
      },
      {
        label: "Resolved",
        value: filteredTickets.filter((t) => t.status === "resolved").length,
        icon: <CheckCircle className="w-6 h-6 text-green-600" />,
        bg: "bg-gradient-to-br from-green-50 to-green-100",
        border: "border-green-200",
        color: "text-green-700"
      },
    ],
    [filteredTickets]
  );

  const allGrants = useMemo(() => {
    const names = tickets.map((t) => t.grant_name);
    return [...new Set(names)].sort();
  }, [tickets]);

  return (
    <div className="w-full px-6 py-8">
      {/* Enhanced Professional Header */}
      <div className="professional-card mb-8 p-8">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
          <div className="professional-accent-border pl-6">
            <CardTitle className="text-3xl font-bold text-gray-900 mb-2">
              <span className="professional-text-gradient">Support Tickets</span>
            </CardTitle>
            <CardDescription className="text-gray-600 text-base">
              Manage and track support requests with our professional ticketing system
            </CardDescription>
          </div>
          <div className="flex gap-4">
            <Button
              size="lg"
              className="professional-button-secondary"
            >
              <Download className="h-5 w-5 mr-2" />
              Export Data
            </Button>
          </div>
        </div>
      </div>
      {/* Enhanced Professional Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {quickStats.map((stat) => (
          <div
            key={stat.label}
            className={`professional-card ${stat.bg} ${stat.border} group cursor-default overflow-hidden`}
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 rounded-xl bg-white/80 shadow-sm">
                  {stat.icon}
                </div>
                <div className="text-right">
                  <p className={`text-3xl font-bold ${stat.color} mb-1`}>{stat.value}</p>
                  <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                </div>
              </div>
              <div className="w-full bg-white/50 rounded-full h-1">
                <div
                  className="bg-gradient-to-r from-teal-500 to-teal-600 h-1 rounded-full transition-all duration-1000 ease-out"
                  style={{ width: `${Math.min((stat.value / Math.max(...quickStats.map(s => s.value))) * 100, 100)}%` }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Enhanced Professional Search and Filters */}
      <div className="professional-card p-6 mb-8">
        <div className="flex flex-col lg:flex-row justify-between items-center gap-6">
          <div className="relative w-full lg:flex-grow">
            <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search tickets by ID, title, or grant name..."
              className="professional-input w-full pl-12 pr-4"
            />
          </div>
          <div className="flex items-center gap-4 w-full lg:w-auto flex-wrap">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="sm"
                  className="professional-button-secondary text-sm"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Status Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56 shadow-xl border-gray-200 bg-white rounded-xl">
                <DropdownMenuLabel className="text-gray-800 font-semibold px-4 py-3">Filter by Status</DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-gray-200" />
                {STATUSES.map((status) => (
                  <DropdownMenuCheckboxItem
                    key={status}
                    checked={statusFilter.includes(status)}
                    onCheckedChange={(checked) => {
                      setStatusFilter((prev) =>
                        checked ? [...prev, status] : prev.filter((s) => s !== status)
                      );
                    }}
                    className="capitalize text-gray-700 hover:!bg-teal-50 focus:bg-teal-100 px-4 py-2"
                  >
                    {status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="sm"
                  className="professional-button-secondary text-sm"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Category Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56 shadow-xl border-gray-200 bg-white rounded-xl">
                <DropdownMenuLabel className="text-gray-800 font-semibold px-4 py-3">Filter by Category</DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-gray-200" />
                {CATEGORIES.map((cat) => (
                  <DropdownMenuCheckboxItem
                    key={cat}
                    checked={categoryFilter.includes(cat)}
                    onCheckedChange={(checked) => {
                      setCategoryFilter((prev) =>
                        checked ? [...prev, cat] : prev.filter((c) => c !== cat)
                      );
                    }}
                    className="capitalize text-gray-700 hover:!bg-teal-50 focus:bg-teal-100 px-4 py-2"
                  >
                    {cat}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="sm"
                  className="professional-button-secondary text-sm"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Grant Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-72 max-h-64 overflow-y-auto shadow-xl border-gray-200 bg-white rounded-xl">
                <DropdownMenuLabel className="text-gray-800 font-semibold px-4 py-3">Filter by Grant</DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-gray-200" />
                {allGrants.map((grant) => (
                  <DropdownMenuCheckboxItem
                    key={grant}
                    checked={grantFilter.includes(grant)}
                    onCheckedChange={(checked) => {
                      setGrantFilter((prev) =>
                        checked ? [...prev, grant] : prev.filter((g) => g !== grant)
                      );
                    }}
                    className="text-gray-700 hover:!bg-teal-50 focus:bg-teal-100 px-4 py-2"
                  >
                    {grant.length > 32 ? `${grant.slice(0, 30)}...` : grant}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            <Button
              size="sm"
              variant="ghost"
              onClick={clearFilters}
              className="text-gray-600 hover:bg-gray-100 px-4 py-2 rounded-lg transition-all"
            >
              Clear All
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Filter Tags */}
      {(statusFilter.length > 0 || categoryFilter.length > 0 || grantFilter.length > 0) && (
        <div className="flex flex-wrap gap-3 mb-8">
          {statusFilter.map((status) => (
            <span
              key={status}
              className="flex items-center gap-2 text-sm bg-gradient-to-r from-teal-100 to-teal-50 text-teal-800 px-4 py-2 border border-teal-200 hover:from-teal-200 hover:to-teal-100 transition-all rounded-full shadow-sm font-medium"
            >
              Status: {status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
              <X
                className="w-4 h-4 cursor-pointer text-teal-600 hover:text-teal-800 transition-colors"
                onClick={() => setStatusFilter((prev) => prev.filter((s) => s !== status))}
              />
            </span>
          ))}
          {categoryFilter.map((cat) => (
            <span
              key={cat}
              className="flex items-center gap-2 text-sm bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 px-4 py-2 border border-blue-200 hover:from-blue-200 hover:to-blue-100 transition-all rounded-full shadow-sm font-medium"
            >
              Category: {cat}
              <X
                className="w-4 h-4 cursor-pointer text-blue-600 hover:text-blue-800 transition-colors"
                onClick={() => setCategoryFilter((prev) => prev.filter((c) => c !== cat))}
              />
            </span>
          ))}
          {grantFilter.map((grant) => (
            <span
              key={grant}
              className="flex items-center gap-2 text-sm bg-gradient-to-r from-purple-100 to-purple-50 text-purple-800 px-4 py-2 border border-purple-200 hover:from-purple-200 hover:to-purple-100 transition-all rounded-full shadow-sm font-medium"
            >
              Grant: {grant.length > 24 ? `${grant.slice(0, 22)}...` : grant}
              <X
                className="w-4 h-4 cursor-pointer text-purple-600 hover:text-purple-800 transition-colors"
                onClick={() => setGrantFilter((prev) => prev.filter((g) => g !== grant))}
              />
            </span>
          ))}
        </div>
      )}

      {/* Enhanced Professional Table */}
      <div className="professional-card overflow-hidden">
        {filteredTickets.length === 0 ? (
          <div className="text-center py-20">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-50 rounded-full mb-4">
              <Ticket className="w-8 h-8 text-gray-400" />
            </div>
            <p className="text-gray-700 font-semibold text-lg mb-2">No tickets found</p>
            <p className="text-gray-500 mb-4">Try adjusting your filters or check back later</p>
          </div>
        ) : (
          <div>
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                  <th className="p-6 text-left font-bold text-gray-800 text-sm uppercase tracking-wide">Ticket ID</th>
                  <th className="p-6 text-left font-bold text-gray-800 text-sm uppercase tracking-wide">Title</th>
                  <th className="p-6 text-left font-bold text-gray-800 text-sm uppercase tracking-wide">Grant</th>
                  <th className="p-6 text-left font-bold text-gray-800 text-sm uppercase tracking-wide">Category</th>
                  <th className="p-6 text-left font-bold text-gray-800 text-sm uppercase tracking-wide">Status</th>
                  <th className="p-6 text-left font-bold text-gray-800 text-sm uppercase tracking-wide">Created</th>
                  <th className="p-6 text-left font-bold text-gray-800 text-sm uppercase tracking-wide">Updated</th>
                  <th className="p-6 text-center font-bold text-gray-800 text-sm uppercase tracking-wide">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100">
                {filteredTickets.map((ticket) => (
                  <tr
                    key={ticket.id}
                    className="hover:bg-gradient-to-r hover:from-teal-50/30 hover:to-blue-50/30 transition-all duration-300 cursor-pointer group"
                    onClick={() =>
                      router.push(
                        `/grantmaker/support/my-tickets/support-ticket/support-ticket-detail?id=${ticket.id}`
                      )
                    }
                  >
                    <td className="p-6">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gradient-to-br from-teal-100 to-teal-50 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-teal-700 font-bold text-sm">#{ticket.id}</span>
                        </div>
                      </div>
                    </td>
                    <td className="p-6">
                      <div className="max-w-xs">
                        <p className="text-gray-900 font-semibold text-base mb-1 truncate group-hover:text-teal-700 transition-colors">
                          {ticket.title}
                        </p>
                        <p className="text-gray-500 text-sm">Support Request</p>
                      </div>
                    </td>
                    <td className="p-6">
                      <div className="max-w-xs">
                        <p className="text-gray-700 font-medium truncate">{ticket.grant_name}</p>
                        <p className="text-gray-500 text-sm">Grant Project</p>
                      </div>
                    </td>
                    <td className="p-6">
                      <div className="max-w-xs">
                        <p className="text-gray-700 font-medium capitalize">{ticket.category}</p>
                        <p className="text-gray-500 text-sm">Category</p>
                      </div>
                    </td>
                    <td className="p-6">
                      <span className={`inline-flex items-center ${getStatusBadgeStyle(ticket.status)}`}>
                        {ticket.status?.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                      </span>
                    </td>
                    <td className="p-6">
                      <div>
                        <p className="text-gray-700 font-medium">{format(new Date(ticket.created_at), "dd MMM yyyy")}</p>
                        <p className="text-gray-500 text-sm">Created</p>
                      </div>
                    </td>
                    <td className="p-6">
                      <div>
                        <p className="text-gray-700 font-medium">
                          {formatDistanceToNow(new Date(ticket.updated_at), { addSuffix: true })}
                        </p>
                        <p className="text-gray-500 text-sm">Last activity</p>
                      </div>
                    </td>
                    <td className="p-6 text-center">
                      <Button
                        size="sm"
                        className="professional-button-primary text-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push(
                            `/grantmaker/support/my-tickets/support-ticket/support-ticket-detail?id=${ticket.id}`
                          );
                        }}
                      >
                        View Details
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}