"use client";

import { useEffect, useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { format, formatDistanceToNow } from "date-fns";
import SupportTabsLayout from "../../SupportTabsLayout";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import {
  Search,
  Filter,
  Download,
  AlertCircle,
  Ticket,
  Clock,
  MessageSquare,
  CheckCircle,
  X,
} from "lucide-react";
import { fetchAllTickets } from "@/services/supportTicket.service";

const STATUSES = ["open", "under-review", "resolved"];
const CATEGORIES = ["financial", "reporting", "documents", "technical support", "applications", "account management"];

export default function GrantMakerTicketsPage() {
  const router = useRouter();
  const [tickets, setTickets] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState([]);
  const [categoryFilter, setCategoryFilter] = useState([]);
  const [grantFilter, setGrantFilter] = useState([]);

  useEffect(() => {
    const loadTickets = async () => {
      try {
        const data = await fetchAllTickets();
        const formatted = data.map((t) => ({
          id: t.id,
          title: t.title || `Ticket #${t.id}`,
          grant_name: t.grant_name || "Unknown Grant",
          category: t.category,
          status: t.status?.toLowerCase().replace(/\s+/g, "-"),
          created_at: t.created_at,
          updated_at: t.updated_at,
        }));
        setTickets(formatted);
      } catch (error) {
        console.error("Failed to fetch tickets:", error);
      }
    };
    loadTickets();
  }, []);

  const normalize = (text) =>
    text?.toLowerCase().replace(/[^a-z0-9 ]/gi, "") || "";

  const filteredTickets = useMemo(() => {
    const search = normalize(searchTerm);
    const tokens = search.split(" ").filter(Boolean);

    return tickets.filter((t) => {
      const target = normalize(
        `${t.title} ${t.grant_name} ${t.status} ${t.category}`
      );
      const matchesSearch =
        search.length === 0 || tokens.every((token) => target.includes(token));
      const matchesStatus =
        statusFilter.length === 0 || statusFilter.includes(t.status);
      const matchesCategory =
        categoryFilter.length === 0 || categoryFilter.includes(t.category);
      const matchesGrant =
        grantFilter.length === 0 || grantFilter.includes(t.grant_name);

      return matchesSearch && matchesStatus && matchesCategory && matchesGrant;
    });
  }, [tickets, searchTerm, statusFilter, categoryFilter, grantFilter]);

  const getStatusBadgeStyle = (status) => {
    switch (status) {
      case "open":
        return "bg-teal-100 text-teal-800 border-teal-200";
      case "under-review":
        return "bg-amber-100 text-amber-800 border-amber-200";
      case "resolved":
        return "bg-teal-100 text-teal-800 border-teal-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter([]);
    setCategoryFilter([]);
    setGrantFilter([]);
  };

  const quickStats = useMemo(
    () => [
      {
        label: "Total Tickets",
        value: filteredTickets.length,
        icon: <Ticket className="w-6 h-6 text-teal-400 group-hover:scale-110 transition-transform duration-300" />,
        bg: "bg-gradient-to-br from-teal-50/50 to-white border-t-2 border-teal-200",
      },
      {
        label: "Open",
        value: filteredTickets.filter((t) => t.status === "open").length,
        icon: <MessageSquare className="w-6 h-6 text-teal-400 group-hover:scale-110 transition-transform duration-300" />,
        bg: "bg-gradient-to-br from-teal-50/50 to-white border-t-2 border-teal-200",
      },
      {
        label: "Under Review",
        value: filteredTickets.filter((t) => t.status === "under-review").length,
        icon: <Clock className="w-6 h-6 text-teal-400 group-hover:scale-110 transition-transform duration-300" />,
        bg: "bg-gradient-to-br from-teal-50/50 to-white border-t-2 border-teal-200",
      },
      {
        label: "Resolved",
        value: filteredTickets.filter((t) => t.status === "resolved").length,
        icon: <CheckCircle className="w-6 h-6 text-teal-400 group-hover:scale-110 transition-transform duration-300" />,
        bg: "bg-gradient-to-br from-teal-50/50 to-white border-t-2 border-teal-200",
      },
    ],
    [filteredTickets]
  );

  const allGrants = useMemo(() => {
    const names = tickets.map((t) => t.grant_name);
    return [...new Set(names)].sort();
  }, [tickets]);

  return (
    <SupportTabsLayout>
      <div className="w-full px-4 py-6">
        <Card className="bg-white shadow-sm border-none">
          <CardHeader className="bg-gradient-to-r from-teal-50 to-white border-b border-teal-100 px-6 py-5">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div className="border-l-4 border-teal-500 pl-4">
                <CardTitle className="text-2xl font-semibold text-gray-900">
                  Support Tickets
                </CardTitle>
                <CardDescription className="text-gray-500 text-sm mt-1">
                  Seamlessly manage and track your support tickets
                </CardDescription>
              </div>
              <Button
                size="sm"
                className="bg-teal-600 hover:bg-teal-500 text-white transition-all duration-200 hover:shadow-md rounded-lg"
              >
                <Download className="h-4 w-4 mr-2" />
                Export Tickets
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              {quickStats.map((stat) => (
                <Card
                  key={stat.label}
                  className={`${stat.bg} rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group cursor-default`}
                >
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      {stat.icon}
                      {stat.label}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-3xl font-bold text-gray-800">{stat.value}</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Filters and Search */}
            <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
              <div className="relative w-full md:w-80">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search tickets or grants..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 outline-none transition-all text-sm placeholder:text-gray-400 rounded-lg"
                />
              </div>
              <div className="flex items-center gap-2 w-full md:w-auto">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="sm"
                      className="bg-teal-600 hover:bg-teal-500 text-white transition-all duration-200 rounded-lg"
                    >
                      <Filter className="h-4 w-4 mr-2" />
                      Status
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-48 shadow-lg border-gray-200">
                    <DropdownMenuLabel className="text-gray-700 font-medium">Filter by Status</DropdownMenuLabel>
                    <DropdownMenuSeparator className="bg-gray-200" />
                    {STATUSES.map((status) => (
                      <DropdownMenuCheckboxItem
                        key={status}
                        checked={statusFilter.includes(status)}
                        onCheckedChange={(checked) => {
                          setStatusFilter((prev) =>
                            checked ? [...prev, status] : prev.filter((s) => s !== status)
                          );
                        }}
                        className="capitalize text-gray-700 hover:bg-teal-50"
                      >
                        {status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="sm"
                      className="bg-teal-600 hover:bg-teal-500 text-white transition-all duration-200 rounded-lg"
                    >
                      <Filter className="h-4 w-4 mr-2" />
                      Category
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-48 shadow-lg border-gray-200">
                    <DropdownMenuLabel className="text-gray-700 font-medium">Filter by Category</DropdownMenuLabel>
                    <DropdownMenuSeparator className="bg-gray-200" />
                    {CATEGORIES.map((cat) => (
                      <DropdownMenuCheckboxItem
                        key={cat}
                        checked={categoryFilter.includes(cat)}
                        onCheckedChange={(checked) => {
                          setCategoryFilter((prev) =>
                            checked ? [...prev, cat] : prev.filter((c) => c !== cat)
                          );
                        }}
                        className="capitalize text-gray-700 hover:bg-teal-50"
                      >
                        {cat}
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="sm"
                      className="bg-teal-600 hover:bg-teal-500 text-white transition-all duration-200 rounded-lg"
                    >
                      <Filter className="h-4 w-4 mr-2" />
                      Grant
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-64 max-h-60 overflow-y-auto shadow-lg border-gray-200">
                    <DropdownMenuLabel className="text-gray-700 font-medium">Filter by Grant</DropdownMenuLabel>
                    <DropdownMenuSeparator className="bg-gray-200" />
                    {allGrants.map((grant) => (
                      <DropdownMenuCheckboxItem
                        key={grant}
                        checked={grantFilter.includes(grant)}
                        onCheckedChange={(checked) => {
                          setGrantFilter((prev) =>
                            checked ? [...prev, grant] : prev.filter((g) => g !== grant)
                          );
                        }}
                        className="text-gray-700 hover:bg-teal-50"
                      >
                        {grant.length > 28 ? `${grant.slice(0, 26)}...` : grant}
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                <Button
                  size="sm"
                  onClick={clearFilters}
                  className="bg-teal-600 hover:bg-teal-500 text-white transition-all duration-200 rounded-lg"
                >
                  Clear Filters
                </Button>
              </div>
            </div>

            {/* Filter Tags */}
            <div className="flex flex-wrap gap-2 mb-6">
              {statusFilter.map((status) => (
                <span
                  key={status}
                  className="flex items-center gap-1 text-xs bg-teal-100 text-teal-800 px-2 py-1 border border-teal-200 hover:bg-teal-200 transition-all rounded-lg"
                >
                  {status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                  <X
                    className="w-3 h-3 cursor-pointer text-gray-500 hover:text-teal-800"
                    onClick={() => setStatusFilter((prev) => prev.filter((s) => s !== status))}
                  />
                </span>
              ))}
              {categoryFilter.map((cat) => (
                <span
                  key={cat}
                  className="flex items-center gap-1 text-xs bg-teal-100 text-teal-800 px-2 py-1 border border-teal-200 hover:bg-teal-200 transition-all rounded-lg"
                >
                  {cat}
                  <X
                    className="w-3 h-3 cursor-pointer text-gray-500 hover:text-teal-800"
                    onClick={() => setCategoryFilter((prev) => prev.filter((c) => c !== cat))}
                  />
                </span>
              ))}
              {grantFilter.map((grant) => (
                <span
                  key={grant}
                  className="flex items-center gap-1 text-xs bg-teal-100 text-teal-800 px-2 py-1 border border-teal-200 hover:bg-teal-200 transition-all rounded-lg"
                >
                  {grant.length > 28 ? `${grant.slice(0, 26)}...` : grant}
                  <X
                    className="w-3 h-3 cursor-pointer text-gray-500 hover:text-teal-800"
                    onClick={() => setGrantFilter((prev) => prev.filter((g) => g !== grant))}
                  />
                </span>
              ))}
            </div>

            {/* Table View */}
            <div className="w-full">
              <table className="w-full border-collapse text-sm">
                <thead>
                  <tr className="bg-gray-50 text-gray-600">
                    <th className="p-4 text-left font-medium">Title</th>
                    <th className="p-4 text-left font-medium">Grant</th>
                    <th className="p-4 text-left font-medium">Category</th>
                    <th className="p-4 text-left font-medium">Status</th>
                    <th className="p-4 text-left font-medium">Created</th>
                    <th className="p-4 text-left font-medium">Updated</th>
                    <th className="p-4 text-left font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredTickets.length > 0 ? (
                    filteredTickets.map((ticket, index) => (
                      <tr
                        key={ticket.id}
                        className="hover:bg-teal-50 transition-all duration-200 cursor-pointer"
                        style={{ animation: `fadeIn 0.3s ease-in ${index * 100}ms` }}
                        onClick={() =>
                          router.push(
                            `/grantmaker/support/my-tickets/support-ticket/support-ticket-detail?id=${ticket.id}`
                          )
                        }
                      >
                        <td className="p-4 border-b border-teal-100">
                          <div className="font-medium text-gray-800">{ticket.title}</div>
                          <div className="text-xs text-gray-500">
                            Created: {format(new Date(ticket.created_at), "dd MMM yyyy")}
                          </div>
                        </td>
                        <td className="p-4 border-b border-teal-100 text-gray-700">
                          {ticket.grant_name}
                        </td>
                        <td className="p-4 border-b border-teal-100 text-gray-700 capitalize">
                          {ticket.category}
                        </td>
                        <td className="p-4 border-b border-teal-100">
                          <span
                            className={`inline-flex items-center px-2 py-1 text-xs font-medium border rounded-lg ${getStatusBadgeStyle(
                              ticket.status
                            )}`}
                          >
                            {ticket.status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                          </span>
                        </td>
                        <td className="p-4 border-b border-teal-100 text-gray-600 text-xs">
                          {format(new Date(ticket.created_at), "dd MMM yyyy")}
                        </td>
                        <td className="p-4 border-b border-teal-100 text-gray-600 text-xs">
                          {formatDistanceToNow(new Date(ticket.updated_at), {
                            addSuffix: true,
                          })}
                        </td>
                        <td className="p-4 border-b border-teal-100">
                          <Button
                            size="sm"
                            className="bg-teal-600 hover:bg-teal-500 text-white transition-all duration-200 rounded-lg"
                            onClick={(e) => {
                              e.stopPropagation();
                              router.push(
                                `/grantmaker/support/my-tickets/support-ticket/support-ticket-detail?id=${ticket.id}`
                              );
                            }}
                          >
                            View
                          </Button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={7} className="p-6 text-center text-gray-500">
                        <AlertCircle className="w-6 h-6 mb-2 mx-auto text-gray-400" />
                        <p className="text-sm font-medium">No tickets found matching your filters.</p>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </SupportTabsLayout>
  );
}