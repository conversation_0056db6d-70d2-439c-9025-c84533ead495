"use client";

import { useEffect, useRef, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import SupportTabsLayout from "../../../SupportTabsLayout";
import { Button } from "@/components/ui/button";
import { ArrowDown, Paperclip, AlertCircle } from "lucide-react";
import { format, formatDistanceToNow } from "date-fns";
import {
  getTicketDetails,
  getTicketUpdates,
  sendTicketUpdate,
  updateTicketStatus,
} from "@/services/supportTicket.service";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function GrantmakerSupportTicketDetailClient() {
  const router = useRouter();
  const id = Number(useSearchParams().get("id"));

  const chatRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateIdRef = useRef<number | null>(null);
  const lastStatusCountRef = useRef<number | null>(null);
  const lastSentMessageIdRef = useRef<number | null>(null);

  const [ticket, setTicket] = useState<any>(null);
  const [timeline, setTimeline] = useState<any[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [isSending, setIsSending] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [newMessageArrived, setNewMessageArrived] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);

  const isClosed = ticket?.status === "resolved";

  const scrollToBottom = () => {
    setTimeout(() => {
      chatRef.current?.scrollTo({ top: chatRef.current.scrollHeight, behavior: "smooth" });
      setNewMessageArrived(false);
    }, 100);
  };

  const buildTimeline = (ticketDetail: any, updates: any[]) => {
    const timelineItems: any[] = [];

    if (ticketDetail?.description) {
      timelineItems.push({
        type: "message",
        id: "desc",
        update_text: ticketDetail.description,
        updated_at: ticketDetail.created_at,
        user: {
          type: { code: "GRANTEE" },
          first_name: ticketDetail.created_by_first_name,
          last_name: ticketDetail.created_by_last_name,
          email: ticketDetail.created_by_email,
        },
        attachments: ticketDetail.attachments || [],
      });
    }

    ticketDetail?.status_history?.forEach((s: any, i: number) => {
      timelineItems.push({
        type: "status",
        id: `status-${s.id}-${i}`,
        updated_at: s.changed_at,
        changed_by: s.changed_by,
        to_status: s.to_status,
      });
    });

    updates.forEach((msg: any) => {
      timelineItems.push({ type: "message", ...msg });
    });

    timelineItems.sort((a, b) => new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime());
    setTimeline(timelineItems);
  };

  useEffect(() => {
    if (!id) return;
    (async () => {
      try {
        const [ticketDetail, updates] = await Promise.all([
          getTicketDetails(String(id)),
          getTicketUpdates(id),
        ]);
        setTicket(ticketDetail);
        buildTimeline(ticketDetail, updates);
        lastUpdateIdRef.current = updates.at(-1)?.id || null;
        lastStatusCountRef.current = ticketDetail.status_history?.length || 0;
        scrollToBottom();
      } catch {
        toast.error("Ticket not found or already cancelled.");
        router.replace("/grantmaker/support/my-tickets");
      }
    })();
  }, [id, router]);

  useEffect(() => {
    pollingRef.current = setInterval(async () => {
      try {
        const [latestTicket, updates] = await Promise.all([
          getTicketDetails(String(id)),
          getTicketUpdates(id),
        ]);

        const newUpdateId = updates.at(-1)?.id || null;
        const newStatusCount = latestTicket.status_history?.length || 0;

        const hasNewUpdate = newUpdateId !== lastUpdateIdRef.current;
        const hasNewStatus = newStatusCount !== lastStatusCountRef.current;

        if (hasNewUpdate || hasNewStatus) {
          lastUpdateIdRef.current = newUpdateId;
          lastStatusCountRef.current = newStatusCount;
          setTicket(latestTicket);
          buildTimeline(latestTicket, updates);

          const isAtBottom =
            chatRef.current &&
            chatRef.current.scrollHeight - chatRef.current.scrollTop - chatRef.current.clientHeight < 100;

          if (hasNewUpdate) {
            const latestMessage = updates.at(-1);
            const latestMessageId = latestMessage?.id;
            const latestMessageUserEmail = latestMessage?.user?.email;

            const isOwnMessage =
              latestMessageId === lastSentMessageIdRef.current ||
              latestMessageUserEmail === ticket?.created_by_email;

            if (!isOwnMessage) {
              isAtBottom ? scrollToBottom() : setNewMessageArrived(true);
              toast.success("New message received");
            }

            lastSentMessageIdRef.current = null;
          }
        }
      } catch {}
    }, 2000);

    return () => clearInterval(pollingRef.current!);
  }, [ticket?.id, id]);

  useEffect(() => {
    const el = chatRef.current;
    if (!el) return;
    const onScroll = () => {
      const isAtBottom = el.scrollHeight - el.scrollTop - el.clientHeight < 100;
      setShowScrollButton(!isAtBottom);
      if (isAtBottom) setNewMessageArrived(false);
    };
    el.addEventListener("scroll", onScroll);
    return () => el.removeEventListener("scroll", onScroll);
  }, []);

  const handleSendMessage = async () => {
    if ((!newMessage.trim() && !file) || isClosed) return;

    try {
      setIsSending(true);
      const newUpdate = await sendTicketUpdate(id, newMessage, file);
      lastSentMessageIdRef.current = newUpdate.id;
      buildTimeline(ticket, [...timeline.filter(t => t.type === "message" && t.id !== "desc"), newUpdate]);
      setNewMessage("");
      setFile(null);
      if (fileInputRef.current) fileInputRef.current.value = "";
      scrollToBottom();
    } catch {
      toast.error("Failed to send message.");
    } finally {
      setIsSending(false);
    }
  };

  const handleStatusChange = async () => {
    try {
      setIsUpdatingStatus(true);
      const newStatus = ticket.status === "resolved" ? "under review" : "resolved";
      const updated = await updateTicketStatus(id, newStatus);
      setTicket((prev: any) => ({ ...prev, status: updated.status }));
    } catch {
      toast.error("Failed to update status.");
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const getStatusBadgeStyle = (status: string) => {
    switch (status?.toLowerCase()) {
      case "open":
        return "bg-teal-100 text-teal-800 border-teal-50 border-opacity-50";
      case "under review":
        return "bg-amber-100 text-amber-800 border-amber-50 border-opacity-50";
      case "resolved":
        return "bg-teal-100 text-teal-800 border-teal-50 border-opacity-50";
      default:
        return "bg-gray-100 text-gray-700 border-gray-50 border-opacity-50";
    }
  };

  const renderStatusChange = (item: any, idx: number) => {
    const date = format(new Date(item.updated_at), "dd MMM yyyy, HH:mm");
    return (
      <div
        key={item.id}
        className="text-center text-sm text-gray-500 my-4"
        style={{ animation: `fadeIn 0.4s ease-in-out ${idx * 0.05}s` }}
      >
        Status changed to{" "}
        <span
          className={`inline-flex items-center px-3 py-1 text-xs font-medium border rounded-lg ${getStatusBadgeStyle(
            item.to_status
          )}`}
        >
          {item.to_status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
        </span>{" "}
        - {date}
      </div>
    );
  };

  const renderMessage = (msg: any, idx: number) => {
    const isGM = msg.user?.type?.code === "GRANT_MAKER";
    const fullName = [msg.user?.first_name, msg.user?.last_name].filter(Boolean).join(" ") || msg.user?.email;

    return (
      <div
        key={msg.id}
        className={`flex ${isGM ? "justify-end" : "justify-start"} hover:bg-teal-50 transition-all duration-300 ease-in-out rounded-lg p-2`}
        style={{ animation: `fadeIn 0.4s ease-in-out ${idx * 0.05}s` }}
      >
        <div
          className={`rounded-lg px-4 py-3 text-sm max-w-md shadow-sm border border-opacity-50 ${
            isGM ? "bg-teal-100 text-teal-800 border-teal-50" : "bg-gray-100 text-gray-700 border-gray-50"
          } transition-all duration-300 ease-in-out hover:shadow-md`}
        >
          <p className="text-xs font-medium text-gray-600 mb-1">
            {fullName} · {msg.updated_at && formatDistanceToNow(new Date(msg.updated_at), { addSuffix: true })}
          </p>
          {msg.id === "desc" && <p className="font-medium text-gray-800 mb-2">{ticket?.title}</p>}
          <p className="whitespace-pre-line">{msg.update_text}</p>
          {msg.attachments?.length > 0 && (
            <div className="mt-2 text-xs text-teal-600">
              {msg.attachments.map((url: string, i: number) => (
                <a
                  key={i}
                  href={url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block break-all hover:text-teal-800 transition-colors duration-200"
                >
                  <Paperclip className="inline w-4 h-4 mr-1" />
                  {new URL(url).pathname.split("/").pop()?.split("?")[0]}
                </a>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderPriorityBadge = (priority: string) => {
    const styles: Record<string, string> = {
      low: "bg-gray-100 text-gray-700 border-gray-50 border-opacity-50",
      medium: "bg-amber-100 text-amber-800 border-amber-50 border-opacity-50",
      high: "bg-orange-100 text-orange-800 border-amber-50 border-opacity-50",
      urgent: "bg-red-100 text-red-800 border-amber-50 border-opacity-50",
    };
    return (
      <span
        className={`text-xs font-medium px-3 py-1 rounded-lg ${
          styles[priority?.toLowerCase()] || "bg-gray-100 text-gray-600 border-gray-50 border-opacity-50"
        }`}
      >
        {priority?.replace(/\b\w/g, (l) => l.toUpperCase())}
      </span>
    );
  };

  return (
    <SupportTabsLayout>
      <div className="w-full h-[calc(100vh-64px)] overflow-hidden my-0 py-0">
        <Card className="bg-white border-none rounded-lg h-full flex flex-col p-0 m-0">
          <CardContent className="flex-1 overflow-hidden flex flex-col lg:flex-row gap-6 h-full p-0">
            {/* Ticket Info Sidebar */}
            <aside className="lg:w-1/3">
              <Card className="bg-gradient-to-br from-teal-50 to-white rounded-lg border-gray-100 border-opacity-50 shadow-sm">
                <CardHeader className="pb-4">
                  <div className="flex justify-between items-center">
                    <Button
                      size="sm"
                      onClick={() => router.push("/grantmaker/support/my-tickets/support-ticket")}
                      className="bg-teal-600 hover:bg-teal-500 text-white rounded-lg px-4 py-2 text-sm font-medium transition-all duration-300 ease-in-out hover:shadow-md hover:scale-105"
                    >
                      ← Back to Tickets
                    </Button>
                    <CardTitle className="text-lg font-medium text-gray-700">Ticket Info</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="text-sm text-gray-700 space-y-4">
                  {ticket && (
                    <>
                      {[
                        ["ID", ticket.id],
                        ["Title", ticket.title],
                        ["Grant", ticket.grant_name || "N/A"],
                        ["Status", (
                          <span
                            className={`inline-flex items-center px-3 py-1 text-xs font-medium border rounded-lg ${getStatusBadgeStyle(
                              ticket.status
                            )}`}
                          >
                            {ticket.status?.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                          </span>
                        )],
                        ["Category", ticket.category?.replace(/\b\w/g, (l) => l.toUpperCase())],
                        ["Priority", ticket.priority && renderPriorityBadge(ticket.priority)],
                        ["Point of Contact", ticket.point_of_contact_name || "N/A"],
                        ["Created", format(new Date(ticket.created_at), "dd MMM yyyy")],
                        ["Updated", format(new Date(ticket.updated_at), "dd MMM yyyy")],
                      ].map(([label, value], i) => (
                        value && (
                          <div key={i} className="flex justify-between items-center" style={{ animation: `fadeIn 0.4s ease-in-out ${i * 0.05}s` }}>
                            <span className="font-medium text-gray-600">{label}:</span>
                            <span className="text-gray-700">{value}</span>
                          </div>
                        )
                      ))}
                      <Button
                        onClick={handleStatusChange}
                        disabled={isUpdatingStatus}
                        className="w-full mt-4 bg-teal-600 hover:bg-teal-500 text-white rounded-lg px-4 py-2 text-sm font-medium transition-all duration-300 ease-in-out hover:shadow-md hover:scale-105 disabled:bg-gray-300 disabled:text-gray-500 disabled:scale-100"
                      >
                        {isUpdatingStatus
                          ? ticket.status === "resolved"
                            ? "Reopening..."
                            : "Closing..."
                          : ticket.status === "resolved"
                          ? "Reopen Ticket"
                          : "Close Ticket"}
                      </Button>
                    </>
                  )}
                </CardContent>
              </Card>
            </aside>

            {/* Chat Section */}
            <div className="lg:w-2/3 flex flex-col">
              <section
                ref={chatRef}
                className="flex-1 overflow-y-auto p-6 space-y-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border-gray-100 border-opacity-50 shadow-sm border-b-0"
              >
                {timeline.length > 0 ? (
                  timeline.map((item, idx) =>
                    item.type === "status" ? renderStatusChange(item, idx) : renderMessage(item, idx)
                  )
                ) : (
                  <div
                    className="p-8 text-center text-gray-600"
                    style={{ animation: `fadeIn 0.4s ease-in-out` }}
                  >
                    <AlertCircle className="w-8 h-8 mb-3 mx-auto text-gray-400" />
                    <p className="text-sm font-medium text-gray-700">No messages available.</p>
                  </div>
                )}
              </section>

              {newMessageArrived && showScrollButton && (
                <div className="absolute bottom-28 right-6 animate-pulse">
                  <Button
                    onClick={scrollToBottom}
                    size="sm"
                    className="bg-teal-600 hover:bg-teal-500 text-white rounded-full px-4 py-2 text-xs font-medium shadow-md transition-all duration-300 ease-in-out hover:scale-110"
                  >
                    New messages <ArrowDown className="w-4 h-4 ml-1" />
                  </Button>
                </div>
              )}

<footer className=" p-3 border-x border-b border-gray-100 border-opacity-50 bg-white rounded-t-lg">
                {isClosed ? (
                  <p className="text-center text-sm text-gray-600">This ticket is resolved. You cannot send new messages.</p>
                ) : (
                  <div className="flex items-center gap-4 ">
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className="group"
                      title="Attach file"
                    >
                      <Paperclip className="w-6 h-6 text-gray-400 group-hover:text-teal-400 transition-all duration-300 ease-in-out hover:scale-110" />
                    </button>
                    <input
                      ref={fileInputRef}
                      type="file"
                      onChange={(e) => setFile(e.target.files?.[0] || null)}
                      className="hidden"
                    />
                    {file && (
                      <span className="text-xs text-gray-600 truncate max-w-[160px] bg-gray-50 px-3 py-1.5 rounded-lg border border-gray-100 border-opacity-50">
                        {file.name}
                      </span>
                    )}
                    <textarea
                      placeholder="Type your message..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      className="flex-grow mx-5 border border-gray-100 border-opacity-50 rounded-lg px-4 py-3 text-sm text-gray-700 placeholder:text-gray-400 bg-gray-50 focus:outline-none focus:border-teal-200 focus:ring-2 focus:ring-teal-100 transition-all duration-300 ease-in-out min-h-[80px] max-h-[200px] resize-y hover:shadow-sm focus:shadow-md focus:bg-white focus:scale-[1.02]"
                    />
                    <Button
                      onClick={handleSendMessage}
                      disabled={isSending}
                      className="bg-teal-600 m-2 hover:bg-teal-500 text-white px-6 py-3 text-sm font-medium rounded-lg transition-all duration-300 ease-in-out hover:shadow-md hover:scale-105 disabled:bg-gray-300 disabled:text-gray-500 disabled:scale-100"
                    >
                      {isSending ? "Sending..." : "Send"}
                    </Button>
                  </div>
                )}
              </footer>
            </div>
          </CardContent>
        </Card>
      </div>
    </SupportTabsLayout>
  );
}