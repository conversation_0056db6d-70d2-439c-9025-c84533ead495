"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import Layout from '@/components/grantmaker/Layout';
import { motion } from 'framer-motion';
import { Search, Filter, ArrowUpDown, Download, Plus } from 'lucide-react';
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import { FundingSummaryCard } from '@/components/funding/FundingSummaryCard';
import { AnimatedLoader } from '@/components/grantmaker/AnimatedLoader';
import { CurrencyDollarIcon, ChartBarIcon, WalletIcon } from '@/components/ui/icons';

// Import the grantmaker service for data fetching
import { FundingOverview, formatCurrency } from '@/services/grantmaker/grantmaker-service';

export default function GrantmakerFundingPage() {
  const router = useRouter();
  const [selectedYear, setSelectedYear] = useState('2024');
  const [selectedQuarter, setSelectedQuarter] = useState('All');
  const [isLoading, setIsLoading] = useState(true);
  const [fundingOverview, setFundingOverview] = useState<FundingOverview | null>(null);

  // Mock data for development - this would be replaced with actual API calls
  const mockFundingOverview: FundingOverview = {
    totalBudget: 1100000,
    totalDisbursed: 850000,
    pendingDisbursements: 250000,
    quarterlyBreakdown: [
      { quarter: "Q1 2023", budgeted: 300000, disbursed: 280000 },
      { quarter: "Q2 2023", budgeted: 280000, disbursed: 260000 },
      { quarter: "Q3 2023", budgeted: 320000, disbursed: 310000 },
      { quarter: "Q4 2023", budgeted: 200000, disbursed: 0 },
    ],
    organizationBreakdown: [
      {
        organizationId: "1",
        organizationName: "Education First Foundation",
        totalBudget: 250000,
        totalDisbursed: 200000
      },
      {
        organizationId: "2",
        organizationName: "Healthcare Initiative",
        totalBudget: 350000,
        totalDisbursed: 300000
      },
      {
        organizationId: "3",
        organizationName: "Community Development Trust",
        totalBudget: 180000,
        totalDisbursed: 150000
      },
      {
        organizationId: "4",
        organizationName: "Green Earth Project",
        totalBudget: 200000,
        totalDisbursed: 180000
      },
      {
        organizationId: "5",
        organizationName: "Arts & Culture Foundation",
        totalBudget: 120000,
        totalDisbursed: 120000
      },
    ]
  };

  // Fetch funding data
  useEffect(() => {
    const fetchFundingData = async () => {
      setIsLoading(true);
      try {
        // In a real implementation, this would be an API call
        // const data = await grantmakerService.getFundingOverview();
        // setFundingOverview(data);

        // Using mock data for now
        setTimeout(() => {
          setFundingOverview(mockFundingOverview);
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error fetching funding data:', error);
        setIsLoading(false);
      }
    };

    fetchFundingData();
  }, [selectedYear, selectedQuarter]);

  // Calculate percentages for the budget utilization
  const budgetUtilizationPercentage = fundingOverview ?
    Math.round((fundingOverview.totalDisbursed / fundingOverview.totalBudget) * 100) : 0;

  // Prepare data for the organization breakdown chart
  const organizationChartData = fundingOverview?.organizationBreakdown.map(org => ({
    name: org.organizationName,
    budget: org.totalBudget,
    disbursed: org.totalDisbursed
  })) || [];

  // Prepare data for the quarterly breakdown chart
  const quarterlyChartData = fundingOverview?.quarterlyBreakdown.map(q => ({
    name: q.quarter,
    budgeted: q.budgeted,
    disbursed: q.disbursed
  })) || [];

  // Handle click on organization in chart
  const handleOrganizationClick = (organizationId: string) => {
    router.push(`/grantmaker/grantees/${organizationId}`);
  };

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 shadow-lg rounded-lg">
          <p className="font-semibold text-gray-800">{`${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.name}: ${formatCurrency(entry.value)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <Layout title="Funding Overview">
        <AnimatedLoader type="grantee" message="Analyzing funding data..." />
      </Layout>
    );
  }

  return (
    <Layout title="Funding Overview">
      <div className="space-y-8 pb-8">
        <div className="bg-gradient-to-r from-orange-50 to-amber-50 p-6 rounded-lg shadow-sm mb-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className="text-2xl font-bold text-gray-800">Budget Overview</h1>
              <p className="text-gray-600 mt-1">Comprehensive view of all grantee funding</p>
            </motion.div>
            <div className="flex flex-col sm:flex-row gap-4">
              <motion.div
                className="flex items-center"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <span className="text-sm mr-2 text-gray-600">Year</span>
                <Select value={selectedYear} onValueChange={setSelectedYear}>
                  <SelectTrigger className="w-[150px] border-orange-200 focus:ring-orange-500">
                    <SelectValue placeholder="Select year" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="2024">2024</SelectItem>
                    <SelectItem value="2023">2023</SelectItem>
                  </SelectContent>
                </Select>
              </motion.div>
              <motion.div
                className="flex items-center"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <span className="text-sm mr-2 text-gray-600">Quarter</span>
                <Select value={selectedQuarter} onValueChange={setSelectedQuarter}>
                  <SelectTrigger className="w-[150px] border-orange-200 focus:ring-orange-500">
                    <SelectValue placeholder="Select quarter" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Quarters</SelectItem>
                    <SelectItem value="Q1">Q1</SelectItem>
                    <SelectItem value="Q2">Q2</SelectItem>
                    <SelectItem value="Q3">Q3</SelectItem>
                    <SelectItem value="Q4">Q4</SelectItem>
                  </SelectContent>
                </Select>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ staggerChildren: 0.1 }}
        >
          <FundingSummaryCard
            title="Total Budget Allocated"
            amount={formatCurrency(fundingOverview?.totalBudget || 0)}
            icon={<CurrencyDollarIcon className="w-5 h-5" />}
            accentColor="orange"
            description="Total funding allocated to all grantees"
          />

          <FundingSummaryCard
            title="Total Disbursed"
            amount={formatCurrency(fundingOverview?.totalDisbursed || 0)}
            icon={<WalletIcon className="w-5 h-5" />}
            accentColor="amber"
            description={`${budgetUtilizationPercentage}% of total budget utilized`}
          />

          <FundingSummaryCard
            title="Pending Disbursements"
            amount={formatCurrency(fundingOverview?.pendingDisbursements || 0)}
            icon={<ChartBarIcon className="w-5 h-5" />}
            accentColor="orange"
            description="Remaining funds to be disbursed"
          />
        </motion.div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
          {/* Organization Breakdown Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
          >
            <Card className="shadow-md border-0 rounded-lg overflow-hidden h-full">
              <div className="h-1 bg-gradient-to-r from-orange-500 to-amber-400"></div>
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-gray-800">Funding by Organization</CardTitle>
                <CardDescription>Click on bars to view organization details</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={organizationChartData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                      barSize={20}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis
                        dataKey="name"
                        tick={{ fill: '#6B7280' }}
                        angle={-45}
                        textAnchor="end"
                        height={70}
                      />
                      <YAxis
                        tickFormatter={(value) => `₹${value / 1000}k`}
                        tick={{ fill: '#6B7280' }}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Legend />
                      <Bar
                        dataKey="budget"
                        name="Budget Allocated"
                        fill="#FF9800"
                        radius={[4, 4, 0, 0]}
                        onClick={(data) => {
                          const org = fundingOverview?.organizationBreakdown.find(
                            o => o.organizationName === data.name
                          );
                          if (org) handleOrganizationClick(org.organizationId);
                        }}
                      />
                      <Bar
                        dataKey="disbursed"
                        name="Amount Disbursed"
                        fill="#FFC107"
                        radius={[4, 4, 0, 0]}
                        onClick={(data) => {
                          const org = fundingOverview?.organizationBreakdown.find(
                            o => o.organizationName === data.name
                          );
                          if (org) handleOrganizationClick(org.organizationId);
                        }}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Quarterly Breakdown Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            <Card className="shadow-md border-0 rounded-lg overflow-hidden h-full">
              <div className="h-1 bg-gradient-to-r from-amber-500 to-orange-400"></div>
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-gray-800">Quarterly Funding Overview</CardTitle>
                <CardDescription>Budget vs. actual disbursements by quarter</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={quarterlyChartData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      barSize={30}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis dataKey="name" tick={{ fill: '#6B7280' }} />
                      <YAxis
                        tickFormatter={(value) => `₹${value / 1000}k`}
                        tick={{ fill: '#6B7280' }}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Legend />
                      <Bar
                        dataKey="budgeted"
                        name="Budgeted Amount"
                        fill="#FF9800"
                        radius={[4, 4, 0, 0]}
                      />
                      <Bar
                        dataKey="disbursed"
                        name="Disbursed Amount"
                        fill="#FFC107"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-4 mt-8">
          <Button
            onClick={() => router.push('/grantmaker/funding/summary')}
            className="bg-orange-600 hover:bg-orange-700"
          >
            View Detailed Summary
          </Button>
          <Button
            onClick={() => router.push('/grantmaker/funding/expenses')}
            className="bg-amber-600 hover:bg-amber-700"
          >
            View Expenses
          </Button>
          <Button
            onClick={() => router.push('/grantmaker/funding/disbursements')}
            className="bg-orange-500 hover:bg-orange-600"
          >
            View Disbursements
          </Button>
        </div>
      </div>
    </Layout>
  );
}