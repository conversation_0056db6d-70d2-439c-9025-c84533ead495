"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import Layout from '@/components/grantmaker/Layout';
import { motion } from 'framer-motion';

import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { FundingSummaryCard } from '@/components/funding/FundingSummaryCard';
import { CurrencyDollarIcon, ChartBarIcon, WalletIcon } from '@/components/ui/icons';

import { GranteeTable } from '@/components/grantmaker/GranteeTable';

// Import the services for data fetching
import { formatCurrency } from '@/services/grantmaker/grantmaker-service';
import { getFundingSummary, FundingSummary } from '@/services/grantmaker/funding-summary-service';

export default function GrantmakerFundingSummaryPage() {
  const router = useRouter();
  const [selectedYear, setSelectedYear] = useState('2024');
  const [selectedQuarter, setSelectedQuarter] = useState('All');
  const [selectedSector, setSelectedSector] = useState('All');

  const [isLoading, setIsLoading] = useState(true);
  const [fundingSummary, setFundingSummary] = useState<FundingSummary | null>(null);

  // No need for mock data here as it's now in the service

  // Fetch funding summary data
  useEffect(() => {
    const fetchFundingSummary = async () => {
      setIsLoading(true);
      try {
        const data = await getFundingSummary(selectedYear, selectedQuarter, selectedSector);
        setFundingSummary(data);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching funding summary:', error);
        setIsLoading(false);
      }
    };

    fetchFundingSummary();
  }, [selectedYear, selectedQuarter, selectedSector]);

  // Calculate overall budget utilization percentage
  const overallUtilizationPercentage = fundingSummary ?
    Math.round((fundingSummary.totalDisbursed / fundingSummary.totalBudget) * 100) : 0;

  // Filter organizations based on sector
  const filteredOrganizations = fundingSummary?.organizationFunding.filter(org => {
    const matchesSector = selectedSector === 'All' || org.sector === selectedSector;
    return matchesSector;
  }) || [];

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 shadow-lg rounded-lg">
          <p className="font-semibold text-gray-800">{`${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.name}: ${formatCurrency(entry.value)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Handle click on organization row
  const handleOrganizationClick = (organizationId: string) => {
    router.push(`/grantmaker/grantees/${organizationId}`);
  };

  if (isLoading) {
    return (
      <Layout title="Funding Summary">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-pulse text-orange-500 font-medium">Loading funding summary...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Funding Summary">
      <div className="space-y-8 pb-8">
        <div className="bg-gradient-to-r from-orange-50 to-amber-50 p-6 rounded-lg shadow-sm mb-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className="text-2xl font-bold text-gray-800">Detailed Funding Summary</h1>
              <p className="text-gray-600 mt-1">Comprehensive breakdown of all grantee funding</p>
            </motion.div>
            <div className="flex flex-col sm:flex-row gap-4">
              <motion.div
                className="flex items-center"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <span className="text-sm mr-2 text-gray-600">Year</span>
                <Select value={selectedYear} onValueChange={setSelectedYear}>
                  <SelectTrigger className="w-[150px] border-orange-200 focus:ring-orange-500">
                    <SelectValue placeholder="Select year" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="2024">2024</SelectItem>
                    <SelectItem value="2023">2023</SelectItem>
                  </SelectContent>
                </Select>
              </motion.div>
              <motion.div
                className="flex items-center"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <span className="text-sm mr-2 text-gray-600">Quarter</span>
                <Select value={selectedQuarter} onValueChange={setSelectedQuarter}>
                  <SelectTrigger className="w-[150px] border-orange-200 focus:ring-orange-500">
                    <SelectValue placeholder="Select quarter" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Quarters</SelectItem>
                    <SelectItem value="Q1">Q1</SelectItem>
                    <SelectItem value="Q2">Q2</SelectItem>
                    <SelectItem value="Q3">Q3</SelectItem>
                    <SelectItem value="Q4">Q4</SelectItem>
                  </SelectContent>
                </Select>
              </motion.div>
              <motion.div
                className="flex items-center"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.3 }}
              >
                <span className="text-sm mr-2 text-gray-600">Sector</span>
                <Select value={selectedSector} onValueChange={setSelectedSector}>
                  <SelectTrigger className="w-[150px] border-orange-200 focus:ring-orange-500">
                    <SelectValue placeholder="Select sector" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Sectors</SelectItem>
                    <SelectItem value="Education">Education</SelectItem>
                    <SelectItem value="Healthcare">Healthcare</SelectItem>
                    <SelectItem value="Community">Community</SelectItem>
                    <SelectItem value="Environment">Environment</SelectItem>
                    <SelectItem value="Arts">Arts</SelectItem>
                  </SelectContent>
                </Select>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ staggerChildren: 0.1 }}
        >
          <FundingSummaryCard
            title="Total Budget Allocated"
            amount={formatCurrency(fundingSummary?.totalBudget || 0)}
            icon={<CurrencyDollarIcon className="w-5 h-5" />}
            accentColor="orange"
            description="Total funding allocated to all grantees"
          />

          <FundingSummaryCard
            title="Total Disbursed"
            amount={formatCurrency(fundingSummary?.totalDisbursed || 0)}
            icon={<WalletIcon className="w-5 h-5" />}
            accentColor="amber"
            description={`${overallUtilizationPercentage}% of total budget utilized`}
          />

          <FundingSummaryCard
            title="Remaining Balance"
            amount={formatCurrency(fundingSummary?.remainingBalance || 0)}
            icon={<ChartBarIcon className="w-5 h-5" />}
            accentColor="orange"
            description="Remaining funds to be disbursed"
          />
        </motion.div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
          {/* Sector Distribution Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
          >
            <Card className="shadow-md border-0 rounded-lg overflow-hidden h-full">
              <div className="h-1 bg-gradient-to-r from-orange-500 to-amber-400"></div>
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-gray-800">Funding by Sector</CardTitle>
                <CardDescription>Distribution of funding across different sectors</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={fundingSummary?.sectorDistribution}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={120}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {fundingSummary?.sectorDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip content={<CustomTooltip />} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Budget Utilization Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            <Card className="shadow-md border-0 rounded-lg overflow-hidden h-full">
              <div className="h-1 bg-gradient-to-r from-amber-500 to-orange-400"></div>
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-gray-800">Budget Utilization by Organization</CardTitle>
                <CardDescription>Comparison of budget allocation and utilization</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={fundingSummary?.organizationFunding.map(org => ({
                        name: org.name,
                        budget: org.totalBudget,
                        disbursed: org.totalDisbursed
                      }))}
                      margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                      barSize={20}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis
                        dataKey="name"
                        tick={{ fill: '#6B7280' }}
                        angle={-45}
                        textAnchor="end"
                        height={70}
                      />
                      <YAxis
                        tickFormatter={(value) => `₹${value / 1000}k`}
                        tick={{ fill: '#6B7280' }}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Legend />
                      <Bar
                        dataKey="budget"
                        name="Budget Allocated"
                        fill="#FF9800"
                        radius={[4, 4, 0, 0]}
                      />
                      <Bar
                        dataKey="disbursed"
                        name="Amount Disbursed"
                        fill="#FFC107"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Organization Funding Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.2 }}
          className="mt-8"
        >
          <GranteeTable
            organizations={filteredOrganizations.map(org => ({
              id: org.id,
              name: org.name,
              sector: org.sector,
              totalBudget: org.totalBudget,
              totalDisbursed: org.totalDisbursed,
              remainingBalance: org.remainingBalance,
              utilizationPercentage: org.utilizationPercentage,
              lastDisbursementDate: org.lastDisbursementDate,
              status: org.status,
              type: 'organization'
            }))}
            onViewDetails={handleOrganizationClick}
            title="Grantee Funding Details"
            description="Detailed funding information for each grantee"
          />
        </motion.div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-4 mt-8">
          <Button
            onClick={() => router.push('/grantmaker/funding')}
            className="bg-orange-600 hover:bg-orange-700"
          >
            Back to Funding Overview
          </Button>
          <Button
            onClick={() => router.push('/grantmaker/funding/expenses')}
            className="bg-amber-600 hover:bg-amber-700"
          >
            View Expenses
          </Button>
          <Button
            onClick={() => router.push('/grantmaker/funding/disbursements')}
            className="bg-orange-500 hover:bg-orange-600"
          >
            View Disbursements
          </Button>
        </div>
      </div>
    </Layout>
  );
}