"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import Layout from '@/components/grantmaker/Layout';
import { motion } from 'framer-motion';
import { Filter, ArrowUpDown, Download, FileSpreadsheet } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { FundingSummaryCard } from '@/components/funding/FundingSummaryCard';
import { AnimatedLoader } from '@/components/grantmaker/AnimatedLoader';
import { ExpenseTable } from '@/components/funding/ExpenseTable';
import { CurrencyDollarIcon, ChartBarIcon, DocumentIcon } from '@/components/ui/icons';

// Import the services for data fetching
import { formatCurrency } from '@/services/grantmaker/grantmaker-service';
import { ExpenseRecord, ExpenseBreakdown } from '@/services/funding-service';
import { getExpenseHistory, ExpenseHistoryData } from '@/services/grantmaker/expense-history-service';

export default function GrantmakerExpensesPage() {
  const router = useRouter();
  const [selectedYear, setSelectedYear] = useState('2024');
  const [selectedQuarter, setSelectedQuarter] = useState('All');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [isLoading, setIsLoading] = useState(true);
  const [expenseData, setExpenseData] = useState<ExpenseHistoryData | null>(null);

  // Mock data is no longer needed as we're using the API service

  // Fetch expense data from the backend API
  useEffect(() => {
    const fetchExpenseData = async () => {
      setIsLoading(true);
      try {
        // Call the expense history service with filters
        const data = await getExpenseHistory(selectedYear, selectedQuarter, selectedCategory);
        setExpenseData(data);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching expense data:', error);
        setIsLoading(false);
      }
    };

    fetchExpenseData();
  }, [selectedYear, selectedQuarter, selectedCategory]);

  // Transform monthly expenses to quarterly expenses
  const transformMonthlyToQuarterly = (monthlyData: any[]) => {
    if (!monthlyData || monthlyData.length === 0) return [];

    // Map months to financial quarters
    const monthToQuarter: Record<string, string> = {
      'Jan': 'Q4', 'Feb': 'Q4', 'Mar': 'Q4',
      'Apr': 'Q1', 'May': 'Q1', 'Jun': 'Q1',
      'Jul': 'Q2', 'Aug': 'Q2', 'Sep': 'Q2',
      'Oct': 'Q3', 'Nov': 'Q3', 'Dec': 'Q3'
    };

    // Initialize quarterly data
    const quarterlyData: Record<string, { quarter: string, budget: number, actual: number }> = {
      'Q1': { quarter: 'Q1', budget: 0, actual: 0 },
      'Q2': { quarter: 'Q2', budget: 0, actual: 0 },
      'Q3': { quarter: 'Q3', budget: 0, actual: 0 },
      'Q4': { quarter: 'Q4', budget: 0, actual: 0 }
    };

    // Aggregate monthly data into quarters
    monthlyData.forEach(month => {
      const quarter = monthToQuarter[month.month];
      if (quarter) {
        quarterlyData[quarter].budget += month.budget || 0;
        quarterlyData[quarter].actual += month.actual || 0;
      }
    });

    // Convert to array and sort by quarter
    return Object.values(quarterlyData).sort((a, b) => {
      const quarterOrder = { 'Q1': 1, 'Q2': 2, 'Q3': 3, 'Q4': 4 };
      return quarterOrder[a.quarter as keyof typeof quarterOrder] - quarterOrder[b.quarter as keyof typeof quarterOrder];
    });
  };

  // Transform monthly data to quarterly data
  const quarterlyExpenses = transformMonthlyToQuarterly(expenseData?.monthlyExpenses || []);

  // Calculate percentages for the budget utilization
  const budgetUtilizationPercentage = expenseData ?
    Math.round((expenseData.totalSpent / expenseData.totalBudget) * 100) : 0;

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 shadow-lg rounded-lg">
          <p className="font-semibold text-gray-800">{`${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.name}: ${formatCurrency(entry.value)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // COLORS for the pie chart
  const COLORS = ['#FF9800', '#FFC107', '#FFD54F', '#FFECB3'];

  if (isLoading) {
    return (
      <Layout title="Expense Management">
        <AnimatedLoader type="expense" message="Analyzing expense data..." />
      </Layout>
    );
  }

  return (
    <Layout title="Expense Management">
      <div className="space-y-8 pb-8">
        <div className="bg-gradient-to-r from-orange-50 to-amber-50 p-6 rounded-lg shadow-sm mb-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className="text-2xl font-bold text-gray-800">Expense Overview</h1>
              <p className="text-gray-600 mt-1">Track and analyze all grantee expenses</p>
            </motion.div>
            <div className="flex flex-col sm:flex-row gap-4">
              <motion.div
                className="flex items-center"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <span className="text-sm mr-2 text-gray-600">Year</span>
                <Select value={selectedYear} onValueChange={setSelectedYear}>
                  <SelectTrigger className="w-[150px] border-orange-200 focus:ring-orange-500">
                    <SelectValue placeholder="Select year" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="2024">2024</SelectItem>
                    <SelectItem value="2023">2023</SelectItem>
                  </SelectContent>
                </Select>
              </motion.div>
              <motion.div
                className="flex items-center"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <span className="text-sm mr-2 text-gray-600">Quarter</span>
                <Select value={selectedQuarter} onValueChange={setSelectedQuarter}>
                  <SelectTrigger className="w-[150px] border-orange-200 focus:ring-orange-500">
                    <SelectValue placeholder="Select quarter" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Quarters</SelectItem>
                    <SelectItem value="Q1">Q1</SelectItem>
                    <SelectItem value="Q2">Q2</SelectItem>
                    <SelectItem value="Q3">Q3</SelectItem>
                    <SelectItem value="Q4">Q4</SelectItem>
                  </SelectContent>
                </Select>
              </motion.div>
              <motion.div
                className="flex items-center"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.3 }}
              >
                <span className="text-sm mr-2 text-gray-600">Category</span>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-[150px] border-orange-200 focus:ring-orange-500">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Categories</SelectItem>
                    <SelectItem value="Personnel">Personnel</SelectItem>
                    <SelectItem value="Operations">Operations</SelectItem>
                    <SelectItem value="Programs">Programs</SelectItem>
                    <SelectItem value="Equipment">Equipment</SelectItem>
                  </SelectContent>
                </Select>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ staggerChildren: 0.1 }}
        >
          <FundingSummaryCard
            title="Total Budget Allocated"
            amount={formatCurrency(expenseData?.totalBudget || 0)}
            icon={<CurrencyDollarIcon className="w-5 h-5" />}
            accentColor="orange"
            description="Total budget allocated across all grantees"
          />

          <FundingSummaryCard
            title="Total Expenses"
            amount={formatCurrency(expenseData?.totalSpent || 0)}
            icon={<ChartBarIcon className="w-5 h-5" />}
            accentColor="amber"
            description={`${budgetUtilizationPercentage}% of total budget utilized`}
          />

          <FundingSummaryCard
            title="Remaining Budget"
            amount={formatCurrency(expenseData?.remainingBudget || 0)}
            icon={<DocumentIcon className="w-5 h-5" />}
            accentColor="orange"
            description="Budget remaining across all grantees"
          />
        </motion.div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
          {/* Category Breakdown Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
          >
            <Card className="shadow-md border-0 rounded-lg overflow-hidden h-full">
              <div className="h-1 bg-gradient-to-r from-orange-500 to-amber-400"></div>
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-gray-800">Expense by Category</CardTitle>
                <CardDescription>Breakdown of expenses by category</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={expenseData?.categoryBreakdown}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={120}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {expenseData?.categoryBreakdown.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip content={<CustomTooltip />} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Monthly Expense Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            <Card className="shadow-md border-0 rounded-lg overflow-hidden h-full">
              <div className="h-1 bg-gradient-to-r from-amber-500 to-orange-400"></div>
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-gray-800">Financial Quarterly Expense Trends</CardTitle>
                <CardDescription>Budget vs. actual expenses by financial quarter</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={quarterlyExpenses}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      barSize={20}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis dataKey="quarter" tick={{ fill: '#6B7280' }} />
                      <YAxis
                        tickFormatter={(value) => `₹${value / 1000}k`}
                        tick={{ fill: '#6B7280' }}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Legend />
                      <Bar
                        dataKey="budget"
                        name="Budgeted Amount"
                        fill="#FF9800"
                        radius={[4, 4, 0, 0]}
                      />
                      <Bar
                        dataKey="actual"
                        name="Actual Expense"
                        fill="#FFC107"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Expense Records Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.2 }}
          className="mt-8"
        >
          <Card className="shadow-md border-0 rounded-lg overflow-hidden">
            <div className="h-1 bg-gradient-to-r from-orange-500 to-amber-400"></div>
            <CardHeader>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                <div>
                  <CardTitle className="text-xl font-semibold text-gray-800">Expense Records</CardTitle>
                  <CardDescription>Detailed list of all expense entries</CardDescription>
                </div>
                <div className="flex gap-2 mt-2 sm:mt-0">
                  <Button
                    variant="default"
                    size="sm"
                    className="flex items-center gap-1 bg-[#00998F] hover:bg-[#007b73]"
                    onClick={() => router.push('/grantmaker/expense/upload')}
                  >
                    <FileSpreadsheet className="h-4 w-4" />
                    Upload Expense Data
                  </Button>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <Filter className="h-4 w-4" />
                    Filter
                  </Button>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <ArrowUpDown className="h-4 w-4" />
                    Sort
                  </Button>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <Download className="h-4 w-4" />
                    Export
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {expenseData?.expenseRecords && (
                <ExpenseTable data={expenseData.expenseRecords} formatCurrency={formatCurrency} />
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-4 mt-8">
          <Button
            onClick={() => router.push('/grantmaker/funding')}
            className="bg-orange-600 hover:bg-orange-700"
          >
            Back to Funding Overview
          </Button>
          <Button
            onClick={() => router.push('/grantmaker/funding/disbursements')}
            className="bg-amber-600 hover:bg-amber-700"
          >
            View Disbursements
          </Button>
          <Button
            onClick={() => router.push('/grantmaker/funding/summary')}
            className="bg-orange-500 hover:bg-orange-600"
          >
            View Detailed Summary
          </Button>
          <Button
            onClick={() => router.push('/grantmaker/expense/upload')}
            className="bg-[#00998F] hover:bg-[#007b73]"
          >
            <FileSpreadsheet className="mr-2 h-4 w-4" />
            Upload Expense Data
          </Button>
        </div>
      </div>
    </Layout>
  );
}