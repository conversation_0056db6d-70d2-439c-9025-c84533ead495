"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Layout from "@/components/grantmaker/Layout";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Search, Filter, ArrowUpDown, X, Mail, User as UserIcon, Building } from "lucide-react";
import { motion } from "framer-motion";
import { User, getAllUsers } from "@/services/grantmaker/user-service";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";

export default function UsersPage() {
  const router = useRouter();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState<keyof User>("username");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [roleFilter, setRoleFilter] = useState<string[]>([]);

  useEffect(() => {
    const isLoggedIn = localStorage.getItem("isLoggedIn");

    if (isLoggedIn !== "true") {
      router.push("/grantmaker/login");
      return;
    }

    const fetchUsers = async () => {
      setLoading(true);
      try {
        const data = await getAllUsers();
        setUsers(data);
      } catch (err) {
        console.error("Error fetching users:", err);
        setError("Failed to load users");
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [router]);

  // Get unique roles
  const roles = [...new Set(users.map(user => user.role || 'Unknown'))];

  const handleSort = (field: keyof User) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const toggleRoleFilter = (role: string) => {
    setRoleFilter(prev =>
      prev.includes(role)
        ? prev.filter(r => r !== role)
        : [...prev, role]
    );
  };

  const clearFilters = () => {
    setRoleFilter([]);
    setSearchTerm("");
  };

  const filteredUsers = users.filter(user => {
    const fullName = `${user.first_name} ${user.last_name}`.toLowerCase();
    const matchesSearch = 
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fullName.includes(searchTerm.toLowerCase()) ||
      (user.organization && user.organization.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesRole = roleFilter.length === 0 || (user.role && roleFilter.includes(user.role));
    
    return matchesSearch && matchesRole;
  });

  const sortedUsers = [...filteredUsers].sort((a, b) => {
    let aValue = a[sortField];
    let bValue = b[sortField];
    
    if (sortField === 'first_name') {
      aValue = `${a.first_name} ${a.last_name}`;
      bValue = `${b.first_name} ${b.last_name}`;
    }
    
    if (aValue === undefined) return 1;
    if (bValue === undefined) return -1;
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }
    
    return sortDirection === 'asc'
      ? (aValue > bValue ? 1 : -1)
      : (bValue > aValue ? 1 : -1);
  });

  const handleViewDetails = (userId: string) => {
    router.push(`/grantmaker/users/${userId}`);
  };

  if (loading) {
    return (
      <Layout title="Users">
        <div className="w-full max-w-7xl mx-auto flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-solid border-orange-400 border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
            <p className="mt-4 text-lg text-gray-600">Loading users...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout title="Users">
        <div className="w-full max-w-7xl mx-auto">
          <Card className="bg-white shadow-md hover:shadow-lg transition-shadow mb-8">
            <CardContent className="p-8 text-center">
              <div className="text-red-500 text-xl mb-4">{error}</div>
              <Button onClick={() => window.location.reload()} className="bg-gradient-to-r from-orange-500 via-amber-500 to-orange-400 hover:from-orange-600 hover:via-amber-600 hover:to-orange-500 text-white">
                Retry Loading
              </Button>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Users">
      <div className="w-full max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="bg-white shadow-md hover:shadow-xl transition-all duration-300 border-0 overflow-hidden">
            <div className="h-1.5 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-400"></div>
            <CardHeader className="pb-2">
              <CardTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                All Users
              </CardTitle>
              <CardDescription className="text-gray-600">View and manage all users in the system</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
                <div className="relative w-full md:w-auto">
                  <div className="relative group">
                    <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg opacity-0 group-hover:opacity-20 transition-all duration-300"></div>
                    <div className="relative flex items-center">
                      <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400 group-hover:text-blue-500 transition-colors duration-200" />
                      <input
                        type="text"
                        placeholder="Search users..."
                        className="h-10 w-full md:w-64 rounded-lg border border-gray-200 bg-white pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 shadow-sm group-hover:shadow transition-all duration-200"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                      {searchTerm && (
                        <button
                          className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          onClick={() => setSearchTerm("")}
                        >
                          <X className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="border-gray-200 hover:border-blue-200 hover:bg-blue-50 transition-all duration-200">
                        <Filter className="h-4 w-4 mr-2 text-blue-500" />
                        Filter
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-56 p-2 bg-white shadow-xl border border-gray-100 rounded-xl overflow-hidden">
                      <DropdownMenuLabel className="text-gray-800 font-medium">Filter Users</DropdownMenuLabel>
                      <DropdownMenuSeparator className="bg-gray-100" />
                      <DropdownMenuLabel className="text-xs font-medium text-gray-500 mt-1">Role</DropdownMenuLabel>
                      {roles.map(role => (
                        <DropdownMenuCheckboxItem
                          key={role}
                          checked={roleFilter.includes(role)}
                          onCheckedChange={() => toggleRoleFilter(role)}
                          className="rounded-md my-0.5 focus:bg-blue-50 focus:text-blue-600"
                        >
                          {role}
                        </DropdownMenuCheckboxItem>
                      ))}
                      {(roleFilter.length > 0 || searchTerm) && (
                        <>
                          <DropdownMenuSeparator className="bg-gray-100 my-2" />
                          <DropdownMenuItem
                            className="rounded-md text-center text-blue-600 hover:text-blue-700 hover:bg-blue-50 cursor-pointer"
                            onClick={clearFilters}
                          >
                            Clear Filters
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>

              <div className="overflow-x-auto rounded-lg border border-gray-100 shadow-sm">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gradient-to-r from-gray-50 to-white border-b border-gray-200">
                      <th className="py-3 px-4 text-sm font-medium text-gray-600">
                        <button
                          onClick={() => handleSort("first_name")}
                          className="flex items-center gap-1 hover:text-blue-600 transition-colors duration-200"
                        >
                          Name
                          <ArrowUpDown className="h-4 w-4" />
                        </button>
                      </th>
                      <th className="py-3 px-4 text-sm font-medium text-gray-600">
                        <button
                          onClick={() => handleSort("username")}
                          className="flex items-center gap-1 hover:text-blue-600 transition-colors duration-200"
                        >
                          Username
                          <ArrowUpDown className="h-4 w-4" />
                        </button>
                      </th>
                      <th className="py-3 px-4 text-sm font-medium text-gray-600">
                        <button
                          onClick={() => handleSort("email")}
                          className="flex items-center gap-1 hover:text-blue-600 transition-colors duration-200"
                        >
                          Email
                          <ArrowUpDown className="h-4 w-4" />
                        </button>
                      </th>
                      <th className="py-3 px-4 text-sm font-medium text-gray-600">
                        <button
                          onClick={() => handleSort("organization")}
                          className="flex items-center gap-1 hover:text-blue-600 transition-colors duration-200"
                        >
                          Organization
                          <ArrowUpDown className="h-4 w-4" />
                        </button>
                      </th>
                      <th className="py-3 px-4 text-sm font-medium text-gray-600">
                        <button
                          onClick={() => handleSort("role")}
                          className="flex items-center gap-1 hover:text-blue-600 transition-colors duration-200"
                        >
                          Role
                          <ArrowUpDown className="h-4 w-4" />
                        </button>
                      </th>
                      <th className="py-3 px-4 text-sm font-medium text-gray-600">
                        Status
                      </th>
                      <th className="py-3 px-4 text-sm font-medium text-gray-600">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {sortedUsers.length === 0 ? (
                      <tr>
                        <td colSpan={7} className="py-8 text-center text-gray-500">
                          <div className="flex flex-col items-center justify-center">
                            <UserIcon className="h-12 w-12 text-gray-300 mb-2" />
                            <p className="text-gray-500 font-medium">No users found</p>
                            <p className="text-gray-400 text-sm mt-1">Try adjusting your search or filters</p>
                          </div>
                        </td>
                      </tr>
                    ) : (
                      sortedUsers.map((user, index) => (
                        <tr
                          key={user.id}
                          className={`border-b border-gray-100 hover:bg-blue-50/30 cursor-pointer transition-colors duration-150 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}
                          onClick={() => handleViewDetails(user.id)}
                        >
                          <td className="py-3 px-4 font-medium text-gray-800">
                            {user.first_name} {user.last_name}
                          </td>
                          <td className="py-3 px-4 text-gray-700">{user.username}</td>
                          <td className="py-3 px-4">
                            <div className="flex items-center">
                              <Mail className="h-4 w-4 text-gray-400 mr-2" />
                              <span className="text-blue-600">{user.email}</span>
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center">
                              <Building className="h-4 w-4 text-gray-400 mr-2" />
                              <span>{user.organization || 'N/A'}</span>
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <Badge className={`
                              ${user.role === 'Grantmaker' ? 'bg-purple-100 text-purple-800' : ''}
                              ${user.role === 'Grantee' ? 'bg-blue-100 text-blue-800' : ''}
                              ${!user.role ? 'bg-gray-100 text-gray-800' : ''}
                            `}>
                              {user.role || 'Unknown'}
                            </Badge>
                          </td>
                          <td className="py-3 px-4">
                            <Badge className={user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                              {user.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </td>
                          <td className="py-3 px-4">
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-blue-200 hover:bg-blue-100 hover:text-blue-600 transition-all duration-200"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleViewDetails(user.id);
                              }}
                            >
                              View Details
                            </Button>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </Layout>
  );
}
