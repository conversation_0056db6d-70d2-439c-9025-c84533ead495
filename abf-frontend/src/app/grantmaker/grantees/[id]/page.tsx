"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import React from 'react';
import { toast } from 'sonner';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';

import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import Layout from '@/components/grantmaker/Layout';
import { motion } from 'framer-motion';
import { ArrowLeft, Download, Calendar, FileText, Users, Wallet, User, CheckCircle, AlertCircle, FileSpreadsheet, Eye, XCircle } from 'lucide-react';
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ons<PERSON><PERSON>r, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import { FundingSummaryCard } from '@/components/funding/FundingSummaryCard';
import { AnimatedLoader } from '@/components/grantmaker/AnimatedLoader';

import { CurrencyDollarIcon, ChartBarIcon, DocumentIcon, WalletIcon } from '@/components/ui/icons';
import { User2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';

// Import the services for data fetching
import { formatCurrency } from '@/services/grantmaker/grantmaker-service';
import { ExpenseRecord, DisbursementHistory } from '@/services/funding-service';
import { Document, verifyDocument } from '@/services/document-service';
import { getGranteeProfile } from '@/services/grantmaker/grantee-service';

export default function GranteeDetailPage({ params }: { params: { id: string } }) {
  const id = params.id;

  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);
  const [documentComments, setDocumentComments] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [granteeData, setGranteeData] = useState<{
    id: string;
    supportingDocuments?: Document[];
    name: string;
    sector: string;
    contactPerson: string;
    contactEmail: string;
    location: string;
    startDate: string;
    endDate?: string;
    status: 'active' | 'pending' | 'completed';
    totalFunding: number;
    disbursedAmount: number;
    remainingAmount: number;
    utilizationPercentage: number;
    expenseBreakdown: Array<{ name: string; value: number; color: string }>;
    monthlyExpenses: Array<{ month: string; budget: number; actual: number }>;
    expenseRecords: ExpenseRecord[];
    disbursementHistory: DisbursementHistory[];

    // Organization details
    organization_name: string;
    organization_legal_type: string;
    organization_function_type: string;
    organization_legal_type_name: string;
    organization_function_type_name: string;
    pan_number: string;
    mailing_address: string;
    phone_number: string;
    email_address: string;
    website_url?: string;
    number_of_team_members?: number;
    mission_vision?: string;
    background_history?: string;
    previous_grants_info?: string;

    // Registration details
    tax_registration_number: string;
    csr_registration_number?: string;
    tax_registration_number_under_12_a?: string;
    trust_registration_number?: string;
    darpan_id?: string;
    fcra_registration_number?: string;

    // Grant details
    grant_id?: string;
    grant_name?: string;
    project_start_date?: string;
    project_end_date?: string;
    purpose_of_grant?: string;
    annual_budget?: number;
    funding_sources?: string;

    // KMP details
    kmp_details?: Array<{
      id: string;
      name: string;
      email: string;
      phone: string;
      din: string;
      designation: string;
    }>;

    // Grant history
    grant_history?: Array<{
      id: string;
      grant_name: string;
      amount: number;
      start_date: string;
      end_date?: string;
      status: 'active' | 'completed' | 'pending';
      purpose: string;
    }>;
  } | null>(null);

  // Fetch grantee data
  useEffect(() => {
    const fetchGranteeData = async () => {
      setIsLoading(true);
      try {
        // Fetch grantee profile from the unified service
        const granteeProfile = await getGranteeProfile(id);

        if (!granteeProfile) {
          throw new Error('Grantee not found');
        }

        // Create grantee data from the profile
        const mockGranteeData = {
          id: id,
          name: granteeProfile.name,
          sector: granteeProfile.sector,
          contactPerson: granteeProfile.contactPerson,
          contactEmail: granteeProfile.contactEmail,
          location: granteeProfile.location,
          startDate: granteeProfile.startDate,
          endDate: granteeProfile.endDate,
          status: granteeProfile.status.toLowerCase() as 'active' | 'pending' | 'completed',
          totalFunding: granteeProfile.totalFunding,
          disbursedAmount: granteeProfile.disbursedAmount,
          remainingAmount: granteeProfile.remainingAmount,
          utilizationPercentage: granteeProfile.utilizationPercentage,

          // Organization details
          organization_name: granteeProfile.organization_name,
          organization_legal_type: granteeProfile.organization_legal_type,
          organization_function_type: granteeProfile.organization_function_type,
          organization_legal_type_name: granteeProfile.organization_legal_type_name,
          organization_function_type_name: granteeProfile.organization_function_type_name,
          pan_number: granteeProfile.pan_number,
          mailing_address: granteeProfile.mailing_address,
          phone_number: granteeProfile.phone_number,
          email_address: granteeProfile.email_address,
          website_url: granteeProfile.website_url,
          number_of_team_members: granteeProfile.number_of_team_members,
          mission_vision: granteeProfile.mission_vision,
          background_history: granteeProfile.background_history,
          previous_grants_info: granteeProfile.previous_grants_info,

          // Registration details
          tax_registration_number: granteeProfile.tax_registration_number,
          csr_registration_number: granteeProfile.csr_registration_number,
          tax_registration_number_under_12_a: granteeProfile.tax_registration_number_under_12_a,
          trust_registration_number: granteeProfile.trust_registration_number,
          darpan_id: granteeProfile.darpan_id,
          fcra_registration_number: granteeProfile.fcra_registration_number,

          // Grant details
          grant_id: granteeProfile.grant_id,
          grant_name: granteeProfile.grant_name,
          project_start_date: granteeProfile.project_start_date,
          project_end_date: granteeProfile.project_end_date,
          purpose_of_grant: granteeProfile.purpose_of_grant,
          annual_budget: granteeProfile.annual_budget,
          funding_sources: granteeProfile.funding_sources,

          // Grant history
          grant_history: granteeProfile.grant_history,
          supportingDocuments: [
            {
              id: "1",
              name: "Registration Certificate",
              attachment_type: "registration",
              status: "verified" as "verified",
              uploadedAt: "2023-01-10T10:30:00Z",
              url: "#"
            },
            {
              id: "2",
              name: "Tax Exemption Certificate",
              attachment_type: "tax-exemption",
              status: "pending" as "pending",
              uploadedAt: "2023-01-10T10:35:00Z",
              url: "#"
            },
            {
              id: "3",
              name: "FCRA Certificate",
              attachment_type: "fcra",
              status: "update-required" as "update-required",
              uploadedAt: "2023-01-20T09:15:00Z",
              url: "#",
              comments: "Please provide a valid FCRA certificate."
            },
            {
              id: "4",
              name: "Annual Report 2023",
              attachment_type: "report",
              status: "verified" as "verified",
              uploadedAt: "2023-01-15T14:20:00Z",
              url: "#"
            },
            {
              id: "5",
              name: "Financial Statements 2023",
              attachment_type: "financial",
              status: "pending" as "pending",
              uploadedAt: "2023-01-15T14:25:00Z",
              url: "#"
            }
          ],
          expenseBreakdown: [
            { name: 'Personnel', value: Math.floor(granteeProfile.totalFunding * 0.4), color: '#FF9800' },
            { name: 'Operations', value: Math.floor(granteeProfile.totalFunding * 0.3), color: '#FFC107' },
            { name: 'Programs', value: Math.floor(granteeProfile.totalFunding * 0.2), color: '#FFD54F' },
            { name: 'Equipment', value: Math.floor(granteeProfile.totalFunding * 0.1), color: '#FFECB3' },
          ],
          monthlyExpenses: [
            { month: 'Jan', budget: Math.floor(granteeProfile.totalFunding / 12), actual: Math.floor((granteeProfile.totalFunding / 12) * 0.95) },
            { month: 'Feb', budget: Math.floor(granteeProfile.totalFunding / 12), actual: Math.floor((granteeProfile.totalFunding / 12) * 0.98) },
            { month: 'Mar', budget: Math.floor(granteeProfile.totalFunding / 12), actual: Math.floor((granteeProfile.totalFunding / 12) * 1.02) },
            { month: 'Apr', budget: Math.floor(granteeProfile.totalFunding / 12), actual: Math.floor((granteeProfile.totalFunding / 12) * 0.97) },
            { month: 'May', budget: Math.floor(granteeProfile.totalFunding / 12), actual: Math.floor((granteeProfile.totalFunding / 12) * 1.01) },
            { month: 'Jun', budget: Math.floor(granteeProfile.totalFunding / 12), actual: Math.floor((granteeProfile.totalFunding / 12) * 0.99) },
            { month: 'Jul', budget: Math.floor(granteeProfile.totalFunding / 12), actual: Math.floor((granteeProfile.totalFunding / 12) * 1.04) },
            { month: 'Aug', budget: Math.floor(granteeProfile.totalFunding / 12), actual: Math.floor((granteeProfile.totalFunding / 12) * 1.00) },
            { month: 'Sep', budget: Math.floor(granteeProfile.totalFunding / 12), actual: Math.floor((granteeProfile.totalFunding / 12) * 0.95) },
            { month: 'Oct', budget: Math.floor(granteeProfile.totalFunding / 12), actual: Math.floor((granteeProfile.totalFunding / 12) * 0.55) },
            { month: 'Nov', budget: Math.floor(granteeProfile.totalFunding / 12), actual: 0 },
            { month: 'Dec', budget: Math.floor(granteeProfile.totalFunding / 12), actual: 0 },
          ],
          expenseRecords: [
            {
              id: '1',
              loggedDate: '2024-01-15',
              totalBudget: Math.floor(granteeProfile.totalFunding * 0.1),
              totalActualSpent: Math.floor(granteeProfile.totalFunding * 0.095),
              status: 'Verified',
              attachment: 'expense-report-jan.xlsx',
              category: 'Personnel',
              description: 'Staff salaries and benefits',
              source_type: 'excel',
            },
            {
              id: '2',
              loggedDate: '2024-02-10',
              totalBudget: Math.floor(granteeProfile.totalFunding * 0.08),
              totalActualSpent: Math.floor(granteeProfile.totalFunding * 0.078),
              status: 'Verified',
              attachment: 'expense-report-feb.xlsx',
              category: 'Operations',
              description: 'Office rent and utilities',
              source_type: 'excel',
            },
            {
              id: '3',
              loggedDate: '2024-03-05',
              totalBudget: Math.floor(granteeProfile.totalFunding * 0.05),
              totalActualSpent: Math.floor(granteeProfile.totalFunding * 0.052),
              status: 'Verified',
              attachment: 'expense-report-mar.xlsx',
              category: 'Programs',
              description: 'Community outreach programs',
              source_type: 'excel',
            },
          ],
          disbursementHistory: [
            {
              id: '1',
              date: '2024-01-15',
              amount: Math.floor(granteeProfile.totalFunding * 0.25),
              status: 'Disbursed',
              remark: 'Q1 operational funding',
              acknowledgement: 'Completed',
              grantName: granteeProfile.name,
            },
            {
              id: '2',
              date: '2024-04-10',
              amount: Math.floor(granteeProfile.totalFunding * 0.25),
              status: 'Disbursed',
              remark: 'Q2 operational funding',
              acknowledgement: 'Completed',
              grantName: granteeProfile.name,
            },
            {
              id: '3',
              date: '2024-07-05',
              amount: Math.floor(granteeProfile.totalFunding * 0.25),
              status: 'Disbursed',
              remark: 'Q3 operational funding',
              acknowledgement: 'Completed',
              grantName: granteeProfile.name,
            },
            {
              id: '4',
              date: '2024-10-30',
              amount: Math.floor(granteeProfile.totalFunding * 0.25),
              status: 'Pending',
              remark: 'Q4 operational funding',
              acknowledgement: 'Pending',
              grantName: granteeProfile.name,
            },
          ],
        };

        setGranteeData(mockGranteeData);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching grantee data:', error);
        setIsLoading(false);
        // Redirect to grantees list if grantee not found
        router.push('/grantmaker/grantees');
      }
    };

    fetchGranteeData();
  }, [id, router]);

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 shadow-lg rounded-lg">
          <p className="font-semibold text-gray-800">{`${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.name}: ${formatCurrency(entry.value)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Handle document review
  const handleReviewDocument = (document: Document) => {
    setSelectedDocument(document);
    setDocumentComments(document.comments || "");
    setIsReviewDialogOpen(true);
  };

  // Handle document approval
  const handleApproveDocument = async () => {
    if (!selectedDocument) return;

    setIsSubmitting(true);
    try {
      const result = await verifyDocument(selectedDocument.id, "verified");
      if (result.success) {
        // Update document status in state
        if (granteeData && granteeData.supportingDocuments) {
          const updatedDocuments = granteeData.supportingDocuments.map(doc =>
            doc.id === selectedDocument.id ? { ...doc, status: "verified" as "verified" } : doc
          );

          setGranteeData({
            ...granteeData,
            supportingDocuments: updatedDocuments
          });
        }

        toast.success("Document approved successfully");
        setIsReviewDialogOpen(false);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error approving document:", error);
      toast.error("Failed to approve document");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle document rejection
  const handleRejectDocument = async () => {
    if (!selectedDocument) return;

    if (!documentComments.trim()) {
      toast.error("Please provide comments explaining why the document is rejected");
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await verifyDocument(selectedDocument.id, "update-required", documentComments);
      if (result.success) {
        // Update document status in state
        if (granteeData && granteeData.supportingDocuments) {
          const updatedDocuments = granteeData.supportingDocuments.map(doc =>
            doc.id === selectedDocument.id ? {
              ...doc,
              status: "update-required" as "update-required",
              comments: documentComments
            } : doc
          );

          setGranteeData({
            ...granteeData,
            supportingDocuments: updatedDocuments
          });
        }

        toast.success("Document rejected with comments");
        setIsReviewDialogOpen(false);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error rejecting document:", error);
      toast.error("Failed to reject document");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get status badge for documents
  const getDocumentStatusBadge = (status: string) => {
    switch (status) {
      case "verified":
        return <Badge className="bg-green-100 text-green-800 flex items-center gap-1"><CheckCircle className="h-3 w-3" />Verified</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 flex items-center gap-1"><AlertCircle className="h-3 w-3" />Pending</Badge>;
      case "update-required":
        return <Badge className="bg-red-100 text-red-800 flex items-center gap-1"><AlertCircle className="h-3 w-3" />Update Required</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  // Handle viewing expense details
  const handleViewExpense = (expense: ExpenseRecord) => {
    // Store expense data for the detail page
    localStorage.setItem('selectedExpense', JSON.stringify(expense));
    localStorage.setItem('granteeId', id);
    localStorage.setItem('returnTab', 'expenses'); // Store current tab
    localStorage.setItem('returnUrl', window.location.pathname + window.location.search); // Store current URL

    // Navigate to expense detail page
    window.location.href = `/grantmaker/expense-detail/${expense.id}`;
  };

  if (isLoading || !granteeData) {
    return (
      <Layout title="Grantee Details">
        <div className="flex flex-col justify-center items-center min-h-[400px] gap-4">
          <div className="h-12 w-12 rounded-full border-4 border-t-[#6366F1] border-r-[#6366F1] border-b-indigo-200 border-l-indigo-200 animate-spin"></div>
          <div className="text-[#6366F1] font-medium bg-gradient-to-r from-[#6366F1] to-indigo-500 bg-clip-text text-transparent animate-pulse">Preparing grantee dashboard...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title={granteeData.name}>
      <div className="space-y-8 pb-8">
        {/* Header with back button */}
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            className="mr-4 p-2"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">{granteeData.name}</h1>
            <div className="flex items-center mt-1">
              <Badge className={`
                ${granteeData.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                ${granteeData.status === 'pending' ? 'bg-amber-100 text-amber-800' : ''}
                ${granteeData.status === 'completed' ? 'bg-blue-100 text-blue-800' : ''}
              `}>
                {granteeData.status.charAt(0).toUpperCase() + granteeData.status.slice(1)}
              </Badge>
              <span className="mx-2 text-gray-400">|</span>
              <span className="text-gray-600">{granteeData.sector}</span>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ staggerChildren: 0.1 }}
        >
          <FundingSummaryCard
            title="Total Grant Amount"
            amount={formatCurrency(granteeData.totalFunding)}
            icon={<CurrencyDollarIcon className="w-5 h-5" />}
            accentColor="teal"
            description="Total funding allocated to this grantee"
          />

          <FundingSummaryCard
            title="Disbursed Amount"
            amount={formatCurrency(granteeData.disbursedAmount)}
            icon={<WalletIcon className="w-5 h-5" />}
            accentColor="emerald"
            description={`${granteeData.utilizationPercentage}% of total grant utilized`}
          />

          <FundingSummaryCard
            title="Remaining Balance"
            amount={formatCurrency(granteeData.remainingAmount)}
            icon={<ChartBarIcon className="w-5 h-5" />}
            accentColor="teal"
            description="Remaining funds to be disbursed"
          />
        </motion.div>

        {/* Tabs for different sections */}
        <Tabs defaultValue="overview" className="mt-8">
          <TabsList className="grid grid-cols-6 w-full max-w-4xl mb-6 bg-gray-100/70 p-1">
            <TabsTrigger
              value="overview"
              className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#6366F1] data-[state=active]:to-indigo-500 data-[state=active]:text-white"
            >
              <Wallet className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="profile"
              className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#6366F1] data-[state=active]:to-indigo-500 data-[state=active]:text-white"
            >
              <User className="h-4 w-4" />
              Profile
            </TabsTrigger>
            <TabsTrigger
              value="documents"
              className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#6366F1] data-[state=active]:to-indigo-500 data-[state=active]:text-white"
            >
              <FileText className="h-4 w-4" />
              Documents
            </TabsTrigger>
            <TabsTrigger
              value="expenses"
              className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#6366F1] data-[state=active]:to-indigo-500 data-[state=active]:text-white"
            >
              <FileSpreadsheet className="h-4 w-4" />
              Expenses
            </TabsTrigger>
            <TabsTrigger
              value="disbursements"
              className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#6366F1] data-[state=active]:to-indigo-500 data-[state=active]:text-white"
            >
              <Calendar className="h-4 w-4" />
              Disbursements
            </TabsTrigger>
            <TabsTrigger
              value="reports"
              className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#6366F1] data-[state=active]:to-indigo-500 data-[state=active]:text-white"
            >
              <Users className="h-4 w-4" />
              Reports
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Expense Breakdown Chart */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
              >
                <Card className="shadow-md border-0 rounded-lg overflow-hidden h-full">
                  <div className="h-1.5 bg-gradient-to-r from-[#6366F1] via-indigo-500 to-violet-400"></div>
                  <CardHeader>
                    <CardTitle className="text-xl font-semibold text-gray-800">Expense by Category</CardTitle>
                    <CardDescription>Breakdown of expenses by category</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={granteeData.expenseBreakdown}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={120}
                            fill="#8884d8"
                            dataKey="value"
                            nameKey="name"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            {granteeData.expenseBreakdown.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip content={<CustomTooltip />} />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Monthly Expense Chart */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.1 }}
              >
                <Card className="shadow-md border-0 rounded-lg overflow-hidden h-full">
                  <div className="h-1.5 bg-gradient-to-r from-[#6366F1] via-indigo-500 to-violet-400"></div>
                  <CardHeader>
                    <CardTitle className="text-xl font-semibold text-gray-800">Monthly Expense Trends</CardTitle>
                    <CardDescription>Budget vs. actual expenses by month</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={granteeData.monthlyExpenses}
                          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                          barSize={20}
                        >
                          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                          <XAxis dataKey="month" tick={{ fill: '#6B7280' }} />
                          <YAxis
                            tickFormatter={(value) => `₹${value / 1000}k`}
                            tick={{ fill: '#6B7280' }}
                          />
                          <Tooltip content={<CustomTooltip />} />
                          <Legend />
                          <Bar
                            dataKey="budget"
                            name="Budgeted Amount"
                            fill="#6366F1"
                            radius={[4, 4, 0, 0]}
                          />
                          <Bar
                            dataKey="actual"
                            name="Actual Expense"
                            fill="#A5B4FC"
                            radius={[4, 4, 0, 0]}
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </TabsContent>

          {/* Profile Tab */}
          <TabsContent value="profile" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4 }}
            >
              {/* Organization Information */}
              <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden mb-6">
                <div className="h-1.5 bg-gradient-to-r from-[#6366F1] via-indigo-500 to-violet-400"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-xl font-semibold text-gray-800">Organization Information</CardTitle>
                      <CardDescription>Basic details about the organization</CardDescription>
                    </div>
                    <User className="h-8 w-8 text-[#6366F1] opacity-80" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">Organization Name</h3>
                      <p className="mt-1 text-gray-900 font-medium">{granteeData.organization_name}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">Organization Type</h3>
                      <p className="mt-1 text-gray-900 font-medium">{granteeData.organization_legal_type_name}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">Function Type</h3>
                      <p className="mt-1 text-gray-900 font-medium">{granteeData.organization_function_type_name}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">PAN Number</h3>
                      <p className="mt-1 text-gray-900 font-medium">{granteeData.pan_number}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">Email</h3>
                      <p className="mt-1 text-gray-900 font-medium">{granteeData.email_address}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">Phone</h3>
                      <p className="mt-1 text-gray-900 font-medium">{granteeData.phone_number}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">Website</h3>
                      <p className="mt-1 text-gray-900 font-medium">
                        <a href={granteeData.website_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                          {granteeData.website_url}
                        </a>
                      </p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">Mailing Address</h3>
                      <p className="mt-1 text-gray-900 font-medium">{granteeData.mailing_address}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">Team Size</h3>
                      <p className="mt-1 text-gray-900 font-medium">{granteeData.number_of_team_members} members</p>
                    </div>
                  </div>

                  <div className="mt-6 space-y-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500 mb-2">Mission & Vision</h3>
                      <p className="text-gray-900">{granteeData.mission_vision}</p>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500 mb-2">Background & History</h3>
                      <p className="text-gray-900">{granteeData.background_history}</p>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500 mb-2">Previous Grant History</h3>
                      {granteeData.previous_grants_info && <p className="text-gray-900">{granteeData.previous_grants_info}</p>}
                      {granteeData.grant_history && granteeData.grant_history.length > 0 && (
                        <div className="mt-4 space-y-3">
                          {granteeData.grant_history.map((grant) => (
                            <div key={grant.id} className="bg-white p-3 rounded-md border border-gray-100 hover:shadow-sm transition-all duration-200">
                              <div className="flex justify-between items-start">
                                <div>
                                  <h4 className="text-sm font-medium text-gray-800">{grant.grant_name}</h4>
                                  <p className="text-xs text-gray-500">
                                    {new Date(grant.start_date).toLocaleDateString('en-IN')}
                                    {grant.end_date && ` - ${new Date(grant.end_date).toLocaleDateString('en-IN')}`}
                                  </p>
                                </div>
                                <div className="flex flex-col items-end">
                                  <Badge className={`text-xs ${grant.status === 'active' ? 'bg-green-100 text-green-800' : ''} ${grant.status === 'pending' ? 'bg-amber-100 text-amber-800' : ''} ${grant.status === 'completed' ? 'bg-blue-100 text-blue-800' : ''}`}>
                                    {grant.status.charAt(0).toUpperCase() + grant.status.slice(1)}
                                  </Badge>
                                  <p className="text-xs font-medium text-gray-700 mt-1">{formatCurrency(grant.amount)}</p>
                                </div>
                              </div>
                              <div className="mt-2">
                                <p className="text-xs text-gray-700">{grant.purpose}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Core Leadership Team Details */}
              <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden mb-6">
                <div className="h-1.5 bg-gradient-to-r from-[#6366F1] via-indigo-500 to-violet-400"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-xl font-semibold text-gray-800">Core Leadership Team</CardTitle>
                      <CardDescription>Details of core leadership team members in the organization</CardDescription>
                    </div>
                    <User2 className="h-8 w-8 text-[#6366F1] opacity-80" />
                  </div>
                </CardHeader>
                <CardContent>
                  {granteeData.kmp_details && granteeData.kmp_details.length > 0 ? (
                    <div className="space-y-4">
                      {granteeData.kmp_details.map((kmp) => (
                        <div key={kmp.id} className="bg-gray-50 p-4 rounded-lg">
                          <div className="flex justify-between items-center mb-2">
                            <h3 className="text-md font-medium text-gray-800">{kmp.name}</h3>
                            <Badge className="bg-blue-100 text-blue-800">{kmp.designation}</Badge>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                            <div>
                              <span className="text-gray-500">Email:</span>{" "}
                              <span className="text-gray-900">{kmp.email}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Phone:</span>{" "}
                              <span className="text-gray-900">{kmp.phone}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">DIN:</span>{" "}
                              <span className="text-gray-900">{kmp.din}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6 text-gray-500">No KMP details available</div>
                  )}
                </CardContent>
              </Card>

              {/* Registration Details */}
              <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden mb-6">
                <div className="h-1.5 bg-gradient-to-r from-[#6366F1] via-indigo-500 to-violet-400"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-xl font-semibold text-gray-800">Registration Details</CardTitle>
                      <CardDescription>Legal and registration information</CardDescription>
                    </div>
                    <DocumentIcon className="h-8 w-8 text-[#6366F1] opacity-80" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">Trust Registration Number</h3>
                      <p className="mt-1 text-gray-900 font-medium">{granteeData.trust_registration_number || 'Not Applicable'}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">Darpan ID</h3>
                      <p className="mt-1 text-gray-900 font-medium">{granteeData.darpan_id || 'Not Available'}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">FCRA Registration Number</h3>
                      <p className="mt-1 text-gray-900 font-medium">{granteeData.fcra_registration_number || 'Not Available'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Grant Details */}
              <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden">
                <div className="h-1.5 bg-gradient-to-r from-[#6366F1] via-indigo-500 to-violet-400"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-xl font-semibold text-gray-800">Grant Details</CardTitle>
                      <CardDescription>Information about the current grant</CardDescription>
                    </div>
                    <WalletIcon className="h-8 w-8 text-[#6366F1] opacity-80" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">Grant ID</h3>
                      <p className="mt-1 text-gray-900 font-medium">{granteeData.grant_id}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">Grant Name</h3>
                      <p className="mt-1 text-gray-900 font-medium">{granteeData.grant_name}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">Project Timeline</h3>
                      <p className="mt-1 text-gray-900 font-medium">
                        {granteeData.project_start_date && new Date(granteeData.project_start_date).toLocaleDateString('en-IN')}
                        {granteeData.project_end_date && ` - ${new Date(granteeData.project_end_date).toLocaleDateString('en-IN')}`}
                      </p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500">Annual Budget</h3>
                      <p className="mt-1 text-gray-900 font-medium">{granteeData.annual_budget ? formatCurrency(granteeData.annual_budget) : formatCurrency(granteeData.totalFunding)}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg col-span-2">
                      <h3 className="text-sm font-medium text-gray-500">Purpose of Grant</h3>
                      <p className="mt-1 text-gray-900">{granteeData.purpose_of_grant}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg col-span-2">
                      <h3 className="text-sm font-medium text-gray-500">Other Funding Sources</h3>
                      <p className="mt-1 text-gray-900">{granteeData.funding_sources || 'None'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          {/* Expenses Tab */}
          <TabsContent value="expenses" className="space-y-6">
            <Card className="shadow-md border-0 rounded-lg overflow-hidden">
              <div className="h-1.5 bg-gradient-to-r from-[#6366F1] via-indigo-500 to-violet-400"></div>
              <CardHeader>
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                  <div>
                    <CardTitle className="text-xl font-semibold text-gray-800">Expense Records</CardTitle>
                    <CardDescription>Detailed list of all expense entries</CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex items-center gap-1 mt-2 sm:mt-0">
                      <Download className="h-4 w-4" />
                      Export
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="bg-gray-50 border-b border-gray-200">
                          <th className="text-left p-4 font-medium text-gray-600">ID</th>
                          <th className="text-left p-4 font-medium text-gray-600">Date</th>
                          <th className="text-left p-4 font-medium text-gray-600">Category</th>
                          <th className="text-left p-4 font-medium text-gray-600">Budget</th>
                          <th className="text-left p-4 font-medium text-gray-600">Actual Spent</th>
                          <th className="text-left p-4 font-medium text-gray-600">Status</th>
                          <th className="text-left p-4 font-medium text-gray-600">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {granteeData.expenseRecords.length > 0 ? (
                          granteeData.expenseRecords.map((item, index) => (
                            <tr key={index} className="border-b last:border-0 hover:bg-gray-50 transition-colors">
                              <td className="p-4 font-medium text-gray-800">
                                <button
                                  onClick={() => handleViewExpense(item)}
                                  className="text-blue-600 hover:underline font-medium"
                                >
                                  {item.id}
                                </button>
                              </td>
                              <td className="p-4 text-gray-700">{item.loggedDate}</td>
                              <td className="p-4 text-gray-700">{item.category}</td>
                              <td className="p-4 font-medium text-gray-800">{formatCurrency(item.totalBudget)}</td>
                              <td className="p-4 font-medium text-gray-800">{formatCurrency(item.totalActualSpent)}</td>
                              <td className="p-4">
                                <span className={`px-2 py-1 rounded-full text-xs ${item.status === 'Verified' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                                  {item.status}
                                </span>
                              </td>
                              <td className="p-4">
                                <div className="flex gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-blue-600 hover:text-blue-800 p-1 h-auto"
                                    onClick={() => handleViewExpense(item)}
                                  >
                                    <FileSpreadsheet className="h-4 w-4" />
                                    <span className="sr-only">View Details</span>
                                  </Button>
                                  {item.source_type === 'excel' && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="text-green-600 hover:text-green-800 p-1 h-auto"
                                      onClick={() => window.open(`/api/expenses/${item.id}/download`, '_blank')}
                                    >
                                      <Download className="h-4 w-4" />
                                      <span className="sr-only">Download</span>
                                    </Button>
                                  )}
                                </div>
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={7} className="p-8 text-center text-gray-500">
                              No expense records found.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Disbursements Tab */}
          <TabsContent value="disbursements" className="space-y-6">
            <Card className="shadow-md border-0 rounded-lg overflow-hidden">
              <div className="h-1.5 bg-gradient-to-r from-[#6366F1] via-indigo-500 to-violet-400"></div>
              <CardHeader>
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                  <div>
                    <CardTitle className="text-xl font-semibold text-gray-800">Disbursement History</CardTitle>
                    <CardDescription>Detailed list of all disbursements</CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex items-center gap-1 mt-2 sm:mt-0">
                      <Download className="h-4 w-4" />
                      Export
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="bg-gray-50 border-b border-gray-200">
                          <th className="text-left p-4 font-medium text-gray-600">Grant Name</th>
                          <th className="text-left p-4 font-medium text-gray-600">Date</th>
                          <th className="text-left p-4 font-medium text-gray-600">Amount</th>
                          <th className="text-left p-4 font-medium text-gray-600">Status</th>

                          <th className="text-left p-4 font-medium text-gray-600">Acknowledgement</th>
                        </tr>
                      </thead>
                      <tbody>
                        {granteeData.disbursementHistory.length > 0 ? (
                          granteeData.disbursementHistory.map((item, index) => (
                            <tr key={index} className="border-b last:border-0 hover:bg-gray-50 transition-colors">
                              <td className="p-4">
                                <div className="font-medium text-gray-800">{item.grantName}</div>
                                <div className="text-xs text-gray-500">
                                  <Link href={`/grantmaker/disbursement/${item.id}`} className="text-blue-600 hover:underline">
                                    ID: {item.id}
                                  </Link>
                                </div>
                              </td>
                              <td className="p-4 text-gray-700">{item.date}</td>
                              <td className="p-4 font-medium text-gray-800">{formatCurrency(item.amount)}</td>
                              <td className="p-4">
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  item.status === 'Disbursed' ? 'bg-green-100 text-green-800' :
                                  item.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                                  item.status === 'Failed' ? 'bg-red-100 text-red-800' :
                                  'bg-gray-100 text-gray-800'}`}>
                                  {item.status}
                                </span>
                              </td>

                              <td className="p-4">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-1">
                                    {item.acknowledgement === 'Completed' ? (
                                      <CheckCircle className="h-4 w-4 text-green-500" />
                                    ) : item.acknowledgement === 'Pending' ? (
                                      <AlertCircle className="h-4 w-4 text-yellow-500" />
                                    ) : null}
                                    <span className={`text-sm ${item.acknowledgement === 'Completed' ? 'text-green-600' : item.acknowledgement === 'Pending' ? 'text-yellow-600' : 'text-blue-600'}`}>
                                      {item.acknowledgement}
                                    </span>
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-blue-600 hover:text-blue-800 p-1 h-auto ml-2"
                                    onClick={() => router.push(`/grantmaker/disbursement/${item.id}`)}
                                  >
                                    <FileText className="h-4 w-4" />
                                    <span className="sr-only">View Details</span>
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={5} className="p-8 text-center text-gray-500">
                              No disbursement history found.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Documents Tab */}
          <TabsContent value="documents" className="space-y-6">
            <Card className="shadow-md border-0 rounded-lg overflow-hidden">
              <div className="h-1 bg-gradient-to-r from-orange-500 to-amber-400"></div>
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-gray-800">Institutional Records</CardTitle>
                <CardDescription>Review and manage organization documents</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Group documents by type */}
                  {isLoading ? (
                    <AnimatedLoader type="document" message="Loading document data..." />
                  ) : granteeData.supportingDocuments && granteeData.supportingDocuments.length > 0 ? (
                    <>
                      {/* Compliance Documents */}
                      <div>
                        <h3 className="text-lg font-medium mb-3">Compliance Documents</h3>
                        <div className="space-y-3">
                          {granteeData.supportingDocuments
                            .filter(doc => ['registration', 'tax-exemption', 'fcra'].includes(doc.attachment_type))
                            .map(doc => (
                              <div key={doc.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-md hover:shadow-md transition-all duration-200 hover:border-orange-200 bg-white">
                                <div className="flex items-center">
                                  <div className="bg-orange-50 p-2 rounded-full mr-3">
                                    <FileText className="h-5 w-5 text-orange-500" />
                                  </div>

                                  <div>
                                    <p className="font-medium">{doc.name}</p>
                                    {getDocumentStatusBadge(doc.status)}
                                    {doc.status === "update-required" && doc.comments && (
                                      <p className="text-sm text-red-500 mt-1">{doc.comments}</p>
                                    )}
                                  </div>
                                </div>
                                {doc.status === "pending" ? (
                                  <div className="flex gap-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-orange-600 hover:bg-orange-50 hover:text-orange-700 flex items-center gap-1 transition-all duration-300 hover:shadow-md border-orange-200 hover:border-orange-300 relative overflow-hidden group"
                                      onClick={() => handleReviewDocument(doc)}
                                    >
                                      <span className="absolute inset-0 w-0 bg-orange-100 transition-all duration-300 ease-out group-hover:w-full opacity-80"></span>
                                      <Eye className="h-3 w-3 relative z-10" />
                                      <span className="relative z-10">Review</span>
                                    </Button>
                                  </div>
                                ) : (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-orange-600 hover:bg-orange-50 hover:text-orange-700 flex items-center gap-1 transition-all duration-300 hover:shadow-md border-orange-200 hover:border-orange-300 relative overflow-hidden group"
                                    onClick={() => doc.url && window.open(doc.url, "_blank")}
                                  >
                                    <span className="absolute inset-0 w-0 bg-orange-100 transition-all duration-300 ease-out group-hover:w-full opacity-80"></span>
                                    <Eye className="h-3 w-3 relative z-10" />
                                    <span className="relative z-10">View</span>
                                  </Button>
                                )}
                              </div>
                            ))
                          }
                        </div>
                      </div>

                      {/* Financial Documents */}
                      <div className="mt-6">
                        <h3 className="text-lg font-medium mb-3">Financial Documents</h3>
                        <div className="space-y-3">
                          {granteeData.supportingDocuments
                            .filter(doc => ['report', 'financial'].includes(doc.attachment_type))
                            .map(doc => (
                              <div key={doc.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-md hover:shadow-md transition-all duration-200 hover:border-orange-200 bg-white">
                                <div className="flex items-center">
                                  <div className="bg-orange-50 p-2 rounded-full mr-3">
                                    <FileText className="h-5 w-5 text-orange-500" />
                                  </div>
                                  <div>
                                    <p className="font-medium">{doc.name}</p>
                                    {getDocumentStatusBadge(doc.status)}
                                    {doc.status === "update-required" && doc.comments && (
                                      <p className="text-sm text-red-500 mt-1">{doc.comments}</p>
                                    )}
                                  </div>
                                </div>
                                {doc.status === "pending" ? (
                                  <div className="flex gap-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-orange-600 hover:bg-orange-50 hover:text-orange-700 flex items-center gap-1 transition-all duration-300 hover:shadow-md border-orange-200 hover:border-orange-300 relative overflow-hidden group"
                                      onClick={() => handleReviewDocument(doc)}
                                    >
                                      <span className="absolute inset-0 w-0 bg-orange-100 transition-all duration-300 ease-out group-hover:w-full opacity-80"></span>
                                      <Eye className="h-3 w-3 relative z-10" />
                                      <span className="relative z-10">Review</span>
                                    </Button>
                                  </div>
                                ) : (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-orange-600 hover:bg-orange-50 hover:text-orange-700 flex items-center gap-1 transition-all duration-300 hover:shadow-md border-orange-200 hover:border-orange-300 relative overflow-hidden group"
                                    onClick={() => doc.url && window.open(doc.url, "_blank")}
                                  >
                                    <span className="absolute inset-0 w-0 bg-orange-100 transition-all duration-300 ease-out group-hover:w-full opacity-80"></span>
                                    <Eye className="h-3 w-3 relative z-10" />
                                    <span className="relative z-10">View</span>
                                  </Button>
                                )}
                              </div>
                            ))
                          }
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <FileText className="h-16 w-16 text-gray-300 mb-4" />
                      <h3 className="text-lg font-medium text-gray-700 mb-2">No documents available</h3>
                      <p className="text-gray-500 max-w-md">No supporting documents have been uploaded by this organization yet.</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Document Review Dialog */}
            <Dialog open={isReviewDialogOpen} onOpenChange={setIsReviewDialogOpen}>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle className="text-xl font-semibold text-gray-800">Document Verification</DialogTitle>
                  <DialogDescription>
                    Review and verify the uploaded document
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-4 py-4">
                  {selectedDocument && (
                    <div className="bg-gradient-to-r from-orange-50 to-amber-50 p-4 rounded-lg border border-orange-100 shadow-sm">
                      <div className="flex items-center gap-3">
                        <div className="bg-white p-2 rounded-full shadow-sm">
                          <FileText className="h-8 w-8 text-orange-500" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900">{selectedDocument.name}</h3>
                          <p className="text-sm text-gray-500">Uploaded on {new Date(selectedDocument.uploadedAt).toLocaleDateString('en-IN')}</p>
                        </div>
                        <div className="animate-pulse">
                          {getDocumentStatusBadge(selectedDocument.status)}
                        </div>
                      </div>
                    </div>
                  )}

                  {selectedDocument?.url && (
                    <div className="flex justify-center my-4">
                      <div className="bg-gray-50 p-6 rounded-lg w-full flex flex-col items-center justify-center border border-gray-100">
                        <FileText className="h-12 w-12 text-gray-400 mb-2" />
                        <p className="text-sm text-gray-600 mb-3">Preview document before verification</p>
                        <Button
                          variant="outline"
                          className="flex items-center gap-2 bg-white hover:bg-gray-50 transition-all duration-200"
                          onClick={() => window.open(selectedDocument.url, "_blank")}
                        >
                          <Eye className="h-4 w-4" />
                          View Document
                        </Button>
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700">Verification Comments</h4>
                    <Textarea
                      placeholder="Provide comments about this document, especially if rejecting"
                      value={documentComments}
                      onChange={(e) => setDocumentComments(e.target.value)}
                      rows={4}
                      className="resize-none focus:ring-orange-500 focus:border-orange-500"
                    />
                  </div>
                </div>

                <DialogFooter className="flex justify-between sm:justify-between mt-4">
                  <Button
                    type="button"
                    variant="destructive"
                    onClick={handleRejectDocument}
                    disabled={isSubmitting}
                    className="flex items-center gap-2 transition-all duration-200 hover:bg-red-700"
                  >
                    <XCircle className="h-4 w-4" />
                    Reject Document
                  </Button>
                  <Button
                    type="button"
                    variant="default"
                    className="bg-green-600 hover:bg-green-700 flex items-center gap-2 transition-all duration-200"
                    onClick={handleApproveDocument}
                    disabled={isSubmitting}
                  >
                    <CheckCircle className="h-4 w-4" />
                    Approve Document
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-6">
            <Card className="shadow-md border-0 rounded-lg overflow-hidden">
              <div className="h-1 bg-gradient-to-r from-orange-500 to-amber-400"></div>
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-gray-800">Reports</CardTitle>
                <CardDescription>Access all reports for this grantee</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <FileText className="h-16 w-16 text-gray-300 mb-4" />
                  <h3 className="text-lg font-medium text-gray-700 mb-2">No reports available yet</h3>
                  <p className="text-gray-500 max-w-md">Reports for this grantee will appear here once they are submitted.</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Removed unnecessary action buttons as requested */}
      </div>
    </Layout>
  );
}