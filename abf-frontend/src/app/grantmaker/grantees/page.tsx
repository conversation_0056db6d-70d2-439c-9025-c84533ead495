"use client";

import { Suspense, useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Layout from "@/components/grantmaker/Layout";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { User, getAllUsers } from "@/services/grantmaker/user-service";
import { GranteeUserTable } from "@/components/grantmaker/GranteeUserTable";
import { Organization } from "@/types/profile";
import { getAllOrganizations } from "@/services/grantmaker/organization-service";
import { transformOrganizationListAPIResponse } from "@/services/grantmaker/organization-service";

export default function GranteesPage() {
  return (
    <Suspense fallback={<Layout title="Grantees"><div className="p-10 text-center text-gray-500">Loading grantees...</div></Layout>}>
      <GranteesPageInner />
    </Suspense>
  );
}

function GranteesPageInner() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [grantees, setGrantees] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [error, setError] = useState("");

  useEffect(() => {
    const isLoggedIn = localStorage.getItem("isLoggedIn");

    if (isLoggedIn !== "true") {
      router.push("/grantmaker/login");
      return;
    }

    const fetchGrantees = async () => {
      setLoading(true);
      try {
        const organizations = await getAllOrganizations();
        const frontendData = transformOrganizationListAPIResponse(organizations.data)
        console.log(JSON.stringify(organizations));
        setOrganizations(frontendData);
        // TODO: IMP use code instead of type
        // Filter to only include grantee users (type = 1)
      } catch (err) {
        console.error("Error fetching grantees:", err);
        setError("Failed to load grantees");
      } finally {
        setLoading(false);
      }
    };

    fetchGrantees();
  }, [router, searchParams]);



  const handleViewDetails = (granteeId: number) => {
    router.push(`/grantmaker/grantees/${granteeId}/profile`);
  };

  if (loading) {
    return (
      <Layout title="Grantees">
        <div className="w-full max-w-7xl mx-auto flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-solid border-orange-400 border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
            <p className="mt-4 text-lg text-gray-600">Loading grantees...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout title="Grantees">
        <div className="w-full max-w-7xl mx-auto">
          <Card className="bg-white shadow-md hover:shadow-lg transition-shadow mb-8">
            <CardContent className="p-8 text-center">
              <div className="text-red-500 text-xl mb-4">{error}</div>
              <Button onClick={() => window.location.reload()} className="bg-gradient-to-r from-orange-500 via-amber-500 to-orange-400 hover:from-orange-600 hover:via-amber-600 hover:to-orange-500 text-white">
                Retry Loading
              </Button>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Grantees">
      <div className="w-full max-w-7xl mx-auto">
        {loading ? (
          <div className="flex justify-center items-center h-[calc(100vh-6rem)]">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          </div>
        ) : error ? (
          <Card className="bg-white shadow-md hover:shadow-lg transition-shadow mb-8">
            <CardContent className="p-8 text-center">
              <div className="text-red-500 text-xl mb-4">{error}</div>
              <Button onClick={() => window.location.reload()} className="bg-gradient-to-r from-orange-500 via-amber-500 to-orange-400 hover:from-orange-600 hover:via-amber-600 hover:to-orange-500 text-white">
                Retry Loading
              </Button>
            </CardContent>
          </Card>
        ) : (
          <GranteeUserTable
            users={organizations}
            onViewDetails={handleViewDetails}
            title="All Grantees"
            description="View and manage all grantee users in the system"
          />
        )}
      </div>
    </Layout>
  );
}
