"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Layout from "@/components/grantmaker/Layout";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Calendar, DollarSign, MapPin, Mail, Phone, User } from "lucide-react";
import { Organization, getOrganizationDetails, formatCurrency } from "@/services/grantmaker/grantmaker-service";
import { Badge } from "@/components/ui/badge";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from "recharts";
import React from "react";

type Params = {
  id: string
};

export default function OrganizationDetailsPage({ params }: { params: Promise<Params> }) {
  // Unwrap params outside of any try/catch
  const { id } = React.use(params);

  const router = useRouter();
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const isLoggedIn = localStorage.getItem("isLoggedIn");

    if (isLoggedIn !== "true") {
      router.push("/login");
      return;
    }

    const fetchOrganizationDetails = async () => {
      setLoading(true);

      try {
        const data = await getOrganizationDetails(id);
        if (data) {
          setOrganization(data);
        } else {
          setError("Organization not found");
        }
      } catch (err) {
        console.error("Error fetching organization details:", err);
        setError("Failed to load organization details");
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizationDetails();
  }, [router, id]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "completed":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  // Mock data for charts
  const fundingData = [
    { name: 'Q1', amount: organization?.totalFunding ? organization.totalFunding * 0.3 : 0 },
    { name: 'Q2', amount: organization?.totalFunding ? organization.totalFunding * 0.25 : 0 },
    { name: 'Q3', amount: organization?.totalFunding ? organization.totalFunding * 0.35 : 0 },
    { name: 'Q4', amount: organization?.totalFunding ? organization.totalFunding * 0.1 : 0 },
  ];

  const milestoneData = [
    { name: 'Completed', value: 65, color: '#10B981' },
    { name: 'In Progress', value: 25, color: '#F59E0B' },
    { name: 'Upcoming', value: 10, color: '#6366F1' },
  ];

  const COLORS = ['#10B981', '#F59E0B', '#6366F1'];

  if (loading) {
    return (
      <Layout title="Organization Details">
        <div className="flex justify-center items-center h-[calc(100vh-6rem)]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
        </div>
      </Layout>
    );
  }

  if (error || !organization) {
    return (
      <Layout title="Organization Details">
        <div className="w-full max-w-7xl mx-auto">
          <Card className="bg-white shadow-md hover:shadow-lg transition-shadow mb-8">
            <CardContent className="p-8 text-center">
              <div className="text-red-500 text-xl mb-4">{error || "Organization not found"}</div>
              <Button onClick={() => router.back()} className="bg-gradient-to-r from-orange-500 via-amber-500 to-orange-400 hover:from-orange-600 hover:via-amber-600 hover:to-orange-500 text-white">
                Go Back
              </Button>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title={organization.name}>
      <div className="w-full max-w-7xl mx-auto">
        <div className="mb-6">
          <Button
            variant="ghost"
            className="flex items-center text-gray-600 hover:text-gray-900"
            onClick={() => router.back()}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Organizations
          </Button>
        </div>

        {/* Organization Header */}
        <Card className="bg-white shadow-md hover:shadow-lg transition-shadow mb-8 overflow-hidden">
          <div className="h-2 bg-gradient-to-r from-orange-500 via-amber-500 to-orange-400"></div>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{organization.name}</h1>
                <div className="flex items-center mt-2">
                  <Badge className={`${getStatusColor(organization.status)} capitalize mr-2`}>
                    {organization.status}
                  </Badge>
                  <span className="text-gray-500 flex items-center">
                    <MapPin className="h-4 w-4 mr-1" />
                    {organization.location}
                  </span>
                </div>
              </div>
              <div className="flex flex-col items-end">
                <div className="text-sm text-gray-500">Total Funding</div>
                <div className="text-2xl font-bold text-gray-900">{formatCurrency(organization.totalFunding)}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Organization Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* Contact Information */}
          <Card className="bg-white shadow-md hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-lg text-gray-800">Contact Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start">
                  <User className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">{organization.contactPerson}</div>
                    <div className="text-sm text-gray-500">Primary Contact</div>
                  </div>
                </div>
                <div className="flex items-start">
                  <Mail className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">{organization.contactEmail}</div>
                    <div className="text-sm text-gray-500">Email</div>
                  </div>
                </div>
                <div className="flex items-start">
                  <Phone className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">+91 98765 43210</div>
                    <div className="text-sm text-gray-500">Phone</div>
                  </div>
                </div>
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">{organization.location}</div>
                    <div className="text-sm text-gray-500">Location</div>
                  </div>
                </div>
                <div className="flex items-start">
                  <Calendar className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">{new Date(organization.startDate).toLocaleDateString()}</div>
                    <div className="text-sm text-gray-500">Start Date</div>
                  </div>
                </div>
                {organization.endDate && (
                  <div className="flex items-start">
                    <Calendar className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                    <div>
                      <div className="font-medium text-gray-900">{new Date(organization.endDate).toLocaleDateString()}</div>
                      <div className="text-sm text-gray-500">End Date</div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Funding Distribution */}
          <Card className="bg-white shadow-md hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-lg text-gray-800">Funding Distribution</CardTitle>
              <CardDescription>Quarterly breakdown</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={fundingData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                    <XAxis dataKey="name" />
                    <YAxis tickFormatter={(value) => `₹${value / 1000}k`} />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Bar dataKey="amount" fill="#FF8C00" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Milestone Status */}
          <Card className="bg-white shadow-md hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-lg text-gray-800">Milestone Status</CardTitle>
              <CardDescription>Progress overview</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={milestoneData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={80}
                      fill="#8884d8"
                      paddingAngle={5}
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {milestoneData.map((_, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card className="bg-white shadow-md hover:shadow-lg transition-shadow mb-8">
          <CardHeader>
            <CardTitle className="text-lg text-gray-800">Recent Activity</CardTitle>
            <CardDescription>Latest updates and milestones</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-start pb-4 border-b border-gray-100 last:border-0">
                  <div className="flex-shrink-0 mr-4">
                    <div className="w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center">
                      {i === 1 ? (
                        <DollarSign className="h-5 w-5 text-orange-600" />
                      ) : i === 2 ? (
                        <Calendar className="h-5 w-5 text-orange-600" />
                      ) : (
                        <Mail className="h-5 w-5 text-orange-600" />
                      )}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">
                      {i === 1
                        ? "Funding Disbursement"
                        : i === 2
                        ? "Milestone Completed"
                        : "Report Submitted"}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      {i === 1
                        ? `Disbursed ${formatCurrency(organization.totalFunding * 0.25)} for Q2 activities`
                        : i === 2
                        ? "Completed the baseline survey milestone"
                        : "Quarterly progress report submitted"}
                    </div>
                    <div className="text-xs text-gray-400 mt-1">
                      {i === 1
                        ? "2 days ago"
                        : i === 2
                        ? "1 week ago"
                        : "2 weeks ago"}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}
