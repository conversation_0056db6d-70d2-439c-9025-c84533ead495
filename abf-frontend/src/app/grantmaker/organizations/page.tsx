"use client";

import { Suspense, useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Layout from "@/components/grantmaker/Layout";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Search, Filter, ArrowUpDown, Download, Grid, List } from "lucide-react";
import { getAllOrganizations, formatCurrency, transformOrganizationListAPIResponse } from "@/services/grantmaker/organization-service";
import { Organization } from "@/types/profile";
import { Badge } from "@/components/ui/badge";

export default function OrganizationsPage() {
  return(
    <Suspense fallback={<Layout title="Organizations"><div className="p-10 text-center text-gray-500">Loading grantees...</div></Layout>}>
      <OrganizationsPageInner/>
    </Suspense>

  )
}

function OrganizationsPageInner() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState<keyof Organization>("organizationName");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [viewMode, setViewMode] = useState<"list" | "grid">("list");
  const [selectedSector, setSelectedSector] = useState<string | null>(null);

  useEffect(() => {
    const isLoggedIn = localStorage.getItem("isLoggedIn");

    if (isLoggedIn !== "true") {
      router.push("/grantmaker/login");
      return;
    }

    const sector = searchParams.get("sector");
    if (sector) {
      setSelectedSector(sector);
    }

    const fetchOrganizations = async () => {
      setLoading(true);
      try {
        const organizations = await getAllOrganizations();
        const frontendData = transformOrganizationListAPIResponse(organizations.data)
        console.log(JSON.stringify(organizations));
        setOrganizations(frontendData);
      } catch (err) {
        console.error("Error fetching organizations:", err);
        setError("Failed to load organizations");
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizations();
  }, [router, searchParams]);

  const handleSort = (field: keyof Organization) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const filteredOrganizations = organizations.filter(org => {
    const matchesSearch =
      org.organization_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (org.organization_legal_type_name && org.organization_legal_type_name.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesSector = selectedSector
      ? (org.organization_legal_type_name && org.organization_legal_type_name.toLowerCase() === selectedSector.toLowerCase())
      : true;

    return matchesSearch && matchesSector;
  });
  const sortedOrganizations = [...filteredOrganizations].sort((a, b) => {
    if (sortField === "totalBudget") {
      return sortDirection === "asc"
        ? (a.totalBudget || 0) - (b.totalBudget || 0)
        : (b.totalBudget || 0) - (a.totalBudget || 0);
    }
    const aValue = a[sortField]?.toString().toLowerCase() || "";
    const bValue = b[sortField]?.toString().toLowerCase() || "";

    if (sortDirection === "asc") {
      return aValue.localeCompare(bValue);
    } else {
      return bValue.localeCompare(aValue);
    }
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "completed":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const handleViewDetails = (organizationId: number) => {
    router.push(`/grantmaker/organizations/${organizationId}`);
  };

  const clearSectorFilter = () => {
    setSelectedSector(null);
    router.push("/grantmaker/organizations");
  };

  if (loading) {
    return (
      <Layout title="Organizations">
        <div className="flex justify-center items-center h-[calc(100vh-6rem)]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout title="Organizations">
        <div className="w-full max-w-7xl mx-auto">
          <Card className="bg-white shadow-md hover:shadow-lg transition-shadow mb-8">
            <CardContent className="p-8 text-center">
              <div className="text-red-500 text-xl mb-4">{error}</div>
              <Button onClick={() => window.location.reload()} className="bg-gradient-to-r from-orange-500 via-amber-500 to-orange-400 hover:from-orange-600 hover:via-amber-600 hover:to-orange-500 text-white">
                Retry Loading
              </Button>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Organizations">
      <div className="w-full max-w-7xl mx-auto">
        <Card className="bg-white shadow-md hover:shadow-lg transition-shadow mb-8">
          <CardHeader>
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div>
                <CardTitle className="text-2xl text-gray-800">
                  {selectedSector ? `${selectedSector} Organizations` : "All Organizations"}
                </CardTitle>
                <CardDescription>
                  {selectedSector ? (
                    <div className="flex items-center gap-2 mt-1">
                      <span>Filtered by sector:</span>
                      <Badge className="bg-orange-100 text-orange-800 border-orange-200">
                        {selectedSector}
                      </Badge>
                      <button
                        onClick={clearSectorFilter}
                        className="text-sm text-orange-600 hover:text-orange-800 underline"
                      >
                        Clear filter
                      </button>
                    </div>
                  ) : (
                    "Manage and view all your grantee organizations"
                  )}
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === "list" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className={viewMode === "list" ? "bg-orange-500 hover:bg-orange-600" : ""}
                >
                  <List className="h-4 w-4 mr-1" />
                  List
                </Button>
                <Button
                  variant={viewMode === "grid" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className={viewMode === "grid" ? "bg-orange-500 hover:bg-orange-600" : ""}
                >
                  <Grid className="h-4 w-4 mr-1" />
                  Grid
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-1" />
                  Export
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
              <div className="relative w-full md:w-auto">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search organizations..."
                  className="h-10 w-full md:w-64 rounded-md border border-gray-300 bg-white pl-10 pr-4 text-sm focus:border-orange-500 focus:outline-none focus:ring-1 focus:ring-orange-500"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="flex items-center gap-2 w-full md:w-auto">
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <Filter className="h-4 w-4" />
                  <span>Filter</span>
                </Button>
              </div>
            </div>

            {viewMode === "list" ? (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="p-3 text-left font-medium text-gray-600 border-b">
                        <button
                          className="flex items-center gap-1 hover:text-gray-900"
                          onClick={() => handleSort("organization_name")}
                        >
                          Name
                          {sortField === "organization_name" && (
                            <ArrowUpDown className="h-4 w-4" />
                          )}
                        </button>
                      </th>
                      <th className="p-3 text-left font-medium text-gray-600 border-b">
                        <button
                          className="flex items-center gap-1 hover:text-gray-900"
                          onClick={() => handleSort("organization_legal_type_name")}
                        >
                          Organization Type
                          {sortField === "organization_legal_type_name" && (
                            <ArrowUpDown className="h-4 w-4" />
                          )}
                        </button>
                      </th>
                      <th className="p-3 text-left font-medium text-gray-600 border-b">
                        <button
                          className="flex items-center gap-1 hover:text-gray-900"
                          onClick={() => handleSort("totalBudget")}
                        >
                          Amount
                          {sortField === "totalBudget" && (
                            <ArrowUpDown className="h-4 w-4" />
                          )}
                        </button>
                      </th>
                      <th className="p-3 text-left font-medium text-gray-600 border-b">
                        <button
                          className="flex items-center gap-1 hover:text-gray-900"
                          onClick={() => handleSort("status")}
                        >
                          Status
                          {sortField === "status" && (
                            <ArrowUpDown className="h-4 w-4" />
                          )}
                        </button>
                      </th>
                      <th className="p-3 text-left font-medium text-gray-600 border-b">Contact</th>
                      <th className="p-3 text-left font-medium text-gray-600 border-b">Email</th>
                      <th className="p-3 text-left font-medium text-gray-600 border-b">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {sortedOrganizations.length > 0 ? (
                      sortedOrganizations.map((org) => (
                        <tr
                          key={org.id}
                          className="hover:bg-gray-50 cursor-pointer transition-colors"
                          onClick={() => handleViewDetails(org.id)}
                        >
                          <td className="p-3 border-b">
                            <div className="font-medium text-gray-900">{org.organization_name}</div>
                            <div className="text-sm text-gray-500">Created: {new Date(org.created_at).toLocaleDateString()}</div>
                          </td>
                          <td className="p-3 border-b text-gray-700">
                            <Badge className="bg-blue-100 text-blue-800">
                              {org.organization_legal_type_name || 'NON_PROFIT'}
                            </Badge>
                          </td>
                          <td className="p-3 border-b font-medium text-gray-900">{formatCurrency(org.totalBudget || 0)}</td>
                          <td className="p-3 border-b">
                            <Badge className="bg-green-100 text-green-800 capitalize">
                              active
                            </Badge>
                          </td>
                          <td className="p-3 border-b">
                            <div className="text-sm">
                              <div className="text-gray-700">{org.phone}</div>
                            </div>
                          </td>
                          <td className="p-3 border-b">
                            <div className="text-sm">
                              <div className="text-blue-600">{org.email}</div>
                            </div>
                          </td>
                          <td className="p-3 border-b">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleViewDetails(org.id);
                              }}
                            >
                              View Details
                            </Button>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={6} className="p-4 text-center text-gray-500">
                          No organizations found matching your search.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {sortedOrganizations.length > 0 ? (
                  sortedOrganizations.map((org) => (
                    <Card
                      key={org.id}
                      className="cursor-pointer hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02]"
                      onClick={() => handleViewDetails(org.id)}
                    >
                      <div className="h-1.5 bg-gradient-to-r from-orange-500 via-amber-500 to-orange-400"></div>
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h3 className="font-semibold text-lg text-gray-900">{org.organization_name}</h3>
                            <p className="text-sm text-gray-500">Created: {new Date(org.created_at).toLocaleDateString()}</p>
                          </div>
                          <Badge className="bg-green-100 text-green-800 capitalize">
                            active
                          </Badge>
                        </div>

                        <div className="space-y-3">
                          <div>
                            <p className="text-sm font-medium text-gray-500">Organization Type</p>
                            <p className="text-gray-700">{org.organization_legal_type_name || 'NON_PROFIT'}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-500">Total Budget</p>
                            <p className="text-gray-900 font-semibold">{formatCurrency(org.totalBudget || 0)}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-500">Registration</p>
                            <p className="text-gray-700">{org.trust_registration_number || org.csr_registration_number || 'N/A'}</p>
                            <p className="text-sm text-gray-500">{org.tax_registration_number || 'N/A'}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-500">Contact</p>
                            <p className="text-gray-700">{org.phone}</p>
                            <p className="text-sm text-blue-600">{org.email}</p>
                          </div>
                        </div>

                        <div className="mt-4 pt-4 border-t border-gray-100">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleViewDetails(org.id);
                            }}
                          >
                            View Details
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <div className="col-span-3 p-4 text-center text-gray-500">
                    No organizations found matching your search.
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}
