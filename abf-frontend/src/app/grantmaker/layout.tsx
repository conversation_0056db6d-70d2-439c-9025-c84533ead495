import { AuthGuard } from "@/components/auth-guard";
import type { Metada<PERSON> } from "next";
import { Toaster } from "sonner";

export const metadata: Metadata = {
  title: "Grant Maker Dashboard",
  description: "Grant Management System for Grant Makers",
};

export default function GrantMakerLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <AuthGuard user_types={["GRANT_MAKER"]} user_roles={["ADMIN"]}>
      {children}
      <Toaster position="top-center" richColors />
    </AuthGuard>
  );
}
