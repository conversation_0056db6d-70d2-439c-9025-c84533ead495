"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Layout from "@/components/grantmaker/Layout";
import { toast } from "sonner";
import { ArrowLeft, MessageCircle } from 'lucide-react';
import { AnimatedLoader } from '@/components/grantmaker/AnimatedLoader';

import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/services/grantmaker/grantmaker-service";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { ExpenseApproval } from "@/components/grantmaker/expense/ExpenseApproval";

interface ExpenseRow {
  sr_no: number;
  particulars: string;
  main_header: string;
  sub_headers: string;
  units?: string;
  frequency?: string;
  cost_per_unit?: number;
  budget_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  actuals_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  total_budget?: number;
  total_actual?: number;
}

interface ExpenseDetail {
  id: string;
  loggedDate: string;
  totalBudget: number;
  totalActualSpent: number;
  status: string;
  attachment: string;
  category: string;
  description: string;
  source_type: string;
  grantee: {
    id: string;
    name: string;
    contactPerson: string;
    contactEmail: string;
  };
  expenseRows: ExpenseRow[];
  supportingDocuments: Array<{
    id: string;
    name: string;
    uploadedAt: string;
    status: "verified" | "pending" | "update-required";
    url?: string;
    comments?: string;
  }>;
  comments: Array<{
    id: string;
    author: string;
    date: string;
    text: string;
  }>;
  remarks?: string;
}

export default function ExpenseDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [expense, setExpense] = useState<ExpenseDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [newComment, setNewComment] = useState("");

  useEffect(() => {
    fetchExpenseDetail();
  }, [params.id]);

  const fetchExpenseDetail = async () => {
    setLoading(true);
    try {
      // In a real implementation, this would be an API call
      // const response = await fetch(`/api/expenses/${params.id}`);
      // const data = await response.json();
      // setExpense(data);

      // Using mock data for now
      setTimeout(() => {
        setExpense({
          id: params.id,
          loggedDate: "2024-03-15",
          totalBudget: 120000,
          totalActualSpent: 115000,
          status: "pending",
          attachment: "expense-report-mar.xlsx",
          category: "Personnel",
          description: "Staff salaries and benefits for Q1 2024",
          source_type: "excel",
          remarks: "The Program Manager Salary exceeded budget due to an approved performance bonus that was not initially budgeted. This was approved by the board in their meeting dated March 5, 2024.",
          grantee: {
            id: "1",
            name: "Education First Foundation",
            contactPerson: "John Smith",
            contactEmail: "<EMAIL>"
          },
          expenseRows: [
            {
              sr_no: 1,
              particulars: "Program Manager Salary",
              main_header: "Personnel",
              sub_headers: "Staff",
              units: "Months",
              frequency: "Monthly",
              cost_per_unit: 50000,
              budget_quarterly: { Q1: 15000, Q2: 15000, Q3: 15000, Q4: 15000 },
              actuals_quarterly: { Q1: 15000, Q2: 15000, Q3: 15500, Q4: 15500 },
              total_budget: 60000,
              total_actual: 61000
            },
            {
              sr_no: 2,
              particulars: "Field Staff Salaries",
              main_header: "Personnel",
              sub_headers: "Staff",
              units: "People",
              frequency: "Monthly",
              cost_per_unit: 10000,
              budget_quarterly: { Q1: 10000, Q2: 10000, Q3: 10000, Q4: 10000 },
              actuals_quarterly: { Q1: 10000, Q2: 10000, Q3: 9000, Q4: 9000 },
              total_budget: 40000,
              total_actual: 38000
            },
            {
              sr_no: 3,
              particulars: "Staff Benefits",
              main_header: "Personnel",
              sub_headers: "Benefits",
              units: "Package",
              frequency: "Quarterly",
              cost_per_unit: 7500,
              budget_quarterly: { Q1: 7500, Q2: 7500, Q3: 7500, Q4: 7500 },
              actuals_quarterly: { Q1: 7000, Q2: 7000, Q3: 6500, Q4: 6500 },
              total_budget: 30000,
              total_actual: 27000
            }
          ],
          supportingDocuments: [
            {
              id: "1",
              name: "Salary Slips.pdf",
              uploadedAt: "2024-03-16T10:30:00Z",
              status: "verified",
              url: "#"
            },
            {
              id: "2",
              name: "Benefits Calculation.xlsx",
              uploadedAt: "2024-03-16T10:35:00Z",
              status: "pending",
              url: "#"
            }
          ],
          comments: [
            {
              id: "1",
              author: "Sarah Johnson",
              date: "2024-03-17T09:15:00Z",
              text: "All salary slips verified and approved."
            },
            {
              id: "2",
              author: "Michael Chen",
              date: "2024-03-18T14:20:00Z",
              text: "Please provide additional details on the benefits calculation."
            }
          ]
        });
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error("Error fetching expense detail:", error);
      toast.error("Failed to load expense details");
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case "rejected":
        return <Badge className="bg-red-100 text-red-800">Rejected</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const handleStatusChange = (expenseId: string, newStatus: string) => {
    if (expense && expense.id === expenseId) {
      setExpense({
        ...expense,
        status: newStatus
      });
    }
  };

  const handleAddComment = () => {
    if (!newComment.trim()) {
      toast.error("Please enter a comment");
      return;
    }

    if (expense) {
      const newCommentObj = {
        id: Date.now().toString(),
        author: "Grantmaker",
        date: new Date().toISOString(),
        text: newComment
      };

      setExpense({
        ...expense,
        comments: [...expense.comments, newCommentObj]
      });

      setNewComment("");
      toast.success("Comment added successfully");
    }
  };

  if (loading) {
    return (
      <Layout title="Expense Details">
        <AnimatedLoader type="expense" message="Analyzing expense data..." />
      </Layout>
    );
  }

  if (!expense) {
    return (
      <Layout title="Expense Details">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="text-red-500">Expense not found</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Expense Details">
      <div className="space-y-6 pb-8">
        {/* Header with back button */}
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            className="mr-4 p-2"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">Expense Details</h1>
            <div className="flex items-center mt-1">
              <span className="text-gray-600">{expense.category}</span>
              <span className="mx-2 text-gray-400">|</span>
              {getStatusBadge(expense.status)}
            </div>
          </div>
        </div>

        {/* Expense Details */}
        <Card className="shadow-md border-0 rounded-lg overflow-hidden">
          <div className="h-1 bg-gradient-to-r from-orange-500 to-amber-400"></div>
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-gray-800">Expense Details</CardTitle>
            <CardDescription>Detailed breakdown of expense items</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse bg-white">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="p-3 border text-left font-semibold">Sr no.</th>
                    <th className="p-3 border text-left font-semibold">Particulars</th>
                    <th className="p-3 border text-left font-semibold">Main header</th>
                    <th className="p-3 border text-left font-semibold">Sub-headers</th>
                    <th className="p-3 border text-left font-semibold">Units</th>
                    <th className="p-3 border text-left font-semibold">Frequency</th>
                    <th className="p-3 border text-left font-semibold">Cost per unit</th>
                    <th className="p-3 border text-center font-semibold" colSpan={4}>Budget quarterly breakup</th>
                    <th className="p-3 border text-center font-semibold">Total Budget</th>
                    <th className="p-3 border text-center font-semibold" colSpan={4}>Actuals quarterly breakup</th>
                    <th className="p-3 border text-center font-semibold">Total Actual</th>
                  </tr>
                  <tr className="bg-gray-50">
                    <th className="p-3 border" colSpan={7}></th>
                    <th className="p-3 border text-center">Q1</th>
                    <th className="p-3 border text-center">Q2</th>
                    <th className="p-3 border text-center">Q3</th>
                    <th className="p-3 border text-center">Q4</th>
                    <th className="p-3 border"></th>
                    <th className="p-3 border text-center">Q1</th>
                    <th className="p-3 border text-center">Q2</th>
                    <th className="p-3 border text-center">Q3</th>
                    <th className="p-3 border text-center">Q4</th>
                    <th className="p-3 border"></th>
                  </tr>
                </thead>
                <tbody>
                  {expense.expenseRows.map((row, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="p-3 border">{row.sr_no}</td>
                      <td className="p-3 border">{row.particulars}</td>
                      <td className="p-3 border">{row.main_header}</td>
                      <td className="p-3 border">{row.sub_headers}</td>
                      <td className="p-3 border">{row.units || ''}</td>
                      <td className="p-3 border">{row.frequency || ''}</td>
                      <td className="p-3 border">{row.cost_per_unit?.toFixed(2) || ''}</td>
                      {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                        <td key={`budget_${quarter}`} className="p-3 border">
                          {parseFloat(row.budget_quarterly[quarter as keyof typeof row.budget_quarterly].toString()).toFixed(2)}
                        </td>
                      ))}
                      <td className="p-3 border text-center font-medium">
                        {(row.total_budget || Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0)).toFixed(2)}
                      </td>
                      {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                        <td key={`actual_${quarter}`} className="p-3 border">
                          {parseFloat(row.actuals_quarterly[quarter as keyof typeof row.actuals_quarterly].toString()).toFixed(2)}
                        </td>
                      ))}
                      <td className="p-3 border text-center font-medium">
                        {(row.total_actual || Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0)).toFixed(2)}
                      </td>
                    </tr>
                  ))}
                  <tr className="bg-gray-100 font-bold">
                    <td className="p-3 border" colSpan={7}>Total</td>
                    {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                      <td key={`total_budget_${quarter}`} className="p-3 border text-right">
                        {expense.expenseRows.reduce((total, row) => total + (row.budget_quarterly[quarter as keyof typeof row.budget_quarterly] || 0), 0).toFixed(2)}
                      </td>
                    ))}
                    <td className="p-3 border text-right">
                      {expense.expenseRows.reduce((total, row) => total + (row.total_budget || Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0)), 0).toFixed(2)}
                    </td>
                    {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                      <td key={`total_actual_${quarter}`} className="p-3 border text-right">
                        {expense.expenseRows.reduce((total, row) => total + (row.actuals_quarterly[quarter as keyof typeof row.actuals_quarterly] || 0), 0).toFixed(2)}
                      </td>
                    ))}
                    <td className="p-3 border text-right">
                      {expense.expenseRows.reduce((total, row) => total + (row.total_actual || Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0)), 0).toFixed(2)}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>


        {/* Approval Section */}
        <Card className="shadow-md border-0 rounded-lg overflow-hidden">
          <div className="h-1 bg-gradient-to-r from-green-500 to-green-400"></div>
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-gray-800">Expense Review</CardTitle>
            <CardDescription>Approve or reject this expense submission</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Grantee Remarks Section (if any) */}
              {expense.remarks && (
                <div className="bg-gray-50 p-4 rounded-md">
                  <h4 className="font-medium text-gray-800 mb-2">Grantee Remarks:</h4>
                  <p className="text-gray-700">{expense.remarks}</p>
                </div>
              )}

              {/* Approval/Rejection Controls */}
              <ExpenseApproval
                expense={expense}
                onStatusChange={handleStatusChange}
              />
            </div>
          </CardContent>
        </Card>

        {/* Comments Section */}
        <Card className="shadow-md border-0 rounded-lg overflow-hidden">
          <div className="h-1 bg-gradient-to-r from-blue-500 to-blue-400"></div>
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-gray-800">Comments</CardTitle>
            <CardDescription>Discussion about this expense</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {expense.comments.length > 0 ? (
                <div className="space-y-3">
                  {expense.comments.map((comment) => (
                    <div key={comment.id} className="bg-gray-50 p-3 rounded-md">
                      <div className="flex justify-between items-center mb-1">
                        <span className="font-medium text-gray-800">{comment.author}</span>
                        <span className="text-sm text-gray-500">
                          {new Date(comment.date).toLocaleDateString()} {new Date(comment.date).toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="text-gray-700">{comment.text}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center p-4 bg-gray-50 rounded-md">
                  <p className="text-gray-500">No comments yet</p>
                </div>
              )}

              {/* Add Comment Form */}
              <div className="mt-4 space-y-2">
                <div className="flex items-center gap-2">
                  <MessageCircle className="h-5 w-5 text-gray-500" />
                  <h4 className="font-medium">Add Comment</h4>
                </div>
                <Textarea
                  placeholder="Add a comment about this expense..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  rows={3}
                  className="w-full"
                />
                <div className="flex justify-end">
                  <Button
                    onClick={handleAddComment}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Post Comment
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}