"use client";

import { useState } from "react";
import Layout from "@/components/grantmaker/Layout";
import { GranteeExpenseUpload } from "@/components/grantmaker/expense/GranteeExpenseUpload";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { AnimatedLoader } from '@/components/grantmaker/AnimatedLoader';

export default function ExpenseUploadPage() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  return (
    <Layout title="Upload Grantee Expenses">
      {isLoading ? (
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          <AnimatedLoader />
          <p className="mt-4 text-lg text-gray-600">Processing your request...</p>
        </div>
      ) : (
        <div className="max-w-7xl mx-auto">
          <div className="mb-6">
            <Button
              variant="ghost"
              className="text-gray-600 hover:text-gray-900"
              onClick={() => router.back()}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          </div>
          
          <GranteeExpenseUpload />
        </div>
      )}
    </Layout>
  );
}
