"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Layout from "@/components/grantmaker/Layout";
import { toast } from "sonner";
import { ArrowLeft, CheckCircle, AlertCircle } from 'lucide-react';
import { AnimatedLoader } from '@/components/grantmaker/AnimatedLoader';
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/services/grantmaker/grantmaker-service";
import { Input } from "@/components/ui/input";

interface DisbursementRow {
  sr_no: number;
  particulars: string;
  scheduled_payment_date: string;
  scheduled_amount: number;
  received_amount?: number;
  pending_amount?: number;
  payment_received_date?: string;
  remarks?: string;
}

interface DisbursementDetail {
  id: string;
  date: string;
  amount: number;
  status: 'Disbursed' | 'Pending' | 'Failed';
  remark: string;
  acknowledgement: 'Completed' | 'Send Receipt' | 'Pending';
  grantName: string;
  grantee: {
    id: string;
    name: string;
    contactPerson: string;
    contactEmail: string;
  };
  paymentDetails: {
    method: string;
    reference: string;
    processingDate?: string;
    bankName?: string;
    accountNumber?: string;
  };
  disbursementRows: DisbursementRow[];
}

export default function DisbursementDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [disbursement, setDisbursement] = useState<DisbursementDetail | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDisbursementDetail();
  }, [params.id]);

  const fetchDisbursementDetail = async () => {
    setLoading(true);
    try {
      // In a real implementation, this would be an API call
      // const response = await fetch(`/api/disbursements/${params.id}`);
      // const data = await response.json();
      // setDisbursement(data);

      // Using mock data for now
      setTimeout(() => {
        setDisbursement({
          id: params.id,
          date: "2024-04-10",
          amount: 250000,
          status: "Disbursed",
          remark: "Q2 operational funding",
          acknowledgement: "Completed",
          grantName: "Education Support Grant",
          grantee: {
            id: "1",
            name: "Education First Foundation",
            contactPerson: "John Smith",
            contactEmail: "<EMAIL>"
          },
          paymentDetails: {
            method: "Bank Transfer",
            reference: "TRX-2024-04-10-001",
            processingDate: "2024-04-10T14:30:00Z",
            bankName: "State Bank of India",
            accountNumber: "XXXX-XXXX-1234"
          },
          disbursementRows: [
            {
              sr_no: 1,
              particulars: "Q1 Grant Payment",
              scheduled_payment_date: "2024-03-15",
              scheduled_amount: 100000,
              payment_received_date: "2024-03-15",
              received_amount: 100000,
              pending_amount: 0,
              remarks: "First quarter payment"
            },
            {
              sr_no: 2,
              particulars: "Q2 Grant Payment",
              scheduled_payment_date: "2024-06-15",
              scheduled_amount: 75000,
              payment_received_date: "2024-04-10",
              received_amount: 75000,
              pending_amount: 0,
              remarks: "Second quarter payment (early)"
            },
            {
              sr_no: 3,
              particulars: "Q3 Grant Payment",
              scheduled_payment_date: "2024-09-15",
              scheduled_amount: 75000,
              payment_received_date: undefined,
              received_amount: undefined,
              pending_amount: 75000,
              remarks: "Upcoming payment"
            }
          ]
        });
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error("Error fetching disbursement detail:", error);
      toast.error("Failed to load disbursement details");
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "disbursed":
        return <Badge className="bg-green-100 text-green-800">Disbursed</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case "failed":
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getAcknowledgementBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return (
          <div className="flex items-center gap-1">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span className="text-green-600">Completed</span>
          </div>
        );
      case "pending":
        return (
          <div className="flex items-center gap-1">
            <AlertCircle className="h-4 w-4 text-yellow-500" />
            <span className="text-yellow-600">Pending</span>
          </div>
        );
      default:
        return <span className="text-blue-600">{status}</span>;
    }
  };

  if (loading) {
    return (
      <Layout title="Disbursement Details">
        <AnimatedLoader type="disbursement" message="Processing disbursement data..." />
      </Layout>
    );
  }

  if (!disbursement) {
    return (
      <Layout title="Disbursement Details">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="text-red-500">Disbursement not found</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Disbursement Details">
      <div className="space-y-6 pb-8">
        {/* Header with back button */}
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            className="mr-4 p-2"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">Disbursement Details</h1>
            <div className="flex items-center mt-1">
              <span className="text-gray-600">{disbursement.grantName}</span>
              <span className="mx-2 text-gray-400">|</span>
              {getStatusBadge(disbursement.status)}
            </div>
          </div>
        </div>

        {/* Disbursement Details */}
        <Card className="shadow-md border-0 rounded-lg overflow-hidden">
          <div className="h-1 bg-gradient-to-r from-orange-500 to-amber-400"></div>
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-gray-800">Disbursement Details</CardTitle>
            <CardDescription>Information about the disbursement transaction</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse bg-white">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="p-3 border text-left font-semibold">Sr No.</th>
                    <th className="p-3 border text-left font-semibold">Particulars</th>
                    <th className="p-3 border text-center font-semibold" colSpan={2}>Scheduled payments</th>
                    <th className="p-3 border text-center font-semibold" colSpan={2}>Received payments</th>
                    <th className="p-3 border text-left font-semibold">Remarks</th>
                  </tr>
                  <tr className="bg-gray-50">
                    <th className="p-3 border" colSpan={2}></th>
                    <th className="p-3 border text-left font-semibold">Date of disbursement</th>
                    <th className="p-3 border text-right font-semibold">Amount</th>
                    <th className="p-3 border text-left font-semibold">Date of disbursement</th>
                    <th className="p-3 border text-right font-semibold">Amount</th>
                    <th className="p-3 border"></th>
                  </tr>
                </thead>
                <tbody>
                  {disbursement.disbursementRows.map((row, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="p-3 border">{row.sr_no}</td>
                      <td className="p-3 border">{row.particulars}</td>
                      <td className="p-3 border">{new Date(row.scheduled_payment_date).toLocaleDateString()}</td>
                      <td className="p-3 border text-right">{formatCurrency(row.scheduled_amount)}</td>
                      <td className="p-3 border">{row.payment_received_date ? new Date(row.payment_received_date).toLocaleDateString() : '-'}</td>
                      <td className="p-3 border text-right">{row.received_amount ? formatCurrency(row.received_amount) : '-'}</td>
                      <td className="p-3 border">{row.remarks || ''}</td>
                    </tr>
                  ))}
                  <tr className="bg-gray-100 font-bold">
                    <td className="p-3 border" colSpan={3}>Total</td>
                    <td className="p-3 border text-right">
                      {formatCurrency(disbursement.disbursementRows.reduce((total, row) => total + row.scheduled_amount, 0))}
                    </td>
                    <td className="p-3 border"></td>
                    <td className="p-3 border text-right">
                      {formatCurrency(disbursement.disbursementRows.reduce((total, row) => total + (row.received_amount || 0), 0))}
                    </td>
                    <td className="p-3 border"></td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Payment Method</h3>
                <p className="mt-1 text-gray-900">{disbursement.paymentDetails.method}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Reference Number</h3>
                <p className="mt-1 text-gray-900">{disbursement.paymentDetails.reference}</p>
              </div>
              {disbursement.paymentDetails.processingDate && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Processing Date</h3>
                  <p className="mt-1 text-gray-900">
                    {new Date(disbursement.paymentDetails.processingDate).toLocaleString()}
                  </p>
                </div>
              )}
              {disbursement.paymentDetails.bankName && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Bank Name</h3>
                  <p className="mt-1 text-gray-900">{disbursement.paymentDetails.bankName}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}