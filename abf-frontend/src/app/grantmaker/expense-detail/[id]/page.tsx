"use client";

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import {
  ArrowLeft,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle,
  Hash,
  Clock,
  Target,
  TrendingUp,
  TrendingDown,
  Minus,
  BarChart3,
  Wallet,
  Banknote
} from 'lucide-react';
import { toast } from 'sonner';
import { ExpenseRecord } from '@/services/funding-service';
import { updateExpenseStatus } from '@/services/grantmaker/grantee-expense-service';
import { formatCurrency } from '@/services/grantmaker/grantmaker-service';

export default function ExpenseDetailPage() {
  const params = useParams();
  const router = useRouter();
  const expenseId = params.id as string;

  const [expense, setExpense] = useState<ExpenseRecord | null>(null);

  const [isLoading, setIsLoading] = useState(true);
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [rejectionNotes, setRejectionNotes] = useState('');

  useEffect(() => {
    // Load expense data from localStorage
    const storedExpense = localStorage.getItem('selectedExpense');

    if (storedExpense) {
      setExpense(JSON.parse(storedExpense));
    }

    setIsLoading(false);
  }, [expenseId]);

  const handleBack = () => {
    const returnUrl = localStorage.getItem('returnUrl');
    const returnTab = localStorage.getItem('returnTab');
    const storedGranteeId = localStorage.getItem('granteeId');

    // Clean up localStorage
    localStorage.removeItem('selectedExpense');
    localStorage.removeItem('granteeId');
    localStorage.removeItem('returnTab');
    localStorage.removeItem('returnUrl');

    if (returnUrl && returnTab) {
      // Navigate back to the stored URL with the correct tab
      window.location.href = returnUrl + `#${returnTab}`;
    } else if (storedGranteeId) {
      // Navigate to the grantee's expense tab specifically
      router.push(`/grantmaker/grantee/${storedGranteeId}#expense`);
    } else {
      // Try to determine the grantee ID from the current URL or referrer
      const referrer = document.referrer;
      const granteeMatch = referrer.match(/\/grantmaker\/grantee\/(\d+)/);

      if (granteeMatch) {
        // Navigate back to the grantee's expense tab
        router.push(`/grantmaker/grantee/${granteeMatch[1]}#expense`);
      } else {
        // Check if we came from the main expense management page
        if (referrer.includes('/grantmaker/funding/expenses')) {
          router.push('/grantmaker/funding/expenses');
        } else {
          // Fallback to grantmaker dashboard
          router.push('/grantmaker');
        }
      }
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: {
        color: 'bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-800 border-amber-200',
        label: 'Pending Review',
        icon: <Clock className="h-3 w-3 mr-1" />
      },
      approved: {
        color: 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200',
        label: 'Approved',
        icon: <CheckCircle className="h-3 w-3 mr-1" />
      },
      rejected: {
        color: 'bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border-red-200',
        label: 'Rejected',
        icon: <XCircle className="h-3 w-3 mr-1" />
      }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <Badge className={`${config.color} border font-medium px-3 py-1.5 rounded-xl shadow-sm flex items-center`}>
        {config.icon}
        {config.label}
      </Badge>
    );
  };

  const handleApprove = async () => {
    if (!expense) return;

    try {
      await updateExpenseStatus(expense.id, 'approved');
      setExpense(prev => prev ? { ...prev, status: 'approved' } : null);
      setIsApproveDialogOpen(false);
      toast.success('Expense approved successfully');
    } catch (error) {
      toast.error('Failed to approve expense');
    }
  };

  const handleReject = async () => {
    if (!expense || !rejectionNotes.trim()) return;

    try {
      await updateExpenseStatus(expense.id, 'rejected', rejectionNotes);
      setExpense(prev => prev ? { ...prev, status: 'rejected', rejection_notes: rejectionNotes } : null);
      setIsRejectDialogOpen(false);
      setRejectionNotes('');
      toast.success('Expense rejected successfully');
    } catch (error) {
      toast.error('Failed to reject expense');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-teal-50/30 flex items-center justify-center relative overflow-hidden">
        {/* Enhanced background elements */}
        <div className="absolute top-20 left-20 w-72 h-72 bg-gradient-to-br from-teal-400/10 to-emerald-300/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-br from-blue-400/10 to-purple-300/10 rounded-full blur-3xl animate-pulse"></div>

        <div className="text-center relative z-10">
          <div className="relative mb-6">
            <div className="absolute inset-0 bg-gradient-to-r from-[#00998F] to-teal-600 rounded-full blur-md opacity-30 animate-pulse"></div>
            <div className="relative animate-spin rounded-full h-16 w-16 border-4 border-transparent bg-gradient-to-r from-[#00998F] to-teal-600 mx-auto">
              <div className="absolute inset-2 bg-white rounded-full"></div>
            </div>
          </div>
          <h3 className="text-xl font-semibold text-gray-800 mb-2">Loading Expense Details</h3>
          <p className="text-gray-600">Please wait while we fetch the information...</p>
        </div>
      </div>
    );
  }

  if (!expense) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-red-50/30 flex items-center justify-center relative overflow-hidden">
        {/* Enhanced background elements */}
        <div className="absolute top-20 left-20 w-72 h-72 bg-gradient-to-br from-red-400/10 to-orange-300/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-br from-red-400/10 to-pink-300/10 rounded-full blur-3xl"></div>

        <div className="text-center relative z-10">
          <div className="relative mb-6">
            <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-red-600 rounded-full blur-md opacity-20"></div>
            <AlertCircle className="h-16 w-16 text-red-500 mx-auto relative z-10" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-3">Expense Not Found</h2>
          <p className="text-gray-600 mb-6 max-w-md">The requested expense could not be loaded. It may have been removed or you may not have permission to view it.</p>
          <Button
            onClick={() => router.back()}
            className="bg-gradient-to-r from-[#00998F] to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white rounded-xl px-6 py-3 font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  const variance = expense.totalActualSpent - expense.totalBudget;
  const variancePercentage = expense.totalBudget > 0 ? (variance / expense.totalBudget) * 100 : 0;

  // Check for validation errors that require approval
  const validationErrors = [];
  const hasErrors = [];

  if (variance > 0) {
    validationErrors.push(`Budget exceeded by ${formatCurrency(variance)}`);
    hasErrors.push('budget');
  }
  if (!expense.description || expense.description.trim() === '') {
    validationErrors.push('Missing expense description');
    hasErrors.push('description');
  }
  if (!expense.category || expense.category.trim() === '') {
    validationErrors.push('Missing category information');
    hasErrors.push('category');
  }
  if (expense.totalActualSpent === 0) {
    validationErrors.push('No actual expenses recorded');
    hasErrors.push('actual');
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Document Container */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        {/* Navigation Bar */}
        <div className="mb-6">
          <Button
            onClick={handleBack}
            variant="outline"
            className="border-gray-300 hover:bg-gray-50 text-gray-700 px-4 py-2 font-medium"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Expenses
          </Button>
        </div>

        {/* Professional Document with Realistic Mixed Torn Edges */}
        <div className="relative mx-auto max-w-4xl"
             style={{
               marginTop: '20px',
               marginBottom: '20px'
             }}>



          {/* Main Document Container with Enhanced Realistic Edges */}
          <div className="bg-white shadow-xl relative"
               style={{
                 backgroundImage: `
                   radial-gradient(circle at 1px 1px, rgba(0,0,0,0.02) 1px, transparent 0),
                   linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.01) 50%, transparent 100%)
                 `,
                 backgroundSize: '20px 20px, 100% 1px',
                 clipPath: `polygon(
                   0% 7px, 3.8% 1px, 7.9% 9px, 11.7% 0px, 16.2% 8px, 19.8% 2px, 24.1% 7px, 27.9% 0px, 32.3% 9px, 35.7% 1px, 40.2% 8px, 43.8% 0px, 48.1% 7px, 51.9% 2px, 56.3% 9px, 59.7% 0px, 64.2% 8px, 67.8% 1px, 72.1% 7px, 75.9% 0px, 80.3% 9px, 83.7% 2px, 88.2% 8px, 91.8% 0px, 96.1% 7px, 100% 1px,
                   100% calc(100% - 2px), 98% calc(100% - 8px), 95% calc(100% - 3px), 92% calc(100% - 12px), 89% calc(100% - 5px), 86% calc(100% - 15px), 83% calc(100% - 7px), 80% calc(100% - 10px), 77% calc(100% - 4px), 74% calc(100% - 18px), 71% calc(100% - 6px), 68% calc(100% - 13px), 65% calc(100% - 9px), 62% calc(100% - 16px), 59% calc(100% - 3px), 56% calc(100% - 11px), 53% calc(100% - 7px), 50% calc(100% - 14px), 47% calc(100% - 5px), 44% calc(100% - 19px), 41% calc(100% - 8px), 38% calc(100% - 12px), 35% calc(100% - 4px), 32% calc(100% - 17px), 29% calc(100% - 6px), 26% calc(100% - 13px), 23% calc(100% - 9px), 20% calc(100% - 15px), 17% calc(100% - 5px), 14% calc(100% - 11px), 11% calc(100% - 7px), 8% calc(100% - 16px), 5% calc(100% - 4px), 2% calc(100% - 12px), 0% calc(100% - 6px)
                 )`,
                 filter: 'drop-shadow(0 4px 12px rgba(0,0,0,0.15)) drop-shadow(0 2px 4px rgba(0,0,0,0.1))',
                 border: '1px solid rgba(0,0,0,0.08)'
               }}>


          {/* Document Status and Actions */}
          <div className="px-8 pt-8 pb-4">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium text-gray-600">Status:</span>
                {getStatusBadge(expense.status)}
              </div>
              {expense.status === 'pending' && (
                <div className="flex gap-3">
                  <Button
                    onClick={() => setIsApproveDialogOpen(true)}
                    className="bg-[#00998F] hover:bg-[#00998F]/90 text-white px-4 py-2 font-medium"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Approve
                  </Button>
                  <Button
                    onClick={() => setIsRejectDialogOpen(true)}
                    variant="outline"
                    className="border-red-300 text-red-600 hover:bg-red-50 px-4 py-2 font-medium"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Reject
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Document Content */}
          <div className="px-8 py-6">
            {/* Validation Issues Section */}
            {validationErrors.length > 0 && expense.status === 'pending' && (
              <div className="mb-8 p-4 bg-amber-50 border-l-4 border-amber-400 rounded-r">
                <div className="flex items-center gap-3 mb-4">
                  <AlertCircle className="h-5 w-5 text-amber-600" />
                  <h3 className="text-lg font-semibold text-amber-800">Validation Issues Requiring Approval</h3>
                </div>
                <div className="space-y-2 mb-4">
                  {validationErrors.map((error, index) => (
                    <div key={index} className="flex items-center gap-2 text-amber-700">
                      <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
                      <span className="text-sm font-medium">{error}</span>
                    </div>
                  ))}
                </div>
                <p className="text-sm text-amber-600 italic">
                  These issues require grantmaker approval before the expense can be processed.
                </p>
              </div>
            )}

            {/* Basic Information Content */}
            <div className="mb-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div className="border-l-4 border-[#00998F] pl-4">
                  <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Date</label>
                  <p className="text-lg font-medium text-gray-900 mt-1">{expense.loggedDate}</p>
                </div>

                <div className={`border-l-4 pl-4 ${hasErrors.includes('main_header') ? 'border-red-500' : 'border-[#00998F]'}`}>
                  <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Main Header</label>
                  <p className={`text-lg font-medium mt-1 ${hasErrors.includes('main_header') ? 'text-red-700' : 'text-gray-900'}`}>
                    {(expense as any).main_headers || (expense as any).main_header || expense.category || 'N/A'}
                  </p>
                  {hasErrors.includes('main_header') && (
                    <p className="text-xs text-red-600 mt-1 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      Missing main header information
                    </p>
                  )}
                </div>

                <div className={`border-l-4 pl-4 ${hasErrors.includes('sub_header') ? 'border-red-500' : 'border-[#00998F]'}`}>
                  <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Sub Header</label>
                  <p className={`text-lg font-medium mt-1 ${hasErrors.includes('sub_header') ? 'text-red-700' : 'text-gray-900'}`}>
                    {(expense as any).sub_headers || (expense as any).sub_header || 'N/A'}
                  </p>
                  {hasErrors.includes('sub_header') && (
                    <p className="text-xs text-red-600 mt-1 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      Missing sub header information
                    </p>
                  )}
                </div>

                <div className="border-l-4 border-[#00998F] pl-4">
                  <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Units</label>
                  <p className="text-lg font-medium text-gray-900 mt-1">{expense.units || 'N/A'}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className={`border-l-4 pl-4 ${hasErrors.includes('particulars') ? 'border-red-500' : 'border-[#00998F]'}`}>
                  <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Particulars</label>
                  <p className={`leading-relaxed mt-2 ${hasErrors.includes('particulars') ? 'text-red-700' : 'text-gray-900'}`}>
                    {(expense as any).particulars || expense.description || 'No particulars provided'}
                  </p>
                  {hasErrors.includes('particulars') && (
                    <p className="text-xs text-red-600 mt-2 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      Missing particulars information
                    </p>
                  )}
                </div>

                {((expense as any).activity_description || (expense as any).activityDescription) && (
                  <div className="border-l-4 border-[#00998F] pl-4">
                    <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Activity Description</label>
                    <p className="leading-relaxed text-gray-900 mt-2">
                      {(expense as any).activity_description || (expense as any).activityDescription}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Unit & Cost Details Content */}
            <div className="mb-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-6 bg-gray-50 border border-gray-200 rounded">
                  <div className="p-2 bg-white rounded w-fit mx-auto mb-3 shadow-sm">
                    <Hash className="h-5 w-5 text-[#00998F]" />
                  </div>
                  <p className="text-sm font-medium text-gray-600 mb-2">No. of Units</p>
                  <p className="text-xl font-semibold text-gray-900">{expense.units || 'N/A'}</p>
                </div>

                <div className="text-center p-6 bg-gray-50 border border-gray-200 rounded">
                  <div className="p-2 bg-white rounded w-fit mx-auto mb-3 shadow-sm">
                    <Clock className="h-5 w-5 text-[#00998F]" />
                  </div>
                  <p className="text-sm font-medium text-gray-600 mb-2">Frequency</p>
                  <p className="text-xl font-semibold text-gray-900">{expense.frequency || 'N/A'}</p>
                </div>

                <div className="text-center p-6 bg-gray-50 border border-gray-200 rounded">
                  <div className="p-2 bg-white rounded w-fit mx-auto mb-3 shadow-sm">
                    <Banknote className="h-5 w-5 text-[#00998F]" />
                  </div>
                  <p className="text-sm font-medium text-gray-600 mb-2">Cost per Unit</p>
                  <p className="text-xl font-semibold text-gray-900">
                    {expense.cost_per_unit ? formatCurrency(expense.cost_per_unit) : 'N/A'}
                  </p>
                </div>
              </div>
            </div>

            {/* Financial Summary Section */}
            <div className="mb-8">
              <div className="border-b-2 border-[#00998F] pb-2 mb-6">
                <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                  <Wallet className="h-5 w-5 text-[#00998F]" />
                  Financial Summary
                </h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-6 bg-gray-50 border border-gray-200 rounded">
                  <div className="p-2 bg-white rounded w-fit mx-auto mb-3 shadow-sm">
                    <Target className="h-5 w-5 text-[#00998F]" />
                  </div>
                  <p className="text-sm font-medium text-gray-600 mb-2">Total Budget</p>
                  <p className="text-xl font-semibold text-gray-900">
                    {formatCurrency(expense.totalBudget)}
                  </p>
                </div>

                <div className={`text-center p-6 border rounded ${hasErrors.includes('actual')
                  ? 'bg-red-50 border-red-200'
                  : 'bg-gray-50 border-gray-200'
                }`}>
                  <div className="p-2 bg-white rounded w-fit mx-auto mb-3 shadow-sm">
                    <TrendingUp className={`h-5 w-5 ${hasErrors.includes('actual') ? 'text-red-600' : 'text-[#00998F]'}`} />
                  </div>
                  <p className={`text-sm font-medium mb-2 ${hasErrors.includes('actual') ? 'text-red-600' : 'text-gray-600'}`}>Total Actual</p>
                  <p className={`text-xl font-semibold ${hasErrors.includes('actual') ? 'text-red-700' : 'text-gray-900'}`}>
                    {formatCurrency(expense.totalActualSpent)}
                  </p>
                  {hasErrors.includes('actual') && (
                    <p className="text-xs text-red-600 mt-2 flex items-center justify-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      No expenses recorded
                    </p>
                  )}
                </div>

                <div className={`text-center p-6 border rounded ${
                  variance > 0
                    ? 'bg-red-50 border-red-200'
                    : variance < 0
                      ? 'bg-[#00998F]/5 border-[#00998F]/20'
                      : 'bg-gray-50 border-gray-200'
                }`}>
                  <div className="p-2 bg-white rounded w-fit mx-auto mb-3 shadow-sm">
                    {variance > 0 ? (
                      <TrendingUp className="h-5 w-5 text-red-600" />
                    ) : variance < 0 ? (
                      <TrendingDown className="h-5 w-5 text-[#00998F]" />
                    ) : (
                      <Minus className="h-5 w-5 text-gray-600" />
                    )}
                  </div>
                  <p className={`text-sm font-medium mb-2 ${
                    variance > 0 ? 'text-red-600' : variance < 0 ? 'text-[#00998F]' : 'text-gray-600'
                  }`}>
                    Variance
                  </p>
                  <p className={`text-xl font-semibold ${
                    variance > 0 ? 'text-red-700' : variance < 0 ? 'text-[#00998F]' : 'text-gray-900'
                  }`}>
                    {variance > 0 ? '+' : ''}{formatCurrency(Math.abs(variance))}
                  </p>
                  <p className={`text-xs mt-1 ${
                    variance > 0 ? 'text-red-600' : variance < 0 ? 'text-[#00998F]' : 'text-gray-600'
                  }`}>
                    ({variancePercentage > 0 ? '+' : ''}{variancePercentage.toFixed(1)}%)
                  </p>
                </div>
              </div>
            </div>

            {/* Quarterly Financial Breakdown Section */}
            <div className="mb-8">
              <div className="border-b-2 border-[#00998F] pb-2 mb-6">
                <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-[#00998F]" />
                  Quarterly Financial Breakdown
                </h2>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full border border-gray-300 bg-white">
                  <thead>
                    <tr className="bg-gray-100 border-b-2 border-[#00998F]">
                      <th className="py-4 px-6 text-left font-bold text-gray-800 border-r border-gray-300">QUARTER</th>
                      <th className="py-4 px-6 text-right font-bold text-gray-800 border-r border-gray-300">BUDGET</th>
                      <th className="py-4 px-6 text-right font-bold text-gray-800 border-r border-gray-300">ACTUAL</th>
                      <th className="py-4 px-6 text-right font-bold text-gray-800 border-r border-gray-300">VARIANCE</th>
                      <th className="py-4 px-6 text-right font-bold text-gray-800">VARIANCE %</th>
                    </tr>
                  </thead>
                  <tbody>
                    {[
                      { name: 'Q1 (Apr-Jun)', budgetKey: 'budget_q1', actualKey: 'actual_q1' },
                      { name: 'Q2 (Jul-Sep)', budgetKey: 'budget_q2', actualKey: 'actual_q2' },
                      { name: 'Q3 (Oct-Dec)', budgetKey: 'budget_q3', actualKey: 'actual_q3' },
                      { name: 'Q4 (Jan-Mar)', budgetKey: 'budget_q4', actualKey: 'actual_q4' }
                    ].map((quarter, index) => {
                      const budget = Number(expense[quarter.budgetKey as keyof typeof expense]) || 0;
                      const actual = Number(expense[quarter.actualKey as keyof typeof expense]) || 0;
                      const qVariance = actual - budget;
                      const qVariancePercentage = budget > 0 ? (qVariance / budget) * 100 : 0;

                      return (
                        <tr key={quarter.name} className={`border-b border-gray-200 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                          <td className="py-4 px-6 font-semibold text-gray-800 border-r border-gray-200">{quarter.name}</td>
                          <td className="py-4 px-6 text-right font-medium text-gray-800 border-r border-gray-200">{formatCurrency(budget)}</td>
                          <td className="py-4 px-6 text-right font-medium text-gray-800 border-r border-gray-200">{formatCurrency(actual)}</td>
                          <td className={`py-4 px-6 text-right font-semibold border-r border-gray-200 ${
                            qVariance > 0 ? 'text-red-600' : qVariance < 0 ? 'text-[#00998F]' : 'text-gray-600'
                          }`}>
                            {qVariance > 0 ? '+' : ''}{formatCurrency(Math.abs(qVariance))}
                          </td>
                          <td className={`py-4 px-6 text-right font-semibold ${
                            qVariance > 0 ? 'text-red-600' : qVariance < 0 ? 'text-[#00998F]' : 'text-gray-600'
                          }`}>
                            {qVariancePercentage > 0 ? '+' : ''}{qVariancePercentage.toFixed(1)}%
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                  <tfoot>
                    <tr className="bg-gray-100 border-t-2 border-[#00998F] font-bold">
                      <td className="py-5 px-6 font-bold text-gray-900 border-r border-gray-300">TOTAL</td>
                      <td className="py-5 px-6 text-right font-bold text-gray-900 border-r border-gray-300">
                        {formatCurrency(
                          (Number(expense.budget_q1) || 0) +
                          (Number(expense.budget_q2) || 0) +
                          (Number(expense.budget_q3) || 0) +
                          (Number(expense.budget_q4) || 0)
                        )}
                      </td>
                      <td className="py-5 px-6 text-right font-bold text-gray-900 border-r border-gray-300">
                        {formatCurrency(
                          (Number(expense.actual_q1) || 0) +
                          (Number(expense.actual_q2) || 0) +
                          (Number(expense.actual_q3) || 0) +
                          (Number(expense.actual_q4) || 0)
                        )}
                      </td>
                      <td className={`py-5 px-6 text-right font-bold border-r border-gray-300 ${
                        variance > 0 ? 'text-red-600' : variance < 0 ? 'text-[#00998F]' : 'text-gray-600'
                      }`}>
                        {formatCurrency(Math.abs(variance))}
                      </td>
                      <td className={`py-5 px-6 text-right font-bold ${
                        variance > 0 ? 'text-red-600' : variance < 0 ? 'text-[#00998F]' : 'text-gray-600'
                      }`}>
                        {variancePercentage > 0 ? '+' : ''}{variancePercentage.toFixed(1)}%
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>

            {/* Additional Information Content */}
            {(expense.remarks || expense.rejection_notes) && (
              <div className="mb-8">
                <div className="space-y-6">
                  {expense.remarks && (
                    <div className="border-l-4 border-[#00998F] pl-4">
                      <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide block mb-2">Remarks</label>
                      <p className="leading-relaxed text-gray-900">{expense.remarks}</p>
                    </div>
                  )}

                  {expense.rejection_notes && (
                    <div className="border-l-4 border-red-500 pl-4 bg-red-50 p-4 rounded-r-lg">
                      <label className="text-sm font-semibold text-red-600 uppercase tracking-wide block mb-2 flex items-center gap-2">
                        <AlertCircle className="h-4 w-4" />
                        Rejection Notes
                      </label>
                      <p className="leading-relaxed text-red-700 font-medium">{expense.rejection_notes}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Document Footer */}
            <div className="mt-8 pt-6 border-t-2 border-[#00998F]">
              <div className="h-4"></div>
            </div>
          </div>

          </div>
        </div>
      </div>

      {/* Approval Dialog */}
      <Dialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle className="text-lg flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Approve Expense
            </DialogTitle>
            <DialogDescription>
              Review validation issues and confirm approval for this expense.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            {validationErrors.length > 0 && (
              <div className="p-4 bg-amber-50 border border-amber-200 rounded">
                <h4 className="font-semibold text-amber-800 mb-2 flex items-center gap-2">
                  <AlertCircle className="h-4 w-4" />
                  Validation Issues:
                </h4>
                <ul className="space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index} className="text-sm text-amber-700 flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
                      {error}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            <div className="grid grid-cols-2 gap-4">
              <div className="p-3 bg-gray-50 border border-gray-200 rounded">
                <p className="font-medium text-gray-500 text-xs mb-1">Description</p>
                <p className="text-sm text-gray-800">{expense?.description || 'N/A'}</p>
              </div>
              <div className="p-3 bg-gray-50 border border-gray-200 rounded">
                <p className="font-medium text-gray-500 text-xs mb-1">Category</p>
                <p className="text-sm text-gray-800">{expense?.category || 'N/A'}</p>
              </div>
              <div className="p-3 bg-gray-50 border border-gray-200 rounded">
                <p className="font-medium text-gray-500 text-xs mb-1">Budget</p>
                <p className="font-semibold text-gray-800">{formatCurrency(expense?.totalBudget || 0)}</p>
              </div>
              <div className="p-3 bg-gray-50 border border-gray-200 rounded">
                <p className="font-medium text-gray-500 text-xs mb-1">Actual</p>
                <p className="font-semibold text-gray-800">{formatCurrency(expense?.totalActualSpent || 0)}</p>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsApproveDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleApprove}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Approve
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rejection Dialog */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle className="text-lg flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-600" />
              Reject Expense
            </DialogTitle>
            <DialogDescription>
              Review validation issues and provide a detailed rejection reason.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            {validationErrors.length > 0 && (
              <div className="p-4 bg-red-50 border border-red-200 rounded">
                <h4 className="font-semibold text-red-800 mb-2 flex items-center gap-2">
                  <AlertCircle className="h-4 w-4" />
                  Validation Issues Found:
                </h4>
                <ul className="space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index} className="text-sm text-red-700 flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                      {error}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            <div className="grid grid-cols-2 gap-4">
              <div className="p-3 bg-gray-50 border border-gray-200 rounded">
                <p className="font-medium text-gray-500 text-xs mb-1">Description</p>
                <p className="text-sm text-gray-800">{expense?.description || 'N/A'}</p>
              </div>
              <div className="p-3 bg-gray-50 border border-gray-200 rounded">
                <p className="font-medium text-gray-500 text-xs mb-1">Category</p>
                <p className="text-sm text-gray-800">{expense?.category || 'N/A'}</p>
              </div>
              <div className="p-3 bg-gray-50 border border-gray-200 rounded">
                <p className="font-medium text-gray-500 text-xs mb-1">Budget</p>
                <p className="font-semibold text-gray-800">{formatCurrency(expense?.totalBudget || 0)}</p>
              </div>
              <div className="p-3 bg-gray-50 border border-gray-200 rounded">
                <p className="font-medium text-gray-500 text-xs mb-1">Actual</p>
                <p className="font-semibold text-gray-800">{formatCurrency(expense?.totalActualSpent || 0)}</p>
              </div>
            </div>
            <div className="space-y-3">
              <Label htmlFor="rejection-notes" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <FileText className="h-4 w-4 text-red-600" />
                Rejection Notes *
              </Label>
              <Textarea
                id="rejection-notes"
                placeholder="Please provide a detailed reason for rejecting this expense..."
                value={rejectionNotes}
                onChange={(e) => setRejectionNotes(e.target.value)}
                className="min-h-[120px] border-gray-300 focus:border-red-500 focus:ring-red-500"
              />
              <p className="text-xs text-gray-500">This information will be shared with the grantee.</p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRejectDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleReject}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              <XCircle className="h-4 w-4 mr-2" />
              Reject
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}