import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Plus as PlusIcon } from "lucide-react";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";

interface DisbursementRow {
  sr_no: number;
  particulars: string;
  scheduled_payment_date: string;
  scheduled_amount: number;
  received_amount?: number;
  pending_amount?: number;
  payment_received_date?: string;
  remarks?: string;
}

interface DisbursementTableProps {
  disbursementRows: DisbursementRow[];
  onAddRow: () => void;
  onInputChange: (index: number, field: string, value: string | number) => void;
}

export function DisbursementTable({ disbursementRows, onAddRow, onInputChange }: DisbursementTableProps) {
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const validateRow = (row: DisbursementRow) => {
    const rowErrors: {[key: string]: string} = {};
    
    if (!row.particulars.trim()) rowErrors.particulars = 'This field is required';
    if (!row.scheduled_payment_date.trim()) rowErrors.scheduled_payment_date = 'This field is required';
    if (row.scheduled_amount <= 0) rowErrors.scheduled_amount = 'Amount must be greater than 0';
    if (row.received_amount !== undefined && row.received_amount < 0) {
      rowErrors.received_amount = 'Amount cannot be negative';
    }
    if (row.received_amount !== undefined && row.received_amount > row.scheduled_amount) {
      rowErrors.received_amount = 'Received amount cannot exceed scheduled amount';
    }
    
    return rowErrors;
  };

  const calculateTotal = (field: string) => {
    return disbursementRows.reduce((total, row) => {
      return total + (row[field as keyof DisbursementRow] as number || 0);
    }, 0);
  };

  const handleSubmit = async () => {
    console.log('Starting disbursement submission...');
    setErrors({});

    let hasValidationErrors = false;
    const allErrors: {[key: string]: string} = {};
  
    if (disbursementRows.length === 0) {
      console.log('No disbursement rows found');
      toast.error('Please add at least one disbursement row');
      return;
    }
  
    console.log('Validating disbursement rows:', disbursementRows);
    disbursementRows.forEach((row, index) => {
      const rowErrors = validateRow(row);
      
      if (Object.keys(rowErrors).length > 0) {
        hasValidationErrors = true;
        Object.entries(rowErrors).forEach(([field, message]) => {
          allErrors[`${index}-${field}`] = message;
        });
      }
    });
  
    if (hasValidationErrors) {
      console.log('Validation errors found:', allErrors);
      setErrors(allErrors);
      const errorMessages = Object.values(allErrors);
      const uniqueErrors = [...new Set(errorMessages)];
      uniqueErrors.forEach(error => toast.error(error));
      return;
    }
  
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URI || 'http://localhost:8000';
    
    try {
      console.log('Submitting disbursement data to API...');
      for (let i = 0; i < disbursementRows.length; i++) {
        const row = disbursementRows[i];
        
        const formData = new FormData();
        formData.append('grant', '1');
        formData.append('particulars', row.particulars);
        formData.append('scheduled_payment_date', row.scheduled_payment_date);
        formData.append('scheduled_amount', row.scheduled_amount.toString());
        
        if (row.received_amount !== undefined) {
          formData.append('received_amount', row.received_amount.toString());
        }
        if (row.payment_received_date) {
          formData.append('payment_received_date', row.payment_received_date);
        }
        if (row.remarks) {
          formData.append('remarks', row.remarks);
        }
  
        console.log(`Submitting row ${i + 1}:`, formData);
        const response = await fetch(`${API_BASE_URL}/api/funding/v1/disbursements/`, {
          method: 'POST',
          body: formData
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          console.error(`Error data for row ${i + 1}:`, errorData);
          throw new Error(errorData.message || 'Failed to submit disbursement data');
        }
  
        const data = await response.json();
        console.log(`Success data for row ${i + 1}:`, data);
      }
      
      console.log('All rows submitted successfully');
      toast.success('Disbursement data submitted successfully');
      
      // Reset the table after successful submission
      const emptyRow = {
        sr_no: 1,
        particulars: "",
        scheduled_payment_date: "",
        scheduled_amount: 0,
        received_amount: undefined,
        payment_received_date: undefined,
        remarks: ""
      };
      
      // Reset all fields for the first row
      Object.keys(emptyRow).forEach(field => {
        onInputChange(0, field, emptyRow[field as keyof DisbursementRow]);
      });
      
    } catch (error) {
      console.error('Error submitting disbursement data:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to submit disbursement data');
    }
  };

  return (
    <div className="space-y-4">
      <div className="overflow-x-auto">
        <table className="w-full border-collapse bg-white">
          <thead>
            <tr className="bg-gray-100">
              <th className="p-3 border text-left font-semibold">Sr No.</th>
              <th className="p-3 border text-left font-semibold">Particulars</th>
              <th className="p-3 border text-center font-semibold" colSpan={2}>Scheduled payments</th>
              <th className="p-3 border text-center font-semibold" colSpan={2}>Received payments</th>
              <th className="p-3 border text-left font-semibold">Remarks</th>
            </tr>
            <tr className="bg-gray-50">
              <th className="p-3 border" colSpan={2}></th>
              <th className="p-3 border text-left font-semibold">Date of disbursement</th>
              <th className="p-3 border text-right font-semibold">Amount</th>
              <th className="p-3 border text-left font-semibold">Date of disbursement</th>
              <th className="p-3 border text-right font-semibold">Amount</th>
              <th className="p-3 border"></th>
            </tr>
          </thead>
          <tbody>
            {disbursementRows.map((row, index) => (
              <tr key={index} className="hover:bg-gray-50">
                <td className="p-3 border">{row.sr_no}</td>
                <td className="p-3 border">
                  <Input
                    type="text"
                    value={row.particulars}
                    onChange={(e) => onInputChange(index, 'particulars', e.target.value)}
                    className={`w-full ${errors[`${index}-particulars`] ? 'border-red-500' : ''}`}
                  />
                </td>
                <td className="p-3 border">
                  <Input
                    type="date"
                    value={row.scheduled_payment_date}
                    onChange={(e) => onInputChange(index, 'scheduled_payment_date', e.target.value)}
                    className={`w-full ${errors[`${index}-scheduled_payment_date`] ? 'border-red-500' : ''}`}
                  />
                </td>
                <td className="p-3 border">
                  <Input
                    type="number"
                    value={row.scheduled_amount}
                    onChange={(e) => onInputChange(index, 'scheduled_amount', parseFloat(e.target.value))}
                    className={`text-right ${errors[`${index}-scheduled_amount`] ? 'border-red-500' : ''}`}
                  />
                </td>
                <td className="p-3 border">
                  <Input
                    type="date"
                    value={row.payment_received_date || ''}
                    onChange={(e) => onInputChange(index, 'payment_received_date', e.target.value)}
                    className="w-full"
                  />
                </td>
                <td className="p-3 border">
                  <Input
                    type="number"
                    value={row.received_amount || ''}
                    onChange={(e) => onInputChange(index, 'received_amount', parseFloat(e.target.value))}
                    className={`text-right ${errors[`${index}-received_amount`] ? 'border-red-500' : ''}`}
                  />
                </td>
                <td className="p-3 border">
                  <Input
                    type="text"
                    value={row.remarks || ''}
                    onChange={(e) => onInputChange(index, 'remarks', e.target.value)}
                    className="w-full"
                  />
                </td>
              </tr>
            ))}
            <tr className="bg-gray-100 font-bold">
              <td className="p-3 border" colSpan={3}>Total</td>
              <td className="p-3 border text-right">{calculateTotal('scheduled_amount').toFixed(2)}</td>
              <td className="p-3 border"></td>
              <td className="p-3 border text-right">{calculateTotal('received_amount').toFixed(2)}</td>
              <td className="p-3 border"></td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="flex justify-between items-center mt-4">
        <Button
          onClick={onAddRow}
          variant="outline"
          className="flex items-center gap-2"
        >
          <PlusIcon className="w-4 h-4" />
          Add Row
        </Button>
        <Button
          onClick={handleSubmit}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          Submit Disbursement Data
        </Button>
      </div>
    </div>
  );
}