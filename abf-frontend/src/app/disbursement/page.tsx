"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { ExcelDataTable } from "./components/ExcelDataTable";
import { ManualEntryTable } from "./components/ManualEntryTable";
import { useRouter } from "next/navigation";

interface DisbursementRow {
  sr_no: number;
  particulars: string;
  scheduled_payment_date: string;
  scheduled_amount: number;
  received_amount?: number;
  pending_amount?: number;
  payment_received_date?: string;
  remarks?: string;
}

export default function DisbursementPage() {
  const router = useRouter();
  const [file, setFile] = useState<File | null>(null);
  const [excelData, setExcelData] = useState<DisbursementRow[]>([]);
  const [manualRows, setManualRows] = useState<DisbursementRow[]>([]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    console.log("File selected:", file);
    
    if (file) {
      if (!file.name.match(/\.(xlsx|xls|csv)$/)) {
        toast.error("Please upload only Excel or CSV files (.xlsx, .xls, or .csv)");
        return;
      }
      setFile(file);
      console.log("File set in state:", file.name);
      
      // Mock data for testing without backend
      console.log("Generating mock data for testing");
      const mockData = [
        {
          particulars: "Q1 Grant Payment",
          scheduled_payment_date: "2024-03-15",
          scheduled_amount: 10000,
          payment_received_date: "2024-03-15",
          received_amount: 10000,
          remarks: "Received on time"
        },
        {
          particulars: "Q2 Grant Payment",
          scheduled_payment_date: "2024-06-15",
          scheduled_amount: 5000,
          payment_received_date: "2024-01-15",
          received_amount: 5000,
          remarks: "Early payment received"
        },
        {
          particulars: "Q3 Grant Payment",
          scheduled_payment_date: "2024-09-15",
          scheduled_amount: 3000,
          payment_received_date: "2024-02-15",
          received_amount: 3000,
          remarks: "Early payment"
        }
      ];
      
      try {
        console.log("Processing mock data");
        const processedData = mockData.map((row, index) => ({
          ...row,
          sr_no: index + 1,
          pending_amount: row.scheduled_amount - (row.received_amount || 0)
        }));
        
        console.log("Processed data:", processedData);
        setExcelData(processedData);
        toast.success("File uploaded successfully. Data is now displayed in the table below.");
        event.target.value = "";
        setFile(null);
      } catch (error) {
        console.error("Error processing file:", error);
        toast.error("Error processing file");
      }
    }
  };

  const handleAddRow = () => {
    const newRow: DisbursementRow = {
      sr_no: manualRows.length + 1,
      particulars: "",
      scheduled_payment_date: "",
      scheduled_amount: 0,
      received_amount: undefined,
      pending_amount: undefined,
      payment_received_date: undefined,
      remarks: ""
    };
    setManualRows([...manualRows, newRow]);
  };

  const handleInputChange = <K extends keyof DisbursementRow> (index: number, field: K, value: DisbursementRow[K]) => {
    const updatedRows = [...manualRows];
    updatedRows[index][field] = value;
    
    // Update pending amount when scheduled or received amount changes
    if (field === 'scheduled_amount' || field === 'received_amount') {
      const row = updatedRows[index];
      row.pending_amount = row.scheduled_amount - (row.received_amount || 0);
    }
    
    setManualRows(updatedRows);
  };

  const handleExcelInputChange = <K extends keyof DisbursementRow>(index: number, field: K, value: DisbursementRow[K]) => {
    const updatedRows = [...excelData];
    updatedRows[index][field] = value;
    
    // Update pending amount when scheduled or received amount changes
    if (field === 'scheduled_amount' || field === 'received_amount') {
      const row = updatedRows[index];
      row.pending_amount = row.scheduled_amount - (row.received_amount || 0);
    }
    
    setExcelData(updatedRows);
  };

  return (
    <div className="min-h-screen p-8 bg-gradient-to-br from-gray-50 to-gray-100">
      <Card className="max-w-7xl mx-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-2xl font-bold">Disbursement Management</CardTitle>
          <Button 
            onClick={() => router.push('/disbursement/history')} 
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            View Submitted Disbursements
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <Card className="p-4">
              <CardTitle className="text-xl mb-4">Excel Upload</CardTitle>
              <div className="flex items-center space-x-4">
                <input
                  type="file"
                  accept=".xlsx,.xls,.csv"
                  onChange={handleFileUpload}
                  className="hidden"
                  id="excel-upload"
                />
                <Button
                  variant="default"
                  onClick={() => document.getElementById("excel-upload")?.click()}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Upload File
                </Button>
                <p className="text-sm text-gray-500">
                  {file ? file.name : "Only .xlsx, .xls, or .csv files are allowed"}
                </p>
              </div>
            </Card>

            {excelData.length > 0 && (
              <Card className="p-4">
                <CardTitle className="text-xl mb-4">Excel Data</CardTitle>
                <ExcelDataTable 
                  data={excelData} 
                  onInputChange={handleExcelInputChange}
                  editable={true}
                />
              </Card>
            )}

            <Card className="p-4">
              <CardTitle className="text-xl mb-4">Manual Entry</CardTitle>
              <ManualEntryTable
                disbursementRows={manualRows}
                onAddRow={handleAddRow}
                onInputChange={handleInputChange}
              />
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}