"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import apiClient from "@/lib/apiClient";
import { useAuth } from "@/lib/apiClient";
import { useAuthContext } from "@/contexts/AuthContext";

export default function LoginPage() {
  const router = useRouter();
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("e,an6[zE4Xl1");
  const [error, setError] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  const { login } = useAuthContext();

  useEffect(() => {
    const redirectIfLoggedIn = async () => {
      const isAuthenticated = await useAuth();
      if (isAuthenticated) {
        router.push('/')
      }
    }

    redirectIfLoggedIn();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(""); // Clear previous errors
    if (!email || !password) {
      setError("Please enter both email and password");
      return;
    }

    // Enhanced email validation regex for testing
    if (!email.includes("@")) {
      setError("Please enter a valid email address");
      return;
    }

    try {
      const response = await apiClient.post("/api/auth/v1/login/",{
        username: email,
        password: password
      },
      {
        headers: {
          "Content-Type": "application/json"
        }
      }
    );

      if (response.status == 200) {
        const authResult = response.data.AuthenticationResult;

        const user = {
          email: email,
          firstName: authResult.User.FirstName,
          lastName: authResult.User.LastName,
          role: authResult.User.Role,
          type: authResult.User.Type
        }
        console.log("authResult.IdToken = " + authResult.IdToken);

        login(user, authResult.IdToken)
        // Login successfull go to home screen (right now it's for grantee only)
        setTimeout(() => {
          router.push('/');
        }, 50);
      }

    }  catch (error: any) {
      if (error?.response?.status) {
        const status = error.response.status;
        const data = error.response.data;

        console.log("Login error:", status, data);

        if (status === 403 && data?.status === 'NEW_PASSWORD_REQUIRED') {
          sessionStorage.setItem('session', data.session)
          sessionStorage.setItem('username', email);
          router.push('/confirm-new-password');
        } else {
          setError(data?.message || "Invalid login credentials.");
        }
      } else if (error?.request) {
        // Request was made, but no response
        console.error("No response received:", error.request);
        setError("No response from server. Please try again.");
      } else {
        // Something else went wrong
        console.error("Unexpected error:", error);
        setError(error.message || "Something went wrong.");
      }
    }
  };



  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Professional Multi-layer Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-gray-50"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-teal-50/90 via-white/60 to-purple-50/80"></div>
      <div className="absolute inset-0 bg-gradient-to-bl from-cyan-50/70 via-transparent to-emerald-50/60"></div>
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-50/30 to-transparent"></div>

      {/* Professional Background Pattern */}
      <div className="absolute inset-0" style={{
        backgroundImage: `
          radial-gradient(circle at 25% 25%, rgba(20, 184, 166, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),
          radial-gradient(circle at 75% 25%, rgba(59, 130, 246, 0.06) 0%, transparent 50%),
          radial-gradient(circle at 25% 75%, rgba(6, 182, 212, 0.09) 0%, transparent 50%)
        `
      }}></div>

      {/* Static Blur Elements for Depth */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Large Static Orbs */}
        <div className="absolute -top-60 -right-60 w-[500px] h-[500px] bg-gradient-to-br from-blue-200/35 via-purple-200/25 to-teal-200/30 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-60 -left-60 w-[450px] h-[450px] bg-gradient-to-tr from-teal-200/40 via-cyan-200/30 to-indigo-200/25 rounded-full blur-3xl"></div>
        <div className="absolute top-10 right-10 w-80 h-80 bg-gradient-to-r from-purple-200/30 via-blue-200/25 to-teal-200/35 rounded-full blur-2xl"></div>
        <div className="absolute bottom-10 left-10 w-96 h-96 bg-gradient-to-l from-indigo-200/35 via-teal-200/30 to-cyan-200/25 rounded-full blur-2xl"></div>

        {/* Medium Static Elements */}
        <div className="absolute top-1/4 right-1/3 w-48 h-48 bg-gradient-to-br from-cyan-200/30 via-teal-200/25 to-blue-200/20 rounded-full blur-xl"></div>
        <div className="absolute bottom-1/3 left-1/4 w-56 h-56 bg-gradient-to-tr from-purple-200/25 via-indigo-200/30 to-teal-200/25 rounded-full blur-xl"></div>
        <div className="absolute top-1/2 left-1/6 w-40 h-40 bg-gradient-to-r from-teal-200/35 via-cyan-200/25 to-emerald-200/30 rounded-full blur-lg"></div>

        {/* Additional Depth Elements */}
        <div className="absolute top-1/6 left-1/2 w-32 h-32 bg-gradient-to-br from-indigo-200/20 via-blue-200/25 to-teal-200/20 rounded-full blur-lg"></div>
        <div className="absolute bottom-1/6 right-1/2 w-36 h-36 bg-gradient-to-tl from-purple-200/20 via-cyan-200/25 to-emerald-200/20 rounded-full blur-lg"></div>
      </div>

      {/* Professional Geometric Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Static Geometric Shapes */}
        <div className="absolute inset-0 opacity-8">
          <div className="absolute top-20 left-20 w-32 h-32 border border-teal-300/30 rotate-45"></div>
          <div className="absolute top-40 right-32 w-24 h-24 border border-purple-300/25 rotate-12"></div>
          <div className="absolute bottom-32 left-40 w-28 h-28 border border-cyan-300/30 -rotate-12"></div>
          <div className="absolute bottom-20 right-20 w-36 h-36 border border-indigo-300/25 rotate-45"></div>
          <div className="absolute top-1/2 left-1/3 w-20 h-20 border border-blue-300/20 rotate-30"></div>
          <div className="absolute top-1/3 right-1/3 w-16 h-16 border border-emerald-300/25 -rotate-30"></div>
        </div>

        {/* Subtle Dot Pattern */}
        <div className="absolute inset-0 opacity-15">
          <div className="absolute top-1/6 left-1/5 w-2 h-2 bg-teal-400/40 rounded-full"></div>
          <div className="absolute top-2/3 right-1/5 w-1.5 h-1.5 bg-purple-400/35 rounded-full"></div>
          <div className="absolute bottom-1/4 left-1/3 w-2 h-2 bg-blue-400/40 rounded-full"></div>
          <div className="absolute top-1/2 right-1/6 w-1.5 h-1.5 bg-cyan-400/35 rounded-full"></div>
          <div className="absolute top-1/3 left-2/3 w-1 h-1 bg-indigo-400/30 rounded-full"></div>
          <div className="absolute bottom-1/6 right-1/3 w-1 h-1 bg-emerald-400/35 rounded-full"></div>
          <div className="absolute top-3/4 left-1/6 w-1.5 h-1.5 bg-teal-400/30 rounded-full"></div>
        </div>

        {/* Enhanced Line Patterns */}
        <div className="absolute inset-0 opacity-12">
          <svg className="w-full h-full">
            <defs>
              <linearGradient id="lineGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#14b8a6" stopOpacity="0.3" />
                <stop offset="50%" stopColor="#8b5cf6" stopOpacity="0.2" />
                <stop offset="100%" stopColor="#06b6d4" stopOpacity="0.3" />
              </linearGradient>
              <linearGradient id="lineGradient2" x1="0%" y1="100%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#06b6d4" stopOpacity="0.25" />
                <stop offset="50%" stopColor="#6366f1" stopOpacity="0.15" />
                <stop offset="100%" stopColor="#10b981" stopOpacity="0.25" />
              </linearGradient>
            </defs>
            <line x1="10%" y1="20%" x2="90%" y2="80%" stroke="url(#lineGradient1)" strokeWidth="1.5" />
            <line x1="20%" y1="90%" x2="80%" y2="10%" stroke="url(#lineGradient2)" strokeWidth="1.5" />
            <line x1="5%" y1="50%" x2="95%" y2="50%" stroke="url(#lineGradient1)" strokeWidth="0.8" />
            <line x1="50%" y1="5%" x2="50%" y2="95%" stroke="url(#lineGradient2)" strokeWidth="0.6" />
          </svg>
        </div>
      </div>
      <div className="relative w-full max-w-md p-6">
        {/* Header Section */}
        <div className="text-center mb-6">
          {/* Company Logos */}
          <div className="flex items-center justify-center gap-8 mb-6">
            {/* OAB Logo */}
            <div className="flex flex-col items-center">
              <img
                src="/OAB-logo.svg"
                alt="OAB Logo"
                className="w-30 h-26 object-contain drop-shadow-lg"
                onError={(e) => {
                  // Show fallback text logo
                  const target = e.currentTarget as HTMLImageElement;
                  target.style.display = 'none';
                  const fallback = target.nextElementSibling as HTMLElement;
                  if (fallback) fallback.style.display = 'block';
                }}
              />
              <span className="text-lg font-bold text-teal-700 hidden">OAB</span>
            </div>

            {/* AB Foundation Logo */}
            <div className="flex flex-col items-center">
              <img
                src="/AB_Foundation.png"
                alt="AB Foundation Logo"
                className="w-30 h-46 object-contain drop-shadow-lg"
                onError={(e) => {
                  // Show fallback text logo
                  const target = e.currentTarget as HTMLImageElement;
                  target.style.display = 'none';
                  const fallback = target.nextElementSibling as HTMLElement;
                  if (fallback) fallback.style.display = 'block';
                }}
              />
              <span className="text-lg font-bold text-teal-700 hidden">AB Foundation</span>
            </div>
          </div>

          <h1 className="text-xl font-bold text-gray-900 mb-2 drop-shadow-sm">
            Sign In to Continue
          </h1>
          <p className="text-sm text-gray-700">
            Access your Dashboard
          </p>
        </div>

        {/* Main Card */}
        <Card className="relative overflow-hidden border-0 bg-white/90 backdrop-blur-md">
          {/* Multiple shadow layers for depth */}
          <div className="absolute inset-0 bg-white/95 rounded-xl shadow-2xl"></div>
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-teal-500/5 rounded-xl"></div>
          <div className="absolute -inset-1 bg-gradient-to-r from-purple-300/20 via-blue-300/20 to-teal-300/20 rounded-xl blur-sm"></div>
          <div className="absolute -inset-2 bg-gradient-to-br from-indigo-200/15 via-purple-200/15 to-cyan-200/15 rounded-xl blur-md"></div>

          {/* Glowing border effect */}
          <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-400/20 via-blue-400/20 to-teal-400/20 p-[1px]">
            <div className="h-full w-full rounded-xl bg-white/95"></div>
          </div>

          <CardContent className="relative p-6 z-10">
            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <Alert variant="destructive" className="bg-red-50 border border-red-200 rounded-lg py-2">
                  <AlertDescription className="text-red-700 font-medium text-sm">{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-4">
                {/* Email Field */}
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-700 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-teal-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Email Address
                  </label>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="h-10 rounded-lg border-gray-300 focus:border-teal-500 focus:ring-1 focus:ring-teal-200 transition-all duration-200"
                  />
                </div>

                {/* Password Field */}
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <label className="text-sm font-medium text-gray-700 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-teal-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                      Password
                    </label>
                    <a href="#" className="text-sm text-teal-600 hover:text-teal-700 transition-colors">Forgot password?</a>
                  </div>
                  <div className="relative">
                    <Input
                      type={showPassword ? "text" : "password"}
                      placeholder="••••••••"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="h-10 rounded-lg border-gray-300 focus:border-teal-500 focus:ring-1 focus:ring-teal-200 transition-all duration-200 pr-10"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-teal-600 transition-colors"
                    >
                      {showPassword ? (
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88" />
                        </svg>
                      ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                          <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                className="w-full h-11 bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 text-white font-semibold transition-all duration-300 transform hover:scale-[1.02] rounded-lg shadow-lg hover:shadow-xl"
              >
                <div className="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                  </svg>
                  Sign In
                </div>
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-6">
          <p className="text-xs text-gray-600 max-w-sm mx-auto">
            By signing in, you agree to our Terms of Service and Privacy Policy.
          </p>
          <div className="mt-3 flex items-center justify-center gap-2 text-xs text-gray-500">
            <div className="w-1 h-1 bg-teal-500 rounded-full shadow-sm shadow-teal-500/30"></div>
            <span>Secure • Encrypted • Protected</span>
            <div className="w-1 h-1 bg-teal-500 rounded-full shadow-sm shadow-teal-500/30"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
