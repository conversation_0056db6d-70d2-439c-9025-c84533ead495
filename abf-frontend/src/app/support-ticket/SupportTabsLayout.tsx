"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { Layout } from "@/components/Layout";

const tabs = [
  { name: "My Tickets", path: "/support-ticket/my-tickets" },
  { name: "FAQs", path: "/support-ticket/articles-and-faq" },
  { name: "Resources", path: "/support-ticket/resources" },
];

export default function SupportTabsLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const [activePath, setActivePath] = useState<string | null>(null);

  useEffect(() => {
    setActivePath(pathname);
  }, [pathname]);

const isActiveTab = (tabPath: string) => {
  return activePath?.startsWith(tabPath);
};

  return (
    <Layout>
      <div className="h-full flex flex-col bg-gradient-to-br from-slate-50 to-gray-100 m-0 p-0">
        {/* Enhanced Professional Header */}
        <div className="professional-header-gradient shadow-lg border-b border-gray-800 m-0">
          <div className="w-full px-8 py-2">
            {/* Header Title */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-teal-400 to-teal-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-white">Support Center</h1>
                  <p className="text-sm text-gray-300">Professional assistance and resources</p>
                </div>
              </div>
              <div className="hidden md:flex items-center space-x-2 text-xs text-gray-400">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>System Online</span>
              </div>
            </div>

            {/* Enhanced Navigation Tabs */}
            <div className="flex space-x-1 bg-black/20 rounded-xl p-1">
              {tabs.map((tab) => (
                <button
                  key={tab.name}
                  onClick={() => router.push(tab.path)}
                  className={`relative px-6 py-3 text-sm font-semibold rounded-lg transition-all duration-300 ${
                    isActiveTab(tab.path)
                      ? "bg-white text-gray-900 shadow-lg transform scale-105"
                      : "text-gray-300 hover:text-white hover:bg-white/10"
                  }`}
                >
                  {tab.name}
                  {isActiveTab(tab.path) && (
                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-teal-500 rounded-full"></div>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Enhanced Main Content Area */}
        <div className="flex-1 w-full px-8 py-8 overflow-y-auto m-0 relative">
          {/* Subtle background pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 1px 1px, rgba(15, 118, 110, 0.3) 1px, transparent 0)`,
              backgroundSize: '20px 20px'
            }}></div>
          </div>
          <div className="relative z-10">
            {children}
          </div>
        </div>
      </div>
    </Layout>
  );
}