"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { Layout } from "@/components/Layout";

const tabs = [
  { name: "My Tickets", path: "/support-ticket/my-tickets" },
  { name: "FAQs", path: "/support-ticket/articles-and-faq" },
  { name: "Resources", path: "/support-ticket/resources" },
];

export default function SupportTabsLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const [activePath, setActivePath] = useState<string | null>(null);

  useEffect(() => {
    setActivePath(pathname);
  }, [pathname]);

const isActiveTab = (tabPath: string) => {
  return activePath?.startsWith(tabPath);
};

  return (
    <Layout>
      <div className="h-full flex flex-col bg-gray-50 m-0 p-0">
        {/* Tabs Header */}
        <div className="bg-matte-black border-b border-gray-200 m-0">
          <div className="w-full px-6">
            <div className="flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.name}
                  onClick={() => router.push(tab.path)}
                  className={`py-4 px-1 text-sm font-medium transition-colors duration-200 border-b-2 ${
                    isActiveTab(tab.path)
                      ? "text-teal-500 border-teal-500"
                      : "text-gray-400 hover:text-white hover:border-teal-500 border-transparent"
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 w-full px-6 py-8 bg-transparent overflow-y-auto m-0">
          {children}
        </div>
      </div>
    </Layout>
  );
}