// src/app/support-ticket/resources/page.tsx
"use client";

import Head from "next/head";
import { useEffect, useState } from "react";
import { FileText, Download, DownloadCloud } from "lucide-react";
import { getResources } from "@/services/resources-service";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface Resource {
  id: number;
  title: string;
  file_description?: string;
  created_at: string;
  attachments: string[];
  file_names?: string[];
}

export default function SupportResources() {
  const [resources, setResources] = useState<Resource[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredResources, setFilteredResources] = useState<Resource[]>([]);

  // Fetch resources on component mount
  useEffect(() => {
    const fetchResources = async () => {
      try {
        const data = await getResources();
        const mappedData = data.map((resource: Resource) => ({
          ...resource,
          file_names: resource.file_names || resource.attachments.map((url, i) => url.split('/').pop() || `File ${i + 1}`),
        }));
        setResources(mappedData);
        setFilteredResources(mappedData);
        setLoading(false);
      } catch (err: any) {
        setError(err.message);
        setLoading(false);
      }
    };
    fetchResources();
  }, []);

  // Handle search functionality
  useEffect(() => {
    if (!searchTerm) {
      setFilteredResources(resources);
      return;
    }
    const lowerSearch = searchTerm.toLowerCase();
    const filtered = resources.filter(
      (resource) =>
        resource.title.toLowerCase().includes(lowerSearch) ||
        (resource.file_description && resource.file_description.toLowerCase().includes(lowerSearch))
    );
    setFilteredResources(filtered);
  }, [searchTerm, resources]);

  // Handle individual file download
  const handleDownload = (fileUrl: string, fileName: string) => {
    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = fileName || "resource";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Handle download all files via server-side ZIP
  const handleDownloadAll = async () => {
    const JSZip = (await import('jszip')).default;
    const FileSaver = (await import('file-saver')).default;
    const zip = new JSZip();
    try {
      for (const resource of resources) {
        for (let i = 0; i < resource.attachments.length; i++) {
          const fileUrl = resource.attachments[i];
          const fileName = resource.file_names?.[i] || `File ${i + 1}`;
          const response = await fetch(fileUrl);
          if (!response.ok) throw new Error(`Failed to fetch ${fileName}`);
          const blob = await response.blob();
          zip.file(fileName, blob);
        }
      }
      const content = await zip.generateAsync({ type: 'blob' });
      FileSaver.saveAs(content, 'resources.zip');
    } catch (err: any) {
      console.error('Download all error:', err);
      setError('Failed to download all files');
    }
  };

  return (
    <>
      <Head>
        <title>Download Resources - Grant Management System</title>
      </Head>
      <div className="min-h-screen p-8 bg-transparent">
        <div className="w-full max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center space-x-4 mb-8">
            <FileText className="w-10 h-10 text-teal-600" />
            <div>
              <h2 className="text-3xl font-bold text-gray-900 tracking-tight">
                Resources
              </h2>
              <p className="text-gray-500 mt-1">
                Download essential documents, templates, and guides.
              </p>
            </div>
          </div>

          {/* Search and Download All */}
          <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-8 p-4 bg-white rounded-xl shadow-md">
            <div className="relative w-full md:flex-grow">
              <input
                type="text"
                placeholder="Search resources by title or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full p-3 pl-10 rounded-full border border-gray-300 shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-sm bg-white transition-all duration-300"
              />
              <svg
                className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 1116.65 16.65z"
                />
              </svg>
            </div>
            {filteredResources.length > 0 && (
              <Button
                onClick={handleDownloadAll}
                className="flex items-center space-x-2 bg-teal-600 hover:bg-teal-700 text-white text-sm px-5 py-2.5 rounded-full transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                aria-label="Download all resources as ZIP"
              >
                <DownloadCloud className="w-5 h-5" />
                <span>Download All</span>
              </Button>
            )}
          </div>

          {/* Resources List */}
          <div className="bg-white rounded-xl shadow-xl overflow-hidden">
            <CardContent className="p-6">
              {loading && (
                <p className="text-gray-500 text-center py-16 font-medium animate-pulse">
                  Loading resources...
                </p>
              )}
              {error && (
                <div className="text-center text-red-600 py-16">
                  <p className="text-base font-medium bg-red-50 p-4 rounded-lg">
                    {error}
                  </p>
                </div>
              )}
              {!loading && !error && filteredResources.length === 0 && (
                <div className="text-center text-gray-500 py-16">
                  <FileText className="w-12 h-12 mx-auto text-gray-400" />
                  <p className="text-xl mt-4 font-semibold">
                    No resources found.
                  </p>
                  <p className="text-sm mt-2">
                    There are currently no resources matching your search.
                  </p>
                </div>
              )}
              <div className="space-y-5">
                {filteredResources.map((resource) => (
                  <div
                    key={resource.id}
                    className="group bg-gray-50 border border-gray-200 rounded-lg shadow-sm hover:shadow-lg transition-all duration-300"
                  >
                    <div className="p-5 flex flex-col md:flex-row justify-between items-start">
                      <div className="flex-1 mb-4 md:mb-0">
                        <div className="flex items-center space-x-4">
                          <FileText className="w-7 h-7 text-teal-500" />
                          <h3 className="text-lg font-semibold text-gray-800 group-hover:text-teal-600 transition-colors duration-300">
                            {resource.title}
                          </h3>
                        </div>
                        <p className="text-sm text-gray-500 mt-2 ml-11">
                          Created: {new Date(resource.created_at).toLocaleDateString()}
                        </p>
                        {resource.file_description && (
                          <p className="text-sm text-gray-600 mt-2 ml-11 line-clamp-3">
                            {resource.file_description}
                          </p>
                        )}
                      </div>
                      {resource.attachments?.length > 0 && (
                        <div className="flex flex-col space-y-2 w-full md:w-auto">
                          {resource.attachments.map((attachment, index) => (
                            <Button
                              key={index}
                              onClick={() => handleDownload(attachment, resource.file_names?.[index] || `File ${index + 1}`)}
                              className="flex items-center justify-center space-x-2 bg-teal-100 text-teal-800 hover:bg-teal-200 text-sm px-4 py-2 rounded-lg transition-all duration-300 shadow-sm"
                              aria-label={`Download ${resource.file_names?.[index] || `File ${index + 1}`}`}
                            >
                              <Download className="w-4 h-4" />
                              <span>{resource.file_names?.[index] || `File ${index + 1}`}</span>
                            </Button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </div>
        </div>
      </div>
    </>
  );
}