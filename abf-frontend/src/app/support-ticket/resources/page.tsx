// src/app/support-ticket/resources/page.tsx
"use client";

import Head from "next/head";
import { useEffect, useState } from "react";
import { FileText, Download, DownloadCloud } from "lucide-react";
import { getResources } from "@/services/resources-service";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface Resource {
  id: number;
  title: string;
  file_description?: string;
  created_at: string;
  attachments: string[];
  file_names?: string[];
}

export default function SupportResources() {
  const [resources, setResources] = useState<Resource[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredResources, setFilteredResources] = useState<Resource[]>([]);

  // Fetch resources on component mount
  useEffect(() => {
    const fetchResources = async () => {
      try {
        const data = await getResources();
        const mappedData = data.map((resource: Resource) => ({
          ...resource,
          file_names: resource.file_names || resource.attachments.map((url, i) => url.split('/').pop() || `File ${i + 1}`),
        }));
        setResources(mappedData);
        setFilteredResources(mappedData);
        setLoading(false);
      } catch (err: any) {
        setError(err.message);
        setLoading(false);
      }
    };
    fetchResources();
  }, []);

  // Handle search functionality
  useEffect(() => {
    if (!searchTerm) {
      setFilteredResources(resources);
      return;
    }
    const lowerSearch = searchTerm.toLowerCase();
    const filtered = resources.filter(
      (resource) =>
        resource.title.toLowerCase().includes(lowerSearch) ||
        (resource.file_description && resource.file_description.toLowerCase().includes(lowerSearch))
    );
    setFilteredResources(filtered);
  }, [searchTerm, resources]);

  // Handle individual file download
  const handleDownload = (fileUrl: string, fileName: string) => {
    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = fileName || "resource";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Handle download all files via server-side ZIP
  const handleDownloadAll = async () => {
    const JSZip = (await import('jszip')).default;
    const FileSaver = (await import('file-saver')).default;
    const zip = new JSZip();
    try {
      for (const resource of resources) {
        for (let i = 0; i < resource.attachments.length; i++) {
          const fileUrl = resource.attachments[i];
          const fileName = resource.file_names?.[i] || `File ${i + 1}`;
          const response = await fetch(fileUrl);
          if (!response.ok) throw new Error(`Failed to fetch ${fileName}`);
          const blob = await response.blob();
          zip.file(fileName, blob);
        }
      }
      const content = await zip.generateAsync({ type: 'blob' });
      FileSaver.saveAs(content, 'resources.zip');
    } catch (err: any) {
      console.error('Download all error:', err);
      setError('Failed to download all files');
    }
  };

  return (
    <>
      <Head>
        <title>Download Resources - Grant Management System</title>
      </Head>
      <div className="min-h-screen p-8">
        <div className="w-full max-w-7xl mx-auto">
          {/* Enhanced Professional Header */}
          <div className="professional-card p-8 mb-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                  <FileText className="w-7 h-7 text-white" />
                </div>
                <div>
                  <h2 className="text-4xl font-bold text-gray-900 mb-2">
                    <span className="professional-text-gradient">Resource Library</span>
                  </h2>
                  <p className="text-gray-600 text-lg">
                    Access essential documents, templates, and comprehensive guides for grant management
                  </p>
                </div>
              </div>
              <div className="hidden lg:flex items-center space-x-4">
                <div className="text-right">
                  <p className="text-2xl font-bold text-gray-900">{filteredResources.length}</p>
                  <p className="text-sm text-gray-500">Available Resources</p>
                </div>
              </div>
            </div>

            {/* Enhanced Search and Actions */}
            <div className="flex flex-col lg:flex-row justify-between items-center gap-6">
              <div className="relative w-full lg:flex-grow">
                <svg
                  className="absolute left-5 top-1/2 transform -translate-y-1/2 w-6 h-6 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 1116.65 16.65z"
                  />
                </svg>
                <input
                  type="text"
                  placeholder="Search through our resource library..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="professional-input w-full pl-16 pr-6 py-4 text-lg"
                />
              </div>
              {filteredResources.length > 0 && (
                <Button
                  onClick={handleDownloadAll}
                  className="professional-button-primary text-lg px-8 py-4"
                  aria-label="Download all resources as ZIP"
                >
                  <DownloadCloud className="w-6 h-6 mr-3" />
                  Download All Resources
                </Button>
              )}
            </div>
          </div>

          {/* Enhanced Resources Grid */}
          {loading ? (
            <div className="professional-card p-20 text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-100 to-blue-50 rounded-full mb-6">
                <div className="w-10 h-10 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              </div>
              <p className="text-gray-600 font-medium text-lg">Loading resource library...</p>
            </div>
          ) : error ? (
            <div className="professional-card p-20 text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-red-100 to-red-50 rounded-full mb-6">
                <FileText className="w-10 h-10 text-red-500" />
              </div>
              <h3 className="text-red-600 font-bold text-2xl mb-3">Error Loading Resources</h3>
              <p className="text-gray-600 text-lg">{error}</p>
            </div>
          ) : filteredResources.length === 0 ? (
            <div className="professional-card p-20 text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-50 rounded-full mb-6">
                <FileText className="w-10 h-10 text-gray-400" />
              </div>
              <h3 className="text-gray-700 font-bold text-2xl mb-3">No Resources Found</h3>
              <p className="text-gray-500 text-lg mb-6 max-w-md mx-auto">
                There are currently no resources matching your search criteria.
              </p>
              <button
                onClick={() => setSearchTerm("")}
                className="professional-button-primary"
              >
                Show All Resources
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {filteredResources.map((resource) => (
                <div
                  key={resource.id}
                  className="professional-card p-8 group hover:scale-105 transition-all duration-300"
                >
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-50 rounded-xl flex items-center justify-center group-hover:from-blue-200 group-hover:to-blue-100 transition-all">
                        <FileText className="w-7 h-7 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-700 transition-colors mb-1">
                          {resource.title}
                        </h3>
                        <p className="text-sm text-gray-500">
                          Created {new Date(resource.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-gray-900">{resource.attachments?.length || 0}</p>
                      <p className="text-xs text-gray-500">Files</p>
                    </div>
                  </div>

                  {resource.file_description && (
                    <div className="mb-6">
                      <p className="text-gray-700 leading-relaxed">
                        {resource.file_description}
                      </p>
                    </div>
                  )}

                  {resource.attachments?.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="text-sm font-semibold text-gray-800 mb-3">Available Downloads:</h4>
                      {resource.attachments.map((attachment, index) => (
                        <button
                          key={index}
                          onClick={() => handleDownload(attachment, resource.file_names?.[index] || `File ${index + 1}`)}
                          className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-blue-50 hover:to-blue-100 rounded-xl border border-gray-200 hover:border-blue-300 transition-all duration-300 group/download"
                        >
                          <div className="flex items-center space-x-3">
                            <Download className="w-5 h-5 text-gray-600 group-hover/download:text-blue-600 transition-colors" />
                            <span className="font-medium text-gray-800 group-hover/download:text-blue-800 transition-colors">
                              {resource.file_names?.[index] || `File ${index + 1}`}
                            </span>
                          </div>
                          <div className="text-xs text-gray-500 group-hover/download:text-blue-600 transition-colors">
                            Click to download
                          </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
}