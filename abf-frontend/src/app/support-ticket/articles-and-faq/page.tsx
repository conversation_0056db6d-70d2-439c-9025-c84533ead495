// app/support-faq/page.tsx
"use client";

import { useEffect, useState } from "react";
import Head from "next/head";
import { Search, ChevronDown, ChevronUp, HelpCircle } from "lucide-react";

export default function SupportFAQ() {
  const [faqs, setFaqs] = useState([
    {
      question: "What is the eligibility criteria for applying to a grant?",
      answer:
        "To be eligible for a grant, your organization must be a registered non-profit, have a valid tax-exempt status, and align with the grant's focus area (e.g., education, healthcare, or environmental sustainability). Additionally, you must submit a detailed project proposal and budget plan.",
      isOpen: false,
    },
    {
      question: "How long does the grant approval process take?",
      answer:
        "The grant approval process typically takes 6-8 weeks from the submission deadline. This includes an initial review (2 weeks), a detailed evaluation by the committee (3-4 weeks), and final approval (1-2 weeks). You will be notified via email at each stage.",
      isOpen: false,
    },
    {
      question: "Can I apply for multiple grants at the same time?",
      answer:
        "Yes, you can apply for multiple grants simultaneously. However, each application must be for a distinct project, and you must ensure that the projects do not overlap in terms of funding requests or objectives. Duplicate submissions may lead to disqualification.",
      isOpen: false,
    },
    {
      question: "What happens if I miss the application deadline?",
      answer:
        "If you miss the application deadline, your application will not be considered for that funding cycle. You can apply in the next cycle, which typically opens every quarter. Check the 'Grant Calendar' section for upcoming deadlines.",
      isOpen: false,
    },
    {
      question: "How can I contact support for technical issues?",
      answer:
        "For technical issues, you can reach out to our support team via <NAME_EMAIL> or call our helpline at +1-800-555-1234, available Monday to Friday, 9 AM to 5 PM EST. Alternatively, you can raise a ticket through the 'Support' section of the portal.",
      isOpen: false,
    },
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [filteredFaqs, setFilteredFaqs] = useState(faqs);

  // Handle search functionality
  useEffect(() => {
    if (!searchTerm) {
      setFilteredFaqs(faqs);
      return;
    }

    const lowerSearch = searchTerm.toLowerCase();
    const filtered = faqs.filter(
      (faq) =>
        faq.question.toLowerCase().includes(lowerSearch) ||
        faq.answer.toLowerCase().includes(lowerSearch)
    );
    setFilteredFaqs(filtered);
  }, [searchTerm, faqs]);

  const toggleFAQ = (index: number) => {
    setFilteredFaqs(
      filteredFaqs.map((faq, i) =>
        i === index ? { ...faq, isOpen: !faq.isOpen } : { ...faq, isOpen: false }
      )
    );
  };

  return (
    <>
      <Head>
        <title>Support FAQ - Grant Management System</title>
      </Head>

      <div className="min-h-screen p-8">
        {/* Enhanced Professional FAQ Header */}
        <div className="w-full max-w-6xl mx-auto">
          <div className="professional-card p-8 mb-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center">
                  <HelpCircle className="w-7 h-7 text-white" />
                </div>
                <div>
                  <h2 className="text-4xl font-bold text-gray-900 mb-2">
                    <span className="professional-text-gradient">Frequently Asked Questions</span>
                  </h2>
                  <p className="text-gray-600 text-lg">
                    Find comprehensive answers to common questions about our grant management system
                  </p>
                </div>
              </div>
              <div className="hidden lg:flex items-center space-x-2 text-sm text-gray-500">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>Help Center Online</span>
              </div>
            </div>

            {/* Enhanced Search Bar */}
            <div className="relative">
              <Search className="absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-400 w-6 h-6" />
              <input
                type="text"
                placeholder="Search through our knowledge base..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="professional-input w-full pl-16 pr-6 py-4 text-lg"
              />
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-100 border border-gray-200 rounded">
                  ⌘K
                </kbd>
              </div>
            </div>
          </div>

          {/* Enhanced FAQ List */}
          {filteredFaqs.length === 0 ? (
            <div className="professional-card p-16 text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-50 rounded-full mb-6">
                <HelpCircle className="w-10 h-10 text-gray-400" />
              </div>
              <h3 className="text-gray-700 text-2xl font-bold mb-3">
                No FAQs Found
              </h3>
              <p className="text-gray-500 text-lg mb-6 max-w-md mx-auto">
                We couldn't find any questions matching your search. Try different keywords or browse all questions.
              </p>
              <button
                onClick={() => setSearchTerm("")}
                className="professional-button-primary"
              >
                Show All Questions
              </button>
            </div>
          ) : (
            <div className="space-y-6">
              {filteredFaqs.map((faq, index) => (
                <div
                  key={index}
                  className="professional-card overflow-hidden group"
                >
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="w-full text-left p-8 flex justify-between items-start hover:bg-gradient-to-r hover:from-teal-50/30 hover:to-blue-50/30 transition-all duration-300 focus:outline-none group"
                  >
                    <div className="flex-1 pr-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-teal-700 transition-colors">
                        {faq.question}
                      </h3>
                      <p className="text-gray-500 text-sm">
                        Click to {faq.isOpen ? 'collapse' : 'expand'} answer
                      </p>
                    </div>
                    <div className={`flex-shrink-0 transform transition-all duration-300 ${faq.isOpen ? 'rotate-180 text-teal-600' : 'text-gray-400 group-hover:text-teal-500'}`}>
                      <ChevronDown className="w-7 h-7" />
                    </div>
                  </button>
                  {faq.isOpen && (
                    <div className="px-8 pb-8 border-t border-gray-100">
                      <div className="pt-6">
                        <div className="prose prose-lg max-w-none">
                          <p className="text-gray-700 leading-relaxed text-lg">
                            {faq.answer || "No answer provided yet."}
                          </p>
                        </div>
                        <div className="mt-6 flex items-center justify-between">
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>Was this helpful?</span>
                            <button className="text-teal-600 hover:text-teal-700 font-medium">Yes</button>
                            <button className="text-gray-400 hover:text-gray-600 font-medium">No</button>
                          </div>
                          <button className="text-teal-600 hover:text-teal-700 text-sm font-medium">
                            Contact Support →
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
}