// app/support-faq/page.tsx
"use client";

import { useEffect, useState } from "react";
import Head from "next/head";
import { Search, ChevronDown, ChevronUp, HelpCircle } from "lucide-react";

export default function SupportFAQ() {
  const [faqs, setFaqs] = useState([
    {
      question: "What is the eligibility criteria for applying to a grant?",
      answer:
        "To be eligible for a grant, your organization must be a registered non-profit, have a valid tax-exempt status, and align with the grant's focus area (e.g., education, healthcare, or environmental sustainability). Additionally, you must submit a detailed project proposal and budget plan.",
      isOpen: false,
    },
    {
      question: "How long does the grant approval process take?",
      answer:
        "The grant approval process typically takes 6-8 weeks from the submission deadline. This includes an initial review (2 weeks), a detailed evaluation by the committee (3-4 weeks), and final approval (1-2 weeks). You will be notified via email at each stage.",
      isOpen: false,
    },
    {
      question: "Can I apply for multiple grants at the same time?",
      answer:
        "Yes, you can apply for multiple grants simultaneously. However, each application must be for a distinct project, and you must ensure that the projects do not overlap in terms of funding requests or objectives. Duplicate submissions may lead to disqualification.",
      isOpen: false,
    },
    {
      question: "What happens if I miss the application deadline?",
      answer:
        "If you miss the application deadline, your application will not be considered for that funding cycle. You can apply in the next cycle, which typically opens every quarter. Check the 'Grant Calendar' section for upcoming deadlines.",
      isOpen: false,
    },
    {
      question: "How can I contact support for technical issues?",
      answer:
        "For technical issues, you can reach out to our support team via <NAME_EMAIL> or call our helpline at +1-800-555-1234, available Monday to Friday, 9 AM to 5 PM EST. Alternatively, you can raise a ticket through the 'Support' section of the portal.",
      isOpen: false,
    },
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [filteredFaqs, setFilteredFaqs] = useState(faqs);

  // Handle search functionality
  useEffect(() => {
    if (!searchTerm) {
      setFilteredFaqs(faqs);
      return;
    }

    const lowerSearch = searchTerm.toLowerCase();
    const filtered = faqs.filter(
      (faq) =>
        faq.question.toLowerCase().includes(lowerSearch) ||
        faq.answer.toLowerCase().includes(lowerSearch)
    );
    setFilteredFaqs(filtered);
  }, [searchTerm, faqs]);

  const toggleFAQ = (index: number) => {
    setFilteredFaqs(
      filteredFaqs.map((faq, i) =>
        i === index ? { ...faq, isOpen: !faq.isOpen } : { ...faq, isOpen: false }
      )
    );
  };

  return (
    <>
      <Head>
        <title>Support FAQ - Grant Management System</title>
      </Head>

      <div className="min-h-screen p-8 bg-transparent">
        {/* FAQ Section */}
        <div className="w-full max-w-5xl mx-auto">
          <div className="flex items-center space-x-4 mb-8">
            <HelpCircle className="w-10 h-10 text-teal-600" />
            <div>
              <h2 className="text-3xl font-bold text-gray-900 tracking-tight">
                Frequently Asked Questions
              </h2>
              <p className="text-gray-500 mt-1">
                Find answers to common questions about our grant application and management process.
              </p>
            </div>
          </div>

          {/* Search Bar */}
          <div className="relative mb-10">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search for questions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-3 text-base border border-gray-300 rounded-full shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-all duration-300 bg-white placeholder-gray-500"
            />
          </div>

          {/* FAQ List */}
          {filteredFaqs.length === 0 ? (
            <div className="text-center py-16">
              <HelpCircle className="w-12 h-12 mx-auto text-gray-400" />
              <p className="text-gray-600 text-xl mt-4 font-semibold">
                No FAQs found
              </p>
              <p className="text-gray-500 mt-2">
                We couldn't find any questions matching your search. Try another keyword.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredFaqs.map((faq, index) => (
                <div
                  key={index}
                  className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-200 w-full overflow-hidden"
                >
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="w-full text-left py-5 px-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200 focus:outline-none"
                  >
                    <span className="text-lg font-semibold text-gray-800">
                      {faq.question}
                    </span>
                    <span className={`transform transition-transform duration-300 ${faq.isOpen ? 'rotate-180' : ''}`}>
                      <ChevronDown className="w-6 h-6 text-teal-600" />
                    </span>
                  </button>
                  {faq.isOpen && (
                    <div className="px-6 pb-6 text-gray-700 leading-relaxed border-t border-gray-200">
                      <p className="mt-4">{faq.answer || "No answer provided yet."}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
}