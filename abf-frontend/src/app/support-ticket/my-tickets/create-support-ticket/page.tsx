
// app/support-ticket/my-tickets/create-support-ticket/page.tsx
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Head from "next/head";
import { toast } from "sonner";
import { Upload } from "lucide-react";
import TicketFormSkeleton from "@/components/TicketFormSkeleton";
import { submitSupportTicket } from "@/services/supportTicket.service";
import { getGrants } from "@/services/supportTicket.service";
import { getProfile, transformProfileData } from "@/services/profile-service";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface TicketFormData {
  category: string;
  priority: string;
  title: string;
  description: string;
  email: string;
  phone: string;
  status: string;
  grant?: number;
  point_of_contact_name: string;
}

interface Grant {
  id: number;
  grant_name: string;
  annual_budget: number;
  grant_purpose?: string;
}

export default function CreateSupportTicketPage() {
  const router = useRouter();

  const [formData, setFormData] = useState<TicketFormData>({
    category: "",
    priority: "",
    title: "",
    description: "",
    email: "",
    phone: "",
    status: "open",
    grant: undefined,
    point_of_contact_name: "",
  });

  const [file, setFile] = useState<File | null>(null);
  const [grants, setGrants] = useState<Grant[]>([]);
  const [serverErrors, setServerErrors] = useState<Record<string, string[]>>({});
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchData() {
      try {
        const grantsResult = await getGrants();
        setGrants(grantsResult || []);

        const profileResponse = await getProfile();
        if (profileResponse.status === "SUCCESS") {
          const profileData = transformProfileData(profileResponse.data);
          setFormData((prev) => ({
            ...prev,
            email: profileData.contact.email || "",
            phone: profileData.contact.phone || "",
            point_of_contact_name: profileData.contact.fullName || "",
          }));
        } else {
          toast.error("Failed to load profile data.");
        }
      } catch (error) {
        toast.error("Failed to load data.");
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "grant" ? Number(value) : value,
    }));
    if (serverErrors[name]) {
      setServerErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFile(e.target.files?.[0] || null);
    if (serverErrors.file) {
      setServerErrors((prev) => ({ ...prev, file: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setServerErrors({});

    setSubmitting(true);
    try {
      await submitSupportTicket(formData, file);
      toast.success("Ticket submitted successfully!");
      router.push("/support-ticket/my-tickets");
      router.replace("/support-ticket/my-tickets");
    } catch (error: any) {
      const errors = error.response?.data?.errors;
      if (error.response?.status === 400 && errors) {
        setServerErrors(errors);
        toast.error("Please fix the highlighted errors.");
      } else {
        toast.error(error.response?.data?.detail || "Submission failed.");
      }
    } finally {
      setSubmitting(false);
    }
  };

  const renderError = (field: string) =>
    serverErrors[field] && (
      <p className="text-xs text-red-600 font-medium mt-1">{serverErrors[field][0]}</p>
    );

  return (
    <>
      <Head>
        <title>Create Support Ticket - Grant Management System</title>
      </Head>

      <div className="w-full px-4 py-6">
        <Card className="bg-white shadow-md border-none max-w-5xl mx-auto">
          <CardHeader className="bg-gradient-to-r from-teal-50 to-white border-b border-teal-100 px-6 py-5">
            <div className="border-l-4 border-teal-500 pl-4">
              <CardTitle className="text-3xl font-bold text-gray-900">
                Raise a Ticket
              </CardTitle>
              <CardDescription className="text-gray-500 text-base mt-1">
                Submit a new support ticket for assistance
              </CardDescription>
            </div>
          </CardHeader>
          <CardContent className="p-8">
            {loading || submitting ? (
              <TicketFormSkeleton />
            ) : (
              <>
                {serverErrors.non_field_errors && (
                  <div className="mb-6 p-4 border border-red-200 bg-red-50 text-sm text-red-600 rounded-lg shadow-sm">
                    {serverErrors.non_field_errors.map((err, i) => (
                      <p key={i} className="font-medium">{err}</p>
                    ))}
                  </div>
                )}

                <form onSubmit={handleSubmit} encType="multipart/form-data" className="space-y-8">
                  <div>
                    <label className="block text-base font-semibold text-gray-800 mb-2">Grant</label>
                    <select
                      name="grant"
                      value={formData.grant || ""}
                      onChange={handleChange}
                      className="w-full px-4 py-3 text-base font-semibold border-2 border-gray-200 rounded-lg focus:border-teal-600 focus:ring-2 focus:ring-teal-300 outline-none transition-all shadow-sm hover:shadow-md bg-white text-gray-800"
                    >
                      <option value="">Select Grant</option>
                      {grants.map((grant) => (
                        <option key={grant.id} value={grant.id}>
                          {grant.grant_name} — ₹{Number(grant.annual_budget).toLocaleString("en-IN")}{" "}
                          {grant.grant_purpose ? `(${grant.grant_purpose.slice(0, 50)}...)` : ""}
                        </option>
                      ))}
                    </select>
                    {renderError("grant")}
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-base font-semibold text-gray-800 mb-2">Category</label>
                      <select
                        name="category"
                        value={formData.category}
                        onChange={handleChange}
                        className="w-full px-4 py-3 text-base font-semibold border-2 border-gray-200 rounded-lg focus:border-teal-600 focus:ring-2 focus:ring-teal-300 outline-none transition-all shadow-sm hover:shadow-md bg-white text-gray-800"
                      >
                        <option value="">Select Category</option>
                        <option value="financial">Financial</option>
                        <option value="reporting">Reporting</option>
                        <option value="documents">Documents</option>
                        <option value="technical support">Technical Support</option>
                        <option value="applications">Applications</option>
                        <option value="account management">Account Management</option>
                      </select>
                      {renderError("category")}
                    </div>
                    <div>
                      <label className="block text-base font-semibold text-gray-800 mb-2">Priority</label>
                      <select
                        name="priority"
                        value={formData.priority}
                        onChange={handleChange}
                        className="w-full px-4 py-3 text-base font-semibold border-2 border-gray-200 rounded-lg focus:border-teal-600 focus:ring-2 focus:ring-teal-300 outline-none transition-all shadow-sm hover:shadow-md bg-white text-gray-800"
                      >
                        <option value="">Select Priority</option>
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                      </select>
                      {renderError("priority")}
                    </div>
                  </div>

                  <div>
                    <label className="block text-base font-semibold text-gray-800 mb-2">Title</label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleChange}
                      className="w-full px-4 py-3 text-base font-semibold border-2 border-gray-200 rounded-lg focus:border-teal-600 focus:ring-2 focus:ring-teal-300 outline-none transition-all shadow-sm hover:shadow-md bg-white text-gray-800"
                    />
                    {renderError("title")}
                  </div>

                  <div>
                    <label className="block text-base font-semibold text-gray-800 mb-2">Description</label>
                    <textarea
                      name="description"
                      rows={6}
                      value={formData.description}
                      onChange={handleChange}
                      className="w-full px-4 py-3 text-base font-semibold border-2 border-gray-200 rounded-lg focus:border-teal-600 focus:ring-2 focus:ring-teal-300 outline-none transition-all shadow-sm hover:shadow-md bg-white text-gray-800"
                    />
                    {renderError("description")}
                  </div>

                  <div>
                    <label className="block text-base font-semibold text-gray-800 mb-2">
                      Point of Contact Name
                    </label>
                    <input
                      type="text"
                      name="point_of_contact_name"
                      value={formData.point_of_contact_name}
                      onChange={handleChange}
                      className="w-full px-4 py-3 text-base font-semibold border-2 border-gray-200 rounded-lg focus:border-teal-600 focus:ring-2 focus:ring-teal-300 outline-none transition-all shadow-sm hover:shadow-md bg-white text-gray-800"
                    />
                    {renderError("point_of_contact_name")}
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-base font-semibold text-gray-800 mb-2">Email</label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        className="w-full px-4 py-3 text-base font-semibold border-2 border-gray-200 rounded-lg focus:border-teal-600 focus:ring-2 focus:ring-teal-300 outline-none transition-all shadow-sm hover:shadow-md bg-white text-gray-800"
                      />
                      {renderError("email")}
                    </div>
                    <div>
                      <label className="block text-base font-semibold text-gray-800 mb-2">Phone</label>
                      <input
                        type="text"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        className="w-full px-4 py-3 text-base font-semibold border-2 border-gray-200 rounded-lg focus:border-teal-600 focus:ring-2 focus:ring-teal-300 outline-none transition-all shadow-sm hover:shadow-md bg-white text-gray-800"
                      />
                      {renderError("phone")}
                    </div>
                  </div>

                  <div>
                    <label className="block text-base font-semibold text-gray-800 mb-2">Attachment</label>
                    <label
                      htmlFor="file-upload"
                      className="flex flex-col items-center justify-center gap-3 px-6 py-8 bg-gray-50 border-2 border-dashed border-gray-200 rounded-lg cursor-pointer hover:bg-teal-50 hover:border-teal-400 transition-all shadow-sm hover:shadow-md"
                    >
                      <Upload className="w-10 h-10 text-teal-500" />
                      <p className="text-base text-gray-800 font-semibold">Click to Upload</p>
                      <p className="text-sm text-gray-500">PDF, PNG, JPG (Max 10MB)</p>
                      <input
                        id="file-upload"
                        name="file"
                        type="file"
                        onChange={handleFileChange}
                        className="hidden"
                      />
                    </label>
                    {file && (
                      <p className="mt-3 text-base text-gray-800 font-semibold text-center">
                        Selected: <span className="text-teal-600">{file.name}</span>
                      </p>
                    )}
                    {renderError("file")}
                  </div>

                  <div className="flex justify-end gap-4 pt-6">
                    <Button
                      type="button"
                      onClick={() => router.back()}
                      className="px-8 py-3 text-base font-semibold bg-white text-gray-800 border-2 border-gray-200 hover:bg-gray-100 hover:border-gray-300 transition-all duration-200 rounded-lg shadow-sm hover:shadow-md"
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={submitting}
                      className="px-8 py-3 text-base font-semibold bg-teal-600 text-white hover:bg-teal-700 disabled:bg-teal-400 disabled:cursor-not-allowed transition-all duration-200 rounded-lg shadow-sm hover:shadow-md"
                    >
                      {submitting ? "Submitting..." : "Submit Ticket"}
                    </Button>
                  </div>
                </form>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </>
  );
}
