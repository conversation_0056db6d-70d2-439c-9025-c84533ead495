
// app/support-ticket/my-tickets/create-support-ticket/page.tsx
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Head from "next/head";
import { toast } from "sonner";
import { Upload } from "lucide-react";
import TicketFormSkeleton from "@/components/TicketFormSkeleton";
import { submitSupportTicket } from "@/services/supportTicket.service";
import { getGrants } from "@/services/supportTicket.service";
import { getProfile, transformProfileData } from "@/services/profile-service";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface TicketFormData {
  category: string;
  priority: string;
  title: string;
  description: string;
  email: string;
  phone: string;
  status: string;
  grant?: number;
  point_of_contact_name: string;
}

interface Grant {
  id: number;
  grant_name: string;
  annual_budget: number;
  grant_purpose?: string;
}

export default function CreateSupportTicketPage() {
  const router = useRouter();

  const [formData, setFormData] = useState<TicketFormData>({
    category: "",
    priority: "",
    title: "",
    description: "",
    email: "",
    phone: "",
    status: "open",
    grant: undefined,
    point_of_contact_name: "",
  });

  const [file, setFile] = useState<File | null>(null);
  const [grants, setGrants] = useState<Grant[]>([]);
  const [serverErrors, setServerErrors] = useState<Record<string, string[]>>({});
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchData() {
      try {
        const grantsResult = await getGrants();
        setGrants(grantsResult || []);

        const profileResponse = await getProfile();
        if (profileResponse.status === "SUCCESS") {
          const profileData = transformProfileData(profileResponse.data);
          setFormData((prev) => ({
            ...prev,
            email: profileData.contact.email || "",
            phone: profileData.contact.phone || "",
            point_of_contact_name: profileData.contact.fullName || "",
          }));
        } else {
          toast.error("Failed to load profile data.");
        }
      } catch (error) {
        toast.error("Failed to load data.");
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "grant" ? Number(value) : value,
    }));
    if (serverErrors[name]) {
      setServerErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFile(e.target.files?.[0] || null);
    if (serverErrors.file) {
      setServerErrors((prev) => ({ ...prev, file: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setServerErrors({});

    setSubmitting(true);
    try {
      await submitSupportTicket(formData, file);
      toast.success("Ticket submitted successfully!");
      router.push("/support-ticket/my-tickets");
      router.replace("/support-ticket/my-tickets");
    } catch (error: any) {
      const errors = error.response?.data?.errors;
      if (error.response?.status === 400 && errors) {
        setServerErrors(errors);
        toast.error("Please fix the highlighted errors.");
      } else {
        toast.error(error.response?.data?.detail || "Submission failed.");
      }
    } finally {
      setSubmitting(false);
    }
  };

  const renderError = (field: string) =>
    serverErrors[field] && (
      <p className="text-xs text-red-600 font-medium mt-1">{serverErrors[field][0]}</p>
    );

  return (
    <>
      <Head>
        <title>Create Support Ticket - Grant Management System</title>
      </Head>

      <div className="w-full px-6 py-8">
        {/* Enhanced Professional Header */}
        <div className="professional-card max-w-6xl mx-auto mb-8 p-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center">
                <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <div>
                <h1 className="text-4xl font-bold text-gray-900 mb-2">
                  <span className="professional-text-gradient">Create Support Ticket</span>
                </h1>
                <p className="text-gray-600 text-lg">
                  Submit a detailed support request and get professional assistance from our team
                </p>
              </div>
            </div>
            <div className="hidden lg:flex items-center space-x-2 text-sm text-gray-500">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>Support Available 24/7</span>
            </div>
          </div>
        </div>

        {/* Enhanced Form Container */}
        <div className="professional-card max-w-6xl mx-auto p-10">
          {loading || submitting ? (
            <div className="text-center py-20">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-teal-100 to-teal-50 rounded-full mb-6">
                <div className="w-10 h-10 border-4 border-teal-600 border-t-transparent rounded-full animate-spin"></div>
              </div>
              <p className="text-gray-600 font-medium text-lg">
                {loading ? "Loading form..." : "Submitting your ticket..."}
              </p>
            </div>
          ) : (
            <>
              {serverErrors.non_field_errors && (
                <div className="mb-8 p-6 border-l-4 border-red-500 bg-gradient-to-r from-red-50 to-red-25 rounded-lg shadow-sm">
                  <div className="flex items-center mb-2">
                    <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 className="text-red-800 font-semibold">Please fix the following errors:</h3>
                  </div>
                  {serverErrors.non_field_errors.map((err, i) => (
                    <p key={i} className="text-red-700 font-medium">{err}</p>
                  ))}
                </div>
              )}

              <form onSubmit={handleSubmit} encType="multipart/form-data" className="space-y-10">
                {/* Grant Selection */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200">
                  <label className="block text-lg font-bold text-gray-900 mb-3 flex items-center">
                    <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6" />
                    </svg>
                    Grant Project
                  </label>
                  <select
                    name="grant"
                    value={formData.grant || ""}
                    onChange={handleChange}
                    className="professional-input w-full text-lg"
                  >
                    <option value="">Choose the grant project for this support request</option>
                    {grants.map((grant) => (
                      <option key={grant.id} value={grant.id}>
                        {grant.grant_name} — ₹{Number(grant.annual_budget).toLocaleString("en-IN")}{" "}
                        {grant.grant_purpose ? `(${grant.grant_purpose.slice(0, 50)}...)` : ""}
                      </option>
                    ))}
                  </select>
                  {renderError("grant")}
                </div>

                {/* Category and Priority */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div className="bg-gradient-to-r from-teal-50 to-cyan-50 p-6 rounded-xl border border-teal-200">
                    <label className="block text-lg font-bold text-gray-900 mb-3 flex items-center">
                      <svg className="w-5 h-5 text-teal-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                      </svg>
                      Support Category
                    </label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleChange}
                      className="professional-input w-full text-lg"
                    >
                      <option value="">Choose support category</option>
                      <option value="financial">💰 Financial</option>
                      <option value="reporting">📊 Reporting</option>
                      <option value="documents">📄 Documents</option>
                      <option value="technical support">🔧 Technical Support</option>
                      <option value="applications">📱 Applications</option>
                      <option value="account management">👤 Account Management</option>
                    </select>
                    {renderError("category")}
                  </div>
                  <div className="bg-gradient-to-r from-amber-50 to-orange-50 p-6 rounded-xl border border-amber-200">
                    <label className="block text-lg font-bold text-gray-900 mb-3 flex items-center">
                      <svg className="w-5 h-5 text-amber-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      Priority Level
                    </label>
                    <select
                      name="priority"
                      value={formData.priority}
                      onChange={handleChange}
                      className="professional-input w-full text-lg"
                    >
                      <option value="">Select urgency level</option>
                      <option value="low">🟢 Low - General inquiry</option>
                      <option value="medium">🟡 Medium - Standard issue</option>
                      <option value="high">🟠 High - Important matter</option>
                      <option value="urgent">🔴 Urgent - Critical issue</option>
                    </select>
                    {renderError("priority")}
                  </div>
                </div>

                {/* Title and Description */}
                <div className="space-y-8">
                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl border border-purple-200">
                    <label className="block text-lg font-bold text-gray-900 mb-3 flex items-center">
                      <svg className="w-5 h-5 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                      </svg>
                      Ticket Title
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleChange}
                      placeholder="Provide a clear, concise title for your support request"
                      className="professional-input w-full text-lg"
                    />
                    {renderError("title")}
                  </div>

                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-200">
                    <label className="block text-lg font-bold text-gray-900 mb-3 flex items-center">
                      <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Detailed Description
                    </label>
                    <textarea
                      name="description"
                      rows={8}
                      value={formData.description}
                      onChange={handleChange}
                      placeholder="Please provide a detailed description of your issue, including any relevant context, steps you've already taken, and what outcome you're expecting..."
                      className="professional-input w-full text-lg resize-none"
                    />
                    {renderError("description")}
                    <p className="text-sm text-gray-500 mt-2">
                      💡 Tip: The more details you provide, the faster we can help resolve your issue
                    </p>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="bg-gradient-to-r from-gray-50 to-slate-50 p-6 rounded-xl border border-gray-200">
                  <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                    <svg className="w-6 h-6 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    Contact Information
                  </h3>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-lg font-semibold text-gray-800 mb-3">
                        Point of Contact Name
                      </label>
                      <input
                        type="text"
                        name="point_of_contact_name"
                        value={formData.point_of_contact_name}
                        onChange={handleChange}
                        placeholder="Full name of the primary contact person"
                        className="professional-input w-full text-lg"
                      />
                      {renderError("point_of_contact_name")}
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-lg font-semibold text-gray-800 mb-3">Email Address</label>
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          placeholder="<EMAIL>"
                          className="professional-input w-full text-lg"
                        />
                        {renderError("email")}
                      </div>
                      <div>
                        <label className="block text-lg font-semibold text-gray-800 mb-3">Phone Number</label>
                        <input
                          type="text"
                          name="phone"
                          value={formData.phone}
                          onChange={handleChange}
                          placeholder="+91 98765 43210"
                          className="professional-input w-full text-lg"
                        />
                        {renderError("phone")}
                      </div>
                    </div>
                  </div>
                </div>

                {/* File Attachment */}
                <div className="bg-gradient-to-r from-indigo-50 to-blue-50 p-6 rounded-xl border border-indigo-200">
                  <label className="block text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <svg className="w-5 h-5 text-indigo-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                    </svg>
                    Supporting Documents
                  </label>
                  <label
                    htmlFor="file-upload"
                    className="flex flex-col items-center justify-center gap-4 px-8 py-12 bg-white border-2 border-dashed border-indigo-300 rounded-xl cursor-pointer hover:bg-indigo-50 hover:border-indigo-400 transition-all duration-300 group"
                  >
                    <div className="w-16 h-16 bg-gradient-to-br from-indigo-100 to-indigo-50 rounded-full flex items-center justify-center group-hover:from-indigo-200 group-hover:to-indigo-100 transition-all">
                      <Upload className="w-8 h-8 text-indigo-600" />
                    </div>
                    <div className="text-center">
                      <p className="text-lg text-gray-800 font-bold mb-1">Drop files here or click to upload</p>
                      <p className="text-sm text-gray-600">PDF, PNG, JPG, DOC files up to 10MB</p>
                    </div>
                    <input
                      id="file-upload"
                      name="file"
                      type="file"
                      onChange={handleFileChange}
                      className="hidden"
                      accept=".pdf,.png,.jpg,.jpeg,.doc,.docx"
                    />
                  </label>
                  {file && (
                    <div className="mt-4 p-4 bg-white rounded-lg border border-indigo-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-green-100 to-green-50 rounded-lg flex items-center justify-center">
                            <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <div>
                            <p className="text-gray-800 font-semibold">{file.name}</p>
                            <p className="text-sm text-gray-500">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={() => setFile(null)}
                          className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-all"
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  )}
                  {renderError("file")}
                </div>

                {/* Enhanced Action Buttons */}
                <div className="flex flex-col sm:flex-row justify-end gap-4 pt-8 border-t border-gray-200">
                  <Button
                    type="button"
                    onClick={() => router.back()}
                    className="professional-button-secondary px-10 py-4 text-lg"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={submitting}
                    className="professional-button-primary px-10 py-4 text-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {submitting ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                        Submitting Ticket...
                      </>
                    ) : (
                      <>
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                        Submit Support Ticket
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </>
          )}
        </div>
      </div>
    </>
  );
}
