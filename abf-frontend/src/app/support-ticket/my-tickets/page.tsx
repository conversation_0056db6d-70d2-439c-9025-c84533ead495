// app/support-ticket/my-tickets/page.tsx
"use client";

import { useEffect, useMemo, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import Head from "next/head";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { format, formatDistanceToNow } from "date-fns";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import {
  Search,
  Filter,
  Download,
  AlertCircle,
  Ticket,
  Clock,
  MessageSquare,
  CheckCircle,
  X,
  ChevronRight,
} from "lucide-react";
import { fetchAllTickets, getGrants } from "@/services/supportTicket.service";
import { toast } from "sonner";

interface Ticket {
  id: number;
  title: string;
  grant_name: string;
  status: string;
  created_at: string;
  updated_at: string;
}

interface Grant {
  id: number;
  grant_name: string;
}

export default function MyTickets() {
  const router = useRouter();
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [filteredTickets, setFilteredTickets] = useState<Ticket[]>([]);
  const [grants, setGrants] = useState<Grant[]>([]);
  const [grantFilter, setGrantFilter] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [customStartDate, setCustomStartDate] = useState<Date | null>(null);
  const [customEndDate, setCustomEndDate] = useState<Date | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const lastStatuses = useRef<{ [key: number]: string }>({});
  const lastMessages = useRef<{ [key: number]: number }>({});
  const startRef = useRef(null);
  const endRef = useRef(null);

  const STATUSES = ["open", "under review", "resolved"];

  const formatDate = (date: string | number | Date) => {
    const d = new Date(date);
    return format(d, "dd MMM yyyy");
  };

  const capitalize = (text: string) =>
    text ? text.charAt(0).toUpperCase() + text.slice(1).toLowerCase() : "";

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const [ticketData, grantData] = await Promise.all([fetchAllTickets(), getGrants()]);
        const formattedTickets: Ticket[] = ticketData.map((t: any) => ({
          id: t.id,
          title: t.title || `Ticket #${t.id}`,
          grant_name: grantData.find((g: any) => g.id === t.grant)?.grant_name || `Grant ${t.grant}`,
          status: t.status?.toLowerCase().replace(/\s+/g, "-"),
          created_at: t.created_at,
          updated_at: t.updated_at,
        }));
        setTickets(formattedTickets);
        setGrants(grantData);
        setFilteredTickets(formattedTickets);
        setLoading(false);

        ticketData.forEach((t: any) => {
          lastStatuses.current[t.id] = t.status;
          lastMessages.current[t.id] = new Date(t.created_at).getTime();
        });
      } catch (err: any) {
        setError(err.message || "Failed to load tickets or grants.");
        setLoading(false);
      }
    };

    fetchInitialData();
  }, []);

  useEffect(() => {
    pollingRef.current = setInterval(async () => {
      try {
        const updated = await fetchAllTickets();
        const grantData = await getGrants();
        const formattedUpdated: Ticket[] = updated.map((t: any) => ({
          id: t.id,
          title: t.title || `Ticket #${t.id}`,
          grant_name: grantData.find((g: any) => g.id === t.grant)?.grant_name || `Grant ${t.grant}`,
          status: t.status?.toLowerCase().replace(/\s+/g, "-"),
          created_at: t.created_at,
          updated_at: t.updated_at,
        }));

        updated.forEach((ticket: any) => {
          const prevStatus = lastStatuses.current[ticket.id];
          const prevCreatedAt = lastMessages.current[ticket.id];

          if (ticket.status !== prevStatus) {
            toast.info(`Ticket ${ticket.id} status changed to ${capitalize(ticket.status)}`);
            lastStatuses.current[ticket.id] = ticket.status;
          }

          if (new Date(ticket.created_at).getTime() > prevCreatedAt) {
            toast(`New update on Ticket ${ticket.id}`, {
              description: ticket.title || "",
            });
            lastMessages.current[ticket.id] = new Date(ticket.created_at).getTime();
          }
        });

        setTickets(formattedUpdated);
      } catch {
        // Silent fail
      }
    }, 5000);

    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
    };
  }, []);

  const normalize = (text: string) =>
    text?.toLowerCase().replace(/[^a-z0-9 ]/gi, "") || "";

  const filteredTicketsMemo = useMemo(() => {
    const search = normalize(searchTerm);
    const tokens = search.split(" ").filter(Boolean);

    let result: Ticket[] = [...tickets];

    if (statusFilter.length > 0) {
      result = result.filter((t) => statusFilter.includes(t.status));
    }

    if (grantFilter.length > 0) {
      result = result.filter((t) => grantFilter.includes(t.grant_name));
    }

    if (customStartDate || customEndDate) {
      result = result.filter((t) => {
        const created = new Date(t.created_at);
        return (
          (!customStartDate || created >= customStartDate) &&
          (!customEndDate || created <= customEndDate)
        );
      });
    }

    return result.filter((t) => {
      const target = normalize(`${t.title} ${t.grant_name} ${t.status}`);
      return search.length === 0 || tokens.every((token) => target.includes(token));
    });
  }, [tickets, searchTerm, statusFilter, grantFilter, customStartDate, customEndDate]);

  const quickStats = useMemo(
    () => [
      {
        label: "Total Tickets",
        value: filteredTicketsMemo.length,
        icon: <Ticket className="w-7 h-7 text-teal-600 group-hover:scale-110 transition-transform duration-300" />,
        bg: "bg-gradient-to-br from-blue-50 to-indigo-50",
        border: "border-l-4 border-blue-500",
        color: "text-blue-900"
      },
      {
        label: "Open",
        value: filteredTicketsMemo.filter((t) => t.status === "open").length,
        icon: <MessageSquare className="w-7 h-7 text-green-600 group-hover:scale-110 transition-transform duration-300" />,
        bg: "bg-gradient-to-br from-green-50 to-emerald-50",
        border: "border-l-4 border-green-500",
        color: "text-green-900"
      },
      {
        label: "Under Review",
        value: filteredTicketsMemo.filter((t) => t.status === "under-review").length,
        icon: <Clock className="w-7 h-7 text-amber-600 group-hover:scale-110 transition-transform duration-300" />,
        bg: "bg-gradient-to-br from-amber-50 to-orange-50",
        border: "border-l-4 border-amber-500",
        color: "text-amber-900"
      },
      {
        label: "Resolved",
        value: filteredTicketsMemo.filter((t) => t.status === "resolved").length,
        icon: <CheckCircle className="w-7 h-7 text-teal-600 group-hover:scale-110 transition-transform duration-300" />,
        bg: "bg-gradient-to-br from-teal-50 to-cyan-50",
        border: "border-l-4 border-teal-500",
        color: "text-teal-900"
      },
    ],
    [filteredTicketsMemo]
  );

  const allGrants = useMemo(() => {
    const names = tickets.map((t) => t.grant_name);
    return [...new Set(names)].sort();
  }, [tickets]);

  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case "open":
        return "professional-badge-open";
      case "under-review":
        return "professional-badge-review";
      case "resolved":
        return "professional-badge-resolved";
      default:
        return "bg-gray-500 text-white px-3 py-1 rounded-full text-xs font-semibold";
    }
  };

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter([]);
    setGrantFilter([]);
    setCustomStartDate(null);
    setCustomEndDate(null);
  };

  return (
    <>
      <Head>
        <title>My Tickets - Grant Management System</title>
      </Head>

      <div className="w-full px-6 py-8">
        {/* Enhanced Professional Header */}
        <div className="professional-card mb-8 p-8">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
            <div className="professional-accent-border pl-6">
              <CardTitle className="text-3xl font-bold text-gray-900 mb-2">
                <span className="professional-text-gradient">My Support Tickets</span>
              </CardTitle>
              <CardDescription className="text-gray-600 text-base">
                Manage and track your support requests with our professional ticketing system
              </CardDescription>
            </div>
            <div className="flex gap-4">
              <Button
                size="lg"
                className="professional-button-primary"
                onClick={() => router.push("/support-ticket/my-tickets/create-support-ticket")}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Create Ticket
              </Button>
              <Button
                size="lg"
                className="professional-button-secondary"
              >
                <Download className="h-5 w-5 mr-2" />
                Export Data
              </Button>
            </div>
          </div>
        </div>
        {/* Enhanced Professional Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {quickStats.map((stat) => (
            <div
              key={stat.label}
              className={`professional-card ${stat.bg} ${stat.border} group cursor-default overflow-hidden`}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 rounded-xl bg-white/80 shadow-sm">
                    {stat.icon}
                  </div>
                  <div className="text-right">
                    <p className={`text-3xl font-bold ${stat.color} mb-1`}>{stat.value}</p>
                    <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                  </div>
                </div>
                <div className="w-full bg-white/50 rounded-full h-1">
                  <div
                    className="bg-gradient-to-r from-teal-500 to-teal-600 h-1 rounded-full transition-all duration-1000 ease-out"
                    style={{ width: `${Math.min((stat.value / Math.max(...quickStats.map(s => s.value))) * 100, 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Enhanced Professional Search and Filters */}
        <div className="professional-card p-6 mb-8">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-6">
            <div className="relative w-full lg:flex-grow">
              <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search tickets by ID, title, or grant name..."
                className="professional-input w-full pl-12 pr-4"
              />
            </div>
            <div className="flex items-center gap-4 w-full lg:w-auto flex-wrap">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    className="professional-button-secondary text-sm"
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Status Filter
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56 shadow-xl border-gray-200 bg-white rounded-xl">
                  <DropdownMenuLabel className="text-gray-800 font-semibold px-4 py-3">Filter by Status</DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-gray-200" />
                  {STATUSES.map((status) => (
                    <DropdownMenuCheckboxItem
                      key={status}
                      checked={statusFilter.includes(status)}
                      onCheckedChange={(checked) => {
                        setStatusFilter((prev) =>
                          checked ? [...prev, status] : prev.filter((s) => s !== status)
                        );
                      }}
                      className="capitalize text-gray-700 hover:!bg-teal-50 focus:bg-teal-100 px-4 py-2"
                    >
                      {status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    className="professional-button-secondary text-sm"
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Grant Filter
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-72 max-h-64 overflow-y-auto shadow-xl border-gray-200 bg-white rounded-xl">
                  <DropdownMenuLabel className="text-gray-800 font-semibold px-4 py-3">Filter by Grant</DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-gray-200" />
                  {allGrants.map((grant) => (
                    <DropdownMenuCheckboxItem
                      key={grant}
                      checked={grantFilter.includes(grant)}
                      onCheckedChange={(checked) => {
                        setGrantFilter((prev) =>
                          checked ? [...prev, grant] : prev.filter((g) => g !== grant)
                        );
                      }}
                      className="text-gray-700 hover:!bg-teal-50 focus:bg-teal-100 px-4 py-2"
                    >
                      {grant.length > 32 ? `${grant.slice(0, 30)}...` : grant}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              <DatePicker
                selected={customStartDate}
                onChange={(date: Date | null) => {
                  setCustomStartDate(date);
                  if (customEndDate && date && date > customEndDate) setCustomEndDate(null);
                }}
                placeholderText="Start Date"
                dateFormat="dd MMM yyyy"
                className="professional-input text-sm w-40"
                maxDate={customEndDate || undefined}
                ref={startRef}
              />
              <DatePicker
                selected={customEndDate}
                onChange={setCustomEndDate}
                placeholderText="End Date"
                dateFormat="dd MMM yyyy"
                className="professional-input text-sm w-40"
                minDate={customStartDate || undefined}
                ref={endRef}
              />

              <Button
                size="sm"
                variant="ghost"
                onClick={clearFilters}
                className="text-gray-600 hover:bg-gray-100 px-4 py-2 rounded-lg transition-all"
              >
                Clear All
              </Button>
            </div>
          </div>
        </div>

        {/* Enhanced Filter Tags */}
        {(statusFilter.length > 0 || grantFilter.length > 0 || customStartDate || customEndDate) && (
          <div className="flex flex-wrap gap-3 mb-8">
            {statusFilter.map((status) => (
              <span
                key={status}
                className="flex items-center gap-2 text-sm bg-gradient-to-r from-teal-100 to-teal-50 text-teal-800 px-4 py-2 border border-teal-200 hover:from-teal-200 hover:to-teal-100 transition-all rounded-full shadow-sm font-medium"
              >
                Status: {status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                <X
                  className="w-4 h-4 cursor-pointer text-teal-600 hover:text-teal-800 transition-colors"
                  onClick={() => setStatusFilter((prev) => prev.filter((s) => s !== status))}
                />
              </span>
            ))}
            {grantFilter.map((grant) => (
              <span
                key={grant}
                className="flex items-center gap-2 text-sm bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 px-4 py-2 border border-blue-200 hover:from-blue-200 hover:to-blue-100 transition-all rounded-full shadow-sm font-medium"
              >
                Grant: {grant.length > 24 ? `${grant.slice(0, 22)}...` : grant}
                <X
                  className="w-4 h-4 cursor-pointer text-blue-600 hover:text-blue-800 transition-colors"
                  onClick={() => setGrantFilter((prev) => prev.filter((g) => g !== grant))}
                />
              </span>
            ))}
            {customStartDate && (
              <span
                className="flex items-center gap-2 text-sm bg-gradient-to-r from-purple-100 to-purple-50 text-purple-800 px-4 py-2 border border-purple-200 hover:from-purple-200 hover:to-purple-100 transition-all rounded-full shadow-sm font-medium"
              >
                From: {format(customStartDate, "dd MMM yyyy")}
                <X
                  className="w-4 h-4 cursor-pointer text-purple-600 hover:text-purple-800 transition-colors"
                  onClick={() => setCustomStartDate(null)}
                />
              </span>
            )}
            {customEndDate && (
              <span
                className="flex items-center gap-2 text-sm bg-gradient-to-r from-purple-100 to-purple-50 text-purple-800 px-4 py-2 border border-purple-200 hover:from-purple-200 hover:to-purple-100 transition-all rounded-full shadow-sm font-medium"
              >
                To: {format(customEndDate, "dd MMM yyyy")}
                <X
                  className="w-4 h-4 cursor-pointer text-purple-600 hover:text-purple-800 transition-colors"
                  onClick={() => setCustomEndDate(null)}
                />
              </span>
            )}
          </div>
        )}

        {/* Enhanced Professional Table */}
        <div className="professional-card overflow-hidden">
          {loading ? (
            <div className="text-center py-20">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-teal-100 to-teal-50 rounded-full mb-4">
                <div className="w-8 h-8 border-4 border-teal-600 border-t-transparent rounded-full animate-spin"></div>
              </div>
              <p className="text-gray-600 font-medium">Loading your tickets...</p>
            </div>
          ) : error ? (
            <div className="text-center py-20">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-red-100 to-red-50 rounded-full mb-4">
                <AlertCircle className="w-8 h-8 text-red-500" />
              </div>
              <p className="text-red-600 font-semibold text-lg mb-2">Error Loading Tickets</p>
              <p className="text-gray-600">{error}</p>
            </div>
          ) : filteredTicketsMemo.length === 0 ? (
            <div className="text-center py-20">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-50 rounded-full mb-4">
                <Ticket className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-gray-700 font-semibold text-lg mb-2">No tickets found</p>
              <p className="text-gray-500 mb-4">Try adjusting your filters or create your first ticket</p>
              <Button
                onClick={() => router.push("/support-ticket/my-tickets/create-support-ticket")}
                className="professional-button-primary"
              >
                Create Your First Ticket
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                    <th className="p-6 text-left font-bold text-gray-800 text-sm uppercase tracking-wide">Ticket ID</th>
                    <th className="p-6 text-left font-bold text-gray-800 text-sm uppercase tracking-wide">Title</th>
                    <th className="p-6 text-left font-bold text-gray-800 text-sm uppercase tracking-wide">Grant</th>
                    <th className="p-6 text-left font-bold text-gray-800 text-sm uppercase tracking-wide">Status</th>
                    <th className="p-6 text-left font-bold text-gray-800 text-sm uppercase tracking-wide">Created</th>
                    <th className="p-6 text-left font-bold text-gray-800 text-sm uppercase tracking-wide">Updated</th>
                    <th className="p-6 text-center font-bold text-gray-800 text-sm uppercase tracking-wide">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100">
                  {filteredTicketsMemo.map((ticket, index) => (
                    <tr
                      key={ticket.id}
                      className="hover:bg-gradient-to-r hover:from-teal-50/30 hover:to-blue-50/30 transition-all duration-300 cursor-pointer group"
                      onClick={() =>
                        router.push(`/support-ticket/my-tickets/view-support-ticket?id=${ticket.id}`)
                      }
                    >
                      <td className="p-6">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-gradient-to-br from-teal-100 to-teal-50 rounded-lg flex items-center justify-center mr-3">
                            <span className="text-teal-700 font-bold text-sm">#{ticket.id}</span>
                          </div>
                        </div>
                      </td>
                      <td className="p-6">
                        <div className="max-w-xs">
                          <p className="text-gray-900 font-semibold text-base mb-1 truncate group-hover:text-teal-700 transition-colors">
                            {ticket.title}
                          </p>
                          <p className="text-gray-500 text-sm">Support Request</p>
                        </div>
                      </td>
                      <td className="p-6">
                        <div className="max-w-xs">
                          <p className="text-gray-700 font-medium truncate">{ticket.grant_name}</p>
                          <p className="text-gray-500 text-sm">Grant Project</p>
                        </div>
                      </td>
                      <td className="p-6">
                        <span className={`inline-flex items-center ${getStatusBadgeStyle(ticket.status)}`}>
                          {ticket.status?.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                        </span>
                      </td>
                      <td className="p-6">
                        <div>
                          <p className="text-gray-700 font-medium">{formatDate(ticket.created_at)}</p>
                          <p className="text-gray-500 text-sm">Created</p>
                        </div>
                      </td>
                      <td className="p-6">
                        <div>
                          <p className="text-gray-700 font-medium">
                            {formatDistanceToNow(new Date(ticket.updated_at), { addSuffix: true })}
                          </p>
                          <p className="text-gray-500 text-sm">Last activity</p>
                        </div>
                      </td>
                      <td className="p-6 text-center">
                        <Button
                          size="sm"
                          className="professional-button-primary text-sm opacity-0 group-hover:opacity-100 transition-all duration-300"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/support-ticket/my-tickets/view-support-ticket?id=${ticket.id}`);
                          }}
                        >
                          View Details
                          <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}