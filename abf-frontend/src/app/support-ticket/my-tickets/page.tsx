// app/support-ticket/my-tickets/page.tsx
"use client";

import { useEffect, useMemo, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import Head from "next/head";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { format, formatDistanceToNow } from "date-fns";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import {
  Search,
  Filter,
  Download,
  AlertCircle,
  Ticket,
  Clock,
  MessageSquare,
  CheckCircle,
  X,
  ChevronRight,
} from "lucide-react";
import { fetchAllTickets, getGrants } from "@/services/supportTicket.service";
import { toast } from "sonner";

interface Ticket {
  id: number;
  title: string;
  grant_name: string;
  status: string;
  created_at: string;
  updated_at: string;
}

interface Grant {
  id: number;
  grant_name: string;
}

export default function MyTickets() {
  const router = useRouter();
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [filteredTickets, setFilteredTickets] = useState<Ticket[]>([]);
  const [grants, setGrants] = useState<Grant[]>([]);
  const [grantFilter, setGrantFilter] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [customStartDate, setCustomStartDate] = useState<Date | null>(null);
  const [customEndDate, setCustomEndDate] = useState<Date | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const lastStatuses = useRef<{ [key: number]: string }>({});
  const lastMessages = useRef<{ [key: number]: number }>({});
  const startRef = useRef(null);
  const endRef = useRef(null);

  const STATUSES = ["open", "under review", "resolved"];

  const formatDate = (date: string | number | Date) => {
    const d = new Date(date);
    return format(d, "dd MMM yyyy");
  };

  const capitalize = (text: string) =>
    text ? text.charAt(0).toUpperCase() + text.slice(1).toLowerCase() : "";

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const [ticketData, grantData] = await Promise.all([fetchAllTickets(), getGrants()]);
        const formattedTickets: Ticket[] = ticketData.map((t: any) => ({
          id: t.id,
          title: t.title || `Ticket #${t.id}`,
          grant_name: grantData.find((g: any) => g.id === t.grant)?.grant_name || `Grant ${t.grant}`,
          status: t.status?.toLowerCase().replace(/\s+/g, "-"),
          created_at: t.created_at,
          updated_at: t.updated_at,
        }));
        setTickets(formattedTickets);
        setGrants(grantData);
        setFilteredTickets(formattedTickets);
        setLoading(false);

        ticketData.forEach((t: any) => {
          lastStatuses.current[t.id] = t.status;
          lastMessages.current[t.id] = new Date(t.created_at).getTime();
        });
      } catch (err: any) {
        setError(err.message || "Failed to load tickets or grants.");
        setLoading(false);
      }
    };

    fetchInitialData();
  }, []);

  useEffect(() => {
    pollingRef.current = setInterval(async () => {
      try {
        const updated = await fetchAllTickets();
        const grantData = await getGrants();
        const formattedUpdated: Ticket[] = updated.map((t: any) => ({
          id: t.id,
          title: t.title || `Ticket #${t.id}`,
          grant_name: grantData.find((g: any) => g.id === t.grant)?.grant_name || `Grant ${t.grant}`,
          status: t.status?.toLowerCase().replace(/\s+/g, "-"),
          created_at: t.created_at,
          updated_at: t.updated_at,
        }));

        updated.forEach((ticket: any) => {
          const prevStatus = lastStatuses.current[ticket.id];
          const prevCreatedAt = lastMessages.current[ticket.id];

          if (ticket.status !== prevStatus) {
            toast.info(`Ticket ${ticket.id} status changed to ${capitalize(ticket.status)}`);
            lastStatuses.current[ticket.id] = ticket.status;
          }

          if (new Date(ticket.created_at).getTime() > prevCreatedAt) {
            toast(`New update on Ticket ${ticket.id}`, {
              description: ticket.title || "",
            });
            lastMessages.current[ticket.id] = new Date(ticket.created_at).getTime();
          }
        });

        setTickets(formattedUpdated);
      } catch {
        // Silent fail
      }
    }, 5000);

    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
    };
  }, []);

  const normalize = (text: string) =>
    text?.toLowerCase().replace(/[^a-z0-9 ]/gi, "") || "";

  const filteredTicketsMemo = useMemo(() => {
    const search = normalize(searchTerm);
    const tokens = search.split(" ").filter(Boolean);

    let result: Ticket[] = [...tickets];

    if (statusFilter.length > 0) {
      result = result.filter((t) => statusFilter.includes(t.status));
    }

    if (grantFilter.length > 0) {
      result = result.filter((t) => grantFilter.includes(t.grant_name));
    }

    if (customStartDate || customEndDate) {
      result = result.filter((t) => {
        const created = new Date(t.created_at);
        return (
          (!customStartDate || created >= customStartDate) &&
          (!customEndDate || created <= customEndDate)
        );
      });
    }

    return result.filter((t) => {
      const target = normalize(`${t.title} ${t.grant_name} ${t.status}`);
      return search.length === 0 || tokens.every((token) => target.includes(token));
    });
  }, [tickets, searchTerm, statusFilter, grantFilter, customStartDate, customEndDate]);

  const quickStats = useMemo(
    () => [
      {
        label: "Total Tickets",
        value: filteredTicketsMemo.length,
        icon: <Ticket className="w-6 h-6 text-teal-400 group-hover:scale-110 transition-transform duration-300" />,
        bg: "bg-gradient-to-br from-teal-50/50 to-white border-t-2 border-teal-200",
      },
      {
        label: "Open",
        value: filteredTicketsMemo.filter((t) => t.status === "open").length,
        icon: <MessageSquare className="w-6 h-6 text-teal-400 group-hover:scale-110 transition-transform duration-300" />,
        bg: "bg-gradient-to-br from-teal-50/50 to-white border-t-2 border-teal-200",
      },
      {
        label: "Under Review",
        value: filteredTicketsMemo.filter((t) => t.status === "under-review").length,
        icon: <Clock className="w-6 h-6 text-teal-400 group-hover:scale-110 transition-transform duration-300" />,
        bg: "bg-gradient-to-br from-teal-50/50 to-white border-t-2 border-teal-200",
      },
      {
        label: "Resolved",
        value: filteredTicketsMemo.filter((t) => t.status === "resolved").length,
        icon: <CheckCircle className="w-6 h-6 text-teal-400 group-hover:scale-110 transition-transform duration-300" />,
        bg: "bg-gradient-to-br from-teal-50/50 to-white border-t-2 border-teal-200",
      },
    ],
    [filteredTicketsMemo]
  );

  const allGrants = useMemo(() => {
    const names = tickets.map((t) => t.grant_name);
    return [...new Set(names)].sort();
  }, [tickets]);

  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case "open":
        return "bg-teal-100 text-teal-800 border-teal-200";
      case "under-review":
        return "bg-amber-100 text-amber-800 border-amber-200";
      case "resolved":
        return "bg-teal-100 text-teal-800 border-teal-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter([]);
    setGrantFilter([]);
    setCustomStartDate(null);
    setCustomEndDate(null);
  };

  return (
    <>
      <Head>
        <title>My Tickets - Grant Management System</title>
      </Head>

      <div className="w-full px-4 py-6">
        <Card className="bg-transparent shadow-none border-none">
          <CardHeader className="bg-transparent px-0 py-5">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div className="border-l-4 border-teal-500 pl-4">
                <CardTitle className="text-2xl font-semibold text-gray-800">
                  My Tickets
                </CardTitle>
                <CardDescription className="text-gray-500 text-sm mt-1">
                  Seamlessly manage and track your support tickets
                </CardDescription>
              </div>
              <div className="flex gap-3">
                <Button
                  size="sm"
                  className="bg-teal-600 hover:bg-teal-700 text-white transition-all duration-300 transform hover:scale-105 rounded-lg shadow-md hover:shadow-lg"
                  onClick={() => router.push("/support-ticket/my-tickets/create-support-ticket")}
                >
                  Raise Ticket
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="bg-white hover:bg-gray-100 text-gray-700 border-gray-300 transition-all duration-300 transform hover:scale-105 rounded-lg shadow-md hover:shadow-lg"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Tickets
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {quickStats.map((stat) => (
                <Card
                  key={stat.label}
                  className="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 group cursor-default border-l-4 border-teal-500"
                >
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium text-gray-600 flex items-center gap-2">
                      {stat.icon}
                      {stat.label}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Filters and Search */}
            <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6 p-4 bg-white rounded-xl shadow-md">
              <div className="relative w-full md:w-auto md:flex-grow">
                <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search by ticket ID, title, or grant..."
                  className="w-full pl-12 pr-4 py-2.5 border border-gray-300 focus:border-teal-500 focus:ring-2 focus:ring-teal-200 outline-none transition-all text-sm placeholder:text-gray-500 rounded-lg"
                />
              </div>
              <div className="flex items-center gap-3 w-full md:w-auto flex-wrap">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="sm"
                      variant="outline"
                      className="bg-white hover:bg-gray-100 text-gray-700 border-gray-300"
                    >
                      <Filter className="h-4 w-4 mr-2" />
                      Status
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-48 shadow-lg border-gray-200 bg-white">
                    <DropdownMenuLabel className="text-gray-800 font-semibold">Filter by Status</DropdownMenuLabel>
                    <DropdownMenuSeparator className="bg-gray-200" />
                    {STATUSES.map((status) => (
                      <DropdownMenuCheckboxItem
                        key={status}
                        checked={statusFilter.includes(status)}
                        onCheckedChange={(checked) => {
                          setStatusFilter((prev) =>
                            checked ? [...prev, status] : prev.filter((s) => s !== status)
                          );
                        }}
                        className="capitalize text-gray-700 hover:!bg-teal-50 focus:bg-teal-100"
                      >
                        {status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="sm"
                      variant="outline"
                      className="bg-white hover:bg-gray-100 text-gray-700 border-gray-300"
                    >
                      <Filter className="h-4 w-4 mr-2" />
                      Grant
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-64 max-h-60 overflow-y-auto shadow-lg border-gray-200 bg-white">
                    <DropdownMenuLabel className="text-gray-800 font-semibold">Filter by Grant</DropdownMenuLabel>
                    <DropdownMenuSeparator className="bg-gray-200" />
                    {allGrants.map((grant) => (
                      <DropdownMenuCheckboxItem
                        key={grant}
                        checked={grantFilter.includes(grant)}
                        onCheckedChange={(checked) => {
                          setGrantFilter((prev) =>
                            checked ? [...prev, grant] : prev.filter((g) => g !== grant)
                          );
                        }}
                        className="text-gray-700 hover:!bg-teal-50 focus:bg-teal-100"
                      >
                        {grant.length > 28 ? `${grant.slice(0, 26)}...` : grant}
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                <DatePicker
                  selected={customStartDate}
                  onChange={(date: Date | null) => {
                    setCustomStartDate(date);
                    if (customEndDate && date && date > customEndDate) setCustomEndDate(null);
                  }}
                  placeholderText="Start Date"
                  dateFormat="dd MMM yyyy"
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm w-36 focus:ring-2 focus:ring-teal-500"
                  maxDate={customEndDate || undefined}
                  ref={startRef}
                />
                <DatePicker
                  selected={customEndDate}
                  onChange={setCustomEndDate}
                  placeholderText="End Date"
                  dateFormat="dd MMM yyyy"
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm w-36 focus:ring-2 focus:ring-teal-500"
                  minDate={customStartDate || undefined}
                  ref={endRef}
                />

                <Button
                  size="sm"
                  variant="ghost"
                  onClick={clearFilters}
                  className="text-gray-600 hover:bg-gray-100"
                >
                  Clear Filters
                </Button>
              </div>
            </div>

            {/* Filter Tags */}
            <div className="flex flex-wrap gap-2 mb-6">
              {statusFilter.map((status) => (
                <span
                  key={status}
                  className="flex items-center gap-2 text-xs bg-teal-100 text-teal-800 px-3 py-1.5 border border-teal-200 hover:bg-teal-200 transition-all rounded-full shadow-sm"
                >
                  {status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                  <X
                    className="w-4 h-4 cursor-pointer text-teal-600 hover:text-teal-800"
                    onClick={() => setStatusFilter((prev) => prev.filter((s) => s !== status))}
                  />
                </span>
              ))}
              {grantFilter.map((grant) => (
                <span
                  key={grant}
                  className="flex items-center gap-2 text-xs bg-blue-100 text-blue-800 px-3 py-1.5 border border-blue-200 hover:bg-blue-200 transition-all rounded-full shadow-sm"
                >
                  {grant.length > 28 ? `${grant.slice(0, 26)}...` : grant}
                  <X
                    className="w-4 h-4 cursor-pointer text-blue-600 hover:text-blue-800"
                    onClick={() => setGrantFilter((prev) => prev.filter((g) => g !== grant))}
                  />
                </span>
              ))}
              {customStartDate && (
                <span
                  className="flex items-center gap-2 text-xs bg-purple-100 text-purple-800 px-3 py-1.5 border border-purple-200 hover:bg-purple-200 transition-all rounded-full shadow-sm"
                >
                  Start: {format(customStartDate, "dd MMM yyyy")}
                  <X
                    className="w-4 h-4 cursor-pointer text-purple-600 hover:text-purple-800"
                    onClick={() => setCustomStartDate(null)}
                  />
                </span>
              )}
              {customEndDate && (
                <span
                  className="flex items-center gap-2 text-xs bg-purple-100 text-purple-800 px-3 py-1.5 border border-purple-200 hover:bg-purple-200 transition-all rounded-full shadow-sm"
                >
                  End: {format(customEndDate, "dd MMM yyyy")}
                  <X
                    className="w-4 h-4 cursor-pointer text-purple-600 hover:text-purple-800"
                    onClick={() => setCustomEndDate(null)}
                  />
                </span>
              )}
            </div>

            {/* Table View */}
            <div className="w-full bg-white rounded-xl shadow-xl overflow-hidden">
              {loading ? (
                <p className="text-gray-600 text-center py-16">Loading tickets...</p>
              ) : error ? (
                <div className="text-center text-red-600 py-16">
                  <AlertCircle className="w-8 h-8 mb-3 mx-auto text-red-400" />
                  <p className="text-base font-medium">Error: {error}</p>
                </div>
              ) : filteredTicketsMemo.length === 0 ? (
                <div className="text-center text-gray-500 py-16">
                  <Ticket className="w-8 h-8 mb-3 mx-auto text-gray-400" />
                  <p className="text-base font-medium">No tickets found.</p>
                  <p className="text-sm">Try adjusting your filters or creating a new ticket.</p>
                </div>
              ) : (
                <table className="w-full border-collapse text-sm">
                  <thead className="bg-gray-100">
                    <tr className="text-gray-600">
                      <th className="p-4 text-left font-semibold">Ticket ID</th>
                      <th className="p-4 text-left font-semibold">Title</th>
                      <th className="p-4 text-left font-semibold">Grant</th>
                      <th className="p-4 text-left font-semibold">Status</th>
                      <th className="p-4 text-left font-semibold">Created</th>
                      <th className="p-4 text-left font-semibold">Last Updated</th>
                      <th className="p-4 text-center font-semibold">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {filteredTicketsMemo.map((ticket, index) => (
                      <tr
                        key={ticket.id}
                        className="hover:bg-teal-50/50 transition-all duration-200 cursor-pointer"
                        onClick={() =>
                          router.push(`/support-ticket/my-tickets/view-support-ticket?id=${ticket.id}`)
                        }
                      >
                        <td className="p-4 font-mono text-gray-700">#{ticket.id}</td>
                        <td className="p-4">
                          <div className="font-semibold text-gray-800">{ticket.title}</div>
                        </td>
                        <td className="p-4 text-gray-600">{ticket.grant_name}</td>
                        <td className="p-4">
                          <span
                            className={`inline-flex items-center px-2.5 py-1 text-xs font-semibold border rounded-full ${getStatusBadgeStyle(
                              ticket.status
                            )}`}
                          >
                            {ticket.status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                          </span>
                        </td>
                        <td className="p-4 text-gray-600">
                          {format(new Date(ticket.created_at), "dd MMM yyyy")}
                        </td>
                        <td className="p-4 text-gray-600">
                          {formatDistanceToNow(new Date(ticket.updated_at), {
                            addSuffix: true,
                          })}
                        </td>
                        <td className="p-4 text-center">
                          <Button
                            size="sm"
                            variant="ghost"
                            className="text-teal-600 hover:bg-teal-100"
                            onClick={(e) => {
                              e.stopPropagation();
                              router.push(
                                `/support-ticket/my-tickets/view-support-ticket?id=${ticket.id}`
                              );
                            }}
                          >
                            View Details
                            <ChevronRight className="w-4 h-4 ml-1" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}