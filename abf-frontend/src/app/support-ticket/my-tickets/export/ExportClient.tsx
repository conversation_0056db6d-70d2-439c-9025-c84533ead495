
"use client";

import { useEffect, useRef, useState } from "react";
import { useSearchParams } from "next/navigation";
import apiClient from "@/lib/apiClient";

export default function TicketExportPage() {
  const searchParams = useSearchParams();
  const ticketId = searchParams.get("id");

  const exportRef = useRef<HTMLDivElement>(null);
  const [ticket, setTicket] = useState<any>(null);
  const [updates, setUpdates] = useState<any[]>([]);

  useEffect(() => {
    if (!ticketId) return;
    apiClient.get(`/api/support/v1/tickets/${ticketId}/`).then((res) => {
      setTicket(res.data);
    });
  }, [ticketId]);

  useEffect(() => {
    if (!ticket) return;
    apiClient
      .get(`/api/support/v1/ticket-updates/`, {
        params: { ticket: ticket.id },
      })
      .then((res) => setUpdates(res.data));
  }, [ticket]);

  if (!ticket) {
    return (
      <div className="min-h-screen flex items-center justify-center text-lg text-gray-500 animate-pulse">
        Loading ticket details...
      </div>
    );
  }

  return (
    <div
      ref={exportRef}
      className="bg-white text-black p-10 max-w-4xl mx-auto font-sans space-y-12 rounded-lg shadow-2xl border border-gray-200"
    >
      <header className="text-center border-b pb-4">
        <h1 className="text-3xl font-bold text-gray-900">🎟️ Support Ticket Summary</h1>
        <p className="text-sm text-gray-500 mt-1">
          Generated on {new Date().toLocaleDateString()}
        </p>
      </header>

      <section className="bg-gray-50 border rounded-xl p-6 shadow-sm">
        <dl className="grid sm:grid-cols-2 gap-y-3 gap-x-8 text-sm">
          <div className="flex flex-col">
            <dt className="font-semibold text-gray-900">ID</dt>
            <dd className="text-gray-700">PF-{ticket.id.toString().padStart(4, "0")}</dd>
          </div>
          <div className="flex flex-col">
            <dt className="font-semibold text-gray-900">Title</dt>
            <dd className="text-gray-700">{ticket.title}</dd>
          </div>
          <div className="flex flex-col">
            <dt className="font-semibold text-gray-900">Category</dt>
            <dd className="text-gray-700">{ticket.category}</dd>
          </div>
          <div className="flex flex-col">
            <dt className="font-semibold text-gray-900">Status</dt>
            <dd className="text-gray-700">{ticket.status}</dd>
          </div>
          <div className="flex flex-col">
            <dt className="font-semibold text-gray-900">Priority</dt>
            <dd className="text-gray-700">{ticket.priority}</dd>
          </div>
          <div className="flex flex-col">
            <dt className="font-semibold text-gray-900">Created</dt>
            <dd className="text-gray-700">{new Date(ticket.created_at).toLocaleString()}</dd>
          </div>
          <div className="flex flex-col">
            <dt className="font-semibold text-gray-900">Updated</dt>
            <dd className="text-gray-700">{new Date(ticket.updated_at).toLocaleString()}</dd>
          </div>
        </dl>
      </section>

      <section className="space-y-6">
        <h2 className="text-lg font-semibold border-b pb-2 text-gray-800">💬 Conversation Log</h2>

        <div className="p-5 border bg-white rounded-md shadow-sm">
          <div className="font-semibold text-blue-700 mb-1">You (Initial Request)</div>
          <p className="text-gray-800 whitespace-pre-wrap text-sm">{ticket.description}</p>
          {ticket.file && (
            <p className="text-xs mt-2 text-gray-500">
              📎 Attachment: {ticket.file.split("/").pop()}
            </p>
          )}
        </div>

        {updates.map((u, i) => (
          <div key={i} className="p-5 border bg-gray-50 rounded-md shadow-sm">
            <div className="mb-2 flex justify-between items-center">
              <span className="font-semibold text-gray-900 text-sm">{u.created_by || "Support"}</span>
              <span className="text-xs text-gray-500">{new Date(u.updated_at).toLocaleString()}</span>
            </div>
            <p className="text-gray-800 whitespace-pre-wrap text-sm">{u.update_text}</p>
            {u.file && (
              <p className="text-xs mt-2 text-gray-500">
                📎 Attachment: {u.file.split("/").pop()}
              </p>
            )}
          </div>
        ))}
      </section>

      <footer className="text-center text-xs text-gray-400 pt-10 border-t">
        © {new Date().getFullYear()} Grant Management System · Confidential Export
      </footer>
    </div>
  );
}
