
// app/support-ticket/my-tickets/view-support-ticket/page.tsx
"use client";

import { useEffect, useState, useRef } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import Head from "next/head";
import {
  ArrowLeft,
  FileDown,
  FileImage,
  Download,
  MessageSquareText,
  CheckCircle,
  RefreshCw,
  XCircle,
} from "lucide-react";
import { toast } from "sonner";
import { getTicketDetails } from "@/services/supportTicket.service";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function ViewTicketPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const ticketId = searchParams.get("id");

  const [ticket, setTicket] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const lastStatusRef = useRef<string | null>(null);
  const lastUpdatedRef = useRef<number | null>(null);

  const capitalizeEachWord = (text: string) =>
    text?.split(" ").map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(" ") || "";

  const getStatusBadgeClass = (status: string) => {
    const map: Record<string, string> = {
      "open": "professional-badge-open",
      "under-review": "professional-badge-review",
      "resolved": "professional-badge-resolved",
    };
    return map[status?.toLowerCase().replace(/\s+/g, "-")] ?? "bg-gray-500 text-white px-3 py-1 rounded-full text-sm font-semibold";
  };

  const getPriorityBadgeColor = (priority: string) => {
    const map: Record<string, string> = {
      low: "professional-priority-low",
      medium: "professional-priority-medium",
      high: "professional-priority-high",
      urgent: "professional-priority-high",
    };
    return map[priority?.toLowerCase()] ?? "bg-gray-500 text-white px-2 py-1 rounded-lg text-xs font-bold";
  };

  const showToast = (icon: any, colorClass: string, title: string, description: string) => {
    toast.custom(() => (
      <div className={`flex items-center gap-3 px-5 py-3 border shadow-md rounded-lg ${colorClass}`}>
        {icon}
        <div className="text-sm leading-snug">
          <p className="font-semibold text-gray-800">{title}</p>
          <p className="text-gray-600">{description}</p>
        </div>
      </div>
    ));
  };

  const showStatusChangeToast = (status: string) => {
    showToast(
      <CheckCircle className="w-5 h-5 text-teal-600" />,
      "bg-teal-50 border-teal-200 text-teal-800",
      "Status Updated",
      `Changed to ${capitalizeEachWord(status)}`
    );
  };

  const showUpdatedToast = (id: number) => {
    showToast(
      <RefreshCw className="w-5 h-5 text-teal-600 animate-spin" />,
      "bg-teal-50 border-teal-200 text-teal-800",
      "Ticket Updated",
      `PF-${id.toString().padStart(4, "0")} was modified.`
    );
  };

  const showLoadErrorToast = () => {
    showToast(
      <XCircle className="w-5 h-5 text-red-600" />,
      "bg-red-50 border-red-200 text-red-600",
      "Ticket Load Failed",
      "Unable to load this ticket. Redirecting..."
    );
  };

  useEffect(() => {
    if (!ticketId) {
      showLoadErrorToast();
      router.replace("/support-ticket/my-tickets");
      return;
    }

    const fetchTicket = async () => {
      try {
        const data = await getTicketDetails(ticketId);
        if (!data?.id) throw new Error("Invalid ticket response");
        setTicket(data);
        lastStatusRef.current = data.status;
        lastUpdatedRef.current = new Date(data.updated_at).getTime();
      } catch (err) {
        showLoadErrorToast();
        router.replace("/support-ticket/my-tickets");
      } finally {
        setLoading(false);
      }
    };

    fetchTicket();
  }, [ticketId, router]);

  useEffect(() => {
    if (!ticketId) return;

    pollingRef.current = setInterval(async () => {
      try {
        const updated = await getTicketDetails(ticketId);
        if (!updated) return;

        if (lastStatusRef.current !== updated.status) {
          showStatusChangeToast(updated.status);
          lastStatusRef.current = updated.status;
        }

        const updatedTime = new Date(updated.updated_at).getTime();
        if (updatedTime > (lastUpdatedRef.current ?? 0)) {
          showUpdatedToast(updated.id);
          lastUpdatedRef.current = updatedTime;
        }

        setTicket(updated);
      } catch (err) {
        console.error("Polling error", err);
      }
    }, 5000);

    return () => clearInterval(pollingRef.current!);
  }, [ticketId]);

  const handleExport = () => {
    if (ticket) {
      window.open(`/support-ticket/export?id=${ticket.id}`, "_blank");
    }
  };

  if (loading || !ticket) {
    return (
      <div className="flex items-center justify-center h-full py-20">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-teal-100 to-teal-50 rounded-full mb-4">
            <div className="w-8 h-8 border-4 border-teal-600 border-t-transparent rounded-full animate-spin"></div>
          </div>
          <p className="text-gray-600 font-medium text-lg">Loading ticket details...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Ticket PF-{ticket.id.toString().padStart(4, "0")} - {ticket.title}</title>
        <meta name="description" content={`Support Ticket: ${ticket.title}`} />
      </Head>

      <div className="w-full px-6 py-8">
        {/* Enhanced Professional Header */}
        <div className="professional-card max-w-7xl mx-auto mb-8 p-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="flex items-center space-x-6">
              <div className="w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div>
                <h1 className="text-4xl font-bold text-gray-900 mb-2">
                  <span className="professional-text-gradient">Ticket #{ticket.id.toString().padStart(4, "0")}</span>
                </h1>
                <p className="text-gray-600 text-lg font-medium">{ticket.title}</p>
                <div className="flex items-center space-x-4 mt-3">
                  <span className={getStatusBadgeClass(ticket.status)}>
                    {capitalizeEachWord(ticket.status)}
                  </span>
                  <span className={getPriorityBadgeColor(ticket.priority)}>
                    {capitalizeEachWord(ticket.priority)} Priority
                  </span>
                </div>
              </div>
            </div>
            <div className="flex flex-wrap gap-4">
              <Button
                onClick={() =>
                  router.push(`/support-ticket/my-tickets/ticket-history?id=${ticket.id}`)
                }
                className="professional-button-primary"
              >
                <MessageSquareText className="w-5 h-5 mr-2" /> Start Chat
              </Button>
              <Button
                onClick={handleExport}
                className="professional-button-secondary"
              >
                <FileDown className="w-5 h-5 mr-2" /> Export PDF
              </Button>
              <Button
                onClick={() => router.push("/support-ticket/my-tickets")}
                className="bg-white text-gray-700 border-2 border-gray-300 hover:bg-gray-50 hover:border-gray-400 px-6 py-3 rounded-lg font-semibold transition-all"
              >
                <ArrowLeft className="w-5 h-5 mr-2" /> Back to Tickets
              </Button>
            </div>
          </div>
        </div>

        {/* Enhanced Main Content */}
        <div className="professional-card max-w-7xl mx-auto p-10">
          <div className="space-y-10">
            {/* Enhanced Ticket Information */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-8 rounded-xl border border-blue-200">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <svg className="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Ticket Details
              </h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div className="bg-white p-6 rounded-lg border border-blue-200">
                    <p className="text-gray-500 font-semibold mb-2 text-sm uppercase tracking-wide">Category</p>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <p className="font-bold text-gray-900 text-lg capitalize">{ticket.category}</p>
                    </div>
                  </div>
                  <div className="bg-white p-6 rounded-lg border border-blue-200">
                    <p className="text-gray-500 font-semibold mb-2 text-sm uppercase tracking-wide">Created</p>
                    <p className="font-bold text-gray-900 text-lg">
                      {new Date(ticket.created_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                </div>
                <div className="space-y-6">
                  <div className="bg-white p-6 rounded-lg border border-blue-200">
                    <p className="text-gray-500 font-semibold mb-2 text-sm uppercase tracking-wide">Last Updated</p>
                    <p className="font-bold text-gray-900 text-lg">
                      {new Date(ticket.updated_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                  <div className="bg-white p-6 rounded-lg border border-blue-200">
                    <p className="text-gray-500 font-semibold mb-2 text-sm uppercase tracking-wide">Grant Project</p>
                    <p className="font-bold text-gray-900 text-lg break-words">{ticket.grant_name}</p>
                  </div>
                </div>
              </div>

              {/* Description Section */}
              <div className="mt-8 bg-white p-8 rounded-lg border border-blue-200">
                <h4 className="text-xl font-bold text-gray-900 mb-4">Issue Description</h4>
                <div className="prose prose-lg max-w-none">
                  <p className="text-gray-700 leading-relaxed whitespace-pre-wrap break-words">
                    {ticket.description}
                  </p>
                </div>
              </div>
            </div>

            {/* Enhanced Attachments Section */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-8 rounded-xl border border-green-200">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <svg className="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                </svg>
                Supporting Documents
              </h3>
              {ticket.attachments?.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {ticket.attachments.map((url: string, i: number) => {
                    const fileName = new URL(url).pathname.split("/").pop()?.split("?")[0];
                    const fileExtension = fileName?.split('.').pop()?.toLowerCase();
                    const getFileIcon = () => {
                      switch (fileExtension) {
                        case 'pdf': return '📄';
                        case 'jpg': case 'jpeg': case 'png': return '🖼️';
                        case 'doc': case 'docx': return '📝';
                        default: return '📎';
                      }
                    };

                    return (
                      <div
                        key={i}
                        className="bg-white p-6 rounded-lg border border-green-200 hover:shadow-lg transition-all duration-300 group"
                      >
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-12 h-12 bg-gradient-to-br from-green-100 to-green-50 rounded-lg flex items-center justify-center text-2xl">
                              {getFileIcon()}
                            </div>
                            <div>
                              <p className="font-bold text-gray-900 truncate max-w-48">{fileName}</p>
                              <p className="text-sm text-gray-500 uppercase">{fileExtension} File</p>
                            </div>
                          </div>
                        </div>
                        <a
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-3 px-4 rounded-lg flex items-center justify-center space-x-2 transition-all duration-300 group-hover:scale-105"
                        >
                          <Download className="w-5 h-5" />
                          <span>Download File</span>
                        </a>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="bg-white p-12 rounded-lg border border-green-200 text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                    </svg>
                  </div>
                  <p className="text-gray-500 text-lg font-medium">No supporting documents attached</p>
                  <p className="text-gray-400 text-sm mt-1">Files can be added during ticket updates</p>
                </div>
              )}
            </div>

            {/* Enhanced Contact Information */}
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-8 rounded-xl border border-purple-200">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <svg className="w-6 h-6 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Contact Information
              </h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white p-6 rounded-lg border border-purple-200">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-100 to-purple-50 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-gray-500 font-semibold text-sm uppercase tracking-wide">Point of Contact</p>
                      <p className="font-bold text-gray-900 text-lg">{ticket.point_of_contact_name}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-lg border border-purple-200">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-100 to-purple-50 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-gray-500 font-semibold text-sm uppercase tracking-wide">Email Address</p>
                      <a href={`mailto:${ticket.email}`} className="font-bold text-purple-600 hover:text-purple-700 text-lg transition-colors">
                        {ticket.email}
                      </a>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-lg border border-purple-200">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-100 to-purple-50 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-gray-500 font-semibold text-sm uppercase tracking-wide">Phone Number</p>
                      <a href={`tel:${ticket.phone}`} className="font-bold text-purple-600 hover:text-purple-700 text-lg transition-colors">
                        {ticket.phone}
                      </a>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-lg border border-purple-200">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-100 to-purple-50 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-gray-500 font-semibold text-sm uppercase tracking-wide">Associated Grant</p>
                      <p className="font-bold text-gray-900 text-lg break-words">{ticket.grant_name}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
