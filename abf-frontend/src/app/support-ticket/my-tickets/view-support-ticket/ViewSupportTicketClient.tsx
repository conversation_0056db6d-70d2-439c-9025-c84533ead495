
// app/support-ticket/my-tickets/view-support-ticket/page.tsx
"use client";

import { useEffect, useState, useRef } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import Head from "next/head";
import {
  ArrowLeft,
  FileDown,
  FileImage,
  Download,
  MessageSquareText,
  CheckCircle,
  RefreshCw,
  XCircle,
} from "lucide-react";
import { toast } from "sonner";
import { getTicketDetails } from "@/services/supportTicket.service";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function ViewTicketPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const ticketId = searchParams.get("id");

  const [ticket, setTicket] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const lastStatusRef = useRef<string | null>(null);
  const lastUpdatedRef = useRef<number | null>(null);

  const capitalizeEachWord = (text: string) =>
    text?.split(" ").map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(" ") || "";

  const getStatusBadgeClass = (status: string) => {
    const map: Record<string, string> = {
      "open": "bg-teal-100 text-teal-800 border-teal-200",
      "under-review": "bg-amber-100 text-amber-800 border-amber-200",
      "resolved": "bg-teal-100 text-teal-800 border-teal-200",
    };
    return `${map[status?.toLowerCase().replace(/\s+/g, "-")] ?? "bg-gray-100 text-gray-800 border-gray-200"} px-3 py-1 rounded-full text-sm font-semibold border`;
  };

  const getPriorityBadgeColor = (priority: string) => {
    const map: Record<string, string> = {
      low: "bg-gray-100 text-gray-800 border-gray-200",
      medium: "bg-yellow-100 text-yellow-800 border-yellow-200",
      high: "bg-orange-100 text-orange-800 border-orange-200",
      urgent: "bg-red-100 text-red-800 border-red-200",
    };
    return `${map[priority?.toLowerCase()] ?? "bg-gray-100 text-gray-800 border-gray-200"} px-3 py-1 rounded-full text-sm font-semibold border`;
  };

  const showToast = (icon: any, colorClass: string, title: string, description: string) => {
    toast.custom(() => (
      <div className={`flex items-center gap-3 px-5 py-3 border shadow-md rounded-lg ${colorClass}`}>
        {icon}
        <div className="text-sm leading-snug">
          <p className="font-semibold text-gray-800">{title}</p>
          <p className="text-gray-600">{description}</p>
        </div>
      </div>
    ));
  };

  const showStatusChangeToast = (status: string) => {
    showToast(
      <CheckCircle className="w-5 h-5 text-teal-600" />,
      "bg-teal-50 border-teal-200 text-teal-800",
      "Status Updated",
      `Changed to ${capitalizeEachWord(status)}`
    );
  };

  const showUpdatedToast = (id: number) => {
    showToast(
      <RefreshCw className="w-5 h-5 text-teal-600 animate-spin" />,
      "bg-teal-50 border-teal-200 text-teal-800",
      "Ticket Updated",
      `PF-${id.toString().padStart(4, "0")} was modified.`
    );
  };

  const showLoadErrorToast = () => {
    showToast(
      <XCircle className="w-5 h-5 text-red-600" />,
      "bg-red-50 border-red-200 text-red-600",
      "Ticket Load Failed",
      "Unable to load this ticket. Redirecting..."
    );
  };

  useEffect(() => {
    if (!ticketId) {
      showLoadErrorToast();
      router.replace("/support-ticket/my-tickets");
      return;
    }

    const fetchTicket = async () => {
      try {
        const data = await getTicketDetails(ticketId);
        if (!data?.id) throw new Error("Invalid ticket response");
        setTicket(data);
        lastStatusRef.current = data.status;
        lastUpdatedRef.current = new Date(data.updated_at).getTime();
      } catch (err) {
        showLoadErrorToast();
        router.replace("/support-ticket/my-tickets");
      } finally {
        setLoading(false);
      }
    };

    fetchTicket();
  }, [ticketId, router]);

  useEffect(() => {
    if (!ticketId) return;

    pollingRef.current = setInterval(async () => {
      try {
        const updated = await getTicketDetails(ticketId);
        if (!updated) return;

        if (lastStatusRef.current !== updated.status) {
          showStatusChangeToast(updated.status);
          lastStatusRef.current = updated.status;
        }

        const updatedTime = new Date(updated.updated_at).getTime();
        if (updatedTime > (lastUpdatedRef.current ?? 0)) {
          showUpdatedToast(updated.id);
          lastUpdatedRef.current = updatedTime;
        }

        setTicket(updated);
      } catch (err) {
        console.error("Polling error", err);
      }
    }, 5000);

    return () => clearInterval(pollingRef.current!);
  }, [ticketId]);

  const handleExport = () => {
    if (ticket) {
      window.open(`/support-ticket/export?id=${ticket.id}`, "_blank");
    }
  };

  if (loading || !ticket) {
    return (
      <div className="flex items-center justify-center h-full text-gray-600 text-base font-semibold">
        Loading ticket details...
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Ticket PF-{ticket.id.toString().padStart(4, "0")} - {ticket.title}</title>
        <meta name="description" content={`Support Ticket: ${ticket.title}`} />
      </Head>

      <div className="w-full px-4 py-6">
        <Card className="bg-white shadow-md border-none max-w-5xl mx-auto">
          <CardHeader className="bg-gradient-to-r from-teal-50 to-white border-b border-teal-100 px-6 py-5">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="border-l-4 border-teal-500 pl-4">
                <CardTitle className="text-3xl font-bold text-gray-900">
                  Ticket PF-{ticket.id.toString().padStart(4, "0")}
                </CardTitle>
                <CardDescription className="text-base text-gray-500 mt-1">
                  {ticket.title}
                </CardDescription>
              </div>
              <div className="flex flex-wrap gap-3">
                <Button
                  onClick={() =>
                    router.push(`/support-ticket/my-tickets/ticket-history?id=${ticket.id}`)
                  }
                  className="px-6 py-3 text-base font-semibold bg-teal-600 text-white hover:bg-teal-700 transition-all duration-200 rounded-lg shadow-sm hover:shadow-md"
                >
                  <MessageSquareText className="w-5 h-5 mr-2" /> Chat Now
                </Button>
                <Button
                  onClick={handleExport}
                  className="px-6 py-3 text-base font-semibold bg-teal-600 text-white hover:bg-teal-700 transition-all duration-200 rounded-lg shadow-sm hover:shadow-md"
                >
                  <FileDown className="w-5 h-5 mr-2" /> Export
                </Button>
                <Button
                  onClick={() => router.push("/support-ticket/my-tickets")}
                  className="px-6 py-3 text-base font-semibold bg-white text-gray-800 border-2 border-gray-200 hover:bg-gray-100 hover:border-gray-300 transition-all duration-200 rounded-lg shadow-sm hover:shadow-md"
                >
                  <ArrowLeft className="w-5 h-5 mr-2" /> Back
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-8">
            <div className="space-y-8">
              {/* Ticket Info */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 border-b border-teal-100 pb-2 mb-4">
                  Ticket Information
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 text-base">
                  <div>
                    <p className="text-gray-500 font-medium mb-1">Category</p>
                    <p className="font-semibold text-gray-800 capitalize">{ticket.category}</p>
                  </div>
                  <div>
                    <p className="text-gray-500 font-medium mb-1">Priority</p>
                    <span className={getPriorityBadgeColor(ticket.priority)}>
                      {capitalizeEachWord(ticket.priority)}
                    </span>
                  </div>
                  <div>
                    <p className="text-gray-500 font-medium mb-1">Status</p>
                    <span className={getStatusBadgeClass(ticket.status)}>
                      {capitalizeEachWord(ticket.status)}
                    </span>
                  </div>
                  <div className="sm:col-span-2">
                    <p className="text-gray-500 font-medium mb-1">Title</p>
                    <p className="font-semibold text-gray-800 break-words">{ticket.title}</p>
                  </div>
                  <div className="sm:col-span-2">
                    <p className="text-gray-500 font-medium mb-1">Description</p>
                    <p className="text-gray-700 whitespace-pre-wrap leading-relaxed break-words">
                      {ticket.description}
                    </p>
                  </div>
                </div>
              </div>

              {/* Attachments */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 border-b border-teal-100 pb-2 mb-4">
                  Attachments
                </h3>
                {ticket.attachments?.length > 0 ? (
                  <div className="flex flex-col gap-4">
                    {ticket.attachments.map((url: string, i: number) => {
                      const fileName = new URL(url).pathname.split("/").pop()?.split("?")[0];
                      return (
                        <div
                          key={i}
                          className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 px-6 py-4 rounded-lg bg-gray-50 border border-teal-100 shadow-sm hover:shadow-md transition-all"
                        >
                          <div className="flex items-center gap-3 overflow-hidden">
                            <FileImage className="w-6 h-6 text-teal-400 shrink-0" />
                            <span className="truncate text-base text-gray-800 font-semibold">{fileName}</span>
                          </div>
                          <a
                            href={url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-base text-teal-600 hover:text-teal-700 font-semibold flex items-center gap-2 hover:underline"
                          >
                            <Download className="w-5 h-5" /> Download
                          </a>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <p className="text-gray-500 text-base italic font-medium">No Attachments Available</p>
                )}
              </div>

              {/* Contact Info */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 border-b border-teal-100 pb-2 mb-4">
                  Contact Information
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 text-base">
                  <div>
                    <p className="text-gray-500 font-medium mb-1">Grant Name</p>
                    <p className="font-semibold text-gray-800 break-words">{ticket.grant_name}</p>
                  </div>
                  <div>
                    <p className="text-gray-500 font-medium mb-1">Phone Number</p>
                    <p className="font-semibold text-gray-800 break-words">{ticket.phone}</p>
                  </div>
                  <div>
                    <p className="text-gray-500 font-medium mb-1">Point of Contact Name</p>
                    <p className="font-semibold text-gray-800 break-words">{ticket.point_of_contact_name}</p>
                  </div>
                  <div>
                    <p className="text-gray-500 font-medium mb-1">Email</p>
                    <p className="font-semibold text-gray-800 break-words">{ticket.email}</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
