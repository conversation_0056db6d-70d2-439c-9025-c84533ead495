
"use client";

import { useEffect, useState, useRef, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, useSearch<PERSON>ara<PERSON> } from "next/navigation";
import Head from "next/head";
import {
  ArrowLeft,
  Paperclip,
  Send,
  FileDown,
  XCircle,
  Tag,
  Folder,
  Flag,
  CalendarClock,
  Clock,
  ShieldCheck,
  LoaderCircle,
  ChevronDown,
} from "lucide-react";
import { toast } from "sonner";
import RealisticSkeletonMessage from "@/components/RealisticSkeletonMessage";
import SidebarSkeleton from "@/components/SidebarSkeleton";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  getTicketById,
  getTicketUpdates,
  sendTicketUpdate,
  reopenTicket,
} from "@/services/supportTicket.service";

interface TicketData {
  id: string;
  title: string;
  description: string;
  status: string;
  category: string;
  priority: string;
  created_at: string;
  updated_at: string;
  created_by_first_name: string;
  created_by_last_name: string;
  created_by_email: string;
  status_history: Array<{ changed_at: string; changed_by: any; to_status: string }>;
  attachments: string[];
}

interface UpdateData {
  id: string;
  update_text: string;
  updated_at: string;
  user: { type: { code: string }; first_name: string; last_name: string; email: string };
  attachments: string[];
}

interface TimelineItem {
  type: "message" | "status";
  id: string;
  update_text?: string;
  updated_at: string;
  user?: { type: { code: string }; first_name: string; last_name: string; email: string };
  attachments?: string[];
  changed_by?: any;
  to_status?: string;
}

function Popup({ message, onClose }: { message: string; onClose: () => void }) {
  useEffect(() => {
    const timeout = setTimeout(onClose, 3000);
    return () => clearTimeout(timeout);
  }, [onClose]);

  return (
    <div className="fixed top-6 left-1/2 -translate-x-1/2 z-50 animate-fade-in-down">
      <div className="flex items-center gap-3 px-4 py-2 bg-white border-l-4 border-teal-500 shadow-md rounded-md max-w-md">
        <XCircle className="w-5 h-5 text-teal-600" />
        <p className="text-base font-medium text-teal-800">{message}</p>
        <button
          onClick={onClose}
          className="text-teal-500 hover:text-teal-700 transition"
          aria-label="Close popup"
        >
          <XCircle className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
}

function DetailRow({
  icon: Icon,
  label,
  value,
  badge,
}: {
  icon: React.ComponentType<{ className: string }>;
  label: string;
  value: string | undefined;
  badge?: (value: string) => string;
}) {
  const formatted = value
    ? String(value).replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
    : "-";
  return (
    <div className="flex justify-between items-center gap-4">
      <div className="flex items-center gap-2 text-gray-600 text-sm font-medium">
        <Icon className="w-4 h-4" /> {label}
      </div>
      {badge ? (
        <span
          className={`px-3 py-1 text-sm font-medium rounded-md border ${badge(value || "")}`}
        >
          {formatted}
        </span>
      ) : (
        <span className="text-sm font-medium text-gray-800">{formatted}</span>
      )}
    </div>
  );
}

export default function TicketHistoryPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const ticketId = searchParams.get("id");

  const [ticket, setTicket] = useState<TicketData | null>(null);
  const [updates, setUpdates] = useState<UpdateData[]>([]);
  const [loading, setLoading] = useState(false);
  const [popupMessage, setPopupMessage] = useState("");
  const [initialSidebarLoaded, setInitialSidebarLoaded] = useState(false);
  const [chatVisible, setChatVisible] = useState(false);
  const [messageText, setMessageText] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [newMessageArrived, setNewMessageArrived] = useState(false);

  const chatRef = useRef<HTMLDivElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const lastUpdateCount = useRef(0);
  const lastStatusCount = useRef(0);
  const lastStatusRef = useRef<string | null>(null);
  const lastSentMessageIdRef = useRef<string | null>(null);

  const timeline = useMemo(() => {
    const items: TimelineItem[] = [];
    if (ticket?.description) {
      items.push({
        type: "message",
        id: `desc-${ticket.id}`,
        update_text: ticket.description,
        updated_at: ticket.created_at,
        user: {
          type: { code: "GRANTEE" },
          first_name: ticket.created_by_first_name,
          last_name: ticket.created_by_last_name,
          email: ticket.created_by_email,
        },
        attachments: ticket.attachments || [],
      });
    }
    (ticket?.status_history || []).forEach((s, i) => {
      items.push({
        type: "status",
        id: `status-${i}`,
        updated_at: s.changed_at,
        changed_by: s.changed_by,
        to_status: s.to_status,
      });
    });
    updates.forEach((u, i) => {
      items.push({ type: "message", ...u, id: u.id || `update-${i}` });
    });
    return items.sort((a, b) => new Date(a.updated_at) - new Date(b.updated_at));
  }, [ticket, updates]);

  const scrollToBottom = () => {
    setTimeout(() => {
      if (bottomRef.current) {
        bottomRef.current.scrollIntoView({ behavior: "smooth" });
        setNewMessageArrived(false);
      }
    }, 100);
  };

  const handleScroll = () => {
    if (!chatRef.current) return;
    const { scrollTop, scrollHeight, clientHeight } = chatRef.current;
    const atBottom = scrollHeight - scrollTop - clientHeight < 100;
    setNewMessageArrived(!atBottom && updates.length > lastUpdateCount.current);
  };

  useEffect(() => {
    if (!ticketId) {
      setPopupMessage("Invalid ticket ID.");
      router.replace("/support-ticket/my-tickets");
      return;
    }

    const loadInitial = async () => {
      try {
        const ticketData = await getTicketById(ticketId);
        setTicket(ticketData);
        lastStatusCount.current = ticketData.status_history?.length || 0;
        lastStatusRef.current = ticketData.status_history?.slice(-1)?.[0]?.to_status || null;
        setInitialSidebarLoaded(true);
        setTimeout(() => setChatVisible(true), 300);
      } catch {
        setPopupMessage("Failed to load ticket.");
        router.replace("/support-ticket/my-tickets");
      }
    };

    loadInitial();
  }, [ticketId, router]);

  useEffect(() => {
    if (!ticket?.id || !chatVisible) return;

    const fetchUpdates = async () => {
      try {
        const updatesData = await getTicketUpdates(ticket.id);
        setUpdates(updatesData);
        lastUpdateCount.current = updatesData.length;
        scrollToBottom();
      } catch {
        setPopupMessage("Failed to load updates.");
      }
    };

    fetchUpdates();
  }, [ticket?.id, chatVisible]);

  useEffect(() => {
    if (!ticket?.id || !chatVisible) return;

    const poll = async () => {
      try {
        const [newTicket, newUpdates] = await Promise.all([
          getTicketById(ticket.id),
          getTicketUpdates(ticket.id),
        ]);

        const hasNewUpdate = newUpdates.length > lastUpdateCount.current;
        const hasNewStatus = (newTicket.status_history?.length || 0) > lastStatusCount.current;

        if (hasNewUpdate || hasNewStatus) {
          lastUpdateCount.current = newUpdates.length;
          lastStatusCount.current = newTicket.status_history?.length || 0;

          const newStatus = newTicket.status_history?.slice(-1)?.[0]?.to_status;
          if (newStatus && lastStatusRef.current !== newStatus) {
            toast.info(`Status changed to "${newStatus}"`, {
              style: { background: "#e6fffa", borderColor: "#319795", color: "#234e52" },
            });
            lastStatusRef.current = newStatus;
          }

          if (hasNewUpdate) {
            const newMsg = newUpdates.at(-1);
            if (newMsg?.update_text && newMsg.id !== lastSentMessageIdRef.current) {
              const sender = newMsg.user?.first_name
                ? `${newMsg.user.first_name} ${newMsg.user.last_name}`
                : newMsg.user?.email || "Someone";
              toast(`New message from ${sender}`, {
                description: newMsg.update_text.slice(0, 100),
                style: { background: "#e6fffa", borderColor: "#319795", color: "#234e52" },
              });
            }
          }

          setTicket(newTicket);
          setUpdates(newUpdates);

          const atBottom =
            chatRef.current?.scrollHeight -
            chatRef.current?.scrollTop -
            chatRef.current?.clientHeight <
            100;
          if (hasNewUpdate && !atBottom) setNewMessageArrived(true);
        }
      } catch {}
    };

    pollingRef.current = setInterval(poll, 5000); // Increased to 5s for better performance
    return () => {
      if (pollingRef.current) clearInterval(pollingRef.current);
    };
  }, [ticket?.id, chatVisible]);

  useEffect(() => {
    const chatElement = chatRef.current;
    if (chatElement) {
      chatElement.addEventListener("scroll", handleScroll);
    }
    return () => {
      if (chatElement) {
        chatElement.removeEventListener("scroll", handleScroll);
      }
    };
  }, [updates]);

  const handleSend = async () => {
    if (!ticket || (!messageText.trim() && !file)) {
      setPopupMessage("Please enter a message or attach a file.");
      return;
    }

    if (["resolved"].includes(ticket.status?.toLowerCase())) {
      setPopupMessage("This ticket is resolved.");
      return;
    }

    try {
      setLoading(true);
      const res = await sendTicketUpdate(ticket.id, messageText, file);
      lastSentMessageIdRef.current = res.id;
      setMessageText("");
      setFile(null);
      if (fileInputRef.current) fileInputRef.current.value = "";
      scrollToBottom();
    } catch {
      setPopupMessage("Failed to send update.");
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status?.toLowerCase().replace(/\s+/g, "-")) {
      case "open":
        return "bg-teal-100 text-teal-800 border-teal-200";
      case "under-review":
        return "bg-amber-100 text-amber-800 border-amber-200";
      case "resolved":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case "low":
        return "bg-gray-100 text-gray-800 border-gray-200";
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "urgent":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const renderStatusChange = (item: TimelineItem) => {
    const name = item.changed_by?.first_name
      ? `${item.changed_by.first_name} ${item.changed_by.last_name}`
      : item.changed_by?.email || "System";
    const formattedStatus = item.to_status?.replace(/\b\w/g, (l) => l.toUpperCase());

    return (
      <div
        key={item.id}
        className="relative flex items-center justify-center text-sm font-medium text-gray-600 my-4"
      >
        <div className="absolute left-0 right-0 h-px bg-gray-200"></div>
        <span className="relative bg-white px-3">
          Status changed to{" "}
          <span className="font-semibold text-teal-600">{formattedStatus}</span> by {name} -{" "}
          {new Date(item.updated_at).toLocaleString()}
        </span>
      </div>
    );
  };

  const renderMessage = (msg: TimelineItem) => {
    const isGM = msg.user?.type?.code === "GRANT_MAKER";
    const name = msg.user?.first_name || msg.user?.last_name
      ? `${msg.user.first_name || ""} ${msg.user.last_name || ""}`.trim()
      : msg.user?.email || "System";

    return (
      <div
        key={msg.id}
        className={`flex ${isGM ? "justify-start" : "justify-end"} mb-3 px-3`}
      >
        <div
          className={`max-w-[80%] p-3 rounded-md shadow-sm transition-all text-sm ${
            isGM
              ? "bg-amber-50 text-amber-800 border border-amber-200 hover:bg-amber-100"
              : "bg-teal-50 text-teal-800 border border-teal-200 hover:bg-teal-100"
          }`}
        >
          <div className="font-semibold text-gray-900 mb-1">
            {name}
            <span className="text-xs font-normal text-gray-500 ml-2">
              {new Date(msg.updated_at).toLocaleString()}
            </span>
          </div>
          <p className="text-sm whitespace-pre-line mb-2">{msg.update_text}</p>
          {msg.attachments?.map((url, idx) => (
            <a
              key={idx}
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-blue-600 hover:text-blue-700 hover:underline flex items-center gap-1 mt-1"
            >
              <Paperclip className="w-4 h-4" />
              {new URL(url).pathname.split("/").pop()?.split("?")[0]}
            </a>
          ))}
        </div>
      </div>
    );
  };

  const renderSidebar = () => {
    if (!initialSidebarLoaded) return <SidebarSkeleton />;
    return (
      <Card className="w-full lg:w-[300px] bg-white shadow-md border-none">
        <CardHeader className="bg-white border-b border-teal-100">
          <CardTitle className="text-xl font-semibold text-gray-900">
            Case Overview
          </CardTitle>
        </CardHeader>
        <CardContent className="p-5 space-y-4">
          <div className="flex items-center justify-between gap-3">
            <DetailRow
              icon={ShieldCheck}
              label="Status"
              value={ticket?.status}
              badge={getStatusBadgeClass}
            />
            {ticket?.status?.toLowerCase() === "resolved" && (
              <Button
                onClick={async () => {
                  try {
                    const res = await reopenTicket(ticket.id);
                    setTicket((prev) => prev ? { ...prev, status: res.status } : prev);
                    toast.success("Ticket Reopened", {
                      style: { background: "#e6fffa", borderColor: "#319795", color: "#333" },
                    });
                  } catch {
                    setPopupMessage("Failed to reopen ticket.");
                  }
                }}
                className="px-4 py-2 text-sm font-medium bg-teal-600 text-white hover:bg-teal-700 rounded-md shadow hover:shadow-md"
              >
                Reopen
              </Button>
            )}
          </div>
          <DetailRow icon={Tag} label="Title" value={ticket?.title} />
          <DetailRow icon={Folder} label="Category" value={ticket?.category} />
          <DetailRow
            icon={Flag}
            label="Priority"
            value={ticket?.priority}
            badge={getPriorityBadge}
          />
          <DetailRow
            icon={CalendarClock}
            label="Created"
            value={ticket?.created_at ? new Date(ticket.created_at).toLocaleString() : "-"}
          />
          <DetailRow
            icon={Clock}
            label="Updated"
            value={ticket?.updated_at ? new Date(ticket.updated_at).toLocaleString() : "-"}
          />
          <Button
            onClick={() => window.open(`/support-ticket/export?id=${ticket.id}`, "_blank")}
            className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium bg-teal-600 text-white hover:bg-teal-700 rounded-md shadow hover:shadow-md"
          >
            <FileDown className="w-4 h-4" /> Export
          </Button>
        </CardContent>
      </Card>
    );
  };

  return (
    <>
      <Head>
        <title>Ticket History - Ticket ID: {ticket?.id || "Loading"}</title>
        <meta
          name="description"
          content={`View the history and updates for Ticket ID: ${ticket?.id || "Loading"}`}
        />
      </Head>
      <div className="w-full min-h-screen bg-gray-50">
        {popupMessage && <Popup message={popupMessage} onClose={() => setPopupMessage("")} />}
        <Card className="w-full bg-white shadow-md border-none">
          <CardContent className="p-0">
            <div className="flex flex-col lg:flex-row gap-0 h-[calc(100vh-64px)]">
              <div className="flex flex-col flex-1 bg-white">
                {chatVisible ? (
                  <div className="flex flex-col h-full">
                    <div
                      ref={chatRef}
                      className="flex-1 overflow-y-auto py-6 space-y-4 bg-gray-50"
                    >
                      {timeline.length === 0 ? (
                        <div className="text-center text-sm font-medium text-gray-500">
                          No updates available
                        </div>
                      ) : (
                        timeline.map((item) =>
                          item.type === "status" ? renderStatusChange(item) : renderMessage(item)
                        )
                      )}
                      <div ref={bottomRef} />
                    </div>
                    {newMessageArrived && (
                      <div className="absolute bottom-20 right-4 animate-bounce">
                        <Button
                          onClick={scrollToBottom}
                          className="bg-teal-600 text-white text-sm font-medium px-4 py-2 rounded-md shadow hover:shadow-md"
                        >
                          <ChevronDown className="w-4 h-4 mr-1" />
                          New Messages
                        </Button>
                      </div>
                    )}
                    <div className="border-t px-4 py-3 bg-white flex items-center gap-3 shadow-sm">
                      <Button
                        onClick={() => fileInputRef.current?.click()}
                        className="text-gray-500 hover:text-teal-600 bg-transparent hover:bg-teal-50 p-2 rounded-md"
                        aria-label="Attach a file"
                      >
                        <Paperclip className="w-5 h-5" />
                      </Button>
                      {file && (
                        <div className="flex items-center gap-2 px-3 py-1 bg-gray-100 border border-teal-200 rounded-md text-sm text-gray-800 font-medium max-w-xs truncate">
                          <Paperclip className="w-4 h-4 text-teal-500" />
                          <span className="truncate">{file.name}</span>
                          <Button
                            onClick={() => {
                              setFile(null);
                              if (fileInputRef.current) fileInputRef.current.value = "";
                            }}
                            className="text-gray-500 hover:text-red-600 bg-transparent p-1 rounded-md"
                            aria-label="Remove file"
                          >
                            <XCircle className="w-4 h-4" />
                          </Button>
                        </div>
                      )}
                      <input
                        type="file"
                        ref={fileInputRef}
                        onChange={(e) => setFile(e.target.files?.[0] || null)}
                        className="hidden"
                      />
                      <div className="flex-1">
                        <textarea
                          value={messageText}
                          onChange={(e) => setMessageText(e.target.value)}
                          placeholder="Write your message..."
                          className="w-full h-10 resize-none bg-gray-100 focus:bg-white border border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-300 rounded-md px-4 py-2 text-sm font-medium outline-none placeholder-gray-500 transition-all"
                        />
                      </div>
                      <Button
                        onClick={handleSend}
                        disabled={loading}
                        className="px-4 py-2 bg-teal-600 text-white hover:bg-teal-700 text-sm font-medium rounded-md shadow hover:shadow-md disabled:bg-teal-400 disabled:cursor-not-allowed"
                      >
                        {loading ? (
                          <LoaderCircle className="w-5 h-5 animate-spin" />
                        ) : (
                          <Send className="w-5 h-5" />
                        )}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="flex-1 flex items-center justify-center text-gray-600 text-base font-medium">
                    Loading chat...
                  </div>
                )}
              </div>
              {renderSidebar()}
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
