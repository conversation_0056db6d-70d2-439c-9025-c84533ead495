import { z } from "zod";

export const organizationDetailsFormSchema = z.object({
    organizationName: z.string().min(1, "Organization Name is required"),
    organizationLegalType: z.string().min(1, "Legal Type is required"),
    taxRegistrationNumber: z.string().min(1, "Tax Registration Number is required"),
    panNumber: z.string().min(1, "PAN is required"),
    teamMembers: z.string().min(1, "Number of Team Members is required"),
    websiteUrl: z.string().url("Must be a valid URL"),
    budget: z.string().min(1, "Budget is required"),
    registeredYear: z.string().min(1, "Registered Year is required").max(4, "Registered year must contain at most 4 characters"),
    mission: z.string().min(1, "Mission Statement is required"),
    vision: z.string().min(1, "Vision is required"),
    backgroundHistory: z.string()
      .min(1, "Background & History is required")
      .refine(val => val.trim().split(/\s+/).length <= 500, {
        message: "Background & History must be at most 500 words",
      }),
});

export const organizationDetailsFormDefaultValues = {
    organizationName: "",
    organizationLegalType: "",
    taxRegistrationNumber: "",
    panNumber: "",
    teamMembers: "",
    websiteUrl: "",
    budget: "",
    registeredYear: "",
    mission: "",
    vision: "",
    backgroundHistory: "",
};
export type OrganizationDetailsFormType = z.infer<typeof organizationDetailsFormSchema>;