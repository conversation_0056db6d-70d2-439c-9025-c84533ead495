import { z } from "zod";

export const historicalGrantFormSchema = z.object({
  grantName: z.string().min(1, "Grant name is required"),
  grantPurpose: z.string().min(1, "Grant purpose is required"),
  budget: z.coerce.number().min(1, "Budget must be greater than 0"),
  startDate: z.date(),
  endDate: z.date(),
  status: z.enum(["PENDING", "ACTIVE", "COMPLETED"]),
});

export type HistoricalGrantFormType = z.infer<typeof historicalGrantFormSchema>;



