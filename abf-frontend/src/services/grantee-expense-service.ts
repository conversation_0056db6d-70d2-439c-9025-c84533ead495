/**
 * Grantee Expense Service Module
 *
 * This module provides functions and data structures for working with grantee expense data.
 * It includes interfaces for expense information and API functions to fetch and manipulate expense data.
 */

import axios from 'axios';
import apiClient from '@/lib/apiClient';
import { API_BASE_URL } from '@/config/constants';
import { ExpenseRecord } from './funding-service';
import { useAuthContext } from '@/contexts/AuthContext';

// Configuration flag to determine whether to use the API or mock data
// Set this to true to use the real API endpoints
const USE_API = true;

// Mock data for when the API fails
const mockExpenseHistory: ExpenseRecord[] = [
  {
    id: 'EXP-2024-001',
    loggedDate: '2024-01-15',
    totalBudget: 50000,
    totalActualSpent: 48000,
    status: 'approved',
    attachment: 'expense-1.pdf',
    category: 'Personnel',
    description: 'Staff Salaries',
    source_type: 'manual',
    receipt: 'personnel-receipt-jan2024.pdf',
    remarks: 'Some expenses were lower than budgeted due to staff vacancies.',
    budget_q1: 12500,
    budget_q2: 12500,
    budget_q3: 12500,
    budget_q4: 12500,
    actual_q1: 12000,
    actual_q2: 12000,
    actual_q3: 12000,
    actual_q4: 12000,
    units: 'Months',
    frequency: '12',
    cost_per_unit: 4166.67,
    is_frozen: false,
    particulars: 'Staff Salaries',
    main_headers: 'Personnel',
    sub_headers: 'Permanent Staff'
  },
  {
    id: 'EXP-2024-002',
    loggedDate: '2024-02-01',
    totalBudget: 45000,
    totalActualSpent: 43000,
    status: 'pending',
    attachment: 'Manual Entry',
    category: 'Operations',
    description: 'Office Rent',
    source_type: 'manual',
    receipt: 'operations-receipt-feb2024.pdf',
    budget_q1: 11250,
    budget_q2: 11250,
    budget_q3: 11250,
    budget_q4: 11250,
    actual_q1: 10750,
    actual_q2: 10750,
    actual_q3: 10750,
    actual_q4: 10750,
    units: 'Months',
    frequency: '12',
    cost_per_unit: 3750,
    is_frozen: false,
    particulars: 'Office Rent',
    main_headers: 'Operations',
    sub_headers: 'Facilities'
  },
  {
    id: 'EXP-2024-003',
    loggedDate: '2024-02-15',
    totalBudget: 60000,
    totalActualSpent: 58000,
    status: 'rejected',
    attachment: 'expense-data.xlsx',
    category: 'Programs',
    description: 'Program Supplies',
    source_type: 'excel',
    rejection_notes: 'Please provide more detailed breakdown of program supplies and include receipts for all purchases over $1,000.',
    receipt: null,
    budget_q1: 15000,
    budget_q2: 15000,
    budget_q3: 15000,
    budget_q4: 15000,
    actual_q1: 14500,
    actual_q2: 14500,
    actual_q3: 14500,
    actual_q4: 14500,
    units: 'Items',
    frequency: '4',
    cost_per_unit: 15000,
    is_frozen: false,
    particulars: 'Program Supplies',
    main_headers: 'Programs',
    sub_headers: 'Materials'
  },
  {
    id: 'EXP-2024-004',
    loggedDate: '2024-03-01',
    totalBudget: 55000,
    totalActualSpent: 52000,
    status: 'approved',
    attachment: 'expense-4.pdf',
    category: 'Personnel',
    description: 'Consultant Fees',
    source_type: 'manual',
    budget_q1: 13750,
    budget_q2: 13750,
    budget_q3: 13750,
    budget_q4: 13750,
    actual_q1: 13000,
    actual_q2: 13000,
    actual_q3: 13000,
    actual_q4: 13000,
    units: 'Days',
    frequency: '50',
    cost_per_unit: 1100,
    is_frozen: false,
    particulars: 'Consultant Fees',
    main_headers: 'Personnel',
    sub_headers: 'Consultants'
  },
  {
    id: 'EXP-2024-005',
    loggedDate: '2024-03-15',
    totalBudget: 30000,
    totalActualSpent: 32000,
    status: 'approved',
    attachment: 'Manual Entry',
    category: 'Travel',
    description: 'Field Visits',
    source_type: 'manual',
    receipt: 'travel-receipts-march2024.pdf',
    budget_q1: 7500,
    budget_q2: 7500,
    budget_q3: 7500,
    budget_q4: 7500,
    actual_q1: 8000,
    actual_q2: 8000,
    actual_q3: 8000,
    actual_q4: 8000,
    units: 'Trips',
    frequency: '12',
    cost_per_unit: 2500,
    remarks: 'Increased costs due to fuel price increases and additional emergency field visits.',
    is_frozen: false,
    particulars: 'Field Visits',
    main_headers: 'Travel',
    sub_headers: 'Field Work'
  },
  {
    id: 'EXP-2024-006',
    loggedDate: '2024-04-01',
    totalBudget: 40000,
    totalActualSpent: 38000,
    status: 'pending',
    attachment: 'expense-data.xlsx',
    category: 'Equipment',
    description: 'IT Equipment',
    source_type: 'excel',
    budget_q1: 10000,
    budget_q2: 10000,
    budget_q3: 10000,
    budget_q4: 10000,
    actual_q1: 9500,
    actual_q2: 9500,
    actual_q3: 9500,
    actual_q4: 9500,
    units: 'Items',
    frequency: '20',
    cost_per_unit: 2000,
    is_frozen: true,
    particulars: 'IT Equipment',
    main_headers: 'Equipment',
    sub_headers: 'Hardware'
  }
];

/**
 * Fetches expense history data from the backend
 * @returns Promise with an array of expense records
 */
export const getExpenseHistory = async (): Promise<ExpenseRecord[]> => {
  // If USE_API is false, return mock data immediately
  if (!USE_API) {
    console.log('Using mock expense history data (API disabled)');

    // Try to load saved expenses from localStorage
    try {
      const savedExpenses = localStorage.getItem('mockExpenseHistory');
      if (savedExpenses) {
        const parsedExpenses = JSON.parse(savedExpenses);
        console.log('Loaded saved expenses from localStorage:', parsedExpenses.length);

        // Combine saved expenses with mock data, avoiding duplicates
        const combinedExpenses = [...parsedExpenses];

        // Add any mock expenses that aren't already in the saved expenses
        mockExpenseHistory.forEach(mockExpense => {
          if (!combinedExpenses.some(e => e.id === mockExpense.id)) {
            combinedExpenses.push(mockExpense);
          }
        });

        // Sort by date, newest first
        combinedExpenses.sort((a, b) =>
          new Date(b.loggedDate).getTime() - new Date(a.loggedDate).getTime()
        );

        return combinedExpenses;
      }
    } catch (err) {
      console.error('Error loading from localStorage:', err);
    }

    return mockExpenseHistory;
  }

  try {
    // Use the apiClient which handles authentication
    // The API endpoint should include /api since that's what the backend expects
    console.log('Attempting to fetch expense history from API...');
    const response = await apiClient.get('/api/funding/v1/expenses/list');

    // Check if the response is an array or wrapped in a data property
    console.log('Response data structure:', response.data);

    let responseData = [];
    if (Array.isArray(response.data)) {
      responseData = response.data;
    } else if (response.data && response.data.data) {
      // If the response has a data property
      if (Array.isArray(response.data.data)) {
        responseData = response.data.data;
      } else if (response.data.data.expenses && Array.isArray(response.data.data.expenses)) {
        responseData = response.data.data.expenses;
      }
    } else if (response.data && response.data.expenses && Array.isArray(response.data.expenses)) {
      responseData = response.data.expenses;
    } else if (response.data && response.data.status === 'success' && Array.isArray(response.data.data)) {
      // Handle the new response format from our updated backend
      responseData = response.data.data;
    }

    // Transform the API data to match our ExpenseRecord interface
    const allExpenses = responseData.map((item: any) => {
      // For debugging
      console.log('API response item:', item);

      return {
        id: item.id.toString(),
        loggedDate: item.expense_date || new Date().toISOString().split('T')[0],
        totalBudget: item.total_budget || 0,
        totalActualSpent: item.total_actual || 0,
        status: item.status || 'pending', // Use the status from the API or default to pending
        // Fix attachment display - use the actual file name when available
        attachment: item.receipt ? item.receipt : (item.source_type === 'manual' ? 'Manual Entry' : ''),
        category: item.main_headers || '',
        description: item.particulars || '',
        source_type: item.source_type || 'manual',
        units: item.units || '',
        frequency: item.frequency || '',
        cost_per_unit: item.cost_per_unit || 0,
        budget_q1: item.budget_q1 || 0,
        budget_q2: item.budget_q2 || 0,
        budget_q3: item.budget_q3 || 0,
        budget_q4: item.budget_q4 || 0,
        actual_q1: item.actual_q1 || 0,
        actual_q2: item.actual_q2 || 0,
        actual_q3: item.actual_q3 || 0,
        actual_q4: item.actual_q4 || 0,
        receipt: item.receipt || null,
        remarks: item.remarks || '',
        is_frozen: item.is_frozen || false,
        rejection_notes: item.rejection_notes || ''
      };
    });

    // Sort by date, newest first
    allExpenses.sort((a: ExpenseRecord, b: ExpenseRecord) =>
      new Date(b.loggedDate).getTime() - new Date(a.loggedDate).getTime()
    );

    console.log('Successfully fetched expense history from API');
    return allExpenses;
  } catch (error) {
    console.error('Error fetching expense history:', error);

    // Check if the error is a 500 Internal Server Error
    if (error.response && error.response.status === 500) {
      console.warn('Server returned 500 error. Using mock expense history data as fallback');

      // Log the error message from the server if available
      if (error.response.data && error.response.data.message) {
        console.error('Server error message:', error.response.data.message);
      }
    } else {
      console.warn('API call failed. Using mock expense history data as fallback');
    }

    return mockExpenseHistory;
  }
};

/**
 * Fetches a single expense record by ID
 * @param id The expense record ID
 * @returns Promise with the expense record
 */
export const getExpenseById = async (id: string): Promise<ExpenseRecord> => {
  // If USE_API is false, return mock data immediately
  if (!USE_API) {
    console.log(`Using mock expense data for ID ${id} (API disabled)`);

    // Try to load saved expenses from localStorage first
    try {
      const savedExpenses = localStorage.getItem('mockExpenseHistory');
      if (savedExpenses) {
        const parsedExpenses = JSON.parse(savedExpenses);
        const savedExpense = parsedExpenses.find((expense: any) => expense.id === id);
        if (savedExpense) {
          console.log('Found expense in localStorage:', savedExpense);
          return savedExpense;
        }
      }
    } catch (err) {
      console.error('Error loading from localStorage:', err);
    }

    // If not found in localStorage, check the mock data
    const mockExpense = mockExpenseHistory.find(expense => expense.id === id);
    if (mockExpense) {
      return mockExpense;
    }

    // If no matching mock expense is found, create a default one
    return {
      id: id,
      loggedDate: new Date().toISOString().split('T')[0],
      totalBudget: 0,
      totalActualSpent: 0,
      status: 'pending',
      attachment: 'Not Available',
      category: 'Unknown',
      source_type: 'manual',
      particulars: 'Unknown',
      main_headers: 'Unknown',
      sub_headers: 'Unknown'
    };
  }

  try {
    // Use the apiClient which handles authentication
    console.log(`Attempting to fetch expense with ID ${id} from API...`);
    const response = await apiClient.get(`/api/funding/v1/expenses/${id}/`);

    // Check if the response is wrapped in a data property
    console.log('Response data structure for single expense:', response.data);

    let item;
    if (response.data && response.data.data) {
      item = response.data.data;
    } else if (response.data && response.data.status === 'success' && response.data.data) {
      // Handle the new response format from our updated backend
      item = response.data.data;
    } else {
      item = response.data;
    }

    return {
      id: item.id.toString(),
      loggedDate: item.expense_date || new Date().toISOString().split('T')[0],
      totalBudget: item.total_budget || 0,
      totalActualSpent: item.total_actual || 0,
      status: item.status || 'pending', // Use the status from the API or default to pending
      attachment: item.receipt ? item.receipt : (item.source_type === 'manual' ? 'Manual Entry' : ''),
      category: item.main_headers || '',
      description: item.particulars || '',
      source_type: item.source_type || 'manual',
      units: item.units || '',
      frequency: item.frequency || '',
      cost_per_unit: item.cost_per_unit || 0,
      budget_q1: item.budget_q1 || 0,
      budget_q2: item.budget_q2 || 0,
      budget_q3: item.budget_q3 || 0,
      budget_q4: item.budget_q4 || 0,
      actual_q1: item.actual_q1 || 0,
      actual_q2: item.actual_q2 || 0,
      actual_q3: item.actual_q3 || 0,
      actual_q4: item.actual_q4 || 0,
      receipt: item.receipt || null,
      remarks: item.remarks || '',
      is_frozen: item.is_frozen || false,
      rejection_notes: item.rejection_notes || ''
    };
  } catch (error) {
    console.error(`Error fetching expense with ID ${id}:`, error);

    // Check if the error is a 500 Internal Server Error
    if (error.response && error.response.status === 500) {
      console.warn('Server returned 500 error. Using mock expense data as fallback');

      // Log the error message from the server if available
      if (error.response.data && error.response.data.message) {
        console.error('Server error message:', error.response.data.message);
      }
    } else {
      console.warn('API call failed. Using mock expense data as fallback');
    }

    // Find the expense in the mock data
    const mockExpense = mockExpenseHistory.find(expense => expense.id === id);
    if (mockExpense) {
      return mockExpense;
    }

    // If no matching mock expense is found, create a default one
    return {
      id: id,
      loggedDate: new Date().toISOString().split('T')[0],
      totalBudget: 0,
      totalActualSpent: 0,
      status: 'pending',
      attachment: 'Not Available',
      category: 'Unknown',
      source_type: 'manual'
    };
  }
};

/**
 * Submits an expense to the backend
 * @param formData The expense form data
 * @returns Promise with the created expense
 */
export const submitExpense = async (formData: FormData): Promise<any> => {
  // Extract data from the form for validation and mock data creation
  const sourceType = formData.get('source_type');
  const totalBudget = parseFloat(formData.get('total_budget') as string) || 0;
  const totalActual = parseFloat(formData.get('total_actual') as string) || 0;
  const remarks = formData.get('remarks');

  // Check if actual > budget and remarks are provided
  if (totalActual > totalBudget && (!remarks || remarks.toString().trim() === '')) {
    throw new Error('Remarks are required when actual expenses exceed budget');
  }

  // If USE_API is false, return mock data immediately
  if (!USE_API) {
    console.log('Using mock response for expense submission (API disabled)');

    // Generate a new mock expense ID
    const newId = `EXP-${Date.now()}`;

    // Create a new mock expense record for the session
    const newExpense = {
      id: newId,
      loggedDate: new Date().toISOString().split('T')[0],
      totalBudget: totalBudget,
      totalActualSpent: totalActual,
      status: 'pending',
      attachment: formData.get('receipt') ? (formData.get('receipt') as File).name : 'Manual Entry',
      receipt: formData.get('receipt') ? (formData.get('receipt') as File).name : null,
      category: formData.get('main_headers') as string || 'Uncategorized',
      description: formData.get('particulars') as string || 'New Expense',
      particulars: formData.get('particulars') as string || 'New Expense',
      main_headers: formData.get('main_headers') as string || 'Uncategorized',
      sub_headers: formData.get('sub_headers') as string || '',
      source_type: 'manual',
      remarks: formData.get('remarks') as string || '',
      units: formData.get('units') as string || '',
      frequency: formData.get('frequency') as string || '',
      cost_per_unit: parseFloat(formData.get('cost_per_unit') as string) || 0,
      budget_q1: parseFloat(formData.get('budget_q1') as string) || 0,
      budget_q2: parseFloat(formData.get('budget_q2') as string) || 0,
      budget_q3: parseFloat(formData.get('budget_q3') as string) || 0,
      budget_q4: parseFloat(formData.get('budget_q4') as string) || 0,
      actual_q1: parseFloat(formData.get('actual_q1') as string) || 0,
      actual_q2: parseFloat(formData.get('actual_q2') as string) || 0,
      actual_q3: parseFloat(formData.get('actual_q3') as string) || 0,
      actual_q4: parseFloat(formData.get('actual_q4') as string) || 0,
      budget_quarterly: {
        Q1: parseFloat(formData.get('budget_q1') as string) || 0,
        Q2: parseFloat(formData.get('budget_q2') as string) || 0,
        Q3: parseFloat(formData.get('budget_q3') as string) || 0,
        Q4: parseFloat(formData.get('budget_q4') as string) || 0
      },
      actuals_quarterly: {
        Q1: parseFloat(formData.get('actual_q1') as string) || 0,
        Q2: parseFloat(formData.get('actual_q2') as string) || 0,
        Q3: parseFloat(formData.get('actual_q3') as string) || 0,
        Q4: parseFloat(formData.get('actual_q4') as string) || 0
      }
    };

    // Add the new expense to the mock data (for this session only)
    // Use localStorage to persist the mock data between page refreshes
    mockExpenseHistory.unshift(newExpense as ExpenseRecord);

    // Save the updated mock data to localStorage
    try {
      const existingData = localStorage.getItem('mockExpenseHistory');
      let allExpenses = existingData ? JSON.parse(existingData) : [];
      allExpenses.unshift(newExpense);
      localStorage.setItem('mockExpenseHistory', JSON.stringify(allExpenses));
    } catch (err) {
      console.error('Error saving to localStorage:', err);
    }

    return {
      id: newId,
      status: 'success',
      message: 'Expense submitted successfully (mock response)'
    };
  }

  try {
    // Get the current user's grant ID
    // In a real implementation, this would come from the user's context or profile
    // For now, we'll use a default grant ID of 1
    const grantId = 1; // This should be dynamically fetched in a real implementation
    formData.append('grant', grantId.toString());

    // Get the current date for the expense_date field if not provided
    if (!formData.get('expense_date')) {
      const currentDate = new Date().toISOString().split('T')[0];
      formData.append('expense_date', currentDate);
    }

    console.log('Attempting to submit expense to API...');
    // Submit the expense to the backend using apiClient which handles authentication
    const response = await apiClient.post('/api/funding/v1/expenses/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 30000,
    });

    console.log('Successfully submitted expense to API');
    // Return the response data, which should be in the format { status, message, data }
    return response.data;
  } catch (error) {
    console.error('Error submitting expense:', error);

    // Check if the error is a 500 Internal Server Error
    if (error.response && error.response.status === 500) {
      console.warn('Server returned 500 error. Using mock response for expense submission as fallback');

      // Log the error message from the server if available
      if (error.response.data && error.response.data.message) {
        console.error('Server error message:', error.response.data.message);
      }
    } else {
      console.warn('API call failed. Using mock response for expense submission as fallback');
    }

    // Generate a new mock expense ID
    const newId = `EXP-${Date.now()}`;

    // Create a new mock expense record for the session
    const newExpense = {
      id: newId,
      loggedDate: new Date().toISOString().split('T')[0],
      totalBudget: totalBudget,
      totalActualSpent: totalActual,
      status: 'pending',
      attachment: formData.get('receipt') ? (formData.get('receipt') as File).name : 'Manual Entry',
      receipt: formData.get('receipt') ? (formData.get('receipt') as File).name : null,
      category: formData.get('main_headers') as string || 'Uncategorized',
      description: formData.get('particulars') as string || 'New Expense',
      particulars: formData.get('particulars') as string || 'New Expense',
      main_headers: formData.get('main_headers') as string || 'Uncategorized',
      sub_headers: formData.get('sub_headers') as string || '',
      source_type: 'manual',
      remarks: formData.get('remarks') as string || '',
      units: formData.get('units') as string || '',
      frequency: formData.get('frequency') as string || '',
      cost_per_unit: parseFloat(formData.get('cost_per_unit') as string) || 0,
      budget_q1: parseFloat(formData.get('budget_q1') as string) || 0,
      budget_q2: parseFloat(formData.get('budget_q2') as string) || 0,
      budget_q3: parseFloat(formData.get('budget_q3') as string) || 0,
      budget_q4: parseFloat(formData.get('budget_q4') as string) || 0,
      actual_q1: parseFloat(formData.get('actual_q1') as string) || 0,
      actual_q2: parseFloat(formData.get('actual_q2') as string) || 0,
      actual_q3: parseFloat(formData.get('actual_q3') as string) || 0,
      actual_q4: parseFloat(formData.get('actual_q4') as string) || 0,
      budget_quarterly: {
        Q1: parseFloat(formData.get('budget_q1') as string) || 0,
        Q2: parseFloat(formData.get('budget_q2') as string) || 0,
        Q3: parseFloat(formData.get('budget_q3') as string) || 0,
        Q4: parseFloat(formData.get('budget_q4') as string) || 0
      },
      actuals_quarterly: {
        Q1: parseFloat(formData.get('actual_q1') as string) || 0,
        Q2: parseFloat(formData.get('actual_q2') as string) || 0,
        Q3: parseFloat(formData.get('actual_q3') as string) || 0,
        Q4: parseFloat(formData.get('actual_q4') as string) || 0
      }
    };

    // Add the new expense to the mock data (for this session only)
    // Use localStorage to persist the mock data between page refreshes
    mockExpenseHistory.unshift(newExpense as ExpenseRecord);

    // Save the updated mock data to localStorage
    try {
      const existingData = localStorage.getItem('mockExpenseHistory');
      let allExpenses = existingData ? JSON.parse(existingData) : [];
      allExpenses.unshift(newExpense);
      localStorage.setItem('mockExpenseHistory', JSON.stringify(allExpenses));
    } catch (err) {
      console.error('Error saving to localStorage:', err);
    }

    return {
      id: newId,
      status: 'success',
      message: 'Expense submitted successfully (mock fallback response)'
    };
  }
};
