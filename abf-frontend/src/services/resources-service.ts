import apiClient from "@/lib/apiClient";

export const deleteResources = async (id: number) => {
  try {
    const data = await apiClient.delete(`/api/resources/v1/resources/${id}`);
    return data.data;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.error || error.message || "Unknown error";
    console.error("deleteResources error:", errorMessage, error.response?.data);
    throw new Error(errorMessage);
  }
};

export const getResources = async () => {
  try {
    const { data } = await apiClient.get("/api/resources/v1/resources/");
    return data;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.error || error.message || "Unknown error";
    console.error("getResources error:", errorMessage, error.response?.data);
    throw new Error(errorMessage);
  }
};

// services/resources-service.ts
export const createResources = async (
  resources: { title: string; description: string; file: File }[],
) => {
  try {
    const formData = new FormData();
    resources.forEach((resource, index) => {
      console.log(`Adding resource ${index}:`, {
        title: resource.title,
        description: resource.description,
        file: resource.file.name,
      });
      formData.append(`resources[${index}][title]`, resource.title);
      formData.append(
        `resources[${index}][description]`,
        resource.description || "",
      );
      formData.append(`resources[${index}][file]`, resource.file);
    });
    // Log FormData entries
    for (const [key, value] of formData.entries()) {
      console.log(
        `FormData: ${key} = ${value instanceof File ? value.name : value}`,
      );
    }
    const { data } = await apiClient.post(
      "/api/resources/v1/resources/",
      formData,
      {
        headers: { "Content-Type": "multipart/form-data" },
      },
    );
    return data;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.error || error.message || "Unknown error";
    console.error("createResources error:", errorMessage, error.response?.data);
    throw new Error(errorMessage);
  }
};

export const getResource = async (id: number) => {
  try {
    const { data } = await apiClient.get(`/api/resources/v1/resources/${id}/`);
    return data;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.error || error.message || "Unknown error";
    console.error("getResource error:", errorMessage, error.response?.data);
    throw new Error(errorMessage);
  }
};
