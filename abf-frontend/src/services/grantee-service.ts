/**
 * Grantee Service Module
 *
 * This module provides functions and data structures for working with grantee data.
 * It includes interfaces for grantee information, funding details, milestones, and
 * API functions to fetch and manipulate grantee data.
 */

import apiClient from "@/lib/apiClient";

/**
 * Represents a grantee with basic information
 */
export interface Grantee {
  id: string;
  name: string;
  organization: string;
  organizationId: string;
  totalFunding: number;
  status: 'active' | 'pending' | 'completed';
  contactPerson: string;
  contactEmail: string;
  location: string;
  startDate: string;
  endDate?: string;
  orgType?: 'TRUST' | 'NON_PROFIT' | 'SOCIETY';
}

/**
 * Represents detailed funding information for a grantee
 */
export interface GranteeFunding {
  id: string;
  granteeName: string;
  organizationName: string;
  totalBudget: number;
  totalDisbursed: number;
  pendingAmount: number;
  quarterlyBreakdown: Array<{
    quarter: string;
    budgeted: number;
    disbursed: number;
  }>;
  monthlyBreakdown: Array<{
    month: string;
    amount: number;
  }>;
}

/**
 * Represents a milestone for a grantee
 */
export interface GranteeMilestone {
  id: string;
  title: string;
  granteeName: string;
  organizationName: string;
  dueDate: string;
  status: 'completed' | 'pending' | 'overdue';
  description?: string;
}

/**
 * Comprehensive details about a grantee including funding and milestones
 */
export interface GranteeDetails {
  id: string;
  name: string;
  organization: string;
  organizationId: string;
  totalFunding: number;
  status: 'active' | 'pending' | 'completed';
  contactPerson: string;
  contactEmail: string;
  location: string;
  startDate: string;
  endDate?: string;
  funding: GranteeFunding;
  milestones: GranteeMilestone[];
  expectedOutputs: Array<{
    sr_no: number;
    activity: string;
    description: string;
    target_group: string;
    defined_quarterly: { Q1: string; Q2: string; Q3: string; Q4: string };
    actuals_quarterly: { Q1: string; Q2: string; Q3: string; Q4: string };
  }>;
  expectedOutcomes: Array<{
    sr_no: number;
    activity: string;
    description: string;
    target_group: string;
    defined_quarterly: { Q1: string; Q2: string; Q3: string; Q4: string };
    actuals_quarterly: { Q1: string; Q2: string; Q3: string; Q4: string };
  }>;
}

// Mock data for grantees
const mockGrantees: Grantee[] = [
  {
    id: "1",
    name: "John Doe",
    organization: "Education Foundation",
    organizationId: "1",
    totalFunding: 250000,
    status: 'active',
    contactPerson: "John Doe",
    contactEmail: "<EMAIL>",
    location: "Mumbai",
    startDate: "2023-01-15",
    orgType: "TRUST"
  },
  {
    id: "2",
    name: "Jane Smith",
    organization: "Healthcare Initiative",
    organizationId: "2",
    totalFunding: 350000,
    status: 'active',
    contactPerson: "Jane Smith",
    contactEmail: "<EMAIL>",
    location: "Delhi",
    startDate: "2023-02-10",
    orgType: "NON_PROFIT"
  },
  {
    id: "3",
    name: "Raj Kumar",
    organization: "Community Development Trust",
    organizationId: "3",
    totalFunding: 180000,
    status: 'pending',
    contactPerson: "Raj Kumar",
    contactEmail: "<EMAIL>",
    location: "Bangalore",
    startDate: "2023-03-05",
    orgType: "TRUST"
  },
  {
    id: "4",
    name: "Priya Sharma",
    organization: "Environmental Action Group",
    organizationId: "4",
    totalFunding: 200000,
    status: 'active',
    contactPerson: "Priya Sharma",
    contactEmail: "<EMAIL>",
    location: "Chennai",
    startDate: "2023-01-20",
    orgType: "SOCIETY"
  },
  {
    id: "5",
    name: "Amit Patel",
    organization: "Arts & Culture Foundation",
    organizationId: "5",
    totalFunding: 120000,
    status: 'completed',
    contactPerson: "Amit Patel",
    contactEmail: "<EMAIL>",
    location: "Hyderabad",
    startDate: "2023-02-01",
    endDate: "2023-08-01",
    orgType: "NON_PROFIT"
  },
];

// Mock funding data for each grantee
const mockGranteeFunding: Record<string, GranteeFunding> = {
  "1": {
    id: "1",
    granteeName: "John Doe",
    organizationName: "Education Foundation",
    totalBudget: 250000,
    totalDisbursed: 150000,
    pendingAmount: 100000,
    quarterlyBreakdown: [
      { quarter: "Q1", budgeted: 60000, disbursed: 60000 },
      { quarter: "Q2", budgeted: 70000, disbursed: 70000 },
      { quarter: "Q3", budgeted: 60000, disbursed: 20000 },
      { quarter: "Q4", budgeted: 60000, disbursed: 0 },
    ],
    monthlyBreakdown: [
      { month: "Jan", amount: 20000 },
      { month: "Feb", amount: 20000 },
      { month: "Mar", amount: 20000 },
      { month: "Apr", amount: 25000 },
      { month: "May", amount: 25000 },
      { month: "Jun", amount: 20000 },
      { month: "Jul", amount: 10000 },
      { month: "Aug", amount: 10000 },
      { month: "Sep", amount: 0 },
      { month: "Oct", amount: 0 },
      { month: "Nov", amount: 0 },
      { month: "Dec", amount: 0 },
    ],
  },
  "2": {
    id: "2",
    granteeName: "Jane Smith",
    organizationName: "Healthcare Initiative",
    totalBudget: 350000,
    totalDisbursed: 200000,
    pendingAmount: 150000,
    quarterlyBreakdown: [
      { quarter: "Q1", budgeted: 90000, disbursed: 90000 },
      { quarter: "Q2", budgeted: 90000, disbursed: 90000 },
      { quarter: "Q3", budgeted: 85000, disbursed: 20000 },
      { quarter: "Q4", budgeted: 85000, disbursed: 0 },
    ],
    monthlyBreakdown: [
      { month: "Jan", amount: 30000 },
      { month: "Feb", amount: 30000 },
      { month: "Mar", amount: 30000 },
      { month: "Apr", amount: 30000 },
      { month: "May", amount: 30000 },
      { month: "Jun", amount: 30000 },
      { month: "Jul", amount: 20000 },
      { month: "Aug", amount: 0 },
      { month: "Sep", amount: 0 },
      { month: "Oct", amount: 0 },
      { month: "Nov", amount: 0 },
      { month: "Dec", amount: 0 },
    ],
  },
  // Add more mock funding data for other grantees...
};

// Mock milestone data for each grantee
const mockGranteeMilestones: Record<string, GranteeMilestone[]> = {
  "1": [
    {
      id: "m1",
      title: "Complete teacher training program",
      granteeName: "John Doe",
      organizationName: "Education Foundation",
      dueDate: "2023-03-15",
      status: 'completed',
      description: "Train 50 teachers on new curriculum",
    },
    {
      id: "m2",
      title: "Launch online learning platform",
      granteeName: "John Doe",
      organizationName: "Education Foundation",
      dueDate: "2023-06-30",
      status: 'completed',
      description: "Develop and launch platform for remote learning",
    },
    {
      id: "m3",
      title: "Conduct mid-year assessment",
      granteeName: "John Doe",
      organizationName: "Education Foundation",
      dueDate: "2023-08-15",
      status: 'pending',
      description: "Evaluate program effectiveness and student outcomes",
    },
  ],
  "2": [
    {
      id: "m4",
      title: "Complete healthcare worker training",
      granteeName: "Jane Smith",
      organizationName: "Healthcare Initiative",
      dueDate: "2023-04-10",
      status: 'completed',
      description: "Train 30 healthcare workers on new protocols",
    },
    {
      id: "m5",
      title: "Launch mobile health clinic",
      granteeName: "Jane Smith",
      organizationName: "Healthcare Initiative",
      dueDate: "2023-07-15",
      status: 'completed',
      description: "Deploy mobile clinic to rural areas",
    },
    {
      id: "m6",
      title: "Conduct health awareness campaign",
      granteeName: "Jane Smith",
      organizationName: "Healthcare Initiative",
      dueDate: "2023-09-30",
      status: 'pending',
      description: "Organize community events for health education",
    },
  ],
  // Add more mock milestone data for other grantees...
};

// Mock expected outputs and outcomes for each grantee
const mockExpectedOutputs: Record<string, any[]> = {
  "1": [
    {
      sr_no: 1,
      activity: "Teacher Training Program",
      description: "Comprehensive training for teachers on new curriculum",
      target_group: "School teachers",
      defined_quarterly: { Q1: "20 teachers", Q2: "30 teachers", Q3: "", Q4: "" },
      actuals_quarterly: { Q1: "22 teachers", Q2: "28 teachers", Q3: "", Q4: "" },
    },
    {
      sr_no: 2,
      activity: "Online Learning Platform",
      description: "Development of digital learning resources",
      target_group: "Students",
      defined_quarterly: { Q1: "Planning", Q2: "Development", Q3: "Testing", Q4: "Launch" },
      actuals_quarterly: { Q1: "Completed", Q2: "In progress", Q3: "", Q4: "" },
    },
  ],
  "2": [
    {
      sr_no: 1,
      activity: "Healthcare Worker Training",
      description: "Training on new medical protocols",
      target_group: "Healthcare workers",
      defined_quarterly: { Q1: "15 workers", Q2: "15 workers", Q3: "", Q4: "" },
      actuals_quarterly: { Q1: "15 workers", Q2: "15 workers", Q3: "", Q4: "" },
    },
    {
      sr_no: 2,
      activity: "Mobile Health Clinic",
      description: "Mobile clinic for rural areas",
      target_group: "Rural communities",
      defined_quarterly: { Q1: "Planning", Q2: "Procurement", Q3: "Deployment", Q4: "Operation" },
      actuals_quarterly: { Q1: "Completed", Q2: "Completed", Q3: "", Q4: "" },
    },
  ],
  // Add more mock expected outputs for other grantees...
};

const mockExpectedOutcomes: Record<string, any[]> = {
  "1": [
    {
      sr_no: 1,
      activity: "Improved Teaching Quality",
      description: "Enhanced teaching methodologies and skills",
      target_group: "School teachers",
      defined_quarterly: { Q1: "", Q2: "30% improvement", Q3: "", Q4: "50% improvement" },
      actuals_quarterly: { Q1: "", Q2: "35% improvement", Q3: "", Q4: "" },
    },
    {
      sr_no: 2,
      activity: "Increased Student Engagement",
      description: "Higher participation and interest in learning",
      target_group: "Students",
      defined_quarterly: { Q1: "", Q2: "", Q3: "20% increase", Q4: "40% increase" },
      actuals_quarterly: { Q1: "", Q2: "", Q3: "", Q4: "" },
    },
  ],
  "2": [
    {
      sr_no: 1,
      activity: "Improved Healthcare Access",
      description: "Better access to healthcare services in rural areas",
      target_group: "Rural communities",
      defined_quarterly: { Q1: "", Q2: "", Q3: "30% increase", Q4: "50% increase" },
      actuals_quarterly: { Q1: "", Q2: "", Q3: "", Q4: "" },
    },
    {
      sr_no: 2,
      activity: "Reduced Disease Incidence",
      description: "Lower rates of preventable diseases",
      target_group: "Community members",
      defined_quarterly: { Q1: "", Q2: "", Q3: "10% reduction", Q4: "20% reduction" },
      actuals_quarterly: { Q1: "", Q2: "", Q3: "", Q4: "" },
    },
  ],
  // Add more mock expected outcomes for other grantees...
};

// API functions
export const getGrantees = async (): Promise<Grantee[]> => {
  try {
    // In a real implementation, this would call the backend API
    // const response = await apiClient.get('/api/grantees');
    // return response.data;

    // For now, returning mock data
    return new Promise(resolve => {
      setTimeout(() => resolve(mockGrantees), 500);
    });
  } catch (error) {
    console.error('Error fetching grantees:', error);
    throw error;
  }
};

export const getGranteeDetails = async (id: string): Promise<GranteeDetails | undefined> => {
  try {
    // In a real implementation, this would call the backend API
    // const response = await apiClient.get(`/api/grantees/${id}`);
    // return response.data;

    // For now, returning mock data
    return new Promise(resolve => {
      setTimeout(() => {
        const grantee = mockGrantees.find(g => g.id === id);
        if (!grantee) {
          resolve(undefined);
          return;
        }

        const granteeDetails: GranteeDetails = {
          ...grantee,
          funding: mockGranteeFunding[id] || {
            id,
            granteeName: grantee.name,
            organizationName: grantee.organization,
            totalBudget: grantee.totalFunding,
            totalDisbursed: 0,
            pendingAmount: grantee.totalFunding,
            quarterlyBreakdown: [],
            monthlyBreakdown: [],
          },
          milestones: mockGranteeMilestones[id] || [],
          expectedOutputs: mockExpectedOutputs[id] || [],
          expectedOutcomes: mockExpectedOutcomes[id] || [],
        };

        resolve(granteeDetails);
      }, 500);
    });
  } catch (error) {
    console.error(`Error fetching grantee details for ID ${id}:`, error);
    throw error;
  }
};

export const getGranteeFunding = async (id: string): Promise<GranteeFunding | undefined> => {
  try {
    // In a real implementation, this would call the backend API
    // const response = await apiClient.get(`/api/grantees/${id}/funding`);
    // return response.data;

    // For now, returning mock data
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(mockGranteeFunding[id]);
      }, 500);
    });
  } catch (error) {
    console.error(`Error fetching grantee funding for ID ${id}:`, error);
    throw error;
  }
};

export const getGranteeMilestones = async (id: string): Promise<GranteeMilestone[]> => {
  try {
    // In a real implementation, this would call the backend API
    // const response = await apiClient.get(`/api/grantees/${id}/milestones`);
    // return response.data;

    // For now, returning mock data
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(mockGranteeMilestones[id] || []);
      }, 500);
    });
  } catch (error) {
    console.error(`Error fetching grantee milestones for ID ${id}:`, error);
    throw error;
  }
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    maximumFractionDigits: 0,
  }).format(amount);
};
