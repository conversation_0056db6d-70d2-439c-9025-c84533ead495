import {
  FundingR<PERSON>ord,
  FundingRecordAPIResponse,
  FundingRecordListAPIResponse,
  FundingRecordUpdateRequest,
  AddFundingRecordAPIResponse,
  EditFundingRecordAPIResponse,
  FundingAllocation,
  FundingAllocationCreateRequest,
  FundingAllocationAPIResponse,
  FundingAllocationListAPIResponse,
  AddFundingAllocationAPIResponse,
  EditFundingAllocationAPIResponse
} from "@/types/finance";
/**
 * Grantmaker Service Module
 *
 * This module provides functions and data structures for working with grantmaker data.
 * It includes interfaces for organization information, dashboard summaries, funding
 * and milestone overviews, and API functions to fetch and manipulate grantmaker data.
 */

import apiClient from "@/lib/apiClient";
import { AddFundingEntityAPIResponse, EditFundingEntityAPIResponse, FundingEntity, FundingEntityAPIResponse, FundingEntityListAPIResponse, FundingEntityUpdateRequest } from "@/types/finance";
import { AxiosResponse } from "axios";
import { GrantAPIResponse, OrganizationGrantAPIResponse } from "@/types/profile";

/**
 * Represents an organization with basic information
 */
export interface Organization {
  id: string;
  name: string;
  sector: string;
  totalFunding: number;
  status: 'active' | 'pending' | 'completed';
  startDate: string;
  endDate?: string;
  orgType?: 'TRUST' | 'NON_PROFIT' | 'SOCIETY';
  location?: string;
  contactPerson?: string;
}

/**
 * Represents summary data for the grantmaker dashboard
 */
export interface DashboardSummary {
  totalAmountGiven: number;
  numberOfGrantees: number;
  activeGrantees: number;
  pendingGrantees: number;
  completedGrantees: number;
  sectorDistribution: Array<{ name: string; value: number }>;
  monthlyDisbursements: Array<{ month: string; amount: number }>;
  quarterlyDisbursements: Array<{ quarter: string; amount: number }>;
  organizations: Organization[];
  // New data for enhanced dashboard
  fundingSources: Array<{ name: string; value: number }>;
  fundUtilization: {
    totalBudget: number;
    totalDisbursed: number;
    totalUtilized: number;
  };
  projectsImpact: {
    totalProjects: number;
    totalSectors: number;
    livesImpacted: number;
    projectsByType: Array<{ type: string; count: number }>;
  };
  geographicalSpread: Array<{
    state: string;
    projects: number;
    livesImpacted: number;
    programTypes: string[];
  }>;
  impactStories: Array<{
    id: string;
    title: string;
    description: string;
    imageUrl: string;
    organization: string;
    date: string;
  }>;
}

/**
 * Represents an overview of funding information
 */
export interface FundingOverview {
  totalBudget: number;
  totalDisbursed: number;
  pendingDisbursements: number;
  quarterlyBreakdown: Array<{
    quarter: string;
    budgeted: number;
    disbursed: number;
  }>;
  organizationBreakdown: Array<{
    organizationId: string;
    organizationName: string;
    totalBudget: number;
    totalDisbursed: number;
  }>;
}

/**
 * Represents an overview of milestone information
 */
export interface MilestoneOverview {
  totalMilestones: number;
  completedMilestones: number;
  upcomingMilestones: number;
  milestonesByOrganization: Array<{
    organizationId: string;
    organizationName: string;
    totalMilestones: number;
    completedMilestones: number;
  }>;
  recentMilestones: Array<{
    id: string;
    title: string;
    organizationId: string;
    organizationName: string;
    dueDate: string;
    status: 'completed' | 'pending' | 'overdue';
  }>;
}
/**
 * Represents a grant with detailed information
 */
export interface Grant {
  id: number;
  grant_name: string;
  grant_duration: string;
  grant_purpose: string;
  annual_budget: string; // Keeping it as string because it's returned as a stringified decimal
  funding_sources: string;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
  organization: number; // ID of recipient org (grantee)
  grant_maker_organization: number; // ID of the grant maker org
}

// Mock data for development
const mockOrganizations: Organization[] = [
  {
    id: "1",
    name: "Education First Foundation",
    sector: "Education",
    totalFunding: 1500000,
    status: 'active',
    startDate: "2023-01-15",
    orgType: "TRUST",
    location: "Bangalore, Karnataka",
    contactPerson: "Rahul Sharma"
  },
  {
    id: "2",
    name: "Healthcare Initiative",
    sector: "Healthcare",
    totalFunding: 1000000,
    status: 'active',
    startDate: "2023-02-10",
    orgType: "NON_PROFIT",
    location: "Mumbai, Maharashtra",
    contactPerson: "Priya Patel"
  }
];

const mockDashboardSummary: DashboardSummary = {
  totalAmountGiven: 2500000,
  numberOfGrantees: 15,
  activeGrantees: 8,
  pendingGrantees: 4,
  completedGrantees: 3,
  sectorDistribution: [
    { name: "Livelihood", value: 40.0 },
    { name: "Mental Health", value: 35.0 },
    { name: "Social Advancement", value: 25.0 },
  ],
  monthlyDisbursements: [
    { month: "Jan", amount: 120000 },
    { month: "Feb", amount: 150000 },
    { month: "Mar", amount: 90000 },
    { month: "Apr", amount: 110000 },
    { month: "May", amount: 130000 },
    { month: "Jun", amount: 100000 },
    { month: "Jul", amount: 140000 },
    { month: "Aug", amount: 120000 },
    { month: "Sep", amount: 80000 },
    { month: "Oct", amount: 60000 },
    { month: "Nov", amount: 0 },
    { month: "Dec", amount: 0 },
  ],
  quarterlyDisbursements: [
    { quarter: "Q1 (Apr-Jun)", amount: 340000 },
    { quarter: "Q2 (Jul-Sep)", amount: 340000 },
    { quarter: "Q3 (Oct-Dec)", amount: 60000 },
    { quarter: "Q4 (Jan-Mar)", amount: 360000 },
  ],
  organizations: mockOrganizations,
  // New mock data for enhanced dashboard
  fundingSources: [
    { name: "Svatantra", value: 1500000 },
    { name: "Chaitanya", value: 1000000 },
  ],
  fundUtilization: {
    totalBudget: 2500000,
    totalDisbursed: 1800000,
    totalUtilized: 1500000,
  },
  projectsImpact: {
    totalProjects: 12,
    totalSectors: 3,
    livesImpacted: 25000,
    projectsByType: [
      { type: "Livelihood", count: 5 },
      { type: "Mental Health", count: 4 },
      { type: "Social Advancement", count: 3 },
    ],
  },
  geographicalSpread: [
    {
      state: "Karnataka",
      projects: 3,
      livesImpacted: 7500,
      programTypes: ["Livelihood", "Mental Health", "Social Advancement"],
    },
    {
      state: "Maharashtra",
      projects: 2,
      livesImpacted: 5000,
      programTypes: ["Mental Health", "Livelihood"],
    },
    {
      state: "Tamil Nadu",
      projects: 2,
      livesImpacted: 4500,
      programTypes: ["Livelihood", "Social Advancement"],
    },
    {
      state: "Delhi",
      projects: 2,
      livesImpacted: 3000,
      programTypes: ["Mental Health", "Social Advancement"],
    },
    {
      state: "Gujarat",
      projects: 1,
      livesImpacted: 2500,
      programTypes: ["Mental Health"],
    },
    {
      state: "West Bengal",
      projects: 1,
      livesImpacted: 1500,
      programTypes: ["Livelihood"],
    },
    {
      state: "Uttar Pradesh",
      projects: 1,
      livesImpacted: 1000,
      programTypes: ["Social Advancement"],
    },
  ],
  impactStories: [
    {
      id: "1",
      title: "NeevJivan: Ending Migration, Empowering Transformation",
      description: "Building Livelihoods, Not Just Jobs: NeevJivan Foundation's Vision for Rural Prosperity",
      imageUrl: "/Nivjeevan.png",
      organization: "NeevJivan Foundation",
      date: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
    },
    {
      id: "2",
      title: "Digital Classrooms Transform Rural Education",
      description: "Our digital classroom initiative has helped 1000+ students in rural Karnataka access quality education resources.",
      imageUrl: "https://images.unsplash.com/photo-1503676260728-1c00da094a0b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1122&q=80",
      organization: "Education First Foundation",
      date: "2023-09-15",
    },
    {
      id: "3",
      title: "Mobile Health Clinics Reach Remote Villages",
      description: "Mobile clinics have provided essential healthcare services to over 5000 people in remote villages of Maharashtra.",
      imageUrl: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80",
      organization: "Healthcare Initiative",
      date: "2023-08-22",
    },
    {
      id: "4",
      title: "Community Development Program Empowers Women",
      description: "Our skill development program has helped 300 women start their own small businesses in Delhi NCR.",
      imageUrl: "https://images.unsplash.com/photo-1573497620053-ea5300f94f21?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80",
      organization: "Education First Foundation",
      date: "2023-07-10",
    },
    {
      id: "5",
      title: "Environmental Conservation Project Plants 10,000 Trees",
      description: "Our reforestation initiative has successfully planted 10,000 native trees in Tamil Nadu, creating green spaces for communities.",
      imageUrl: "https://images.unsplash.com/photo-1542601906990-b4d3fb778b09?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1174&q=80",
      organization: "Healthcare Initiative",
      date: "2023-06-05",
    },
  ],
};

const mockFundingOverview: FundingOverview = {
  totalBudget: 1100000,
  totalDisbursed: 850000,
  pendingDisbursements: 250000,
  quarterlyBreakdown: [
    { quarter: "Q1 2023", budgeted: 300000, disbursed: 280000 },
    { quarter: "Q2 2023", budgeted: 280000, disbursed: 260000 },
    { quarter: "Q3 2023", budgeted: 320000, disbursed: 310000 },
    { quarter: "Q4 2023", budgeted: 200000, disbursed: 0 },
  ],
  organizationBreakdown: mockOrganizations.map(org => ({
    organizationId: org.id,
    organizationName: org.name,
    totalBudget: org.totalFunding,
    totalDisbursed: org.status === 'completed'
      ? org.totalFunding
      : Math.floor(org.totalFunding * (Math.random() * 0.3 + 0.6)) // 60-90% disbursed
  }))
};

const mockMilestoneOverview: MilestoneOverview = {
  totalMilestones: 45,
  completedMilestones: 28,
  upcomingMilestones: 17,
  milestonesByOrganization: mockOrganizations.map(org => ({
    organizationId: org.id,
    organizationName: org.name,
    totalMilestones: Math.floor(Math.random() * 10) + 5, // 5-15 milestones
    completedMilestones: Math.floor(Math.random() * 5) + 3, // 3-8 completed
  })),
  recentMilestones: [
    {
      id: "m1",
      title: "Complete baseline survey",
      organizationId: "1",
      organizationName: "Education First Foundation",
      dueDate: "2023-10-15",
      status: 'completed',
    },
    {
      id: "m2",
      title: "Launch community health program",
      organizationId: "2",
      organizationName: "Healthcare Initiative",
      dueDate: "2023-10-30",
      status: 'pending',
    },
    {
      id: "m3",
      title: "Conduct training workshops",
      organizationId: "3",
      organizationName: "Education First Foundation",
      dueDate: "2023-09-20",
      status: 'overdue',
    },
    {
      id: "m4",
      title: "Plant 1000 trees",
      organizationId: "4",
      organizationName: "Healthcare Initiative",
      dueDate: "2023-11-10",
      status: 'pending',
    },
    {
      id: "m5",
      title: "Host art exhibition",
      organizationId: "5",
      organizationName: "Education First Foundation",
      dueDate: "2023-10-05",
      status: 'completed',
    },
  ],
};

// API functions
export const getDashboardSummary = async (
  year: string = '2024',
  quarter: string = 'All',
  grantId: string = 'all',
  viewMode: 'quarterly' | 'yearly' = 'quarterly'
): Promise<DashboardSummary> => {
  try {
    // In a real implementation, this would call the backend API
    // const response = await apiClient.get('/api/grantmaker/dashboard', {
    //   params: { year, quarter, grantId, viewMode }
    // });
    // return response.data;

    // For now, returning mock data with filtering logic
    return new Promise(resolve => {
      setTimeout(() => {
        // Create a proper deep copy of the mock data to modify based on filters
        const filteredData = JSON.parse(JSON.stringify(mockDashboardSummary));

        // Always ensure the default livesImpacted is 25000 when no specific filter is applied
        filteredData.projectsImpact.livesImpacted = 25000;

        // Filter monthly disbursements based on year and quarter
        if (viewMode === 'quarterly' && quarter !== 'All') {
          // Map financial quarters to month ranges
          const quarterMonths: Record<string, string[]> = {
            'Q1 (Apr-Jun)': ['Apr', 'May', 'Jun'],
            'Q2 (Jul-Sep)': ['Jul', 'Aug', 'Sep'],
            'Q3 (Oct-Dec)': ['Oct', 'Nov', 'Dec'],
            'Q4 (Jan-Mar)': ['Jan', 'Feb', 'Mar']
          };

          // Filter monthly data by quarter
          filteredData.monthlyDisbursements = mockDashboardSummary.monthlyDisbursements
            .filter(item => {
              const monthName = item.month.split(' ')[0]; // Extract month name
              return quarterMonths[quarter].includes(monthName);
            });
        }

        // If a specific grant is selected, filter data accordingly
        if (grantId !== 'all') {
          // In a real implementation, this would filter based on the grant ID
          // For mock data, we'll just reduce the numbers by a factor
          const factor = grantId === 'svatantra' ? 0.6 : grantId === 'chaitanya' ? 0.4 : 0.2;

          // Filter funding sources to only show the selected one
          if (grantId === 'svatantra') {
            filteredData.fundingSources = [{ name: "Svatantra", value: 1500000 }];
          } else if (grantId === 'chaitanya') {
            filteredData.fundingSources = [{ name: "Chaitanya", value: 1000000 }];
          }

          filteredData.totalAmountGiven = Math.round(mockDashboardSummary.totalAmountGiven * factor);
          filteredData.fundUtilization = {
            totalBudget: Math.round(mockDashboardSummary.fundUtilization.totalBudget * factor),
            totalDisbursed: Math.round(mockDashboardSummary.fundUtilization.totalDisbursed * factor),
            totalUtilized: Math.round(mockDashboardSummary.fundUtilization.totalUtilized * factor)
          };

          // Set specific livesImpacted values for each funding entity
          if (grantId === 'svatantra') {
            filteredData.projectsImpact.livesImpacted = 15000; // Svatantra's contribution
          } else if (grantId === 'chaitanya') {
            filteredData.projectsImpact.livesImpacted = 10000; // Chaitanya's contribution
          } else {
            filteredData.projectsImpact.livesImpacted = 6000; // For any other entity
          }
        }

        resolve(filteredData);
      }, 500);
    });
  } catch (error) {
    console.error('Error fetching dashboard summary:', error);
    throw error;
  }
};

export const getFundingOverview = async (): Promise<FundingOverview> => {
  try {
    // In a real implementation, this would call the backend API
    // const response = await apiClient.get('/api/grantmaker/funding');
    // return response.data;

    // For now, returning mock data
    return new Promise(resolve => {
      setTimeout(() => resolve(mockFundingOverview), 500);
    });
  } catch (error) {
    console.error('Error fetching funding overview:', error);
    throw error;
  }
};

export const getMilestoneOverview = async (): Promise<MilestoneOverview> => {
  try {
    // In a real implementation, this would call the backend API
    // const response = await apiClient.get('/api/grantmaker/milestones');
    // return response.data;

    // For now, returning mock data
    return new Promise(resolve => {
      setTimeout(() => resolve(mockMilestoneOverview), 500);
    });
  } catch (error) {
    console.error('Error fetching milestone overview:', error);
    throw error;
  }
};

export const getOrganizationDetails = async (id: string): Promise<Organization | undefined> => {
  try {
    // In a real implementation, this would call the backend API
    // const response = await apiClient.get(`/api/grantmaker/organizations/${id}`);
    // return response.data;

    // For now, returning mock data
    return new Promise(resolve => {
      setTimeout(() => {
        const organization = mockOrganizations.find(org => org.id === id);
        resolve(organization);
      }, 500);
    });
  } catch (error) {
    console.error(`Error fetching organization details for ID ${id}:`, error);
    throw error;
  }
};

export const getOrganizations = async (): Promise<Organization[]> => {
  try {
    // In a real implementation, this would call the backend API
    // const response = await apiClient.get('/api/grantmaker/organizations');
    // return response.data;

    // For now, returning mock data
    return new Promise(resolve => {
      setTimeout(() => resolve(mockOrganizations), 500);
    });
  } catch (error) {
    console.error('Error fetching organizations:', error);
    throw error;
  }
};

// Helper function to format currency
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    maximumFractionDigits: 0,
  }).format(amount);
};

export const getGrants = async (): Promise<OrganizationGrantAPIResponse[] | undefined> => {
  try {

    const response = await apiClient.get('/api/v1/grants/');
    return response.data;

  } catch (error: any) {
    console.error('Error fetching grants:', error);
    return undefined;
  }
}

export const transformFundingEntityAPIResponseToFundingEntity = (backendData: FundingEntityAPIResponse): FundingEntity => {

  return {
    id: backendData.id,
    organizationFunctionType: backendData.organization_function_type,
    organizationName: backendData.organization_name,
    panNumber: backendData.pan_number,

  }

}

export const transformFundingEntityListAPIResponse = (
  backendList: FundingEntityAPIResponse[]
): FundingEntity[] => {
  return backendList.map(transformFundingEntityAPIResponseToFundingEntity);
};

export const transformFundingRecordAPIResponseToFundingRecord = (
  backendData: FundingRecordAPIResponse
): FundingRecord => {
  return {
    id: backendData.id,
    fundingEntity: backendData.funding_entity,
    amount: backendData.amount,
    dateReceived: backendData.date_received,
    notes: backendData.notes,
    fundingEntityName: backendData.funding_entity_name
  };
};

export const transformFundingRecordListAPIResponse = (
  backendList: FundingRecordAPIResponse[]
): FundingRecord[] => {
  return backendList.map(transformFundingRecordAPIResponseToFundingRecord);
};

export const getAllFundingEntities = async (): Promise<FundingEntityListAPIResponse> => {

  const response = await apiClient.get('/api/profiles/v1/funding-entity/');
  return response.data;

}

export const addFundingEntity = async (values: FundingEntityUpdateRequest): Promise<AddFundingEntityAPIResponse> => {
  const response = await apiClient.post('/api/profiles/v1/funding-entity/', values);
  return response.data;

}

export const deleteFundingEntity = async (id: number): Promise<AxiosResponse> => {
  const response = await apiClient.delete(`/api/profiles/v1/funding-entity/${id}/`);
  return response;
}

export const editFundingEntity = async (id: number, values: FundingEntityUpdateRequest): Promise<EditFundingEntityAPIResponse> => {
  const response = await apiClient.patch(`/api/profiles/v1/funding-entity/${id}/`, values);
  return response.data;

}

export const getAllFundingRecords = async (): Promise<FundingRecordListAPIResponse> => {
  const response = await apiClient.get('/api/funding/v1/funding-records/');
  return response.data;
};

export const addFundingRecord = async (values: FundingRecordUpdateRequest): Promise<AddFundingRecordAPIResponse> => {
  const response = await apiClient.post('/api/funding/v1/funding-records/', values);
  return response.data;
};

export const deleteFundingRecord = async (id: number): Promise<AxiosResponse> => {
  const response = await apiClient.delete(`/api/funding/v1/funding-records/${id}/`);
  return response;
};

export const editFundingRecord = async (id: number, values: FundingRecordUpdateRequest): Promise<EditFundingRecordAPIResponse> => {
  const response = await apiClient.patch(`/api/funding/v1/funding-records/${id}/`, values);
  return response.data;
};

export const fetchFundingAllocations = async (): Promise<FundingAllocationListAPIResponse> => {
  const response = await apiClient.get('/api/funding/v1/funding-allocations/');
  return response.data;
};

export const addFundingAllocation = async (
  payload: FundingAllocationCreateRequest
): Promise<AddFundingAllocationAPIResponse> => {
  const response = await apiClient.post('/api/funding/v1/funding-allocations/', payload);
  return response.data;
};

export const updateFundingAllocation = async (
  id: number,
  payload: Partial<FundingAllocationCreateRequest>
): Promise<EditFundingAllocationAPIResponse> => {
  const response = await apiClient.patch(`/api/funding/v1/funding-allocations/${id}/`, payload);
  return response.data;
};

export const deleteFundingAllocation = async (
  id: number
): Promise<AxiosResponse> => {
  const response = await apiClient.delete(`/api/funding/v1/funding-allocations/${id}/`);
  return response;
};

export const transformFundingAllocationAPIResponseToFundingAllocation = (
  backendData: FundingAllocationAPIResponse
): FundingAllocation => {
  return {
    id: backendData.id,
    grant: backendData.grant,
    fundingEntity: backendData.funding_entity,
    grantName: backendData.grant_name,
    grantRecipientOrganization: backendData.grant_recipient_organization,
    grantRecipientOrganizationName: backendData.grant_recipient_organization_name,
    fundingEntityName: backendData.funding_entity_name,
    amountAllocated: parseFloat(backendData.amount_allocated),
    notes: backendData.notes,
  };
};

export const transformFundingAllocationListAPIResponse = (
  backendList: FundingAllocationAPIResponse[]
): FundingAllocation[] => {
  return backendList.map(transformFundingAllocationAPIResponseToFundingAllocation);
};
