import apiClient from "@/lib/apiClient";
import { ExpenseRecord } from "../funding-service";

/**
 * Interface for expense summary data
 */
export interface ExpenseSummary {
  total_budget: number;
  total_actual: number;
  pending_count: number;
  approved_count: number;
  rejected_count: number;
  organization_breakdown: Array<{
    organization_id: string;
    organization_name: string;
    total_expenses: number;
  }>;
}

/**
 * Fetches expense summary data for a grantmaker
 * @param granteeId Optional grantee ID to filter expenses
 * @param grantId Optional grant ID to filter expenses
 * @returns Promise with expense summary data
 */
export const getExpenseSummary = async (granteeId?: string, grantId?: string): Promise<ExpenseSummary> => {
  try {
    // Build query parameters
    const params: Record<string, string> = {};
    if (granteeId) params.grantee_id = granteeId;
    if (grantId) params.grant_id = grantId;

    // Make API request
    const response = await apiClient.get('/api/funding/v1/expenses/summary/', { params });
    return response.data.data;
  } catch (error) {
    console.error('Error fetching expense summary:', error);
    throw error;
  }
};

/**
 * Fetches expense records for a specific grantee
 * @param granteeId Grantee ID to filter expenses
 * @param status Optional status to filter expenses
 * @returns Promise with an array of expense records
 */
export const getGranteeExpenses = async (granteeId: string, status?: string): Promise<ExpenseRecord[]> => {
  try {
    // Build query parameters
    const params: Record<string, string> = {};
    if (status) params.status = status;

    // Make API request to the new endpoint
    const response = await apiClient.get(`/api/funding/v1/grantee/${granteeId}/expenses/`, { params });

    if (response.data.status === 'SUCCESS') {
      // Map backend data to frontend format with complete data structure
      return response.data.data.map((expense: any) => ({
        id: expense.id.toString(),
        loggedDate: expense.expense_date,
        particulars: expense.particulars || '',
        main_header: expense.main_header || '',
        sub_header: expense.sub_header || '',
        activity_description: expense.activity_description || '',
        units: expense.units?.toString() || '0',
        frequency: expense.frequency?.toString() || '0',
        cost_per_unit: expense.cost_per_unit || 0,
        total_budget: expense.total_budget || 0,
        total_grant_budget: (expense.units || 0) * (expense.cost_per_unit || 0) * (expense.frequency || 1),
        totalBudget: expense.total_budget || 0,
        totalActualSpent: expense.total_actual || 0,
        total_actual: expense.total_actual || 0,
        budget_q1: expense.budget_q1 || 0,
        budget_q2: expense.budget_q2 || 0,
        budget_q3: expense.budget_q3 || 0,
        budget_q4: expense.budget_q4 || 0,
        actual_q1: expense.actual_q1 || 0,
        actual_q2: expense.actual_q2 || 0,
        actual_q3: expense.actual_q3 || 0,
        actual_q4: expense.actual_q4 || 0,
        budget_quarterly: {
          'Apr-Jun': expense.budget_q1 || 0,
          'Jul-Sep': expense.budget_q2 || 0,
          'Oct-Dec': expense.budget_q3 || 0,
          'Jan-Mar': expense.budget_q4 || 0,
        },
        actual_quarterly: {
          'Apr-Jun': expense.actual_q1 || 0,
          'Jul-Sep': expense.actual_q2 || 0,
          'Oct-Dec': expense.actual_q3 || 0,
          'Jan-Mar': expense.actual_q4 || 0,
        },
        status: expense.status,
        remarks: expense.remarks || '',
        rejection_notes: expense.metadata?.rejection_notes,
        attachment: expense.source_type === 'excel' ? 'Excel Upload' : 'Manual Entry',
        source_type: expense.source_type || 'manual',
        is_frozen: expense.is_frozen || false,
        grant: expense.grant?.toString() || '',
        // Additional fields for compatibility
        category: expense.main_header || '',
        description: expense.particulars || '',
        main_headers: expense.main_header || '',
        sub_headers: expense.sub_header || '',
      }));
    }

    return [];
  } catch (error) {
    console.error(`Error fetching expenses for grantee ${granteeId}:`, error);

    // For development/demo purposes, return mock data if API fails
    return [
      {
        id: "exp-001",
        loggedDate: "2024-05-01",
        particulars: "Staff Salaries",
        main_headers: "Human Resources",
        sub_headers: "Permanent Staff",
        totalBudget: 600000,
        totalActualSpent: 580000,
        status: "pending",
        attachment: "Excel Upload",
        source_type: "excel",
        units: "Months",
        frequency: "12",
        cost_per_unit: 50000,
        budget_q1: 150000,
        budget_q2: 150000,
        budget_q3: 150000,
        budget_q4: 150000,
        actual_q1: 145000,
        actual_q2: 150000,
        actual_q3: 145000,
        actual_q4: 140000
      },
      {
        id: "exp-002",
        loggedDate: "2024-05-05",
        particulars: "Office Rent",
        main_headers: "Administrative",
        sub_headers: "Rent & Utilities",
        totalBudget: 240000,
        totalActualSpent: 240000,
        status: "approved",
        attachment: "Manual Entry",
        source_type: "manual",
        units: "Months",
        frequency: "12",
        cost_per_unit: 20000,
        budget_q1: 60000,
        budget_q2: 60000,
        budget_q3: 60000,
        budget_q4: 60000,
        actual_q1: 60000,
        actual_q2: 60000,
        actual_q3: 60000,
        actual_q4: 60000
      },
      {
        id: "exp-003",
        loggedDate: "2024-05-10",
        particulars: "Program Materials",
        main_headers: "Program",
        sub_headers: "Educational Materials",
        totalBudget: 150000,
        totalActualSpent: 175000,
        status: "rejected",
        attachment: "Excel Upload",
        source_type: "excel",
        units: "Sets",
        frequency: "1",
        cost_per_unit: 150000,
        budget_q1: 37500,
        budget_q2: 37500,
        budget_q3: 37500,
        budget_q4: 37500,
        actual_q1: 45000,
        actual_q2: 40000,
        actual_q3: 50000,
        actual_q4: 40000,
        remarks: "Budget exceeded due to price increase of materials",
        rejection_notes: "Please provide detailed breakdown of price increases and supporting documentation"
      }
    ];
  }
};

/**
 * Updates the status of an expense
 * @param expenseId Expense ID to update
 * @param status New status ('approved' or 'rejected')
 * @param notes Optional notes (required for rejection)
 * @returns Promise with the updated expense
 */
export const updateExpenseStatus = async (
  expenseId: string,
  status: 'approved' | 'rejected',
  notes?: string
): Promise<ExpenseRecord> => {
  try {
    // Validate input
    if (status === 'rejected' && (!notes || notes.trim() === '')) {
      throw new Error('Notes are required when rejecting an expense');
    }

    // Make API request
    const response = await apiClient.post(`/api/funding/v1/expenses/${expenseId}/update-status/`, {
      status,
      notes
    });

    if (response.data.status === 'SUCCESS') {
      const expense = response.data.data;
      // Map backend data to frontend format
      return {
        id: expense.id.toString(),
        loggedDate: expense.expense_date,
        category: expense.main_header,
        description: expense.particulars,
        totalBudget: expense.total_budget,
        totalActualSpent: expense.total_actual,
        status: expense.status,
        attachment: expense.source_type === 'excel' ? 'Excel Upload' : 'Manual Entry',
        source_type: expense.source_type,
        units: expense.units?.toString(),
        frequency: expense.frequency?.toString(),
        cost_per_unit: expense.cost_per_unit,
        budget_q1: expense.budget_q1,
        budget_q2: expense.budget_q2,
        budget_q3: expense.budget_q3,
        budget_q4: expense.budget_q4,
        actual_q1: expense.actual_q1,
        actual_q2: expense.actual_q2,
        actual_q3: expense.actual_q3,
        actual_q4: expense.actual_q4,
        remarks: expense.remarks,
        rejection_notes: expense.metadata?.rejection_notes,
        is_frozen: expense.is_frozen
      };
    }

    throw new Error('Failed to update expense status');
  } catch (error) {
    console.error(`Error updating expense status for expense ${expenseId}:`, error);
    throw error;
  }
};

/**
 * Fetches a specific expense by ID
 * @param expenseId Expense ID to fetch
 * @returns Promise with the expense record
 */
export const getExpenseById = async (expenseId: string): Promise<ExpenseRecord> => {
  try {
    const response = await apiClient.get(`/api/funding/v1/expenses/${expenseId}/`);
    return response.data.data;
  } catch (error) {
    console.error(`Error fetching expense with ID ${expenseId}:`, error);
    throw error;
  }
};
