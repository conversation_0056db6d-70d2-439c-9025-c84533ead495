import apiClient from "@/lib/apiClient";

export interface User {
  id: string;
  cognito_sub: string;
  email: string;
  first_name: string;
  last_name: string;
  organization: number | null;
  role: number;
  type: number;
  organization_name?: string;
  role_name?: string;
  type_name?: string;
}

/**
 * Fetches all users from the backend
 * @returns Promise with an array of users
 */
export const getAllUsers = async (): Promise<User[]> => {
  try {
    // Try to fetch from the backend API
    try {
      const response = await apiClient.get('/api/grantmaker/v1/users/');
      console.log("Response = " + JSON.stringify(response));
      if (response.data && response.data.status === 'success') {
        // Transform the API response to include organization names
        const users = response.data.data;

        // Fetch organizations to get their names
        try {
          const orgResponse = await apiClient.get('/api/grantmaker/v1/grantees/all/');
          if (orgResponse.data && orgResponse.data.status === 'success') {
            const organizations = orgResponse.data.data;

            // Create a map of organization IDs to names
            const orgMap = organizations.reduce((map, org) => {
              map[org.id] = org.organization_name;
              return map;
            }, {});

            // Map users with organization names
            return users.map((user: any) => ({
              ...user,
              organization_name: user.organization ? orgMap[user.organization] || `Organization #${user.organization}` : 'No Organization',
              role_name: user.role === 1 ? 'Grantee' : 'Grant Maker',
              type_name: user.type === 1 ? 'Grantee' : 'Grant Maker'
            }));
          }
        } catch (orgError) {
          console.warn('Failed to fetch organization names:', orgError);
        }

        // Fallback if organization fetch fails
        return users.map((user: any) => ({
          ...user,
          organization_name: user.organization ? `Organization #${user.organization}` : 'No Organization',
          role_name: user.role === 1 ? 'Grantee' : 'Grant Maker',
          type_name: user.type === 1 ? 'Grantee' : 'Grant Maker'
        }));
      }
    } catch (apiError) {
      console.warn('API call for users failed, using mock data:', apiError);
    }

    // Fallback to mock data if API call fails
    return [
      {
        id: "1",
        cognito_sub: "abc123",
        email: "<EMAIL>",
        first_name: "John",
        last_name: "Doe",
        organization: 1,
        organization_name: "ABFF",
        role: 1,
        role_name: "Grantee",
        type: 1,
        type_name: "Grantee"
      },
      {
        id: "2",
        cognito_sub: "def456",
        email: "<EMAIL>",
        first_name: "Jane",
        last_name: "Doe",
        organization: 2,
        organization_name: "SAMM",
        role: 1,
        role_name: "Grantee",
        type: 1,
        type_name: "Grantee"
      },
      {
        id: "3",
        cognito_sub: "ghi789",
        email: "<EMAIL>",
        first_name: "Admin",
        last_name: "User",
        organization: 3,
        organization_name: "Green Future Foundation",
        role: 2,
        role_name: "Grant Maker",
        type: 2,
        type_name: "Grant Maker"
      }
    ];
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};
