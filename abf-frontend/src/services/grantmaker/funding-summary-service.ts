/**
 * Funding Summary Service Module
 *
 * This module provides functions and data structures for working with funding summary data.
 * It includes interfaces for organization funding information, funding summaries,
 * and API functions to fetch and manipulate funding summary data.
 */

// In a real implementation, we would use apiClient to make API calls
// import apiClient from "@/lib/apiClient";

/**
 * Represents funding information for an organization
 */
export interface OrganizationFunding {
  id: string;
  name: string;
  sector: string;
  totalBudget: number;
  totalDisbursed: number;
  remainingBalance: number;
  utilizationPercentage: number;
  lastDisbursementDate: string;
  status: 'Active' | 'Completed' | 'On Hold';
}

/**
 * Represents a summary of funding information across all organizations
 */
export interface FundingSummary {
  totalBudget: number;
  totalDisbursed: number;
  remainingBalance: number;
  organizationFunding: OrganizationFunding[];
  sectorDistribution: Array<{ name: string; value: number; color: string }>;
}

// Mock data for development
const mockFundingSummary: FundingSummary = {
  totalBudget: 1100000,
  totalDisbursed: 850000,
  remainingBalance: 250000,
  organizationFunding: [
    {
      id: "1",
      name: "Education First Foundation",
      sector: "Education",
      totalBudget: 250000,
      totalDisbursed: 200000,
      remainingBalance: 50000,
      utilizationPercentage: 80,
      lastDisbursementDate: "2024-07-15",
      status: 'Active',
    },
    {
      id: "2",
      name: "Healthcare Initiative",
      sector: "Healthcare",
      totalBudget: 350000,
      totalDisbursed: 300000,
      remainingBalance: 50000,
      utilizationPercentage: 86,
      lastDisbursementDate: "2024-08-10",
      status: 'Active',
    },
    {
      id: "3",
      name: "Community Development Trust",
      sector: "Community",
      totalBudget: 180000,
      totalDisbursed: 150000,
      remainingBalance: 30000,
      utilizationPercentage: 83,
      lastDisbursementDate: "2024-07-05",
      status: 'Active',
    },
    {
      id: "4",
      name: "Green Earth Project",
      sector: "Environment",
      totalBudget: 200000,
      totalDisbursed: 180000,
      remainingBalance: 20000,
      utilizationPercentage: 90,
      lastDisbursementDate: "2024-08-20",
      status: 'Active',
    },
    {
      id: "5",
      name: "Arts & Culture Foundation",
      sector: "Arts",
      totalBudget: 120000,
      totalDisbursed: 120000,
      remainingBalance: 0,
      utilizationPercentage: 100,
      lastDisbursementDate: "2024-06-12",
      status: 'Completed',
    },
  ],
  sectorDistribution: [
    { name: "Education", value: 250000, color: "#FF9800" },
    { name: "Healthcare", value: 350000, color: "#FFC107" },
    { name: "Community", value: 180000, color: "#FFD54F" },
    { name: "Environment", value: 200000, color: "#FFECB3" },
    { name: "Arts", value: 120000, color: "#FFE082" },
  ],
};

/**
 * Fetches funding summary data
 * @param year The year to fetch data for
 * @param quarter The quarter to fetch data for (optional)
 * @param sector The sector to fetch data for (optional)
 * @returns Promise with funding summary data
 */
export const getFundingSummary = async (
  year: string, // Would be used in real API call
  quarter: string = 'All', // Would be used in real API call
  sector: string = 'All'
): Promise<FundingSummary> => {
  try {
    // In a real implementation, this would call the backend API
    // const response = await apiClient.get('/api/grantmaker/funding/summary', {
    //   params: { year, quarter, sector }
    // });
    // return response.data;

    // For now, returning mock data
    return new Promise(resolve => {
      setTimeout(() => {
        // Filter data based on sector if needed
        if (sector !== 'All') {
          const filteredOrgs = mockFundingSummary.organizationFunding.filter(
            org => org.sector === sector
          );

          // Calculate new totals based on filtered organizations
          const totalBudget = filteredOrgs.reduce((sum, org) => sum + org.totalBudget, 0);
          const totalDisbursed = filteredOrgs.reduce((sum, org) => sum + org.totalDisbursed, 0);

          resolve({
            ...mockFundingSummary,
            totalBudget,
            totalDisbursed,
            remainingBalance: totalBudget - totalDisbursed,
            organizationFunding: filteredOrgs
          });
        } else {
          resolve(mockFundingSummary);
        }
      }, 500);
    });
  } catch (error) {
    console.error('Error fetching funding summary:', error);
    throw error;
  }
};

/**
 * Fetches organization funding details
 * @param id Organization ID
 * @returns Promise with organization funding details
 */
export const getOrganizationFundingDetails = async (id: string): Promise<OrganizationFunding | undefined> => {
  try {
    // In a real implementation, this would call the backend API
    // const response = await apiClient.get(`/api/grantmaker/funding/organizations/${id}`);
    // return response.data;

    // For now, returning mock data
    return new Promise(resolve => {
      setTimeout(() => {
        const organization = mockFundingSummary.organizationFunding.find(org => org.id === id);
        resolve(organization);
      }, 500);
    });
  } catch (error) {
    console.error(`Error fetching organization funding details for ID ${id}:`, error);
    throw error;
  }
};
