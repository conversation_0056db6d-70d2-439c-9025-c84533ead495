import { OrganizationDetails } from "@/components/profile";
import apiClient from "@/lib/apiClient";
import { OrganizationAPIResponse, Organization, OrganizationListAPIResponse } from "@/types/profile";

/**
 * Fetches all organizations from the backend
 * @returns Promise with an array of organizations
 */
export const getAllOrganizations = async (): Promise<OrganizationListAPIResponse> => {
      const response = await apiClient.get('/api/grantmaker/v1/grantees/');
      return response.data;
      if (response.data && response.data.status === 'success') {
        // Transform the API response to include financial data
        return response.data.data.map((org: any) => {
          // Generate realistic financial values for demonstration
          const totalBudget = Math.floor(Math.random() * 1000000) + 500000;
          const totalDisbursed = Math.floor(Math.random() * totalBudget);
          const remainingBalance = totalBudget - totalDisbursed;
          const utilizationPercentage = Math.round((totalDisbursed / totalBudget) * 100);

          return {
            ...org,
            organization_legal_type_name: org.organization_legal_type_name,
            organization_function_type_name: org.organization_function_type_name,
            totalBudget,
            totalDisbursed,
            remainingBalance,
            utilizationPercentage,
            email: org.email || `contact@${org.organization_name.toLowerCase().replace(/\s+/g, '')}.org`,
            contact_person: org.contact_person || 'Contact Person',
            phone: org.phone || '+91 9876543210',
            address: org.address || 'Organization Address'
          };
        });
      }
};

/**
 * Fetches an organization by ID
 * @param id Organization ID
 * @returns Promise with the organization
 */
export const getOrganizationById = async (id: string): Promise<Organization | undefined> => {
  try {
    // Try to fetch from the backend API
    try {
      const response = await apiClient.get(`/api/grantmaker/v1/grantees/${id}/`);
      if (response.data && response.data.status === 'success') {
        const orgData = response.data.data.organization;

        // Generate realistic financial values for demonstration
        const totalBudget = Math.floor(Math.random() * 1000000) + 500000;
        const totalDisbursed = Math.floor(Math.random() * totalBudget);
        const remainingBalance = totalBudget - totalDisbursed;
        const utilizationPercentage = Math.round((totalDisbursed / totalBudget) * 100);

        return {
          ...orgData,
          organization_legal_type_name: orgData.organization_legal_type === 1 ? 'TRUST' :
                                       orgData.organization_legal_type === 2 ? 'NON_PROFIT' : 'SOCIETY',
          organization_function_type_name: orgData.organization_function_type === 1 ? 'GRANTEE_ORGANIZATION' : 'GRANT_MAKER_ORGANIZATION',
          totalBudget,
          totalDisbursed,
          remainingBalance,
          utilizationPercentage,
          email: orgData.email || `contact@${orgData.organization_name.toLowerCase().replace(/\s+/g, '')}.org`,
          contact_person: orgData.contact_person || 'Contact Person',
          phone: orgData.phone || '+91 9876543210',
          address: orgData.address || 'Organization Address'
        };
      }
    } catch (apiError) {
      console.warn(`API call for organization ${id} failed, using mock data:`, apiError);
    }

    // Fallback to mock data if API call fails
    const mockOrgs = await getAllOrganizations();
    return mockOrgs.find(org => org.id.toString() === id);
  } catch (error) {
    console.error(`Error fetching organization with ID ${id}:`, error);
    throw error;
  }
};

// Helper function to format currency
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    maximumFractionDigits: 0,
  }).format(amount);
};

export const transformOrganizationListAPIResponse = (organizations: OrganizationAPIResponse[]): Organization[] => {
  return organizations.map((org): Organization => {
    return {
      id: org.id,
      logoKey: org.logo_key,
      pointOfContactName: org.point_of_contact_name,
      organizationFunctionType: org.organization_function_type,
      organizationLegalType: org.organization_legal_type,
      organizationFunctionTypeName: org.organization_function_type_name,
      organizationLegalTypeName: org.organization_legal_type_name,
      organizationName: org.organization_name,
      panNumber: org.pan_number,
      phoneNumber: org.phone_number,
      emailAddress: org.email_address,
      websiteUrl: org.website_url,
      numberOfTeamMembers: org.number_of_team_members,
      mission: org.mission,
      vision: org.vision,
      backgroundHistory: org.background_history,
      csrRegistrationNumber: org.csr_registration_number,
      taxRegistrationNumber: org.tax_registration_number,
      taxRegistrationNumberUnder12A: org.tax_registration_number_under_12_a,
      fcraRegistrationNumber: org.fcra_registration_number,
      trustRegistrationNumber: org.trust_registration_number,
      darpanId: org.darpan_id,
      createdAt: org.created_at || "",
      updatedAt: org.updated_at || "",
      previousGrants: (org.previous_grants || []).map(grant => ({
        id: grant.id,
        organization: grant.organization,
        grantName: grant.grant_name,
        grantPurpose: grant.grant_purpose,
        startDate: grant.start_date,
        endDate: grant.end_date,
        amount: parseFloat(grant.budget),
        status: grant.status,
        statusDisplay: grant.status_display,
        createdAt: grant.created_at, 
        updatedAt: grant.updated_at 
      }))
    };
  });
}