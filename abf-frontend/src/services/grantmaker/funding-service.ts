import apiClient from "@/lib/apiClient";
import { FundingOverview } from "./grantmaker-service";

// Define interfaces for API responses
export interface FundingOverviewResponse {
  status: string;
  data: {
    total_budget: number;
    total_disbursed: number;
    pending_disbursements: number;
    quarterly_breakdown: Array<{
      quarter: string;
      budgeted: number;
      disbursed: number;
    }>;
    organization_breakdown: Array<{
      organization_id: string;
      organization_name: string;
      total_budget: number;
      total_disbursed: number;
    }>;
  };
}

export interface DisbursementRequest {
  organization_id: string;
  amount: number;
  description: string;
  disbursement_date: string;
  quarter: string;
  year: string;
}

export interface DisbursementResponse {
  status: string;
  data: {
    id: string;
    organization_id: string;
    organization_name: string;
    amount: number;
    description: string;
    disbursement_date: string;
    quarter: string;
    year: string;
    created_at: string;
  };
}

export interface DisbursementListResponse {
  status: string;
  data: Array<{
    id: string;
    organization_id: string;
    organization_name: string;
    amount: number;
    description: string;
    disbursement_date: string;
    quarter: string;
    year: string;
    created_at: string;
  }>;
}

// Get funding overview data
export const getFundingOverview = async (year?: string, quarter?: string): Promise<FundingOverview> => {
  try {
    // Build query parameters
    const params: Record<string, string> = {};
    if (year) params.year = year;
    if (quarter && quarter !== 'All') params.quarter = quarter;

    // Make API request
    const response = await apiClient.get<FundingOverviewResponse>('/api/funding/v1/overview', { params });
    
    // Transform API response to match our interface
    return {
      totalBudget: response.data.data.total_budget,
      totalDisbursed: response.data.data.total_disbursed,
      pendingDisbursements: response.data.data.pending_disbursements,
      quarterlyBreakdown: response.data.data.quarterly_breakdown.map(item => ({
        quarter: item.quarter,
        budgeted: item.budgeted,
        disbursed: item.disbursed
      })),
      organizationBreakdown: response.data.data.organization_breakdown.map(item => ({
        organizationId: item.organization_id,
        organizationName: item.organization_name,
        totalBudget: item.total_budget,
        totalDisbursed: item.total_disbursed
      }))
    };
  } catch (error) {
    console.error('Error fetching funding overview:', error);
    throw error;
  }
};

// Create a new disbursement
export const createDisbursement = async (disbursementData: DisbursementRequest): Promise<DisbursementResponse['data']> => {
  try {
    const response = await apiClient.post<DisbursementResponse>('/api/funding/v1/disbursements', disbursementData);
    return response.data.data;
  } catch (error) {
    console.error('Error creating disbursement:', error);
    throw error;
  }
};

// Get list of disbursements
export const getDisbursements = async (organizationId?: string, year?: string, quarter?: string): Promise<DisbursementListResponse['data']> => {
  try {
    // Build query parameters
    const params: Record<string, string> = {};
    if (organizationId) params.organization_id = organizationId;
    if (year) params.year = year;
    if (quarter && quarter !== 'All') params.quarter = quarter;

    const response = await apiClient.get<DisbursementListResponse>('/api/funding/v1/disbursements', { params });
    return response.data.data;
  } catch (error) {
    console.error('Error fetching disbursements:', error);
    throw error;
  }
};

// Get a single disbursement by ID
export const getDisbursementById = async (disbursementId: string): Promise<DisbursementResponse['data']> => {
  try {
    const response = await apiClient.get<DisbursementResponse>(`/api/funding/v1/disbursements/${disbursementId}`);
    return response.data.data;
  } catch (error) {
    console.error(`Error fetching disbursement with ID ${disbursementId}:`, error);
    throw error;
  }
};

// Update a disbursement
export const updateDisbursement = async (disbursementId: string, disbursementData: Partial<DisbursementRequest>): Promise<DisbursementResponse['data']> => {
  try {
    const response = await apiClient.put<DisbursementResponse>(`/api/funding/v1/disbursements/${disbursementId}`, disbursementData);
    return response.data.data;
  } catch (error) {
    console.error(`Error updating disbursement with ID ${disbursementId}:`, error);
    throw error;
  }
};

// Delete a disbursement
export const deleteDisbursement = async (disbursementId: string): Promise<void> => {
  try {
    await apiClient.delete(`/api/funding/v1/disbursements/${disbursementId}`);
  } catch (error) {
    console.error(`Error deleting disbursement with ID ${disbursementId}:`, error);
    throw error;
  }
};