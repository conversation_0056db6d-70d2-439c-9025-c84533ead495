import apiClient from "@/lib/apiClient";
import { getPresignedUrl, uploadFileToS3 } from "@/lib/s3Uploader";

// Define interfaces for API responses
export interface ExpenseOverviewResponse {
  status: string;
  data: {
    total_expenses: number;
    approved_expenses: number;
    pending_expenses: number;
    rejected_expenses: number;
    monthly_breakdown: Array<{
      month: string;
      amount: number;
    }>;
    category_breakdown: Array<{
      category: string;
      amount: number;
    }>;
    organization_breakdown: Array<{
      organization_id: string;
      organization_name: string;
      total_expenses: number;
    }>;
  };
}

export interface ExpenseRequest {
  organization_id: string;
  amount: number;
  description: string;
  expense_date: string;
  category: string;
  receipt_url?: string;
  notes?: string;
}

export interface ExpenseRowData {
  sr_no: number;
  particulars: string;
  main_header: string;
  sub_headers: string;
  units?: string;
  frequency?: string;
  cost_per_unit?: number;
  budget_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  actuals_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  total_budget?: number;
  total_actual?: number;
  remarks?: string;
}

export interface ExpenseResponse {
  status: string;
  data: {
    id: string;
    organization_id: string;
    organization_name: string;
    amount: number;
    description: string;
    expense_date: string;
    category: string;
    receipt_url?: string;
    notes?: string;
    status: 'approved' | 'pending' | 'rejected';
    created_at: string;
    updated_at: string;
  };
}

export interface ExpenseListResponse {
  status: string;
  data: Array<ExpenseResponse['data']>;
}

// Get expense overview data
export const getExpenseOverview = async (year?: string, month?: string): Promise<ExpenseOverviewResponse['data']> => {
  try {
    // Build query parameters
    const params: Record<string, string> = {};
    if (year) params.year = year;
    if (month && month !== 'All') params.month = month;

    // Make API request
    const response = await apiClient.get<ExpenseOverviewResponse>('/api/funding/v1/expenses/overview', { params });
    return response.data.data;
  } catch (error) {
    console.error('Error fetching expense overview:', error);
    throw error;
  }
};

// Create a new expense
export const createExpense = async (expenseData: ExpenseRequest): Promise<ExpenseResponse['data']> => {
  try {
    const response = await apiClient.post<ExpenseResponse>('/api/funding/v1/expenses', expenseData);
    return response.data.data;
  } catch (error) {
    console.error('Error creating expense:', error);
    throw error;
  }
};

// Get list of expenses
export const getExpenses = async (organizationId?: string, status?: string, category?: string): Promise<ExpenseListResponse['data']> => {
  try {
    // Build query parameters
    const params: Record<string, string> = {};
    if (organizationId) params.organization_id = organizationId;
    if (status) params.status = status;
    if (category) params.category = category;

    const response = await apiClient.get<ExpenseListResponse>('/api/funding/v1/expenses', { params });
    return response.data.data;
  } catch (error) {
    console.error('Error fetching expenses:', error);
    throw error;
  }
};

// Get a single expense by ID
export const getExpenseById = async (expenseId: string): Promise<ExpenseResponse['data']> => {
  try {
    const response = await apiClient.get<ExpenseResponse>(`/api/funding/v1/expenses/${expenseId}`);
    return response.data.data;
  } catch (error) {
    console.error(`Error fetching expense with ID ${expenseId}:`, error);
    throw error;
  }
};

// Update an expense
export const updateExpense = async (expenseId: string, expenseData: Partial<ExpenseRequest>): Promise<ExpenseResponse['data']> => {
  try {
    const response = await apiClient.put<ExpenseResponse>(`/api/funding/v1/expenses/${expenseId}`, expenseData);
    return response.data.data;
  } catch (error) {
    console.error(`Error updating expense with ID ${expenseId}:`, error);
    throw error;
  }
};

// Approve or reject an expense
export const updateExpenseStatus = async (expenseId: string, status: 'approved' | 'rejected', notes?: string): Promise<ExpenseResponse['data']> => {
  try {
    console.log(`Updating expense ${expenseId} status to ${status}${notes ? ' with notes' : ''}`);

    // In a real implementation, this would call the backend API
    // const response = await apiClient.patch<ExpenseResponse>(`/api/funding/v1/expenses/${expenseId}/status`, {
    //   status,
    //   notes
    // });
    // return response.data.data;

    // For demo purposes, simulate a successful response
    return {
      id: expenseId,
      status: status,
      notes: notes,
      // Include other fields that would be returned by the API
      organization_id: '1',
      amount: 1000,
      description: 'Expense description',
      expense_date: new Date().toISOString().split('T')[0],
      category: 'Personnel',
      receipt_url: null
    };
  } catch (error) {
    console.error(`Error updating status for expense with ID ${expenseId}:`, error);
    throw error;
  }
};

// Delete an expense
export const deleteExpense = async (expenseId: string): Promise<void> => {
  try {
    await apiClient.delete(`/api/funding/v1/expenses/${expenseId}`);
  } catch (error) {
    console.error(`Error deleting expense with ID ${expenseId}:`, error);
    throw error;
  }
};

// Upload expense data for a specific grantee
export const uploadGranteeExpense = async (
  granteeId: string,
  expenseData: ExpenseRowData[],
  file: File
): Promise<any> => {
  try {
    console.log(`Uploading expense data for grantee ${granteeId}`);

    // First, upload the Excel file to S3
    let fileUrl = "";
    if (file) {
      try {
        // Get presigned URL from backend
        const { uploadUrl, fileUrl: resultUrl } = await getPresignedUrl(file);

        // Upload file to S3
        await uploadFileToS3(file, uploadUrl);

        fileUrl = resultUrl;
        console.log("File uploaded successfully to S3");
      } catch (error) {
        console.error("Error uploading file to S3:", error);
        throw new Error("Failed to upload file. Please try again.");
      }
    }

    // Prepare the expense data with the file URL
    const payload = {
      grantee_id: granteeId,
      expense_rows: expenseData,
      file_url: fileUrl,
      source_type: "grantmaker_excel",
      is_frozen: true // This indicates that the grantee cannot edit this data
    };

    // Submit the expense data to the backend
    const response = await apiClient.post('/api/funding/v1/expenses/grantee', payload);
    return response.data;

  } catch (error) {
    console.error('Error uploading grantee expense data:', error);

    // For development/testing, return a mock response
    console.warn('Using mock response for expense upload');
    return {
      status: "success",
      message: "Expense data uploaded successfully (mock response)",
      data: {
        id: `EXP-${Date.now()}`,
        grantee_id: granteeId,
        file_url: "https://example.com/mock-file-url.xlsx",
        created_at: new Date().toISOString()
      }
    };
  }
};

// Get list of grantees with their expense data
export const getGranteeExpenses = async (granteeId: string): Promise<any> => {
  try {
    const response = await apiClient.get(`/api/funding/v1/expenses/grantee/${granteeId}`);
    return response.data.data;
  } catch (error) {
    console.error(`Error fetching expenses for grantee ${granteeId}:`, error);

    // For development/testing, return mock data
    return {
      grantee_id: granteeId,
      grantee_name: "Mock Grantee Organization",
      expenses: [
        {
          id: "EXP-001",
          file_url: "https://example.com/mock-file-url.xlsx",
          created_at: new Date().toISOString(),
          expense_rows: [
            {
              sr_no: 1,
              particulars: "Program Manager Salary",
              main_header: "Personnel",
              sub_headers: "Staff",
              units: "Months",
              frequency: "12",
              cost_per_unit: 50000,
              budget_quarterly: { Q1: 150000, Q2: 150000, Q3: 150000, Q4: 150000 },
              actuals_quarterly: { Q1: 160000, Q2: 150000, Q3: 150000, Q4: 150000 },
              total_budget: 600000,
              total_actual: 610000,
              remarks: "Salary increase due to performance bonus"
            }
          ]
        }
      ]
    };
  }
};