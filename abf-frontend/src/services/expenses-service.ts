import apiClient from '@/lib/apiClient';
import { GrantAPIResponse } from '@/types/profile';
import { json } from 'stream/consumers';

// Fetch all expenses
export const fetchAllExpenses = async () => {
  try {
    const response = await apiClient.get('/api/funding/v1/expenses/list/');
    console.log('fetchAllExpenses success:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('fetchAllExpenses error:', error);

    // If it's a 400 error, it might be authentication or no data
    if (error.response?.status === 400) {
      console.warn('No expenses found or authentication issue, returning empty array');
      return []; // Return empty array instead of throwing
    }

    // For other errors, still return empty array to prevent app crash
    console.warn('API error, returning empty array as fallback');
    return [];
  }
};

export const fetchExpensesByGrant = async (grantId: string) => {
  console.log('Fetching expenses for grant ID:', grantId);
  const response = await apiClient.get(`/api/funding/v1/grants/${grantId}/expenses`)
  console.log("Inner response = ", JSON.stringify(response));
  return response.data;

}

// Fetch a single expense detail
export const fetchExpenseDetail = async (id: string) => {
  try {
    const response = await apiClient.get(`/api/funding/v1/expenses/${id}/`);
    return response.data;
  } catch (error) {
    console.error('fetchExpenseDetail error:', error);
    throw error;
  }
};
// Fetch all grants
export const getGrants = async (): Promise<GrantAPIResponse[]> => {
    const { data } = await apiClient.get("/api/v1/me/grants");
    return data;
};

export const getGrantss = async () => {
  try {
    const { data } = await apiClient.get("/api/funding/v1/expenses/grants/");
    return data;
  } catch (error: any) {
    console.error("getGrants error:", error);
    throw error;
  }
};

// Fetch quarterly totals and budget overview data
export const fetchQuarterlyTotals = async (grantFilter?: string, quarterFilter?: string, yearFilter?: string) => {
  try {
    const params: Record<string, string> = {};
    if (grantFilter && grantFilter !== 'all' && grantFilter !== 'All') {
      params.grant = grantFilter;
    }
    if (quarterFilter && quarterFilter !== 'All') {
      // Pass the quarter filter as-is, backend will handle normalization
      params.quarter = quarterFilter;
    }
    if (yearFilter) {
      params.year = yearFilter;
    }

    console.log('Fetching quarterly totals with params:', params);
    const response = await apiClient.get('/api/funding/v1/expenses/quarterly-totals/', { params });
    console.log('Quarterly totals response:', response.data);
    return response.data;
  } catch (error) {
    console.error('fetchQuarterlyTotals error:', error);
    throw error;
  }
};

// Create a new expense
export const createExpense = async (expenseData: any) => {
  try {
    const response = await apiClient.post('/api/funding/v1/expenses/create/', expenseData);
    return response.data;
  } catch (error) {
    console.error('createExpense error:', error);
    throw error;
  }
};


// Bulk update expenses
export const bulkUpdateExpenses = async (expenseUpdates: any[]) => {
  try {
    const response = await apiClient.put('/api/funding/v1/expenses/bulk_update/', expenseUpdates);
    return response.data;
  } catch (error) {
    console.error('bulkUpdateExpenses error:', error);
    throw error;
  }
};