import apiClient from "@/lib/apiClient";

export interface TicketFormData {
  category: string;
  priority: string;
  title: string;
  description: string;
  email: string;
  phone: string;
  status?: string;
}

// Submit a new support ticket
export const submitSupportTicket = async (
  formData: TicketFormData,
  file: File | null
) => {
  try {
    const body = new FormData();
    
    Object.entries(formData).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        body.append(key, String(value));
      }
    });

    if (file) {
      body.append("file", file);
    }

    const response = await apiClient.post("/api/support/v1/tickets/create/", body, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    return response.data;
  } catch (error: any) {
    console.log("submitSupportTicket error:", error);
    throw error;
  }
};


// Get full ticket details (with attachments fallback)
export const getTicketDetails = async (ticketId: string) => {
  try {
    const { data } = await apiClient.get(`/api/support/v1/tickets/${ticketId}/`);
    return {
      ...data,
      get_attachments: data.get_attachments ?? [],
    };
  } catch (error: any) {
    console.log(`getTicketDetails error for ID ${ticketId}:`, error);
    throw error;
  }
};

// Get ticket by ID (raw version)
export const getTicketById = async (ticketId: string) => {
  try {
    const response = await apiClient.get(`/api/support/v1/tickets/${ticketId}/`);
    return response.data;
  } catch (error: any) {
    console.log(`getTicketById error for ID ${ticketId}:`, error);
    throw error;
  }
};


// Fetch updates for a specific ticket
export const getTicketUpdates = async (ticketId: number) => {
  try {
    const response = await apiClient.get(`/api/support/v1/ticket-updates/`, {
      params: { ticket: ticketId },
    });
    return response.data;
  } catch (error: any) {
    console.log(`getTicketUpdates error for Ticket ID ${ticketId}:`, error);
    throw error;
  }
};

// Send a message/update on a ticket
export const sendTicketUpdate = async (
  ticketId: number,
  updateText: string,
  file: File | null
) => {
  try {
    const formData = new FormData();
    formData.append("ticket", String(ticketId));
    if (updateText?.trim()) formData.append("update_text", updateText.trim());
    if (file) formData.append("file", file);
    const response = await apiClient.post(`/api/support/v1/ticket-updates/`, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });

    return response.data;
  } catch (error: any) {
    console.log(`sendTicketUpdate error for Ticket ID ${ticketId}:`, error);
    throw error;
  }
};

// Reopen a closed ticket
export const reopenTicket = async (ticketId: number) => {
  try {
    const response = await apiClient.patch(`/api/support/v1/tickets/${ticketId}/status/`, {
      status: "open",
    });
    return response.data;
  } catch (error: any) {
    console.log(`reopenTicket error for Ticket ID ${ticketId}:`, error);
    throw error;
  }
};

// Update ticket status to any value
export const updateTicketStatus = async (ticketId: number, status: string) => {
  try {
    const response = await apiClient.patch(`/api/support/v1/tickets/${ticketId}/status/`, {
      status,
    });
    return response.data;
  } catch (error: any) {
    console.log(`updateTicketStatus error for Ticket ID ${ticketId}:`, error);
    throw error;
  }
};

// Fetch all tickets (no filter)
export const fetchAllTickets = async () => {
  try {
    const { data } = await apiClient.get("/api/support/v1/tickets/");
    return data;
  } catch (error: any) {
    console.log("fetchAllTickets error:", error);
    throw error;
  }
};


// Fetch all grants
export const getGrants = async () => {
  try {
    const { data } = await apiClient.get("/api/support/v1/grants-list/");
    return data;
  } catch (error: any) {
    console.error("getGrants error:", error);
    throw error;
  }
};