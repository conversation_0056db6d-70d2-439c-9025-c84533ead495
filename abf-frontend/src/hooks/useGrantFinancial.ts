import { useState, useEffect } from 'react';
import { fundingService, GrantFinancialSummary } from '@/services/funding-service';

export interface GrantOption {
  id: string;
  name: string;
}

export interface GrantFinancialData {
  allocated_amount: number;
  disbursed_amount: number;
  utilized_amount: number;
  remaining_balance: number;
  quarterly_data: Array<{
    quarter: string;
    budget: number;
    actual: number;
  }>;
}

export function useGrantFinancial() {
  const [grants, setGrants] = useState<GrantOption[]>([]);
  const [grantFinancialData, setGrantFinancialData] = useState<Record<string, GrantFinancialData>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchGrantData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const grantSummaries = await fundingService.getGrantFinancialSummaries();

      console.log('Grant summaries received in hook:', grantSummaries);

      if (grantSummaries.length === 0) {
        // No grants found - set empty state
        setGrants([{ id: 'all', name: 'No Grants Available' }]);
        setGrantFinancialData({
          'all': {
            allocated_amount: 0,
            disbursed_amount: 0,
            utilized_amount: 0,
            remaining_balance: 0,
            quarterly_data: [
              { quarter: 'Q1', budget: 0, actual: 0 },
              { quarter: 'Q2', budget: 0, actual: 0 },
              { quarter: 'Q3', budget: 0, actual: 0 },
              { quarter: 'Q4', budget: 0, actual: 0 }
            ]
          }
        });
        return;
      }

      // Create grant options for dropdown
      const grantOptions: GrantOption[] = [
        { id: 'all', name: 'All Grants' },
        ...grantSummaries.map(grant => ({
          id: grant.id.toString(),
          name: grant.grant_name
        }))
      ];

      // Create financial data mapping
      const financialDataMap: Record<string, GrantFinancialData> = {};

      // Helper function to safely convert to number
      const safeNumber = (value: any): number => {
        const num = Number(value);
        return isNaN(num) ? 0 : num;
      };

      // Calculate aggregated data for "All Grants"
      const allGrantsData: GrantFinancialData = {
        allocated_amount: grantSummaries.reduce((sum, grant) => sum + safeNumber(grant.allocated_amount), 0),
        disbursed_amount: grantSummaries.reduce((sum, grant) => sum + safeNumber(grant.disbursed_amount), 0),
        utilized_amount: grantSummaries.reduce((sum, grant) => sum + safeNumber(grant.utilized_amount), 0),
        remaining_balance: grantSummaries.reduce((sum, grant) => sum + safeNumber(grant.remaining_balance), 0),
        quarterly_data: [
          {
            quarter: 'Q1',
            budget: grantSummaries.reduce((sum, grant) => sum + safeNumber(grant.quarterly_data.find(q => q.quarter === 'Q1')?.budget), 0),
            actual: grantSummaries.reduce((sum, grant) => sum + safeNumber(grant.quarterly_data.find(q => q.quarter === 'Q1')?.actual), 0)
          },
          {
            quarter: 'Q2',
            budget: grantSummaries.reduce((sum, grant) => sum + safeNumber(grant.quarterly_data.find(q => q.quarter === 'Q2')?.budget), 0),
            actual: grantSummaries.reduce((sum, grant) => sum + safeNumber(grant.quarterly_data.find(q => q.quarter === 'Q2')?.actual), 0)
          },
          {
            quarter: 'Q3',
            budget: grantSummaries.reduce((sum, grant) => sum + safeNumber(grant.quarterly_data.find(q => q.quarter === 'Q3')?.budget), 0),
            actual: grantSummaries.reduce((sum, grant) => sum + safeNumber(grant.quarterly_data.find(q => q.quarter === 'Q3')?.actual), 0)
          },
          {
            quarter: 'Q4',
            budget: grantSummaries.reduce((sum, grant) => sum + safeNumber(grant.quarterly_data.find(q => q.quarter === 'Q4')?.budget), 0),
            actual: grantSummaries.reduce((sum, grant) => sum + safeNumber(grant.quarterly_data.find(q => q.quarter === 'Q4')?.actual), 0)
          }
        ]
      };

      financialDataMap['all'] = allGrantsData;

      // Add individual grant data
      grantSummaries.forEach(grant => {
        financialDataMap[grant.id.toString()] = {
          allocated_amount: safeNumber(grant.allocated_amount),
          disbursed_amount: safeNumber(grant.disbursed_amount),
          utilized_amount: safeNumber(grant.utilized_amount),
          remaining_balance: safeNumber(grant.remaining_balance),
          quarterly_data: grant.quarterly_data.map(q => ({
            quarter: q.quarter,
            budget: safeNumber(q.budget),
            actual: safeNumber(q.actual)
          }))
        };
      });

      console.log('Grant options:', grantOptions);
      console.log('Financial data map:', financialDataMap);

      setGrants(grantOptions);
      setGrantFinancialData(financialDataMap);

    } catch (err) {
      console.error('Error fetching grant financial data:', err);
      setError('Failed to fetch grant data');
      // Set fallback empty state
      setGrants([{ id: 'all', name: 'Error Loading Grants' }]);
      setGrantFinancialData({
        'all': {
          allocated_amount: 0,
          disbursed_amount: 0,
          utilized_amount: 0,
          remaining_balance: 0,
          quarterly_data: [
            { quarter: 'Q1', budget: 0, actual: 0 },
            { quarter: 'Q2', budget: 0, actual: 0 },
            { quarter: 'Q3', budget: 0, actual: 0 },
            { quarter: 'Q4', budget: 0, actual: 0 }
          ]
        }
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchGrantData();
  }, []);

  const getGrantFinancialData = (grantId: string): GrantFinancialData | null => {
    return grantFinancialData[grantId] || null;
  };

  const getFilteredGrantData = (grantId: string, year: string, quarter: string): GrantFinancialData | null => {
    const baseData = grantFinancialData[grantId];
    if (!baseData) return null;

    console.log(`🔍 Filtering grant ${grantId} data for year: ${year}, quarter: ${quarter}`);

    // Helper function to safely convert to number
    const safeNumber = (value: any): number => {
      const num = Number(value);
      return isNaN(num) || !isFinite(num) ? 0 : num;
    };

    // If showing all data, return as-is
    if (year === 'All' && quarter === 'All') {
      console.log('📊 Returning all data (no filters)');
      return baseData;
    }

    // Start with base data
    let filteredData = { ...baseData };

    // Filter quarterly data based on selected quarter
    let filteredQuarterlyData = baseData.quarterly_data;

    if (quarter !== 'All') {
      filteredQuarterlyData = baseData.quarterly_data.filter(q => q.quarter === quarter);
      console.log(`📅 Filtered to quarter ${quarter}:`, filteredQuarterlyData);

      // When filtering by quarter, adjust the financial amounts to reflect only that quarter
      const quarterData = baseData.quarterly_data.find(q => q.quarter === quarter);
      if (quarterData) {
        // Estimate quarterly amounts (this would be more precise with real date-based filtering)
        const quarterMultiplier = 0.25; // Assuming equal distribution across quarters
        filteredData = {
          ...baseData,
          allocated_amount: safeNumber(baseData.allocated_amount * quarterMultiplier),
          disbursed_amount: safeNumber(baseData.disbursed_amount * quarterMultiplier),
          utilized_amount: safeNumber(quarterData.actual),
          remaining_balance: safeNumber(baseData.allocated_amount * quarterMultiplier - quarterData.actual),
        };
      }
    }

    // For year filtering, in a real implementation this would filter expense records by expense_date
    // For now, we'll show the data as-is since we're using real backend data
    if (year !== 'All') {
      console.log(`📆 Year filter applied: ${year} (using real backend data)`);
      // In real implementation, this would re-fetch data filtered by year
      // For now, we keep the real data as-is since it comes from the backend
    }

    const result = {
      ...filteredData,
      quarterly_data: filteredQuarterlyData
    };

    console.log('✅ Filtered result:', result);
    return result;
  };

  const refreshData = () => {
    fetchGrantData();
  };

  return {
    grants,
    grantFinancialData,
    isLoading,
    error,
    getGrantFinancialData,
    getFilteredGrantData,
    refreshData
  };
}
