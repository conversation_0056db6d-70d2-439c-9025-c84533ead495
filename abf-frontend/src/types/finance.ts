export interface FundingEntity {
    id: number,
    organizationName: string,
    panNumber: string,
    organizationFunctionType: number
}

export interface FundingEntityUpdateRequest {
    organization_name: string,
    pan_number: string,
}

export interface FundingEntityAPIResponse extends FundingEntityUpdateRequest{
    id: number,
    organization_function_type: number 
}

export interface FundingRecord {
    id: number,
    fundingEntity: string,
    fundingEntityName: string,
    amount: number,
    dateReceived: string,
    notes: string
}

export interface FundingRecordCreateRequest {
    funding_entity: string,
    amount: number,
    date_received: string,
    notes: string
}

export interface FundingRecordUpdateRequest extends FundingRecordCreateRequest {}

export interface FundingRecordAPIResponse extends FundingRecordCreateRequest {
    id: number
    funding_entity_name: string,
}

export type FundingRecordListAPIResponse = {
    status: "SUCCESS" | "ERROR",
    data: FundingRecordAPIResponse[],
    message?: string
}

export type AddFundingRecordAPIResponse = {
    status: "SUCCESS" | "ERROR",
    data: FundingRecordAPIResponse,
    message?: string
}

export type EditFundingRecordAPIResponse = {
    status: "SUCCESS" | "ERROR",
    data: FundingRecordAPIResponse,
    message?: string
}

export type FundingEntityListAPIResponse = {
    status: "SUCCESS" | "ERROR",
    data: FundingEntityAPIResponse[],
    message?: string

}

export type AddFundingEntityAPIResponse = {
    status: "SUCCESS" | "ERROR",
    data: FundingEntityAPIResponse,
    message?: string
};


export type EditFundingEntityAPIResponse = {
    status: "SUCCESS" | "ERROR",
    data: FundingEntityAPIResponse,
    message?: string
};

export interface FundingAllocation {
    id: number;
    fundingEntity: number;
    fundingEntityName: string;
    grant: number;
    grantName: string;
    grantRecipientOrganization: string;
    grantRecipientOrganizationName: string;
    amountAllocated: number | string;
    notes: string;
}

export interface FundingAllocationCreateRequest {
    grant: number;
    funding_entity: number;
}

export interface FundingAllocationUpdateRequest extends FundingAllocationCreateRequest {}

export interface FundingAllocationAPIResponse {
    id: number;
    funding_entity: number;
    funding_entity_name: string;
    grant: number;
    grant_name: string;
    grant_recipient_organization: string;
    grant_recipient_organization_name: string;
    amount_allocated: string;
    notes: string;
}

export type FundingAllocationListAPIResponse = {
    status: "SUCCESS" | "ERROR";
    data: FundingAllocationAPIResponse[];
    message?: string;
};

export type AddFundingAllocationAPIResponse = {
    status: "SUCCESS" | "ERROR";
    data: FundingAllocationAPIResponse;
    message?: string;
};

export type EditFundingAllocationAPIResponse = {
    status: "SUCCESS" | "ERROR";
    data: FundingAllocationAPIResponse;
    message?: string;
};
