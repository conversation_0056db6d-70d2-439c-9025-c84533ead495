// Utility functions for quarter handling and testing

export const normalizeQuarter = (quarter: string): string => {
  const quarterMap: Record<string, string> = {
    'Q1': 'Apr-Jun',
    'Q2': 'Jul-Sep', 
    'Q3': 'Oct-Dec',
    'Q4': 'Jan-Mar'
  };
  return quarterMap[quarter] || quarter;
};

export const getQuarterDateRange = (quarter: string, year: string) => {
  // For financial year, the year represents the calendar year when the financial year starts
  // e.g., 2025 means FY 2025-26 (Apr 2025 to Mar 2026)
  const currentYear = parseInt(year);
  const nextYear = currentYear + 1;

  if (quarter === "Q1" || quarter === "Apr-Jun") return `Apr 1 - Jun 30, ${currentYear}`;
  if (quarter === "Q2" || quarter === "Jul-Sep") return `Jul 1 - Sep 30, ${currentYear}`;
  if (quarter === "Q3" || quarter === "Oct-Dec") return `Oct 1 - Dec 31, ${currentYear}`;
  if (quarter === "Q4" || quarter === "Jan-Mar") return `Jan 1 - Mar 31, ${nextYear}`;
  return `FY ${currentYear}-${nextYear.toString().slice(-2)}`; // Format as FY 2025-26
};

// Test function to verify quarter normalization
export const testQuarterNormalization = () => {
  console.log('=== Quarter Normalization Test ===');
  
  const testCases = [
    { input: 'Q1', expected: 'Apr-Jun' },
    { input: 'Q2', expected: 'Jul-Sep' },
    { input: 'Q3', expected: 'Oct-Dec' },
    { input: 'Q4', expected: 'Jan-Mar' },
    { input: 'Apr-Jun', expected: 'Apr-Jun' },
    { input: 'All', expected: 'All' }
  ];

  testCases.forEach(({ input, expected }) => {
    const result = normalizeQuarter(input);
    const status = result === expected ? '✅' : '❌';
    console.log(`${status} ${input} → ${result} (expected: ${expected})`);
  });
};

// Test function to verify date range display
export const testDateRangeDisplay = () => {
  console.log('\n=== Date Range Display Test ===');
  
  const testCases = [
    { quarter: 'Q1', year: '2025', expected: 'Apr 1 - Jun 30, 2025' },
    { quarter: 'Q2', year: '2025', expected: 'Jul 1 - Sep 30, 2025' },
    { quarter: 'Q3', year: '2025', expected: 'Oct 1 - Dec 31, 2025' },
    { quarter: 'Q4', year: '2025', expected: 'Jan 1 - Mar 31, 2026' },
    { quarter: 'Apr-Jun', year: '2025', expected: 'Apr 1 - Jun 30, 2025' }
  ];

  testCases.forEach(({ quarter, year, expected }) => {
    const result = getQuarterDateRange(quarter, year);
    const status = result === expected ? '✅' : '❌';
    console.log(`${status} ${quarter} ${year} → ${result} (expected: ${expected})`);
  });
};

// Run all tests
export const runAllTests = () => {
  testQuarterNormalization();
  testDateRangeDisplay();
  console.log('\n=== Test Summary ===');
  console.log('Quarter filtering and year display fixes have been applied.');
  console.log('Budget overview should now work correctly with quarter filters.');
};
