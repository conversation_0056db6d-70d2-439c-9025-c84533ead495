"use client";

import { useAuthContext } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import React, { useEffect } from "react";

type Props = {
    children: React.ReactNode;
    allowedTypes?: string[] // Like ['GRANTEE]
    fallback: React.ReactNode
};


export const ProtectedRoute = ({ children, allowedTypes, fallback = null }: Props) => {
    const { user, isLoading } = useAuthContext();
    const router = useRouter();
  
    useEffect(() => {
      if (!isLoading) {
        if (!user) {
          router.replace("/login");
          return;
        }
        if (allowedTypes && !allowedTypes.includes(user.type)) {
          router.replace("/unauthorized");
        }
      }
    }, [user, isLoading]);
  
    // Show nothing while loading or before user is available
    if (isLoading || !user) return null;
  
    // Prevent render if user is unauthorized (just in case)
    if (allowedTypes && !allowedTypes.includes(user.type)) {
      return fallback;
    }
  
    return <>{children}</>;
  };
