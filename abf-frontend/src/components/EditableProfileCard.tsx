"use client";

import { ReactNode, useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { LoaderCircle } from "lucide-react";

interface EditableProfileCardProps {
  title: string;
  children: ReactNode;
  editForm?: ReactNode;
  isEditing?: boolean;
  onEdit?: () => void;
  onCancel?: () => void;
  onSave?: () => void;
  isSaving?: boolean;
}

export function EditableProfileCard({
  title,
  children,
  editForm,
  isEditing = false,
  onEdit,
  onCancel,
  onSave,
  isSaving = false,
}: EditableProfileCardProps) {
  return (
    <div className="mb-8">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-800">{title}</h2>
      </div>

      {isEditing && editForm ? (
        <Card className="border-teal-200 shadow-sm rounded-md">
          <CardContent className="p-6">
            {editForm}
            <div className="flex justify-end space-x-4 mt-6">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button onClick={onSave} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Changes'
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card className="border-gray-200 shadow-sm rounded-md">
          <CardContent className="p-6">{children}</CardContent>
        </Card>
      )}
    </div>
  );
}