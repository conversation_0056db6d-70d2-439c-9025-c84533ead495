"use client";

import { useEffect, useState, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Layout } from "@/components/Layout";
import { ArrowLeft, Paperclip, Send, FileDown } from "lucide-react";
import SkeletonMessage from "@/components/SkeletonMessage";
import {
  getTicketById,
  getTicketUpdates,
  sendTicketUpdate,
  reopenTicket,
} from "@/services/supportTicket.service";
import {
  Tag,
  Folder,
  Flag,
  CalendarClock,
  Clock,
  ShieldCheck,
} from "lucide-react";
import { XCircle } from "lucide-react";


function Popup({ message, onClose }) {
  useEffect(() => {
    const timeout = setTimeout(onClose, 4000);
    return () => clearTimeout(timeout);
  }, [onClose]);

  return (
    <div className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50">
      <div className="flex items-center gap-3 bg-red-100 border border-red-300 text-red-800 px-5 py-3 rounded-xl shadow-lg animate-fade-in-down">
        <XCircle className="w-5 h-5 shrink-0" />
        <span className="text-sm font-medium">{message}</span>
      </div>
    </div>
  );
}

export default function TicketHistoryPage({ ticketId }: {ticketId: string | null}) {
  const router = useRouter();

  const [ticket, setTicket] = useState(null);
  const [updates, setUpdates] = useState([]);
  const [loadingTicket, setLoadingTicket] = useState(true);
  const [loadingUpdates, setLoadingUpdates] = useState(true);
  const [messageText, setMessageText] = useState("");
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [formError, setFormError] = useState("");
  const [popupMessage, setPopupMessage] = useState("");

  const fileInputRef = useRef(null);
  const bottomRef = useRef(null);

  const getStatusBadgeClass = (status) => {
    switch (status?.toLowerCase()) {
      case "open": return "bg-blue-100 text-blue-700";
      case "pending": return "bg-yellow-100 text-yellow-800";
      case "resolved": return "bg-green-100 text-green-700";
      case "closed": return "bg-gray-200 text-gray-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case "low":
        return "bg-gray-100 text-gray-600";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "high":
        return "bg-orange-100 text-orange-800";
      case "urgent":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };


  useEffect(() => {
    if (!ticketId) return;
    setLoadingTicket(true);
    getTicketById(ticketId).then((res) => {
      setTicket(res);
      setLoadingTicket(false);
    });
  }, [ticketId]);

  useEffect(() => {
    if (!ticket) return;
    setLoadingUpdates(true);
    getTicketUpdates(ticket.id).then((res) => {
      const formatted = res.map((u) => ({
        sender: u.created_by || "User",
        is_staff: u.is_staff || false,
        time: new Date(u.updated_at).toLocaleString(),
        text: u.update_text,
        attachments: u.attachments || [],
      }));
      setUpdates(formatted);
      setLoadingUpdates(false);
    });
  }, [ticket]);

  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [updates]);

  const handleSendMessage = async () => {
    if (!ticket || (!messageText.trim() && !file)) {
      setPopupMessage("Please enter a message or attach a file.");
      return;
    }
    
    if (["resolved", "closed"].includes(ticket.status?.toLowerCase())) {
      setPopupMessage("This ticket is resolved. Reopen it to continue the conversation.");
      return;
    }
  
    setFormError("");
    try {
      setLoading(true);
      const data = await sendTicketUpdate(ticket.id, messageText, file);
  
      setUpdates((prev) => [
        ...prev,
        {
          sender: "You",
          is_staff: false,
          time: new Date(data.updated_at).toLocaleString(),
          text: data.update_text,
          attachments: data.attachments || [],
        },
      ]);
  
      setMessageText("");
      setFile(null);
      if (fileInputRef.current) fileInputRef.current.value = "";
    } catch (err) {
      setPopupMessage("Failed to send update"); // ✅ fallback if API fails
    } finally {
      setLoading(false);
    }
  };
  
  

  const handleExport = () => {
    if (ticket) {
      window.open(`/support-ticket/export?id=${ticket.id}`, "_blank");
    }
  };

  const handleReopen = async () => {
    try {
      const res = await reopenTicket(ticket.id);
      setTicket((prev) => ({ ...prev, status: res.status }));
      setUpdates((prev) => [
        ...prev,
        {
          sender: "System",
          is_staff: true,
          time: new Date(res.updated_at).toLocaleString(),
          text: "Ticket was reopened.",
        },
      ]);
    } catch (error) {
      setPopupMessage("Failed to reopen ticket.");
    }
  };

  return (
    <Layout title="Ticket History">
      {popupMessage && <Popup message={popupMessage} onClose={() => setPopupMessage("")} />}

      <div className="flex flex-col lg:flex-row gap-6 h-[calc(100vh-114px)]">
        <div className="flex flex-col border rounded-xl bg-white shadow-md flex-1 overflow-hidden">
          <div className="p-4 border-b flex items-center gap-4">
            <button onClick={() => router.back()} className="text-sm text-gray-700 hover:underline  cursor-pointer">
              <ArrowLeft className="w-4 h-4" />
            </button>
            <h2 className="text-lg font-semibold flex items-center gap-2">
              {loadingTicket ? <div className="w-40 h-4 bg-gray-200 rounded animate-pulse" /> : `Ticket ID : PF-${ticket?.id.toString().padStart(4, "0")}`}
              {!loadingTicket && (
                <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusBadgeClass(ticket?.status)}`}>
                  {ticket?.status}
                </span>
              )}
            </h2>
          </div>

          <div className="flex-1 overflow-y-auto p-4 bg-gray-50 space-y-4">
            {loadingTicket ? (
              <SkeletonMessage />
            ) : (
              <div className="flex justify-end">
                <div className="max-w-full sm:max-w-lg p-4 border rounded-md bg-blue-50 text-sm text-blue-800">
                  <div className="font-medium mb-1">
                    You <span className="text-xs ml-2">{new Date(ticket?.created_at).toLocaleString()}</span>
                  </div>
                  <p className="whitespace-pre-wrap">{ticket?.description}</p>
                  {ticket?.attachments?.map((url, i) => (
                    <a key={i} href={url} className="text-xs text-blue-700 hover:underline block break-words mt-2" target="_blank">
                      📌 {new URL(url).pathname.split("/").pop()?.split("?")[0]}
                    </a>
                  ))}
                </div>
              </div>
            )}

            {loadingUpdates ? (
              <>
                <SkeletonMessage />
                <SkeletonMessage />
              </>
            ) : (
              updates.map((msg, i) => (
                <div key={i} className={`flex ${msg.is_staff ? "justify-start" : "justify-end"}`}>
                  <div className={`max-w-full sm:max-w-lg p-4 border rounded-md text-sm ${msg.is_staff ? "bg-gray-100 text-gray-800" : "bg-blue-50 text-blue-800"}`}>
                    <div className="font-medium mb-1">
                      {msg.sender} <span className="text-xs ml-2">{msg.time}</span>
                    </div>
                    <p className="whitespace-pre-wrap mb-2">{msg.text}</p>
                    {msg.attachments?.map((url, j) => (
                      <a key={j} href={url} className="text-xs text-blue-700 hover:underline block break-words">
                        📌 {new URL(url).pathname.split("/").pop()?.split("?")[0]}
                      </a>
                    ))}
                  </div>
                </div>
              ))
            )}
            <div ref={bottomRef} />
          </div>

          <div className="border-t p-3 space-y-2 bg-white">
            {formError && <div className="text-sm text-red-600 px-3">{formError}</div>}
            {ticket && ["resolved", "closed"].includes(ticket.status?.toLowerCase()) ? (
              <div className="text-sm text-gray-500 px-3 py-2 italic">
                This ticket is resolved. You can't send more messages unless you reopen it.
              </div>
            ) : (
              <div className="flex items-center gap-2 px-3 py-2 border rounded-md">
                <button onClick={() => fileInputRef.current?.click()} type="button">
                  <Paperclip className="w-5 h-5 text-gray-400 cursor-pointer" />
                </button>
                {file && <span className="text-xs text-gray-500 truncate max-w-[120px]">{file.name}</span>}
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={(e) => setFile(e.target.files?.[0] || null)}
                  className="hidden"
                  disabled={loading}
                />
                <textarea
                  value={messageText}
                  onChange={(e) => setMessageText(e.target.value)}
                  placeholder="Write your message..."
                  rows={2}
                  className="flex-1 min-w-[150px] resize-none bg-transparent outline-none text-sm px-2"
                  disabled={loading}
                />
                <button onClick={handleSendMessage} disabled={loading} type="button">
                  <Send className="w-5 h-5 text-primary cursor-pointer" />
                </button>
              </div>
            )}
          </div>
        </div>

        <div className="w-full lg:w-80 border rounded-xl p-4 bg-gray-50 shadow-sm flex flex-col justify-between h-fit lg:h-auto">
          {loadingTicket ? (
            <div className="space-y-4 animate-pulse">
              <div className="w-3/4 h-4 bg-gray-200 rounded" />
              <div className="space-y-2">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="w-full h-3 bg-gray-200 rounded" />
                ))}
              </div>
              <div className="w-full h-9 bg-gray-200 rounded mt-4" />
            </div>
          ) : (
            <>
              <div>
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-sm font-semibold">Case Overview</h3>
                  {ticket?.status?.toLowerCase() === "resolved" && (
                    <button onClick={handleReopen} className="h-9 px-4 rounded-md bg-black text-white text-sm cursor-pointer">
                      Re-Open
                    </button>
                  )}
                </div>

                <div className="space-y-4 text-sm text-gray-700">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2 text-gray-500">
                      <ShieldCheck className="w-4 h-4" />
                      Status
                    </div>
                    <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusBadgeClass(ticket?.status)}`}>
                      {ticket?.status}
                    </span>
                  </div>

                  <div className="flex justify-between items-start gap-6">
                    <div className="flex items-center gap-2 text-gray-500">
                      <Tag className="w-4 h-4" />
                      Title
                    </div>
                    <span className="text-right font-medium text-gray-800 max-w-[60%] break-words">{ticket?.title}</span>
                  </div>

                  <div className="flex justify-between items-start gap-6">
                    <div className="flex items-center gap-2 text-gray-500">
                      <Folder className="w-4 h-4" />
                      Category
                    </div>
                    <span className="text-right font-medium text-gray-800">{ticket?.category}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2 text-gray-500">
                      <Flag className="w-4 h-4" />
                      Priority
                    </div>
                    <span className={`px-3 py-1 text-xs font-medium rounded-full ${getPriorityBadgeColor(ticket?.priority)}`}>
                      {ticket?.priority}
                    </span>
                  </div>

                  <div className="flex justify-between items-start gap-6">
                    <div className="flex items-center gap-2 text-gray-500">
                      <CalendarClock className="w-4 h-4" />
                      Created At
                    </div>
                    <span className="text-right text-gray-700">{new Date(ticket?.created_at).toLocaleString()}</span>
                  </div>

                  <div className="flex justify-between items-start gap-6">
                    <div className="flex items-center gap-2 text-gray-500">
                      <Clock className="w-4 h-4" />
                      Last Updated
                    </div>
                    <span className="text-right text-gray-700">{new Date(ticket?.updated_at).toLocaleString()}</span>
                  </div>
                </div>


              </div>
              <div className="pt-4">
                <button onClick={handleExport} className="w-full flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 text-sm font-medium rounded-md hover:bg-gray-100  cursor-pointer">
                  <FileDown className="w-4 h-4" /> Export
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </Layout>
  );
}
