"use client";

export default function RealisticSkeletonMessage({ count = 6 }) {
  return (
    <div className="space-y-4 animate-pulse">
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className={`flex ${i % 2 === 0 ? "justify-start" : "justify-end"}`}>
          <div className="max-w-full sm:max-w-lg p-4 border rounded-md shadow bg-gray-100 w-3/4 space-y-2">
            <div className="h-3 w-1/2 bg-gray-300 rounded" />
            <div className="h-2 w-5/6 bg-gray-300 rounded" />
            <div className="h-2 w-2/3 bg-gray-300 rounded" />
            {i === 2 && <div className="h-2 w-3/4 bg-gray-300 rounded" />}
          </div>
        </div>
      ))}
    </div>
  );
}
