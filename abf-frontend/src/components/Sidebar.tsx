"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { AnimatePresence, motion } from "framer-motion";
import { cn } from "@/lib/utils";
import {
  UserCircle,
  DollarSign,
  FolderArchive,
  BarChart3,
  InfoIcon,
  LockIcon,
} from "lucide-react";

const navItems = [
  { title: "Profile", href: "/profile", icon: UserCircle },
  { title: "Funding", href: "/funding", icon: DollarSign },
  { title: "Archive", href: "#", icon: FolderArchive },
  { title: "Reports & Milestones", href: "/milestones-reports", icon: BarChart3 },
  { title: "Help Center", href: "/support-ticket/my-tickets", icon: InfoIcon },
];

interface SidebarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
}

export function Sidebar({ isOpen, toggleSidebar }: SidebarProps) {
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;
    return (
    <AnimatePresence>
      {/* Mobile overlay with animation */}
      {isOpen && (
        <motion.div
          key="sidebar-overlay"
          className="fixed inset-0 bg-black/30 backdrop-blur-sm z-[35] lg:hidden"
          onClick={toggleSidebar}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.1 }}
        />
      )}

      {/* Sidebar with animation */}
      <motion.aside
        key="sidebar-menu"
        className={`fixed left-0 z-30 w-64 bg-white shadow-sm transition-all`}
        style={{
          top: 'var(--header-height, 5rem)',
          height: 'calc(100vh - var(--header-height, 5rem))'
        }}
        initial={{ x: -280 }}
        animate={{ x: isOpen ? 0 : -280 }}
        transition={{ duration: 0.15 }}
      >
        <div className="flex h-full flex-col overflow-y-auto border-r border-gray-200">
          {/* No extra padding needed */}

          {/* Navigation section - simplified */}
          <div className="flex-1 py-6 px-4">
            <nav className="space-y-1.5">
              {navItems.map((item) => {
                const isActive = item.href === '/grantmaker'
                  ? pathname === '/grantmaker'
                  : pathname === item.href ||
                    (pathname.startsWith(`${item.href}/`) && item.href !== '/grantmaker');
                const Icon = item.icon;
                // Use a div instead of Link for non-functional tabs
                return item.href === "#" ? (
                  <div
                    key={item.title}
                    className={`group flex items-center justify-between rounded-lg px-4 py-2.5 text-sm font-medium transition-all duration-200 opacity-50 cursor-not-allowed text-gray-700`}
                    onMouseEnter={() => setHoveredItem(item.title)}
                    onMouseLeave={() => setHoveredItem(null)}
                  >
                    <div className="flex items-center">
                      <Icon
                        className={`mr-3 h-5 w-5 flex-shrink-0 transition-transform duration-300 text-gray-500 ${hoveredItem === item.name ? "scale-110" : ""}`}
                      />
                      <span className="transition-all duration-200 flex items-center gap-1">
                        {item.title}
                        <LockIcon className="w-4 h-4" />
                      </span>
                    </div>
                  </div>
                ) : (
                  <Link
                    key={item.title}
                    href={item.href}
                    className={`group flex items-center justify-between rounded-lg px-4 py-2.5 text-sm font-medium transition-all duration-200 ${isActive
                      ? "bg-gradient-to-r from-teal-600/90 to-teal-500/90 text-white shadow-md"
                      : "text-gray-700 hover:bg-teal-50 hover:text-teal-600"
                      }`}
                    onMouseEnter={() => setHoveredItem(item.title)}
                    onMouseLeave={() => setHoveredItem(null)}
                  >
                    <div className="flex items-center">
                      <Icon
                        className={`mr-3 h-5 w-5 flex-shrink-0 transition-transform duration-300 ${isActive ? "text-white" : "text-gray-500 group-hover:text-[#6366F1]"} ${hoveredItem === item.name ? "scale-110" : ""}`}
                      />
                      <span className={`transition-all duration-200 ${hoveredItem === item.title && !isActive ? "font-semibold" : ""}`}>
                        {item.title}
                      </span>
                    </div>
                  </Link>
                );
              })}
            </nav>
          </div>

          {/* No close button */}
        </div>
      </motion.aside>
    </AnimatePresence>
  );
}