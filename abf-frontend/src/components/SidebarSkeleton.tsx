"use client";

export default function SidebarSkeleton() {
  return (
    <div className="w-full lg:w-80 border rounded-xl p-4 bg-gray-50 shadow-sm animate-pulse space-y-4">
      <div className="h-4 w-1/2 bg-gray-200 rounded" />
      {Array.from({ length: 6 }).map((_, i) => (
        <div key={i} className="flex justify-between items-start gap-4">
          <div className="h-3 w-1/3 bg-gray-200 rounded" />
          <div className="h-3 w-1/2 bg-gray-300 rounded" />
        </div>
      ))}
      <div className="h-8 bg-gray-200 rounded mt-4" />
    </div>
  );
}
