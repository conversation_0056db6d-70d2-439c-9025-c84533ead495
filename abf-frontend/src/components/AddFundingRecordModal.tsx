import { useEffect, useState } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    <PERSON>alogHeader,
    DialogTitle,
    DialogDescription,
    DialogFooter
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { FundingEntity } from '@/types/finance';
// Modal for adding a funding record
interface AddFundingRecordModalProps {
    isOpen: boolean;
    onClose: () => void;
    handleAdd: (values: { funding_entity: string; amount: number; date_received: string; notes: string }) => void;
    fundingEntities: FundingEntity[];
}

export const AddFundingRecordModal: React.FC<AddFundingRecordModalProps> = ({
    isOpen,
    onClose,
    handleAdd,
    fundingEntities = []
}) => {
    const [fundingEntity, setFundingEntity] = useState<number>(fundingEntities[0]?.id || 0);
    const [amount, setAmount] = useState<number>(0);
    const [dateReceived, setDateReceived] = useState<string>('');
    const [notes, setNotes] = useState<string>('');

    useEffect(() => {
        if (isOpen) {
            setFundingEntity(fundingEntities[0]?.id || 0);
            setAmount(0);
            setDateReceived('');
            setNotes('');
        }
    }, [isOpen, fundingEntities]);

    const onSubmit = () => {
        handleAdd({ funding_entity: fundingEntity, amount, date_received: dateReceived, notes });
        onClose();
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Add Funding Record</DialogTitle>
                    <DialogDescription>Fill in the details to add a new funding record.</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                    <div>
                        <Label htmlFor="fundingEntity">Funding Entity</Label>
                        <select
                            id="fundingEntity"
                            value={fundingEntity}
                            onChange={(e) => setFundingEntity(Number(e.target.value))}
                            className="w-full border rounded px-3 py-2 mt-1"
                        >
                            {fundingEntities.map((entity) => (
                                <option key={entity.id} value={entity.id}>
                                    {entity.organizationName}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <Label htmlFor="amount">Amount</Label>
                        <Input
                            id="amount"
                            type="number"
                            value={amount}
                            onChange={(e) => setAmount(Number(e.target.value))}
                        />
                    </div>
                    <div>
                        <Label htmlFor="dateReceived">Date Received</Label>
                        <Input
                            id="dateReceived"
                            type="date"
                            value={dateReceived}
                            onChange={(e) => setDateReceived(e.target.value)}
                        />
                    </div>
                    <div>
                        <Label htmlFor="notes">Notes</Label>
                        <Textarea
                            id="notes"
                            value={notes}
                            onChange={(e) => setNotes(e.target.value)}
                            rows={3}
                        />
                    </div>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={onClose}>Cancel</Button>
                    <Button
                      onClick={onSubmit}
                      className="bg-gradient-to-r from-[#00998F] to-teal-600 hover:from-teal-700 hover:to-teal-800 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-lg px-6 py-2.5 font-medium"
                    >
                      Add Record
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};