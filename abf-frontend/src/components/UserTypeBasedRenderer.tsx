import { useAuthContext } from "@/contexts/AuthContext";
import React from "react";
import { Spinner } from "./Spinner";
import LoginPage from "@/app/login/page";

interface UserTypeBasedRendererProps {
    allowedUserTypes: string [];
    children: React.ReactNode;
    fallback: React.ReactNode;
}

export default function UserTypeBasedRenderer({ allowedUserTypes, children, fallback = null }: UserTypeBasedRendererProps) {


    const {user, isLoading} = useAuthContext();


    if (isLoading) return <Spinner /> 

    if (!user) {
        return <LoginPage />
    }

    if (!allowedUserTypes.includes(user.type)) {
        return fallback;
    };

    return <>{children}</>

}