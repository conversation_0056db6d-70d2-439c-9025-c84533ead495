"use client";

import { cn } from "@/lib/utils";

interface TabButtonProps {
  active: boolean;
  onClick: () => void;
  children: React.ReactNode;
}

export function TabButton({ active, onClick, children }: TabButtonProps) {
  return (
    <button
      onClick={onClick}
      className={cn(
        "px-6 py-3 text-sm font-medium transition-colors",
        active
          ? "border-b-2 border-teal-500 text-teal-600 font-semibold"
          : "text-gray-600 hover:text-gray-900"
      )}
    >
      {children}
    </button>
  );
}