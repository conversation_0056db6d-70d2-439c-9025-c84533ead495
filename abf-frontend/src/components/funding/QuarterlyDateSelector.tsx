"use client";

import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { motion } from "framer-motion";
import { CalendarRange } from "lucide-react";

interface QuarterlyDateSelectorProps {
  selectedQuarter: string;
  setSelectedQuarter: (quarter: string) => void;
  selectedYear: string;
  setSelectedYear: (year: string) => void;
  // viewMode and setViewMode removed as they're no longer needed in the UI
  viewMode?: "quarterly" | "yearly"; // kept as optional for backward compatibility
  setViewMode?: (mode: "quarterly" | "yearly") => void; // kept as optional for backward compatibility
}

export const getQuarterDateRange = (quarter: string, year: string) => {
  // For financial year, the year represents the calendar year when the financial year starts
  // e.g., 2025 means FY 2025-26 (Apr 2025 to Mar 2026)
  const currentYear = parseInt(year);
  const nextYear = currentYear + 1;

  if (quarter === "Q1" || quarter === "Apr-Jun") return `Apr 1 - Jun 30, ${currentYear}`;
  if (quarter === "Q2" || quarter === "Jul-Sep") return `Jul 1 - Sep 30, ${currentYear}`;
  if (quarter === "Q3" || quarter === "Oct-Dec") return `Oct 1 - Dec 31, ${currentYear}`;
  if (quarter === "Q4" || quarter === "Jan-Mar") return `Jan 1 - Mar 31, ${nextYear}`;
  return `FY ${currentYear}-${nextYear.toString().slice(-2)}`; // Format as FY 2025-26
};

export function QuarterlyDateSelector({
  selectedQuarter,
  setSelectedQuarter,
  selectedYear,
  setSelectedYear,
  // viewMode and setViewMode are not used anymore
  viewMode,
  setViewMode,
}: QuarterlyDateSelectorProps) {
  // Generate years dynamically with current year (2025) as latest
  const currentYear = 2025;
  const years = ["All", ...Array.from({ length: 5 }, (_, i) => (currentYear - i).toString())];
  const quarters = ["All", "Q1", "Q2", "Q3", "Q4"];

  return (
    <motion.div
      className="flex flex-col md:flex-row items-start md:items-center gap-4"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-1">
          <CalendarRange className="h-4 w-4 text-teal-500" />
          <span className="text-sm text-gray-600">Financial Year:</span>
        </div>

        <Select value={selectedQuarter} onValueChange={setSelectedQuarter}>
          <SelectTrigger className="w-[140px] border-teal-200 focus:ring-teal-500 h-9">
            <SelectValue placeholder="Select quarter" />
          </SelectTrigger>
          <SelectContent>
            {quarters.map((quarter) => (
              <SelectItem key={quarter} value={quarter}>
                {quarter === "All" ? "All Quarters" : quarter}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedYear} onValueChange={setSelectedYear}>
          <SelectTrigger className="w-[120px] border-teal-200 focus:ring-teal-500 h-9">
            <SelectValue placeholder="Select year" />
          </SelectTrigger>
          <SelectContent>
            {years.map((year) => (
              <SelectItem key={year} value={year}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <div className="px-3 py-1.5 bg-teal-50 text-teal-700 text-xs rounded-md border border-teal-100 font-medium">
          {selectedQuarter !== "All"
            ? getQuarterDateRange(selectedQuarter, selectedYear)
            : getQuarterDateRange("All", selectedYear)}
        </div>
      </div>
    </motion.div>
  );
}
