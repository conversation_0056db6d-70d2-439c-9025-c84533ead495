import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';

interface FundingSummaryCardProps {
  title: string;
  amount: string;
  icon?: React.ReactNode;
  description?: string;
  accentColor?: string;
}

export function FundingSummaryCard({
  title,
  amount,
  icon,
  description,
  accentColor = 'teal'
}: FundingSummaryCardProps) {

  // Single elegant gradient for all cards
  const gradientMap = {
    // Using a single consistent gradient for professional look
    emerald: 'from-emerald-600 to-emerald-500',
    teal: 'from-emerald-600 to-emerald-500',
    green: 'from-emerald-600 to-emerald-500',
    sage: 'from-emerald-600 to-emerald-500',
    mint: 'from-emerald-600 to-emerald-500',
    forest: 'from-emerald-600 to-emerald-500',
    olive: 'from-emerald-600 to-emerald-500',
    moss: 'from-emerald-600 to-emerald-500',
  };

  const gradient = gradientMap[accentColor as keyof typeof gradientMap] || gradientMap.teal;

  // Get background gradient based on accent color
  // We're using a fixed background instead of gradients for cleaner look

  // Get icon background color based on accent color
  // Elegant icon styling
  const iconBgMap = {
    // Consistent minimal icon styling for all cards
    emerald: 'bg-white text-emerald-600',
    teal: 'bg-white text-emerald-600',
    green: 'bg-white text-emerald-600',
    sage: 'bg-white text-emerald-600',
    mint: 'bg-white text-emerald-600',
    forest: 'bg-white text-emerald-600',
    olive: 'bg-white text-emerald-600',
    moss: 'bg-white text-emerald-600',
  };

  const iconBg = iconBgMap[accentColor as keyof typeof iconBgMap] || iconBgMap.teal;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ y: -5, scale: 1.02 }}
      className="h-full"
    >
      <Card className={`border border-gray-100 shadow-sm hover:shadow-lg transition-all duration-300 rounded-xl overflow-hidden h-full backdrop-blur-sm bg-white/90`}>
        <div className={`h-1 bg-gradient-to-r ${gradient}`}></div>
        <CardContent className="p-6 relative">
          {/* Subtle background decorations */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-emerald-50/20 to-transparent rounded-bl-full"></div>
          <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-gradient-to-tr from-emerald-50/10 to-transparent rounded-tr-full"></div>
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-emerald-50/5 via-transparent to-transparent pointer-events-none"></div>

          <div className="flex justify-between items-start mb-4 relative z-10">
            <h3 className="text-gray-700 text-xs font-medium tracking-wider uppercase">{title}</h3>
            {icon && (
              <div className={`p-2.5 rounded-full ${iconBg} shadow-sm ring-1 ring-gray-100`}>
                {icon}
              </div>
            )}
          </div>

          <p className={`text-2xl font-bold bg-gradient-to-r ${gradient} bg-clip-text text-transparent mb-3 tracking-tight`}>{amount}</p>

          {/* Trend indicators removed as requested */}

          {description && (
            <div className="mt-3 flex items-center">
              <p className="text-xs text-gray-500 py-1 px-2 bg-gray-50/80 rounded-md">{description}</p>
              <ArrowRight className="h-3 w-3 text-emerald-500 ml-1 opacity-70" />
            </div>
          )}

          {/* Minimal elegant effects */}
          <div className="absolute bottom-0 left-0 w-full h-1/4 bg-gradient-to-t from-white/30 to-transparent"></div>
          <div className="absolute -bottom-10 -right-10 w-32 h-32 rounded-full bg-emerald-50/10 blur-2xl"></div>
        </CardContent>
      </Card>
    </motion.div>
  );
}