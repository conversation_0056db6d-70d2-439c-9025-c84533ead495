import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FileText, FileSpreadsheet, ChevronDown, ChevronRight } from 'lucide-react';
import { ExpenseRecord } from '@/services/funding-service';
import { useRouter } from 'next/navigation';

interface ExpenseTableProps {
  data: ExpenseRecord[];
  formatCurrency: (value: number) => string;
}

export function ExpenseTable({ data, formatCurrency }: ExpenseTableProps) {
  const router = useRouter();
  const [expandedRows, setExpandedRows] = useState<{[key: string]: boolean}>({});

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Verified': return 'bg-green-100 text-green-800';
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle click on expense to view details
  const handleExpenseClick = (expenseId: string) => {
    router.push(`/funding/expenses/${expenseId}`);
  };

  // Toggle expanded state for parent rows
  const toggleRowExpand = (id: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  return (
    <div className="overflow-hidden rounded-lg border border-gray-200">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="bg-gray-50 border-b border-gray-200">
              <th className="text-left p-4 font-medium text-gray-600">ID</th>
              <th className="text-left p-4 font-medium text-gray-600">Date</th>
              <th className="text-left p-4 font-medium text-gray-600">Category</th>
              <th className="text-left p-4 font-medium text-gray-600">Budget</th>
              <th className="text-left p-4 font-medium text-gray-600">Actual Spent</th>
              <th className="text-left p-4 font-medium text-gray-600">Status</th>
              <th className="text-left p-4 font-medium text-gray-600">Attachment</th>
            </tr>
          </thead>
          <tbody>
            {data.length > 0 ? (
              data.map((item, index) => (
                <React.Fragment key={index}>
                  <tr className={`border-b ${!item.is_parent ? 'last:border-0' : ''} hover:bg-gray-50 transition-colors`}>
                    <td className="p-4 font-medium text-gray-800">
                      {item.is_parent && item.children && item.children.length > 0 ? (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-0 mr-1 h-auto"
                          onClick={() => toggleRowExpand(item.id)}
                        >
                          {expandedRows[item.id] ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </Button>
                      ) : null}
                      {item.id}
                      {item.is_parent && item.child_count ? (
                        <span className="ml-2 text-xs text-gray-500">
                          ({item.child_count} items)
                        </span>
                      ) : null}
                    </td>
                    <td className="p-4 text-gray-700">{item.loggedDate}</td>
                    <td className="p-4 text-gray-700">{item.category}</td>
                    <td className="p-4 font-medium text-gray-800">{formatCurrency(item.totalBudget)}</td>
                    <td className="p-4 font-medium text-gray-800">{formatCurrency(item.totalActualSpent)}</td>
                    <td className="p-4">
                      <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(item.status)}`}>
                        {item.status}
                      </span>
                    </td>
                    <td className="p-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-blue-600 hover:text-blue-800 p-0 h-auto"
                        onClick={() => handleExpenseClick(item.id)}
                      >
                        {item.attachment && item.attachment.toLowerCase() === 'manual entry' ? (
                          <>
                            <FileText className="h-4 w-4 mr-1" />
                            <span>Manual Entry</span>
                          </>
                        ) : item.attachment && item.attachment.toLowerCase().includes('.xls') ? (
                          <>
                            <FileSpreadsheet className="h-4 w-4 mr-1" />
                            <span>{item.attachment}</span>
                          </>
                        ) : (
                          <>
                            <FileText className="h-4 w-4 mr-1" />
                            <span>{item.attachment}</span>
                          </>
                        )}
                      </Button>
                    </td>
                  </tr>

                  {/* Child rows - shown when parent is expanded */}
                  {item.is_parent && item.children && item.children.length > 0 && expandedRows[item.id] ? (
                    item.children.map((child, childIndex) => (
                      <tr key={`child-${childIndex}`} className="border-b last:border-0 bg-gray-50">
                        <td className="p-4 pl-10 font-medium text-gray-600">{child.id}</td>
                        <td className="p-4 text-gray-600">{child.loggedDate}</td>
                        <td className="p-4 text-gray-600">{child.category}</td>
                        <td className="p-4 font-medium text-gray-600">{formatCurrency(child.totalBudget)}</td>
                        <td className="p-4 font-medium text-gray-600">{formatCurrency(child.totalActualSpent)}</td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(child.status)}`}>
                            {child.status}
                          </span>
                        </td>
                        <td className="p-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-blue-600 hover:text-blue-800 p-0 h-auto"
                            onClick={() => handleExpenseClick(child.id)}
                          >
                            {child.attachment && child.attachment.toLowerCase() === 'manual entry' ? (
                              <>
                                <FileText className="h-4 w-4 mr-1" />
                                <span>Manual Entry</span>
                              </>
                            ) : child.attachment && child.attachment.toLowerCase().includes('.xls') ? (
                              <>
                                <FileSpreadsheet className="h-4 w-4 mr-1" />
                                <span>{child.attachment}</span>
                              </>
                            ) : (
                              <>
                                <FileText className="h-4 w-4 mr-1" />
                                <span>{child.attachment}</span>
                              </>
                            )}
                          </Button>
                        </td>
                      </tr>
                    ))
                  ) : null}
                </React.Fragment>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="p-8 text-center text-gray-500">
                  No expense records found for the selected filters.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
