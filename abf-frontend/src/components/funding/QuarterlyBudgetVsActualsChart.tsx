import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Car<PERSON>ianG<PERSON>, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface QuarterlyBudgetVsActualsChartProps {
  data: Array<{
    quarter: string;
    budget: number;
    actual: number;
  }>;
  formatCurrency: (amount: number) => string;
}

// Compact and Elegant Custom Tooltip
const CustomTooltip = ({ active, payload, label, formatCurrency }: any) => {
  if (active && payload && payload.length) {
    const budgetValue = payload.find((p: any) => p.dataKey === 'budget')?.value || 0;
    const actualValue = payload.find((p: any) => p.dataKey === 'actual')?.value || 0;

    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 5 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        transition={{ duration: 0.15, ease: "easeOut" }}
        className="bg-white/95 backdrop-blur-lg p-3 rounded-lg shadow-xl border border-gray-200/50 min-w-[160px]"
      >
        {/* Header */}
        <div className="text-sm font-bold text-gray-800 mb-2 text-center">{label}</div>

        {/* Compact metrics */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-[#1f4e79] rounded-sm"></div>
              <span className="text-xs font-medium text-gray-600">Budget</span>
            </div>
            <span className="text-sm font-bold text-[#1f4e79]">{formatCurrency(budgetValue)}</span>
          </div>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-[#ea580c] rounded-sm"></div>
              <span className="text-xs font-medium text-gray-600">Actual</span>
            </div>
            <span className="text-sm font-bold text-[#ea580c]">{formatCurrency(actualValue)}</span>
          </div>
        </div>
      </motion.div>
    );
  }
  return null;
};

export function QuarterlyBudgetVsActualsChart({ data, formatCurrency }: QuarterlyBudgetVsActualsChartProps) {
  // Helper function to safely convert to number
  const safeNumber = (value: any): number => {
    const num = Number(value);
    return isNaN(num) || !isFinite(num) ? 0 : num;
  };

  // Debug: Log the data being passed to the chart
  console.log('QuarterlyBudgetVsActualsChart data:', data);

  // Validate and sanitize the data
  const sanitizedData = data && Array.isArray(data) ? data.map(item => ({
    quarter: item.quarter || 'Q1',
    budget: safeNumber(item.budget),
    actual: safeNumber(item.actual)
  })) : [
    { quarter: 'Q1', budget: 0, actual: 0 },
    { quarter: 'Q2', budget: 0, actual: 0 },
    { quarter: 'Q3', budget: 0, actual: 0 },
    { quarter: 'Q4', budget: 0, actual: 0 }
  ];

  // Use the sanitized data
  const hasRealData = sanitizedData.some(item => item.budget > 0 || item.actual > 0);
  const chartData = sanitizedData;

  console.log('Using sanitized data:', hasRealData, 'Chart data:', chartData);

  // Calculate totals for summary cards with safe number conversion
  const totalBudget = chartData.reduce((sum, item) => sum + safeNumber(item.budget), 0);
  const totalActual = chartData.reduce((sum, item) => sum + safeNumber(item.actual), 0);

  console.log('Chart totals - Budget:', totalBudget, 'Actual:', totalActual);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
      whileHover={{
        y: -6,
        transition: { duration: 0.3, ease: "easeOut" }
      }}
      className="w-full"
    >
      <Card className="shadow-lg hover:shadow-2xl transition-all duration-500 border-0 rounded-2xl overflow-hidden bg-gradient-to-br from-white via-gray-50/30 to-white">
        <div className="h-1.5 bg-gradient-to-r from-emerald-600 via-emerald-500 to-emerald-400"></div>
        <CardHeader className="pb-6 bg-gradient-to-br from-white via-gray-50/20 to-transparent">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <CardTitle className="text-lg font-semibold text-gray-800 mb-2">
                Budget vs Actuals Analysis
              </CardTitle>

            </div>

            {/* Enhanced Totals Section */}
            <div className="flex gap-6">
              {/* Total Budget Card */}
              <motion.div
                whileHover={{ scale: 1.02, y: -2 }}
                transition={{ duration: 0.2 }}
                className="relative group"
              >
                <div className="bg-gradient-to-br from-[#1f4e79]/5 via-[#1f4e79]/8 to-[#2563eb]/5 p-4 rounded-xl border border-[#1f4e79]/10 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative z-10">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-[#1f4e79] rounded-full"></div>
                      <div className="text-xs font-bold text-[#1f4e79]/70 uppercase tracking-wider">Total Budget</div>
                    </div>
                    <div className="text-xl font-bold text-[#1f4e79] tracking-tight">{formatCurrency(totalBudget)}</div>
                  </div>
                </div>
              </motion.div>

              {/* Total Actual Card */}
              <motion.div
                whileHover={{ scale: 1.02, y: -2 }}
                transition={{ duration: 0.2 }}
                className="relative group"
              >
                <div className="bg-gradient-to-br from-[#ea580c]/5 via-[#ea580c]/8 to-[#f97316]/5 p-4 rounded-xl border border-[#ea580c]/10 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative z-10">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-[#ea580c] rounded-full"></div>
                      <div className="text-xs font-bold text-[#ea580c]/70 uppercase tracking-wider">Total Actual</div>
                    </div>
                    <div className="text-xl font-bold text-[#ea580c] tracking-tight">{formatCurrency(totalActual)}</div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6 pt-2">
          {/* Ultra-Modern Chart Container */}
          <motion.div
            initial={{ opacity: 0, scale: 0.96 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }}
            className="h-[480px] bg-gradient-to-br from-gray-50/30 via-white to-gray-50/20 rounded-2xl border border-gray-200/50 shadow-lg backdrop-blur-sm p-5 relative overflow-hidden"
          >
            {/* Subtle background pattern */}
            <div className="absolute inset-0 opacity-[0.02]">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 1px 1px, rgba(0,0,0,0.15) 1px, transparent 0)`,
                backgroundSize: '20px 20px'
              }}></div>
            </div>
            <div className="relative z-10 h-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={chartData}
                margin={{ top: 25, right: 35, left: 25, bottom: 45 }}
                barSize={45}
                barGap={8}
                barCategoryGap={35}
              >
                {/* Professional Grid */}
                <CartesianGrid
                  strokeDasharray="2 4"
                  horizontal={true}
                  vertical={false}
                  stroke="#e5e7eb"
                  opacity={0.5}
                />

                {/* Enhanced Professional X-Axis */}
                <XAxis
                  dataKey="quarter"
                  tick={{
                    fill: '#374151',
                    fontSize: 14,
                    fontWeight: 700
                  }}
                  axisLine={{
                    stroke: '#d1d5db',
                    strokeWidth: 1.5
                  }}
                  tickLine={{
                    stroke: '#d1d5db',
                    strokeWidth: 1
                  }}
                  height={50}
                />

                {/* Enhanced Professional Y-Axis with Improved Formatting */}
                <YAxis
                  tickFormatter={(value) => {
                    if (value >= 10000000) return `₹${(value / 10000000).toFixed(1)}Cr`;
                    if (value >= 100000) return `₹${(value / 100000).toFixed(1)}L`;
                    if (value >= 1000) return `₹${(value / 1000).toFixed(0)}K`;
                    return `₹${value}`;
                  }}
                  tick={{
                    fill: '#374151',
                    fontSize: 13,
                    fontWeight: 600
                  }}
                  axisLine={{
                    stroke: '#d1d5db',
                    strokeWidth: 1.5
                  }}
                  tickLine={{
                    stroke: '#d1d5db',
                    strokeWidth: 1
                  }}
                  width={80}
                  domain={[0, 'dataMax + 10000']}
                />

                {/* Enhanced Professional Tooltip */}
                <Tooltip content={<CustomTooltip formatCurrency={formatCurrency} />} />

                {/* Ultra-Modern Legend */}
                <Legend
                  verticalAlign="top"
                  height={40}
                  iconType="rect"
                  wrapperStyle={{
                    paddingBottom: '20px',
                    fontSize: '13px',
                    fontWeight: '700',
                    color: '#374151',
                    letterSpacing: '0.025em'
                  }}
                />

                {/* Ultra-Modern Gradient Definitions */}
                <defs>
                  <linearGradient id="budgetGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#3b82f6" stopOpacity={0.95} />
                    <stop offset="30%" stopColor="#2563eb" stopOpacity={0.98} />
                    <stop offset="70%" stopColor="#1f4e79" stopOpacity={1} />
                    <stop offset="100%" stopColor="#1e3a8a" stopOpacity={1} />
                  </linearGradient>
                  <linearGradient id="actualGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#fb923c" stopOpacity={0.95} />
                    <stop offset="30%" stopColor="#f97316" stopOpacity={0.98} />
                    <stop offset="70%" stopColor="#ea580c" stopOpacity={1} />
                    <stop offset="100%" stopColor="#c2410c" stopOpacity={1} />
                  </linearGradient>
                  <filter id="modernShadow" x="-30%" y="-30%" width="160%" height="160%">
                    <feDropShadow dx="0" dy="4" stdDeviation="6" floodColor="#000000" floodOpacity="0.12"/>
                    <feDropShadow dx="0" dy="1" stdDeviation="2" floodColor="#000000" floodOpacity="0.08"/>
                  </filter>
                  <filter id="hoverGlow" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                    <feMerge>
                      <feMergeNode in="coloredBlur"/>
                      <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                  </filter>
                </defs>

                {/* Budget Bar - Ultra-Modern with Enhanced Gradients */}
                <Bar
                  dataKey="budget"
                  name="Budget"
                  fill="url(#budgetGradient)"
                  radius={[8, 8, 0, 0]}
                  filter="url(#modernShadow)"
                />

                {/* Actual Bar - Ultra-Modern with Enhanced Gradients */}
                <Bar
                  dataKey="actual"
                  name="Actual"
                  fill="url(#actualGradient)"
                  radius={[8, 8, 0, 0]}
                  filter="url(#modernShadow)"
                />
              </BarChart>
            </ResponsiveContainer>
            </div>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
