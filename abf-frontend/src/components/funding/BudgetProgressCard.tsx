import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { motion } from 'framer-motion';

interface BudgetProgressCardProps {
  totalBudget: number;
  totalSpent: number;
  remainingBudget: number;
  formatCurrency: (value: number) => string;
}

export function BudgetProgressCard({
  totalBudget,
  totalSpent,
  remainingBudget,
  formatCurrency
}: BudgetProgressCardProps) {
  const budgetUtilizationPercentage = totalBudget > 0 ? Math.round((totalSpent / totalBudget) * 100) : 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.3 }}
      whileHover={{ y: -5 }}
    >
      <Card className="shadow-md hover:shadow-xl transition-all duration-300 border-0 rounded-xl overflow-hidden">
        <div className="h-2 bg-gradient-to-r from-teal-600 via-teal-500 to-teal-400"></div>
        <CardContent className="p-6 relative">
          <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-gray-100/80 to-transparent rounded-bl-full"></div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <motion.div
              className="p-4 rounded-xl bg-gradient-to-br from-gray-50 to-white border border-gray-100 shadow-sm"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              whileHover={{ y: -3, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)' }}
            >
              <h4 className="text-sm font-medium text-gray-600 mb-2">Total Budget</h4>
              <p className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-700 bg-clip-text text-transparent">{formatCurrency(totalBudget)}</p>
            </motion.div>
            <motion.div
              className="p-4 rounded-xl bg-gradient-to-br from-teal-50 to-white border border-teal-100 shadow-sm"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              whileHover={{ y: -3, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)' }}
            >
              <h4 className="text-sm font-medium text-gray-600 mb-2">Total Spent</h4>
              <p className="text-2xl font-bold bg-gradient-to-r from-teal-600 to-teal-500 bg-clip-text text-transparent">{formatCurrency(totalSpent)}</p>
            </motion.div>
            <motion.div
              className="p-4 rounded-xl bg-gradient-to-br from-cyan-50 to-white border border-cyan-100 shadow-sm"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              whileHover={{ y: -3, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)' }}
            >
              <h4 className="text-sm font-medium text-gray-600 mb-2">Remaining Balance</h4>
              <p className="text-2xl font-bold bg-gradient-to-r from-cyan-600 to-cyan-500 bg-clip-text text-transparent">{formatCurrency(remainingBudget)}</p>
            </motion.div>
            <motion.div
              className="p-4 rounded-xl bg-gradient-to-br from-amber-50 to-white border border-amber-100 shadow-sm"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
              whileHover={{ y: -3, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)' }}
            >
              <h4 className="text-sm font-medium text-gray-600 mb-2">Budget Variance</h4>
              <p className="text-2xl font-bold bg-gradient-to-r from-amber-600 to-amber-500 bg-clip-text text-transparent">{formatCurrency(totalBudget - totalSpent)}</p>
            </motion.div>
          </div>

          <motion.div
            className="mt-8 p-4 rounded-xl bg-gradient-to-br from-gray-50 to-white border border-gray-100 shadow-sm"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <div className="flex justify-between mb-3">
              <span className="text-sm font-medium text-gray-700">Budget Utilization</span>
              <span className="text-sm font-medium px-2 py-1 bg-teal-100 text-teal-700 rounded-full">{budgetUtilizationPercentage}%</span>
            </div>
            <div className="w-full bg-gray-100 rounded-full h-4 shadow-inner overflow-hidden">
              <motion.div
                className="h-4 rounded-full bg-gradient-to-r from-teal-500 to-teal-400 shadow-lg"
                initial={{ width: 0 }}
                animate={{ width: `${budgetUtilizationPercentage}%` }}
                transition={{ duration: 1, delay: 0.5 }}
              >
                <div className="h-full w-full bg-white opacity-20 animate-pulse"></div>
              </motion.div>
            </div>
            <div className="flex justify-between mt-2 text-xs text-gray-500">
              <span>0%</span>
              <span>50%</span>
              <span>100%</span>
            </div>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
