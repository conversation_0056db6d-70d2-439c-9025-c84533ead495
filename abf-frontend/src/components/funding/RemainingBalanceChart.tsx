import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
  Cell
} from 'recharts';
import { motion } from 'framer-motion';
import { CurrencyDollarIcon, ChartBarIcon } from '@/components/ui/icons';
import { TrendingUp, BarChart2 } from 'lucide-react';

interface RemainingBalanceChartProps {
  data: Array<{
    month: string;
    value: number;
  }>;
  formatCurrency: (value: number) => string;
  accentColor?: string;
  title?: string;
  description?: string;
}

export function RemainingBalanceChart({
  data,
  formatCurrency,
  accentColor = 'teal',
  title = 'Remaining Balance',
  description = 'Monthly remaining balance trend'
}: RemainingBalanceChartProps) {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  // Enhanced data for the chart
  const enhancedData = data.map(item => ({
    ...item,
    trend: item.value * 0.8 // Creating a trend line that's slightly below the bars
  }));

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border border-teal-100 shadow-xl rounded-lg animate-fadeIn">
          <div className="flex items-center mb-1">
            <div className="w-3 h-3 rounded-full bg-teal-500 mr-2"></div>
            <p className="font-semibold text-gray-800">{`${label}`}</p>
          </div>
          <p className="text-teal-600 font-medium text-lg">
            {formatCurrency(payload[0].value)}
          </p>
          {payload.length > 1 && (
            <p className="text-xs text-gray-500 mt-1">
              Trend: {formatCurrency(payload[1].value)}
            </p>
          )}
          <div className="h-1 w-full bg-gradient-to-r from-teal-500 to-teal-400 mt-2 rounded-full"></div>
        </div>
      );
    }
    return null;
  };

  const handleMouseEnter = (_, index) => {
    setActiveIndex(index);
  };

  const handleMouseLeave = () => {
    setActiveIndex(null);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.3 }}
      whileHover={{ y: -5 }}
    >
      <Card className="shadow-md hover:shadow-xl transition-all duration-300 border-0 rounded-xl overflow-hidden">
        <div className="h-2 bg-gradient-to-r from-teal-600 via-teal-500 to-teal-400"></div>
        <CardHeader className="pb-2 relative">
          <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-gray-100/80 to-transparent rounded-bl-full"></div>
          <div className="flex justify-between items-center relative z-10">
            <div>
              <CardTitle className="text-xl font-semibold text-gray-800">{title}</CardTitle>
              {description && <CardDescription className="text-sm text-gray-500">{description}</CardDescription>}
            </div>
            <div className="p-2 rounded-lg bg-gradient-to-br from-teal-100 to-teal-50 shadow-sm flex items-center gap-2">
              <CurrencyDollarIcon className="w-5 h-5 text-teal-500" />
              <BarChart2 className="w-4 h-4 text-teal-600" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[350px]">
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart
                data={enhancedData}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                onMouseMove={(data) => {
                  if (data && data.activeTooltipIndex !== undefined) {
                    handleMouseEnter(null, data.activeTooltipIndex);
                  }
                }}
                onMouseLeave={handleMouseLeave}
              >
                <defs>
                  <linearGradient id="barGradient0" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#14b8a6" stopOpacity={1} />
                    <stop offset="100%" stopColor="#14b8a6" stopOpacity={0.8} />
                  </linearGradient>
                  <linearGradient id="barGradient1" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#0ea5e9" stopOpacity={1} />
                    <stop offset="100%" stopColor="#0ea5e9" stopOpacity={0.8} />
                  </linearGradient>
                  <linearGradient id="barGradient2" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#06b6d4" stopOpacity={1} />
                    <stop offset="100%" stopColor="#06b6d4" stopOpacity={0.8} />
                  </linearGradient>
                  <linearGradient id="lineGradient" x1="0" y1="0" x2="1" y2="0">
                    <stop offset="0%" stopColor="#0d9488" stopOpacity={1} />
                    <stop offset="100%" stopColor="#0ea5e9" stopOpacity={1} />
                  </linearGradient>
                  <filter id="balanceBarShadow" height="130%">
                    <feDropShadow dx="0" dy="3" stdDeviation="3" floodColor="#14b8a6" floodOpacity="0.2"/>
                  </filter>
                </defs>
                <CartesianGrid strokeDasharray="3 3" vertical={false} opacity={0.15} stroke="#e2e8f0" />
                <XAxis
                  dataKey="month"
                  axisLine={{ stroke: '#e2e8f0' }}
                  tickLine={false}
                  tick={{ fill: '#64748b', fontSize: 12 }}
                  dy={10}
                />
                <YAxis
                  axisLine={{ stroke: '#e2e8f0' }}
                  tickLine={false}
                  tick={{ fill: '#64748b', fontSize: 12 }}
                  tickFormatter={(value) => `₹${value/1000}k`}
                  dx={-10}
                />
                <Tooltip
                  content={<CustomTooltip />}
                  cursor={{ fill: 'rgba(236, 253, 245, 0.4)' }}
                  animationDuration={300}
                />
                <Legend
                  wrapperStyle={{ paddingTop: '10px' }}
                  formatter={(value) => <span className="text-sm font-medium text-gray-700">{value}</span>}
                  iconType="circle"
                  iconSize={8}
                />
                <Bar
                  dataKey="value"
                  name="Total Balance"
                  radius={[8, 8, 0, 0]}
                  filter="url(#balanceBarShadow)"
                  animationDuration={1500}
                  animationEasing="ease-in-out"
                  barSize={40}
                >
                  {enhancedData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={`url(#barGradient${index % 3})`}
                      style={{
                        opacity: activeIndex === null || activeIndex === index ? 1 : 0.7,
                        transition: 'all 0.3s ease-in-out',
                        transform: activeIndex === index ? 'scaleY(1.05)' : 'scaleY(1)',
                        transformOrigin: 'bottom'
                      }}
                    />
                  ))}
                </Bar>
                <Line
                  type="monotone"
                  dataKey="trend"
                  name="Trend"
                  stroke="url(#lineGradient)"
                  strokeWidth={3}
                  dot={{ r: 4, fill: "#0d9488", strokeWidth: 2, stroke: "#fff" }}
                  activeDot={{ r: 6, fill: "#0d9488", strokeWidth: 2, stroke: "#fff", strokeOpacity: 0.8, className: "animate-pulse" }}
                  animationDuration={2000}
                  animationEasing="ease-in-out"
                  strokeDasharray="0"
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
