'use client';

import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { motion } from 'framer-motion';
import { FileText } from 'lucide-react';

interface Grant {
  id: string;
  name: string;
}

interface GrantSelectorProps {
  selectedGrant: string;
  setSelectedGrant: (grant: string) => void;
  grants: Grant[];
  isLoading?: boolean;
}

export function GrantSelector({
  selectedGrant,
  setSelectedGrant,
  grants,
  isLoading = false
}: GrantSelectorProps) {
  return (
    <motion.div 
      className="flex items-center gap-2"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center gap-1">
        <FileText className="h-4 w-4 text-teal-500" />
        <span className="text-sm text-gray-600">Grant:</span>
      </div>
      
      <Select value={selectedGrant} onValueChange={setSelectedGrant} disabled={isLoading}>
        <SelectTrigger className="w-[220px] border-teal-200 focus:ring-teal-500 h-9">
          <SelectValue placeholder={isLoading ? "Loading grants..." : "Select grant"} />
        </SelectTrigger>
        <SelectContent>
          {grants.map((grant) => (
            <SelectItem key={grant.id} value={grant.id}>
              {grant.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </motion.div>
  );
}
