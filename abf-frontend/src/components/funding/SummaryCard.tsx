import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { motion } from 'framer-motion';

interface SummaryCardProps {
  title: string;
  amount: string;
  icon?: React.ReactNode;
  accentColor?: string;
  description?: string;
}

export function SummaryCard({ 
  title, 
  amount, 
  icon, 
  accentColor = 'teal',
  description 
}: SummaryCardProps) {
  // Define gradient based on accent color
  const gradientMap = {
    teal: 'from-teal-500 to-teal-400',
    blue: 'from-blue-500 to-blue-400',
    amber: 'from-amber-500 to-amber-400',
    emerald: 'from-emerald-500 to-emerald-400',
    orange: 'from-orange-500 to-orange-400',
    red: 'from-red-500 to-red-400',
    purple: 'from-purple-500 to-purple-400',
  };
  
  const gradient = gradientMap[accentColor as keyof typeof gradientMap] || gradientMap.teal;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ y: -5 }}
      className="h-full"
    >
      <Card className="border-0 shadow-md hover:shadow-lg transition-all duration-300 rounded-lg overflow-hidden h-full">
        <div className={`h-1 bg-gradient-to-r ${gradient}`}></div>
        <CardContent className="p-6">
          <div className="flex justify-between items-start mb-2">
            <h3 className="text-gray-600 text-sm font-medium">{title}</h3>
            {icon && <div className="text-gray-400">{icon}</div>}
          </div>
          <p className="text-3xl font-bold text-gray-800">{amount}</p>
          {description && (
            <p className="text-xs text-gray-500 mt-2">{description}</p>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
