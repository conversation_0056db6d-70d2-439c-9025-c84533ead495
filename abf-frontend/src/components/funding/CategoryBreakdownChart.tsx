import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Legend,
  Tooltip,
  Label
} from 'recharts';
import { motion } from 'framer-motion';
// Define local interface for removed type
interface ExpenseBreakdown {
  name: string;
  value: number;
  percentage: string;
  color?: string;
}

interface CategoryBreakdownChartProps {
  data: ExpenseBreakdown[];
  formatCurrency: (value: number) => string;
}

interface CategoryWithPercentage extends ExpenseBreakdown {
  percentValue: number;
}

export function CategoryBreakdownChart({
  data,
  formatCurrency
}: CategoryBreakdownChartProps) {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  // Professional gradient color palette
  const COLORS = [
    '#14b8a6', // Teal
    '#0ea5e9', // Sky blue
    '#8b5cf6', // Violet
    '#06b6d4', // Cyan
    '#10b981', // Emerald
    '#3b82f6', // Blue
    '#6366f1', // Indigo
    '#0d9488', // Teal dark
    '#0284c7', // Sky dark
    '#059669'  // Emerald dark
  ];

  // Calculate percentages for each category
  const totalValue = data.reduce((sum, item) => sum + item.value, 0);
  const dataWithPercentages = data.map((item, index) => ({
    ...item,
    percentValue: (item.value / totalValue) * 100,
    color: item.color || COLORS[index % COLORS.length],
    percentage: `${((item.value / totalValue) * 100).toFixed(0)}%`
  }));

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border-0 rounded-lg shadow-lg animate-fadeIn">
          <p className="font-semibold text-gray-800">{payload[0].name}</p>
          <p className="text-sm font-medium text-teal-600">{formatCurrency(payload[0].value)}</p>
          <p className="text-xs text-gray-500">{`${payload[0].payload.percentage} of total`}</p>
        </div>
      );
    }
    return null;
  };

  const handleMouseEnter = (_, index) => {
    setActiveIndex(index);
  };

  const handleMouseLeave = () => {
    setActiveIndex(null);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.1 }}
      whileHover={{ y: -5 }}
    >
      <Card className="shadow-md border-0 rounded-lg overflow-hidden">
        <div className="h-1 bg-gradient-to-r from-teal-500 to-teal-400"></div>
        <CardHeader className="pb-2">
          <CardTitle className="text-xl font-semibold text-gray-800">Expenses Breakdown</CardTitle>
        </CardHeader>
        <CardContent className="relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              {dataWithPercentages.map((category, index) => (
                <motion.div
                  key={index}
                  className="flex items-center p-2 rounded-lg hover:bg-gray-50 transition-colors"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 * index }}
                  whileHover={{ scale: 1.02, backgroundColor: 'rgba(240, 253, 250, 0.5)' }}
                  onMouseEnter={() => handleMouseEnter(null, index)}
                  onMouseLeave={handleMouseLeave}
                >
                  <div
                    className="w-4 h-4 rounded-full mr-3 shadow-sm"
                    style={{ background: `linear-gradient(135deg, ${category.color}, ${category.color}CC)` }}
                  />
                  <div className="w-full">
                    <div className="flex justify-between mb-1">
                      <span className="text-sm font-medium text-gray-700">{category.name}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-teal-600">{formatCurrency(category.value)}</span>
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">{category.percentage}</span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-100 rounded-full h-2.5 overflow-hidden shadow-inner">
                      <motion.div
                        className="h-2.5 rounded-full"
                        style={{ background: `linear-gradient(90deg, ${category.color}99, ${category.color})` }}
                        initial={{ width: 0 }}
                        animate={{ width: category.percentage }}
                        transition={{ duration: 1, delay: 0.3 + (0.1 * index) }}
                      />
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            <div className="h-[300px] flex items-center justify-center">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <defs>
                    <filter id="categoryChartShadow" height="130%">
                      <feDropShadow dx="0" dy="3" stdDeviation="3" floodOpacity="0.2" />
                    </filter>
                    {dataWithPercentages.map((entry, index) => (
                      <linearGradient key={`gradient-${index}`} id={`colorGradient-${index}`} x1="0" y1="0" x2="1" y2="1">
                        <stop offset="0%" stopColor={entry.color} stopOpacity={0.8}/>
                        <stop offset="100%" stopColor={entry.color} stopOpacity={1}/>
                      </linearGradient>
                    ))}
                  </defs>
                  <Pie
                    data={dataWithPercentages}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={90}
                    paddingAngle={4}
                    dataKey="value"
                    onMouseEnter={handleMouseEnter}
                    onMouseLeave={handleMouseLeave}
                    filter="url(#categoryChartShadow)"
                    animationDuration={1500}
                    animationEasing="ease-in-out"
                  >
                    {dataWithPercentages.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={`url(#colorGradient-${index})`}
                        opacity={activeIndex === null || activeIndex === index ? 1 : 0.6}
                        style={{ transition: 'opacity 0.3s ease-in-out', filter: 'brightness(1.05)' }}
                      />
                    ))}
                    <Label
                      content={({ viewBox }) => {
                        const { cx, cy } = viewBox;
                        return (
                          <text x={cx} y={cy} textAnchor="middle" dominantBaseline="middle">
                            <tspan x={cx} y={cy} dy="-0.5em" fontSize="14" fontWeight="bold" fill="#374151">
                              Total
                            </tspan>
                            <tspan x={cx} y={cy} dy="1.5em" fontSize="12" fill="#6B7280">
                              {formatCurrency(totalValue)}
                            </tspan>
                          </text>
                        );
                      }}
                    />
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
