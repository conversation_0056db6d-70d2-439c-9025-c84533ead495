import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { CheckCircle, AlertCircle, Calendar, FileText, Upload, File } from 'lucide-react';
import { DisbursementHistory } from '@/services/funding-service';
import { motion } from 'framer-motion';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface DisbursementTableProps {
  data: DisbursementHistory[];
  formatCurrency: (value: number) => string;
}

export function DisbursementTable({ data, formatCurrency }: DisbursementTableProps) {
  const [uploadedFiles, setUploadedFiles] = useState<Record<string, File | null>>({});
  const [fileNames, setFileNames] = useState<Record<string, string>>({});

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Disbursed': return 'bg-green-100 text-green-800 border border-green-200';
      case 'Pending': return 'bg-amber-100 text-amber-800 border border-amber-200';
      case 'Failed': return 'bg-red-100 text-red-800 border border-red-200';
      default: return 'bg-gray-100 text-gray-800 border border-gray-200';
    }
  };

  // Get acknowledgement status color
  const getAcknowledgementColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'text-green-600';
      case 'Send Receipt': return 'text-blue-600';
      case 'Pending': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, itemId: string) => {
    const file = e.target.files?.[0];
    if (file) {
      setUploadedFiles(prev => ({ ...prev, [itemId]: file }));
      setFileNames(prev => ({ ...prev, [itemId]: file.name }));
    }
  };

  return (
    <div className="overflow-hidden rounded-xl border border-gray-200 shadow-sm">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
              <th className="text-left p-4 font-medium text-gray-600">Grant Name</th>
              <th className="text-left p-4 font-medium text-gray-600">Date</th>
              <th className="text-left p-4 font-medium text-gray-600">Amount</th>
              <th className="text-left p-4 font-medium text-gray-600">Status</th>
              <th className="text-left p-4 font-medium text-gray-600">Remark</th>
              <th className="text-left p-4 font-medium text-gray-600">Upload Request Letter</th>
              <th className="text-left p-4 font-medium text-gray-600">Acknowledgement</th>
            </tr>
          </thead>
          <tbody>
            {data.length > 0 ? (
              data.map((item, index) => (
                <motion.tr
                  key={index}
                  className="border-b last:border-0 hover:bg-gray-50 transition-colors"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.05 * index }}
                  whileHover={{ backgroundColor: 'rgba(240, 253, 250, 0.5)' }}
                >
                  <td className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-teal-100 text-teal-600 flex-shrink-0">
                        <FileText className="h-4 w-4" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-800">{item.grantName}</div>
                        <div className="text-xs text-gray-500">ID: {item.id}</div>
                      </div>
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-700">{item.date}</span>
                    </div>
                  </td>
                  <td className="p-4 font-medium text-teal-600">{formatCurrency(item.amount)}</td>
                  <td className="p-4">
                    <span className={`px-2.5 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                      {item.status}
                    </span>
                  </td>
                  <td className="p-4 text-gray-700">{item.remark || '-'}</td>
                  <td className="p-4">
                    <div className="flex items-center gap-2">
                      <Label htmlFor={`file-upload-${item.id}`} className="cursor-pointer">
                        <div className={`flex items-center gap-1.5 px-2.5 py-1.5 rounded-md border ${fileNames[item.id] ? 'bg-teal-50 text-teal-700 border-teal-200' : 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-teal-50 hover:text-teal-700 hover:border-teal-200'} transition-colors`}>
                          {fileNames[item.id] ? (
                            <>
                              <File className="h-3.5 w-3.5 text-teal-500" />
                              <span className="text-xs font-medium truncate max-w-[120px]">{fileNames[item.id]}</span>
                            </>
                          ) : (
                            <>
                              <Upload className="h-3.5 w-3.5" />
                              <span className="text-xs font-medium">Upload PDF</span>
                            </>
                          )}
                        </div>
                        <Input
                          id={`file-upload-${item.id}`}
                          type="file"
                          className="hidden"
                          accept=".pdf"
                          onChange={(e) => handleFileChange(e, item.id)}
                        />
                      </Label>
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="flex items-center gap-2">
                      {item.acknowledgement === 'Completed' ? (
                        <div className="flex items-center gap-1.5 px-2.5 py-1 bg-green-50 text-green-700 rounded-full border border-green-200">
                          <CheckCircle className="h-3.5 w-3.5 text-green-500" />
                          <span className="text-xs font-medium">{item.acknowledgement}</span>
                        </div>
                      ) : item.acknowledgement === 'Pending' ? (
                        <div className="flex items-center gap-1.5 px-2.5 py-1 bg-amber-50 text-amber-700 rounded-full border border-amber-200">
                          <AlertCircle className="h-3.5 w-3.5 text-amber-500" />
                          <span className="text-xs font-medium">{item.acknowledgement}</span>
                        </div>
                      ) : (
                        <Button variant="link" size="sm" className="p-0 h-auto text-teal-600 hover:text-teal-800 flex items-center gap-1">
                          <FileText className="h-3.5 w-3.5" />
                          <span className="text-xs font-medium">{item.acknowledgement}</span>
                        </Button>
                      )}
                    </div>
                  </td>
                </motion.tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="p-8 text-center text-gray-500">
                  <div className="flex flex-col items-center justify-center py-6">
                    <Calendar className="h-12 w-12 text-gray-300 mb-3" />
                    <p className="text-gray-500 font-medium">No disbursement history found</p>
                    <p className="text-gray-400 text-sm mt-1">Try changing your filters or date range</p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
