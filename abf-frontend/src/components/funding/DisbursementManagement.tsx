import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select } from '@/components/ui/select';

interface DisbursementManagementProps {
  organization: {
    disbursements?: any[];
  };
}

export function DisbursementManagement({ organization }: DisbursementManagementProps) {
  const [disbursements, setDisbursements] = useState<any[]>(organization.disbursements || []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const handleAddDisbursement = () => {
    const newDisbursement = {
      id: disbursements.length + 1,
      milestone: '',
      scheduled_date: '',
      scheduled_amount: 0,
      actual_date: '',
      actual_amount: 0,
      status: 'pending',
      notes: ''
    };

    setDisbursements([...disbursements, newDisbursement]);
  };

  const calculateTotals = () => {
    return disbursements.reduce((acc, disbursement) => ({
      totalScheduled: acc.totalScheduled + (disbursement.scheduled_amount || 0),
      totalActual: acc.totalActual + (disbursement.actual_amount || 0)
    }), { totalScheduled: 0, totalActual: 0 });
  };

  const totals = calculateTotals();

  return (
    <Card className="bg-white shadow-md hover:shadow-lg transition-shadow mb-8">
      <CardHeader>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <CardTitle className="text-xl text-gray-800">Disbursement Management</CardTitle>
            <CardDescription>Track and manage funding disbursements</CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-6">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Milestone</th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Scheduled Date</th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Scheduled Amount</th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actual Date</th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actual Amount</th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Notes</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {disbursements.map((disbursement, index) => (
                  <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{disbursement.id}</td>
                    <td className="px-3 py-2 whitespace-nowrap">
                      <Input
                        type="text"
                        value={disbursement.milestone}
                        onChange={(e) => {
                          const updatedDisbursements = [...disbursements];
                          updatedDisbursements[index].milestone = e.target.value;
                          setDisbursements(updatedDisbursements);
                        }}
                        className="w-full text-sm"
                      />
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap">
                      <Input
                        type="date"
                        value={disbursement.scheduled_date}
                        onChange={(e) => {
                          const updatedDisbursements = [...disbursements];
                          updatedDisbursements[index].scheduled_date = e.target.value;
                          setDisbursements(updatedDisbursements);
                        }}
                        className="w-full text-sm"
                      />
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap">
                      <Input
                        type="number"
                        value={disbursement.scheduled_amount}
                        onChange={(e) => {
                          const updatedDisbursements = [...disbursements];
                          updatedDisbursements[index].scheduled_amount = parseFloat(e.target.value);
                          setDisbursements(updatedDisbursements);
                        }}
                        className="w-full text-sm"
                      />
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap">
                      <Input
                        type="date"
                        value={disbursement.actual_date}
                        onChange={(e) => {
                          const updatedDisbursements = [...disbursements];
                          updatedDisbursements[index].actual_date = e.target.value;
                          setDisbursements(updatedDisbursements);
                        }}
                        className="w-full text-sm"
                      />
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap">
                      <Input
                        type="number"
                        value={disbursement.actual_amount}
                        onChange={(e) => {
                          const updatedDisbursements = [...disbursements];
                          updatedDisbursements[index].actual_amount = parseFloat(e.target.value);
                          setDisbursements(updatedDisbursements);
                        }}
                        className="w-full text-sm"
                      />
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap">
                      <select
                        value={disbursement.status}
                        onChange={(e) => {
                          const updatedDisbursements = [...disbursements];
                          updatedDisbursements[index].status = e.target.value;
                          setDisbursements(updatedDisbursements);
                        }}
                        className="w-full text-sm border rounded-md"
                      >
                        <option value="pending">Pending</option>
                        <option value="completed">Completed</option>
                        <option value="delayed">Delayed</option>
                      </select>
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap">
                      <Input
                        type="text"
                        value={disbursement.notes}
                        onChange={(e) => {
                          const updatedDisbursements = [...disbursements];
                          updatedDisbursements[index].notes = e.target.value;
                          setDisbursements(updatedDisbursements);
                        }}
                        className="w-full text-sm"
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot className="bg-gray-50">
                <tr>
                  <td colSpan={3} className="px-3 py-3 text-right font-medium">Totals:</td>
                  <td className="px-3 py-3 font-medium">{formatCurrency(totals.totalScheduled)}</td>
                  <td></td>
                  <td className="px-3 py-3 font-medium">{formatCurrency(totals.totalActual)}</td>
                  <td colSpan={2}></td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}