import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface DisbursementRow {
  sr_no: number;
  particulars: string;
  scheduled_payment_date: string;
  scheduled_amount: number;
  received_amount?: number;
  pending_amount?: number;
  payment_received_date?: string;
  remarks?: string;
}

interface ExcelDataTableProps {
  data: DisbursementRow[];
  onInputChange?: (index: number, field: string, value: string | number) => void;
  editable?: boolean;
}

export function ExcelDataTable({ data, onInputChange, editable = false }: ExcelDataTableProps) {
  const calculateTotal = (field: string) => {
    return data.reduce((total, row) => {
      return total + (row[field as keyof DisbursementRow] as number || 0);
    }, 0);
  };

  return (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse bg-white">
        <thead>
          <tr className="bg-gray-100">
            <th className="p-3 border text-left font-semibold">Sr No.</th>
            <th className="p-3 border text-left font-semibold">Particulars</th>
            <th className="p-3 border text-center font-semibold" colSpan={2}>Scheduled payments</th>
            <th className="p-3 border text-center font-semibold" colSpan={2}>Received payments</th>
            <th className="p-3 border text-left font-semibold">Remarks</th>
          </tr>
          <tr className="bg-gray-50">
            <th className="p-3 border" colSpan={2}></th>
            <th className="p-3 border text-left font-semibold">Date of disbursement</th>
            <th className="p-3 border text-right font-semibold">Amount</th>
            <th className="p-3 border text-left font-semibold">Date of disbursement</th>
            <th className="p-3 border text-right font-semibold">Amount</th>
            <th className="p-3 border"></th>
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="p-3 border">{row.sr_no}</td>
              <td className="p-3 border">
                {editable ? (
                  <Input
                    type="text"
                    value={row.particulars}
                    onChange={(e) => onInputChange && onInputChange(index, 'particulars', e.target.value)}
                    className="w-full"
                  />
                ) : (
                  row.particulars
                )}
              </td>
              <td className="p-3 border">
                {editable ? (
                  <Input
                    type="date"
                    value={row.scheduled_payment_date}
                    onChange={(e) => onInputChange && onInputChange(index, 'scheduled_payment_date', e.target.value)}
                    className="w-full"
                  />
                ) : (
                  row.scheduled_payment_date
                )}
              </td>
              <td className="p-3 border">
                {editable ? (
                  <Input
                    type="number"
                    value={row.scheduled_amount}
                    onChange={(e) => onInputChange && onInputChange(index, 'scheduled_amount', parseFloat(e.target.value))}
                    className="text-right"
                  />
                ) : (
                  row.scheduled_amount.toFixed(2)
                )}
              </td>
              <td className="p-3 border">
                {editable ? (
                  <Input
                    type="date"
                    value={row.payment_received_date || ''}
                    onChange={(e) => onInputChange && onInputChange(index, 'payment_received_date', e.target.value)}
                    className="w-full"
                  />
                ) : (
                  row.payment_received_date || ''
                )}
              </td>
              <td className="p-3 border">
                {editable ? (
                  <Input
                    type="number"
                    value={row.received_amount || ''}
                    onChange={(e) => onInputChange && onInputChange(index, 'received_amount', parseFloat(e.target.value))}
                    className="text-right"
                  />
                ) : (
                  row.received_amount?.toFixed(2) || ''
                )}
              </td>
              <td className="p-3 border">
                {editable ? (
                  <Input
                    type="text"
                    value={row.remarks || ''}
                    onChange={(e) => onInputChange && onInputChange(index, 'remarks', e.target.value)}
                    className="w-full"
                  />
                ) : (
                  row.remarks || ''
                )}
              </td>
            </tr>
          ))}
          <tr className="bg-gray-100 font-bold">
            <td className="p-3 border" colSpan={3}>Total</td>
            <td className="p-3 border text-right">{calculateTotal('scheduled_amount').toFixed(2)}</td>
            <td className="p-3 border"></td>
            <td className="p-3 border text-right">{calculateTotal('received_amount').toFixed(2)}</td>
            <td className="p-3 border"></td>
          </tr>
        </tbody>
      </table>
    </div>
  );
}
