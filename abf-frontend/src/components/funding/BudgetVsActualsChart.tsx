import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts';
import { motion, AnimatePresence } from 'framer-motion';

// Define the data structure for budget categories
interface BudgetCategory {
  name: string;
  budget: number;
  actual: number;
}

interface BudgetVsActualsChartProps {
  data: BudgetCategory[];
  formatCurrency: (value: number) => string;
}

// Clean Professional Tooltip for Stacked Bar Chart
const StackedBarTooltip = ({ active, payload, label, formatCurrency }: any) => {
  if (active && payload && payload.length) {
    const budgetValue = payload[0]?.payload?.budget || 0;
    const actualValue = payload[0]?.payload?.actual || 0;
    const remainingValue = budgetValue - actualValue;

    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 8 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 8 }}
        transition={{ duration: 0.2, ease: [0.25, 0.46, 0.45, 0.94] }}
        className="relative z-50"
      >
        {/* Enhanced Glass-morphism background */}
        <div className="absolute inset-0 bg-white/96 backdrop-blur-xl rounded-lg shadow-2xl border border-gray-200/60"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-white/40 via-transparent to-gray-50/30 rounded-lg"></div>

        {/* Content */}
        <div className="relative p-4 min-w-[240px]">
          {/* Clean Header without decorative elements */}
          <div className="mb-3">
            <h4 className="font-bold text-gray-900 text-base leading-tight tracking-tight">{label}</h4>
            <p className="text-xs text-gray-500 font-medium mt-0.5">Budget Analysis</p>
          </div>

          {/* Enhanced Metrics Grid with Superior Color Scheme */}
          <div className="grid grid-cols-2 gap-3 mb-3">
            <div className="text-center p-3 bg-gradient-to-br from-[#006B5D]/10 via-[#00998F]/8 to-teal-50/80 rounded-lg border border-[#00998F]/20 shadow-sm">
              <div className="text-base font-bold text-[#006B5D] tracking-tight">{formatCurrency(budgetValue)}</div>
              <div className="text-xs text-gray-600 font-semibold mt-1">Total Budget</div>
            </div>
            <div className="text-center p-3 bg-gradient-to-br from-sky-50/90 via-cyan-50/80 to-blue-50/70 rounded-lg border border-sky-200/60 shadow-sm">
              <div className="text-base font-bold text-[#0891b2] tracking-tight">{formatCurrency(actualValue)}</div>
              <div className="text-xs text-gray-600 font-semibold mt-1">Utilized</div>
            </div>
          </div>

          {/* Remaining Amount with enhanced color scheme */}
          <div className="pt-3 border-t border-gray-200/60">
            <div className="flex justify-between items-center">
              <span className="text-sm font-semibold text-gray-700">Remaining</span>
              <div className="flex items-center gap-2">
                <span className={`text-sm font-bold tracking-tight ${remainingValue >= 0 ? 'text-[#0891b2]' : 'text-red-500'}`}>
                  {formatCurrency(Math.abs(remainingValue))}
                </span>
                {remainingValue < 0 && (
                  <span className="px-2 py-0.5 text-xs font-bold text-red-600 bg-red-50 rounded-md border border-red-200">
                    Over Budget
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    );
  }
  return null;
};

// Super Professional Animated Bar with Premium Visual Effects
const SuperProfessionalAnimatedBar = (props: any) => {
  const { fill, ...rest } = props;
  const [isHovered, setIsHovered] = useState(false);
  const [isActive, setIsActive] = useState(false);

  return (
    <motion.g>
      {/* Main Bar with Enhanced Effects */}
      <motion.rect
        {...rest}
        fill={fill}
        initial={{
          height: 0,
          y: rest.y + rest.height,
          opacity: 0,
          scaleY: 0
        }}
        animate={{
          height: rest.height,
          y: rest.y,
          opacity: 1,
          scaleY: 1
        }}
        transition={{
          duration: 1.2,
          ease: [0.25, 0.46, 0.45, 0.94],
          delay: Math.random() * 0.6,
          type: "spring",
          stiffness: 100,
          damping: 15
        }}
        whileHover={{
          scaleX: 1.02,
          filter: "brightness(1.15) saturate(1.1)",
          transition: { duration: 0.3, ease: "easeOut" }
        }}
        whileTap={{
          scaleX: 0.98,
          transition: { duration: 0.1 }
        }}
        onMouseEnter={() => {
          setIsHovered(true);
          setIsActive(true);
        }}
        onMouseLeave={() => {
          setIsHovered(false);
          setIsActive(false);
        }}
        style={{
          filter: isHovered
            ? 'drop-shadow(0 8px 25px rgba(0,153,143,0.25)) drop-shadow(0 4px 12px rgba(0,0,0,0.15))'
            : 'drop-shadow(0 3px 8px rgba(0,0,0,0.08)) drop-shadow(0 1px 3px rgba(0,0,0,0.05))',
          transition: 'filter 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
          transformOrigin: 'bottom center'
        }}
      />

      {/* Subtle Inner Highlight */}
      <motion.rect
        x={rest.x + 2}
        y={rest.y}
        width={Math.max(0, rest.width - 4)}
        height={Math.max(0, rest.height)}
        fill="url(#innerHighlight)"
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.3 }}
        transition={{ delay: Math.random() * 0.6 + 0.5, duration: 0.8 }}
        style={{ pointerEvents: 'none' }}
      />

      {/* Hover Glow Effect */}
      <AnimatePresence>
        {isHovered && (
          <motion.rect
            {...rest}
            fill="none"
            stroke="rgba(0,153,143,0.4)"
            strokeWidth="2"
            initial={{ opacity: 0, scale: 1 }}
            animate={{ opacity: 1, scale: 1.05 }}
            exit={{ opacity: 0, scale: 1 }}
            transition={{ duration: 0.3 }}
            style={{
              pointerEvents: 'none',
              filter: 'blur(1px)'
            }}
          />
        )}
      </AnimatePresence>

      {/* Active State Shimmer */}
      <AnimatePresence>
        {isActive && (
          <motion.rect
            x={rest.x}
            y={rest.y}
            width={rest.width}
            height={rest.height}
            fill="url(#shimmerGradient)"
            initial={{ x: rest.x - rest.width }}
            animate={{ x: rest.x + rest.width }}
            exit={{ opacity: 0 }}
            transition={{
              duration: 1.5,
              ease: "easeInOut",
              repeat: Infinity,
              repeatType: "loop"
            }}
            style={{
              pointerEvents: 'none',
              mixBlendMode: 'overlay'
            }}
          />
        )}
      </AnimatePresence>
    </motion.g>
  );
};

export function BudgetVsActualsChart({ data, formatCurrency }: BudgetVsActualsChartProps) {

  // Sort data by budget amount (descending) for better visualization
  const sortedData = [...data].sort((a, b) => b.budget - a.budget);

  // Calculate totals for summary cards
  const totalBudget = data.reduce((sum, item) => sum + item.budget, 0);
  const totalActual = data.reduce((sum, item) => sum + item.actual, 0);

  // Transform data with simplified tranche labels and GM dashboard consistent styling
  const stackedData = sortedData.map((item, index) => ({
    ...item,
    name: `Tranche ${index + 1}`, // Replace category names with simplified tranche labels
    utilized: item.actual,
    remaining: Math.max(0, item.budget - item.actual),
    overBudget: Math.max(0, item.actual - item.budget)
  }));

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
      whileHover={{
        y: -6,
        transition: { duration: 0.3, ease: "easeOut" }
      }}
    >
      <Card className="shadow-lg hover:shadow-xl transition-all duration-300 border-0 rounded-xl overflow-hidden bg-white">
        {/* GM Dashboard Consistent Header */}
        <div className="h-1 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>

        <CardHeader className="pb-2">
          <div className="flex items-start">
            <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-sm group-hover:shadow-md transition-all duration-300">
              <div className="h-6 w-6 text-[#00998F] group-hover:text-teal-600 transition-colors">
                <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
                  <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                </svg>
              </div>
            </div>
            <div>
              <CardTitle className="text-xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                Budget vs Actuals Analysis
              </CardTitle>
              <CardDescription className="text-gray-600">
                Budget allocation and utilization comparison
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {/* GM Dashboard Consistent Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="text-center p-4 bg-gradient-to-br from-[#00998F]/10 via-teal-50 to-emerald-50/50 rounded-xl border border-[#00998F]/20 shadow-sm hover:shadow-md transition-all duration-300"
            >
              <div className="text-xl font-bold text-[#00998F] mb-1">{formatCurrency(totalBudget)}</div>
              <div className="text-sm text-gray-600 font-semibold">Total Budget</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="text-center p-4 bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50/50 rounded-xl border border-emerald-200 shadow-sm hover:shadow-md transition-all duration-300"
            >
              <div className="text-xl font-bold text-emerald-600 mb-1">{formatCurrency(totalActual)}</div>
              <div className="text-sm text-gray-600 font-semibold">Total Utilized</div>
            </motion.div>
          </div>

          {/* Optimized Chart Container with Reduced Bottom Spacing */}
          <motion.div
            initial={{ opacity: 0, scale: 0.96 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }}
            className="h-[460px] bg-gradient-to-br from-gray-50/20 via-white to-gray-50/10 rounded-xl border border-gray-200/30 shadow-sm p-3"
          >
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={stackedData}
                margin={{ top: 15, right: 25, left: 15, bottom: 35 }}
                barSize={65}
                barGap={12}
                barCategoryGap={18}
              >
                {/* GM Dashboard Consistent Grid */}
                <CartesianGrid
                  strokeDasharray="2 4"
                  horizontal={true}
                  vertical={false}
                  stroke="#e5e7eb"
                  opacity={0.5}
                />

                {/* Optimized X-axis for Tranche Labels */}
                <XAxis
                  dataKey="name"
                  tick={{
                    fill: '#374151',
                    fontSize: 13,
                    fontWeight: 600,
                    fontFamily: 'Inter, system-ui, sans-serif'
                  }}
                  axisLine={false}
                  tickLine={false}
                  angle={0}
                  textAnchor="middle"
                  height={40}
                  interval={0}
                />

                {/* Enhanced Y-axis */}
                <YAxis
                  tickFormatter={(value) => formatCurrency(value)}
                  tick={{
                    fill: '#6B7280',
                    fontSize: 12,
                    fontWeight: 500,
                    fontFamily: 'Inter, system-ui, sans-serif'
                  }}
                  axisLine={false}
                  tickLine={false}
                  domain={[0, 'dataMax']}
                  width={70}
                />

                {/* Simplified tooltip for stacked bars */}
                <Tooltip
                  content={<StackedBarTooltip formatCurrency={formatCurrency} />}
                  cursor={{ fill: 'rgba(0, 153, 143, 0.05)' }}
                />

                {/* Enhanced Professional Legend */}
                <Legend
                  verticalAlign="top"
                  align="center"
                  iconType="circle"
                  iconSize={10}
                  wrapperStyle={{
                    paddingBottom: 20,
                    fontSize: '14px',
                    fontWeight: 600,
                    fontFamily: 'Inter, system-ui, sans-serif'
                  }}
                  payload={[
                    {
                      value: 'Utilized Amount',
                      type: 'circle',
                      color: '#00998F'
                    },
                    {
                      value: 'Remaining Budget',
                      type: 'circle',
                      color: '#67e8f9'
                    }
                  ]}
                />

                {/* Enhanced Professional Gradient Definitions with Superior Color Differentiation */}
                <defs>
                  {/* Primary Teal Gradient for Utilized Amount (Bottom Segment) - Rich & Bold */}
                  <linearGradient id="utilizedGradient" x1="0" y1="1" x2="0" y2="0">
                    <stop offset="0%" stopColor="#006B5D" stopOpacity={1} />
                    <stop offset="25%" stopColor="#00998F" stopOpacity={1} />
                    <stop offset="50%" stopColor="#0d9488" stopOpacity={0.98} />
                    <stop offset="75%" stopColor="#14b8a6" stopOpacity={0.95} />
                    <stop offset="100%" stopColor="#2dd4bf" stopOpacity={0.92} />
                  </linearGradient>

                  {/* Complementary Light Teal Gradient for Remaining Budget (Top Segment) - Soft & Distinct */}
                  <linearGradient id="remainingGradient" x1="0" y1="1" x2="0" y2="0">
                    <stop offset="0%" stopColor="#67e8f9" stopOpacity={0.9} />
                    <stop offset="30%" stopColor="#7dd3fc" stopOpacity={0.85} />
                    <stop offset="60%" stopColor="#93c5fd" stopOpacity={0.8} />
                    <stop offset="100%" stopColor="#bfdbfe" stopOpacity={0.75} />
                  </linearGradient>

                  {/* Enhanced Over Budget Gradient with Professional Warning Colors */}
                  <linearGradient id="overBudgetGradient" x1="0" y1="1" x2="0" y2="0">
                    <stop offset="0%" stopColor="#b91c1c" stopOpacity={0.95} />
                    <stop offset="30%" stopColor="#dc2626" stopOpacity={0.9} />
                    <stop offset="70%" stopColor="#ef4444" stopOpacity={0.85} />
                    <stop offset="100%" stopColor="#f87171" stopOpacity={0.8} />
                  </linearGradient>

                  {/* Professional Inner Highlight for Enhanced Visual Depth */}
                  <linearGradient id="innerHighlight" x1="0" y1="0" x2="1" y2="0">
                    <stop offset="0%" stopColor="rgba(255,255,255,0)" />
                    <stop offset="40%" stopColor="rgba(255,255,255,0.25)" />
                    <stop offset="60%" stopColor="rgba(255,255,255,0.25)" />
                    <stop offset="100%" stopColor="rgba(255,255,255,0)" />
                  </linearGradient>

                  {/* Subtle Shadow Gradient for Professional Depth */}
                  <linearGradient id="shadowGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="rgba(0,0,0,0)" />
                    <stop offset="100%" stopColor="rgba(0,0,0,0.05)" />
                  </linearGradient>
                </defs>

                {/* Enhanced Professional Vertical Stacked Bars with Superior Visual Appeal */}
                <Bar
                  dataKey="utilized"
                  name="Utilized Amount"
                  fill="url(#utilizedGradient)"
                  stackId="stack"
                  radius={[0, 0, 4, 4]}
                  stroke="rgba(0, 107, 93, 0.15)"
                  strokeWidth={0.8}
                />

                <Bar
                  dataKey="remaining"
                  name="Remaining Budget"
                  fill="url(#remainingGradient)"
                  stackId="stack"
                  radius={[8, 8, 0, 0]}
                  stroke="rgba(103, 232, 249, 0.25)"
                  strokeWidth={0.8}
                />

                {/* Enhanced Professional Over Budget Indicator */}
                <Bar
                  dataKey="overBudget"
                  name="Over Budget"
                  fill="url(#overBudgetGradient)"
                  stackId="stack"
                  radius={[8, 8, 0, 0]}
                  stroke="rgba(185, 28, 28, 0.25)"
                  strokeWidth={0.8}
                />
              </BarChart>
            </ResponsiveContainer>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
