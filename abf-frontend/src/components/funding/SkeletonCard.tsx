import { Card, CardContent } from "@/components/ui/card";

export function SkeletonCard() {
  return (
    <Card className="border border-gray-100 shadow-sm rounded-xl overflow-hidden h-full bg-white/90">
      <div className="h-1 bg-gray-200 animate-pulse"></div>
      <CardContent className="p-6 relative">
        <div className="flex justify-between items-start mb-4">
          <div className="h-4 w-24 bg-gray-200 rounded-md animate-pulse"></div>
          <div className="h-10 w-10 bg-gray-200 rounded-full animate-pulse"></div>
        </div>
        <div className="h-8 w-32 bg-gray-200 rounded-md animate-pulse mb-3"></div>
        <div className="h-4 w-40 bg-gray-200 rounded-md animate-pulse mt-3"></div>
      </CardContent>
    </Card>
  );
}

export function SkeletonChartCard() {
  return (
    <Card className="border border-gray-100 shadow-sm rounded-xl overflow-hidden h-full bg-white/90">
      <div className="h-1 bg-gray-200 animate-pulse"></div>
      <CardContent className="p-6 relative">
        <div className="flex justify-between items-start mb-4">
          <div className="h-5 w-32 bg-gray-200 rounded-md animate-pulse"></div>
          <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse"></div>
        </div>
        <div className="h-[300px] bg-gray-100 rounded-lg animate-pulse"></div>
      </CardContent>
    </Card>
  );
}

export function SkeletonTable() {
  return (
    <Card className="border border-gray-100 shadow-sm rounded-xl overflow-hidden h-full bg-white/90">
      <div className="h-1 bg-gray-200 animate-pulse"></div>
      <CardContent className="p-6 relative">
        <div className="flex justify-between items-start mb-4">
          <div>
            <div className="h-5 w-32 bg-gray-200 rounded-md animate-pulse mb-2"></div>
            <div className="h-4 w-48 bg-gray-200 rounded-md animate-pulse"></div>
          </div>
          <div className="flex gap-2">
            <div className="h-8 w-20 bg-gray-200 rounded-md animate-pulse"></div>
            <div className="h-8 w-20 bg-gray-200 rounded-md animate-pulse"></div>
          </div>
        </div>
        
        <div className="overflow-hidden rounded-lg border border-gray-200">
          <div className="h-10 bg-gray-100 animate-pulse"></div>
          {[...Array(5)].map((_, index) => (
            <div key={index} className="h-14 bg-gray-50 border-t border-gray-200 animate-pulse" style={{ animationDelay: `${index * 0.1}s` }}></div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
