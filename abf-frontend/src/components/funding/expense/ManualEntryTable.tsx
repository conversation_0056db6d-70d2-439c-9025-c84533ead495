import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus as PlusIcon } from "lucide-react";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";

interface ExpenseRow {
  sr_no: number;
  particulars: string;
  main_header: string;
  sub_headers: string;
  units?: string;
  frequency?: string;
  cost_per_unit?: number;
  budget_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  actuals_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  total_budget?: number;
  total_actual?: number;
  receipt?: File;
}

interface ManualEntryTableProps {
  expenseRows: ExpenseRow[];
  onAddRow: () => void;
  onInputChange: (index: number, field: string, value: string | number) => void;
}

// Interface for grant data if needed in the future
/*
interface Grant {
  id: number;
  grant_name: string;
  organization: {
    organization_name: string;
  };
  grant_maker_organization: number;
  grant_duration?: string;
  grant_purpose?: string;
  annual_budget?: number;
  funding_sources?: string;
}
*/

export function ManualEntryTable({ expenseRows, onAddRow, onInputChange }: ManualEntryTableProps) {
  // State for validation errors
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const displayRows = expenseRows;

  // In a real implementation, we would fetch the grant ID from the user's profile or context
  // For now, we'll use a hardcoded grant ID of 1

  const validateRow = (row: ExpenseRow) => {
    const rowErrors: {[key: string]: string} = {};

    // Only validate required fields during form submission
    if (!row.particulars.trim()) rowErrors.particulars = 'This field is required';
    if (!row.main_header.trim()) rowErrors.main_header = 'This field is required';
    if (!row.sub_headers.trim()) rowErrors.sub_headers = 'This field is required';

    // Validate budget quarterly values only for negative numbers
    const budgetSum = Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0);
    if (budgetSum < 0) {
      rowErrors.budget = 'Budget values cannot be negative';
    }

    // Validate actuals quarterly values only for negative numbers
    const actualsSum = Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0);
    if (actualsSum < 0) {
      rowErrors.actuals = 'Actual values cannot be negative';
    }

    return rowErrors;
  };

  const calculateTotal = (field: string) => {
    return expenseRows.reduce((total, row) => {
      if (field.includes(".")) {
        const [parent, child] = field.split(".");
        return total + ((row as any)[parent][child] || 0);
      }
      return total + ((row as any)[field] || 0);
    }, 0);
  };

  const handleSubmit = async () => {
    console.log('Starting form submission...');
    setErrors({});

    // In a real implementation, we would validate the grant ID

    let hasValidationErrors = false;
    const allErrors: {[key: string]: string} = {};

    if (expenseRows.length === 0) {
      console.log('No expense rows found');
      toast.error('Please add at least one expense row');
      return;
    }

    console.log('Validating expense rows:', expenseRows);
    expenseRows.forEach((row, index) => {
      const rowErrors = validateRow(row);
      const budgetSum = Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0);

      if (row.total_budget && row.total_budget !== budgetSum) {
        rowErrors.budget = `Total budget (${row.total_budget}) does not match quarterly sum (${budgetSum})`;
      }

      if (Object.keys(rowErrors).length > 0) {
        hasValidationErrors = true;
        Object.entries(rowErrors).forEach(([field, message]) => {
          allErrors[`${index}-${field}`] = message;
        });
      }
    });

    if (hasValidationErrors) {
      console.log('Validation errors found:', allErrors);
      setErrors(allErrors);
      const errorMessages = Object.values(allErrors);
      const uniqueErrors = [...new Set(errorMessages)];
      uniqueErrors.forEach(error => toast.error(error));
      return;
    }

    const hasErrors = expenseRows.some(row => {
      const budgetSum = Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0);
      const actualsSum = Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0);
      return budgetSum < actualsSum;
    });

    if (hasErrors) {
      console.log('Budget validation error');
      toast.error('Total budget must be greater than total actuals');
      return;
    }

    try {
      console.log('Preparing expense data for submission...');

      // Prepare for submission

      // Submit each expense row individually to match backend expectations
      const results = [];

      for (let i = 0; i < expenseRows.length; i++) {
        const row = expenseRows[i];
        const formData = new FormData();

        // Use a hardcoded grant ID of 1 for development
        formData.append('grant', '1');
        formData.append('particulars', row.particulars);
        formData.append('main_headers', row.main_header); // Note: backend expects 'main_headers' (plural)
        formData.append('sub_headers', row.sub_headers);

        // Add quarterly budget values
        formData.append('budget_q1', row.budget_quarterly.Q1.toString());
        formData.append('budget_q2', row.budget_quarterly.Q2.toString());
        formData.append('budget_q3', row.budget_quarterly.Q3.toString());
        formData.append('budget_q4', row.budget_quarterly.Q4.toString());

        // Add quarterly actual values
        formData.append('actual_q1', row.actuals_quarterly.Q1.toString());
        formData.append('actual_q2', row.actuals_quarterly.Q2.toString());
        formData.append('actual_q3', row.actuals_quarterly.Q3.toString());
        formData.append('actual_q4', row.actuals_quarterly.Q4.toString());

        // Add optional fields
        if (row.units) formData.append('units', row.units);
        if (row.frequency) formData.append('frequency', row.frequency);
        if (row.cost_per_unit) formData.append('cost_per_unit', row.cost_per_unit.toString());

        // Calculate and add totals
        const budgetTotal = Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0);
        const actualTotal = Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0);
        formData.append('total_budget', budgetTotal.toString());
        formData.append('total_actual', actualTotal.toString());

        // Add required expense_date (current date)
        formData.append('expense_date', new Date().toISOString().split('T')[0]);

        // Add source type
        formData.append('source_type', 'manual');

        // Add receipt if available
        if (row.receipt) {
          formData.append('receipt', row.receipt);
        }

        console.log(`Submitting row ${i + 1}:`, Object.fromEntries(formData));

        // Use the submitExpense function from grantee-expense-service
        try {
          // Import the submitExpense function
          const { submitExpense } = await import('@/services/grantee-expense-service');

          // Submit the expense
          const data = await submitExpense(formData);
          console.log(`Success data for row ${i + 1}:`, data);
          results.push(data);
        } catch (submitError) {
          console.error(`Error submitting row ${i + 1}:`, submitError);
          throw submitError;
        }
      }

      // All rows submitted successfully
      console.log('All rows submitted successfully:', results);

      // Successfully submitted all rows

      // Reset the table after successful submission
      const emptyRow = {
        sr_no: 1,
        particulars: "",
        main_header: "",
        sub_headers: "",
        units: "",
        frequency: "",
        cost_per_unit: 0,
        total_grant_budget: 0,
        budget_quarterly: { Q1: 0, Q2: 0, Q3: 0, Q4: 0 },
        actuals_quarterly: { Q1: 0, Q2: 0, Q3: 0, Q4: 0 },
        receipt: undefined
      };

      // Reset all fields for the first row
      Object.keys(emptyRow).forEach(field => {
        const key = field as keyof typeof emptyRow;
        if (typeof emptyRow[key] === 'object' && emptyRow[key] !== null) {
          // Handle budget_quarterly and actuals_quarterly
          if (key === 'budget_quarterly' || key === 'actuals_quarterly') {
            const quarterlyData = emptyRow[key] as Record<string, number>;
            Object.keys(quarterlyData).forEach(quarter => {
              onInputChange(0, `${key}.${quarter}`, 0);
            });
          }
        } else {
          // Handle primitive values
          onInputChange(0, field, emptyRow[key] as string | number);
        }
      });

      // Remove all additional rows
      while (expenseRows.length > 1) {
        expenseRows.pop();
      }

      setErrors({}); // Clear any validation errors

      toast.success(
        <div className="font-semibold">
          <div className="text-xl mb-2">Hurray! 🎉</div>
          <div>Your expense data has been successfully submitted</div>
        </div>,
        {
          duration: 6000,
          position: 'top-center',
          style: {
            background: '#4CAF50',
            color: '#fff',
            fontSize: '16px',
            padding: '16px',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
            maxWidth: '400px',
            width: '100%'
          }
        }
      );
    } catch (error) {
      console.error('Error submitting expense data:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to submit expense data');
    }
  };

  return (
    <div>
      <div className="flex justify-between mb-4">
        <Button onClick={onAddRow} variant="outline" className="flex items-center gap-2">
          <PlusIcon className="w-4 h-4" /> Add Row
        </Button>
      </div>

      {/* Grant selection has been removed as it will be assigned during registration */}

      {/* Responsive table with horizontal scrolling */}
      <div className="rounded-lg border shadow-sm overflow-x-auto">
        <table className="w-full border-collapse bg-white text-sm">
          <thead>
            <tr className="bg-gray-100">
              <th className="p-3 border text-left font-semibold">Sr</th>
              <th className="p-3 border text-left font-semibold">Particulars</th>
              <th className="p-3 border text-left font-semibold">Main header</th>
              <th className="p-3 border text-left font-semibold">Sub-headers</th>
              <th className="p-3 border text-left font-semibold">Units</th>
              <th className="p-3 border text-left font-semibold">Freq.</th>
              <th className="p-3 border text-left font-semibold">Cost/unit</th>
              <th className="p-3 border text-center font-semibold" colSpan={4}>Budget quarterly</th>
              <th className="p-3 border text-center font-semibold">Total</th>
              <th className="p-3 border text-center font-semibold" colSpan={4}>Actuals quarterly</th>
              <th className="p-3 border text-center font-semibold">Total</th>
              <th className="p-3 border text-center font-semibold">File</th>
            </tr>
            <tr className="bg-gray-50">
              <th className="p-3 border" colSpan={7}></th>
              <th className="p-3 border text-center">Q1</th>
              <th className="p-3 border text-center">Q2</th>
              <th className="p-3 border text-center">Q3</th>
              <th className="p-3 border text-center">Q4</th>
              <th className="p-3 border"></th>
              <th className="p-3 border text-center">Q1</th>
              <th className="p-3 border text-center">Q2</th>
              <th className="p-3 border text-center">Q3</th>
              <th className="p-3 border text-center">Q4</th>
              <th className="p-3 border"></th>
            </tr>
          </thead>
          <tbody>
            {displayRows.map((row, index) => (
              <tr key={index} className="hover:bg-gray-50">
                <td className="p-3 border">{row.sr_no}</td>
                <td className="p-3 border">
                  <div className="relative">
                    <Input
                      type="text"
                      value={row.particulars}
                      onChange={(e) => onInputChange(index, 'particulars', e.target.value)}
                      className={`w-full min-w-[120px] h-9 text-base px-3 ${errors[`${index}-particulars`] ? 'border-red-500' : ''}`}
                    />
                    {errors[`${index}-particulars`] && (
                      <div className="absolute -bottom-4 left-0 text-xs text-red-500">
                        {errors[`${index}-particulars`]}
                      </div>
                    )}
                  </div>
                </td>
                <td className="p-2 border">
                  <div className="relative">
                    <Input
                      type="text"
                      value={row.main_header}
                      onChange={(e) => onInputChange(index, 'main_header', e.target.value)}
                      className={`w-full min-w-[120px] h-9 text-base px-3 ${errors[`${index}-main_header`] ? 'border-red-500' : ''}`}
                    />
                    {errors[`${index}-main_header`] && (
                      <div className="absolute -bottom-4 left-0 text-xs text-red-500">
                        {errors[`${index}-main_header`]}
                      </div>
                    )}
                  </div>
                </td>
                <td className="p-2 border">
                  <div className="relative">
                    <Input
                      type="text"
                      value={row.sub_headers}
                      onChange={(e) => onInputChange(index, 'sub_headers', e.target.value)}
                      className={`w-full min-w-[120px] h-9 text-base px-3 ${errors[`${index}-sub_headers`] ? 'border-red-500' : ''}`}
                    />
                    {errors[`${index}-sub_headers`] && (
                      <div className="absolute -bottom-4 left-0 text-xs text-red-500">
                        {errors[`${index}-sub_headers`]}
                      </div>
                    )}
                  </div>
                </td>
                <td className="p-2 border">
                  <Input
                    type="text"
                    value={row.units || ''}
                    onChange={(e) => onInputChange(index, 'units', e.target.value)}
                    className="w-full min-w-[80px] h-9 text-base px-3"
                  />
                </td>
                <td className="p-2 border">
                  <Input
                    type="text"
                    value={row.frequency || ''}
                    onChange={(e) => onInputChange(index, 'frequency', e.target.value)}
                    className="w-full min-w-[80px] h-9 text-base px-3"
                  />
                </td>
                <td className="p-2 border">
                  <Input
                    type="number"
                    value={row.cost_per_unit || ''}
                    onChange={(e) => onInputChange(index, 'cost_per_unit', parseFloat(e.target.value))}
                    className="w-full min-w-[80px] h-9 text-base px-3 text-right"
                  />
                </td>
                {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                  <td key={`budget_${quarter}`} className="p-3 border">
                    <Input
                      type="number"
                      value={row.budget_quarterly[quarter as keyof typeof row.budget_quarterly]}
                      onChange={(e) => onInputChange(index, `budget_quarterly.${quarter}`, e.target.value)}
                      className="w-full min-w-[80px] h-9 text-base px-3 text-right"
                    />
                  </td>
                ))}
                <td className="p-3 border text-center font-medium text-base">
                  {row.total_budget || Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0)}
                </td>
                {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                  <td key={`actual_${quarter}`} className="p-3 border">
                    <Input
                      type="number"
                      value={row.actuals_quarterly[quarter as keyof typeof row.actuals_quarterly]}
                      onChange={(e) => onInputChange(index, `actuals_quarterly.${quarter}`, e.target.value)}
                      className="w-full min-w-[80px] h-9 text-base px-3 text-right"
                    />
                  </td>
                ))}
                <td className="p-3 border text-center font-medium text-base">
                  {row.total_actual || Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0)}
                </td>
                <td className="p-3 border text-center">
                  <label className="cursor-pointer">
                    <input
                      type="file"
                      className="hidden"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          // Cast file to any to avoid TypeScript error
                          onInputChange(index, 'receipt', file as unknown as string);
                        }
                      }}
                      accept=".pdf,.jpg,.jpeg,.png"
                    />
                    <div className="bg-gray-100 hover:bg-gray-200 text-gray-600 text-xs py-1 px-2 rounded">
                      {row.receipt ? 'Change' : 'Upload'}
                    </div>
                  </label>
                  {row.receipt && (
                    <div className="text-xs text-gray-600 mt-1 truncate max-w-[80px]" title={row.receipt.name}>
                      {row.receipt.name.length > 10 ? row.receipt.name.substring(0, 10) + '...' : row.receipt.name}
                    </div>
                  )}
                </td>
              </tr>
            ))}
            <tr className="bg-gray-100 font-bold text-base">
              <td className="p-3 border" colSpan={7}>Total</td>
              {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                <td key={`total_budget_${quarter}`} className="p-3 border text-right">
                  {calculateTotal(`budget_quarterly.${quarter}`)}
                </td>
              ))}
              <td className="p-3 border text-right">
                {displayRows.reduce((total, row) => total + (row.total_budget || Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0)), 0)}
              </td>
              {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                <td key={`total_actual_${quarter}`} className="p-3 border text-right">
                  {calculateTotal(`actuals_quarterly.${quarter}`)}
                </td>
              ))}
              <td className="p-3 border text-right">
                {displayRows.reduce((total, row) => total + (row.total_actual || Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0)), 0)}
              </td>
              <td className="p-3 border"></td>
            </tr>
          </tbody>
        </table>
      </div>
      <div className="flex justify-end mt-4">
        <Button onClick={handleSubmit} className="bg-orange-500 hover:bg-orange-600 text-white">
          Submit
        </Button>
      </div>
    </div>
  );
}
