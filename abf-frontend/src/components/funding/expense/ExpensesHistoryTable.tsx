'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  fetchAllExpenses,
} from '@/services/expenses-service';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import {
  FileText,
  Filter,
  Eye,
  X,
} from 'lucide-react';

const COLORS = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEEAD', '#D4A5A5'];


interface ExpensesHistoryTableProps {
  refreshTrigger: string;
  selectedGrant: string;
}

export default function ExpensesHistoryTable({ refreshTrigger, selectedGrant }: ExpensesHistoryTableProps) {
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(true);
  const [expenses, setExpenses] = useState([]);
  const [expenseHistory, setExpenseHistory] = useState([]);
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);

  const [historyFilters, setHistoryFilters] = useState({
    date: '',
    header: '',
    totalBudget: '',
    totalActual: '',
    status: '',
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(value);
  };

  // Shared data loading function
  const loadExpensesData = async () => {
    setIsLoading(true);
    try {
      const expensesData = await fetchAllExpenses();
      setExpenses(expensesData);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    console.log("expensePage | refreshTrigger", refreshTrigger);
    loadExpensesData();
  }, [refreshTrigger]);

  useEffect(() => {
    console.log("expensePage | Loading data for first time");
    loadExpensesData();
  }, []);
  


  useEffect(() => {
    let filtered = expenses;

    console.log("expensePage | selectedGrant", selectedGrant);
    if (selectedGrant !== 'All Grants') {
      console.log("expensePage | selectedGrant", selectedGrant);
      filtered = filtered.filter((expense: any) => String(expense.grant) === String(selectedGrant));
    }

    let history = filtered.map((expense: any) => ({
      id: expense.id,
      loggedDate: expense.expense_date || 'N/A',
      category:
        (expense.main_header || expense.particulars || 'N/A').charAt(0).toUpperCase() +
        (expense.main_header || expense.particulars || 'N/A').slice(1),
      totalBudget: parseFloat(expense.total_budget || 0),
      totalActualSpent: parseFloat(expense.total_actual || 0),
      status:
        expense.status.charAt(0).toUpperCase() + expense.status.slice(1).toLowerCase(),
    }));

    history = history.filter((expense: any) => {
      const matchesDate =
        !historyFilters.date ||
        expense.loggedDate.toLowerCase().includes(historyFilters.date.toLowerCase());
      const matchesHeader =
        !historyFilters.header ||
        expense.category.toLowerCase().includes(historyFilters.header.toLowerCase());
      const matchesTotalBudget =
        !historyFilters.totalBudget ||
        expense.totalBudget.toString().includes(historyFilters.totalBudget);
      const matchesTotalActual =
        !historyFilters.totalActual ||
        expense.totalActualSpent.toString().includes(historyFilters.totalActual);
      const matchesStatus =
        !historyFilters.status ||
        expense.status.toLowerCase().includes(historyFilters.status.toLowerCase());
      return (
        matchesDate &&
        matchesHeader &&
        matchesTotalBudget &&
        matchesTotalActual &&
        matchesStatus
      );
    });

    setExpenseHistory(history);

  }, [selectedGrant, expenses, historyFilters]);

  const handleHistoryFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setHistoryFilters((prev) => ({ ...prev, [name]: value }));
  };

  const resetFilters = () => {
    setHistoryFilters({
      date: '',
      header: '',
      totalBudget: '',
      totalActual: '',
      status: '',
    });
    setShowFilterDropdown(false);
  };

  const getStatusStyles = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-gradient-to-r from-amber-100 to-orange-100 text-amber-800 border border-amber-200';
      case 'approved':
        return 'bg-gradient-to-r from-teal-100 to-emerald-100 text-teal-800 border border-teal-200';
      case 'rejected':
        return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border border-red-200';
      default:
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <div className="h-8 w-48 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 w-64 bg-gray-200 rounded animate-pulse mt-2"></div>
          </div>
          <div className="h-10 w-40 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <div className="h-10 w-48 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-10 w-64 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <div className="h-6 w-48 bg-gray-200 rounded animate-pulse mb-4"></div>
            <div className="h-80 bg-gray-200 rounded animate-pulse"></div>
          </div>
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <div className="h-6 w-48 bg-gray-200 rounded animate-pulse mb-4"></div>
            <div className="h-80 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="h-6 w-48 bg-gray-200 rounded animate-pulse mb-4"></div>
          <div className="h-96 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        <Card className="rounded-3xl shadow-2xl bg-gradient-to-br from-white via-slate-50/80 to-teal-50/40 border border-teal-200/30 backdrop-blur-sm overflow-hidden">
          <CardContent className="p-6">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h3 className="text-xl font-bold text-gray-900">Expense History</h3>
                <p className="text-sm text-teal-600 font-medium">View and manage expense records</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2 bg-white/80 hover:bg-teal-50 border-teal-200 text-teal-700 hover:text-teal-800 font-medium transition-all duration-200"
                onClick={() => setShowFilterDropdown(!showFilterDropdown)}
              >
                <Filter className="h-4 w-4" />
                {showFilterDropdown ? 'Hide Filters' : 'Show Filters'}
              </Button>
            </div>
            {showFilterDropdown && (
              <motion.div
                className="bg-gradient-to-r from-teal-50/50 to-emerald-50/50 p-6 rounded-2xl mb-6 border border-teal-200/40"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                  <div>
                    <label className="text-sm font-semibold text-teal-700 mb-2 block">Date</label>
                    <input
                      type="text"
                      name="date"
                      value={historyFilters.date}
                      onChange={handleHistoryFilterChange}
                      placeholder="YYYY-MM-DD"
                      className="w-full px-4 py-2.5 text-sm border border-teal-200 rounded-xl bg-white/80 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-all duration-200"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-semibold text-teal-700 mb-2 block">Header</label>
                    <input
                      type="text"
                      name="header"
                      value={historyFilters.header}
                      onChange={handleHistoryFilterChange}
                      placeholder="Filter by header"
                      className="w-full px-4 py-2.5 text-sm border border-teal-200 rounded-xl bg-white/80 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-all duration-200"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-semibold text-teal-700 mb-2 block">Total Budget</label>
                    <input
                      type="number"
                      name="totalBudget"
                      value={historyFilters.totalBudget}
                      onChange={handleHistoryFilterChange}
                      placeholder="Filter by budget"
                      className="w-full px-4 py-2.5 text-sm border border-teal-200 rounded-xl bg-white/80 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-all duration-200"
                      min="0"
                      step="0.01"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-semibold text-teal-700 mb-2 block">Total Actual</label>
                    <input
                      type="number"
                      name="totalActual"
                      value={historyFilters.totalActual}
                      onChange={handleHistoryFilterChange}
                      placeholder="Filter by actual"
                      className="w-full px-4 py-2.5 text-sm border border-teal-200 rounded-xl bg-white/80 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-all duration-200"
                      min="0"
                      step="0.01"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-semibold text-teal-700 mb-2 block">Status</label>
                    <select
                      name="status"
                      value={historyFilters.status}
                      onChange={handleHistoryFilterChange}
                      className="w-full px-4 py-2.5 text-sm border border-teal-200 rounded-xl bg-white/80 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-all duration-200"
                    >
                      <option value="">All Statuses</option>
                      <option value="Pending">Pending</option>
                      <option value="Approved">Approved</option>
                      <option value="Rejected">Rejected</option>
                    </select>
                  </div>
                </div>
                <div className="flex justify-end mt-6">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={resetFilters}
                    className="flex items-center gap-2 bg-white/80 hover:bg-red-50 border-red-200 text-red-700 hover:text-red-800 font-medium transition-all duration-200"
                  >
                    <X className="h-4 w-4" />
                    Clear Filters
                  </Button>
                </div>
              </motion.div>
            )}
            <div className="overflow-x-auto rounded-2xl border border-teal-200/30">
              <table className="w-full text-sm border-collapse">
                <thead className="sticky top-0 z-20">
                  <tr className="bg-teal-600 text-white border-b border-teal-700">
                    {[
                      { label: 'Sr.', width: 'w-12' },
                      { label: 'Date', width: 'w-28' },
                      { label: 'Header', width: 'w-36' },
                      { label: 'Total Budget', width: 'w-28' },
                      { label: 'Total Actual', width: 'w-28' },
                      { label: 'Status', width: 'w-24' },
                      { label: '', width: 'w-16' },
                    ].map((head, idx) => (
                      <th key={idx} className={`${head.width} px-4 py-4 font-semibold text-sm text-white text-center border-r border-teal-500/30 last:border-r-0`}>
                        <div className="flex items-center justify-center">
                          <span>{head.label}</span>
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white">
                  {expenseHistory.length > 0 ? (
                    expenseHistory.map((expense: any, index: number) => (
                      <motion.tr
                        key={expense.id}
                        className="group transition-all duration-200 border-b border-gray-200/60 bg-white hover:bg-gradient-to-r hover:from-teal-50/40 hover:via-white hover:to-emerald-50/30 cursor-pointer"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: index * 0.05 }}
                        onClick={() => router.push(`/funding/expenses/detail-view/${expense.id}`)}
                      >
                        <td className="w-12 px-3 py-4 text-center border-r border-gray-200/40">
                          <div className="inline-flex items-center justify-center w-7 h-7 bg-gradient-to-br from-teal-600 to-emerald-600 text-white text-xs font-bold rounded-full shadow-sm">
                            {String(index + 1).padStart(2, '0')}
                          </div>
                        </td>
                        <td className="w-28 px-3 py-4 border-r border-gray-200/40">
                          <div className="font-medium text-gray-900 text-xs">
                            {expense.loggedDate}
                          </div>
                        </td>
                        <td className="w-36 px-3 py-4 border-r border-gray-200/40">
                          <div className="font-semibold text-gray-900 text-xs leading-tight">
                            {expense.category}
                          </div>
                        </td>
                        <td className="w-28 px-3 py-4 text-center border-r border-gray-200/40">
                          <div className="bg-gradient-to-r from-teal-50 to-emerald-50 px-2 py-1.5 rounded-lg border border-teal-200/50">
                            <div className="text-xs font-bold text-teal-700">
                              {formatCurrency(expense.totalBudget)}
                            </div>
                          </div>
                        </td>
                        <td className="w-28 px-3 py-4 text-center border-r border-gray-200/40">
                          <div className="bg-gradient-to-r from-emerald-50 to-teal-50 px-2 py-1.5 rounded-lg border border-emerald-200/50">
                            <div className="text-xs font-bold text-emerald-700">
                              {formatCurrency(expense.totalActualSpent)}
                            </div>
                          </div>
                        </td>
                        <td className="w-24 px-3 py-4 text-center border-r border-gray-200/40">
                          <span
                            className={`px-2 py-1 rounded-xl text-xs font-semibold ${getStatusStyles(expense.status)}`}
                          >
                            {expense.status}
                          </span>
                        </td>
                        <td className="w-16 px-3 py-4 text-center">
                          <div className="flex items-center justify-center">
                            <div className="p-1.5 rounded-full bg-teal-100 text-teal-600 hover:bg-teal-200 hover:text-teal-700 transition-all duration-200">
                              <Eye className="h-3 w-3" />
                            </div>
                          </div>
                        </td>
                      </motion.tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={7} className="p-12 text-center">
                        <div className="flex flex-col items-center justify-center space-y-4">
                          <div className="w-16 h-16 bg-gradient-to-br from-teal-100 to-emerald-100 rounded-full flex items-center justify-center">
                            <FileText className="h-8 w-8 text-teal-600" />
                          </div>
                          <div>
                            <p className="text-lg font-semibold text-gray-900 mb-1">No expense records found</p>
                            <p className="text-sm text-gray-500">Start by adding your first expense record</p>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}


