import { EditHistoryLog } from "@/types/expenses";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";

export default function HistoryModal({
  isOpen,
  onClose,
  history,
  title,
}: {
  isOpen: boolean;
  onClose: () => void;
  history: EditHistoryLog[];
  title: string;
}) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50">
      <div className="bg-white rounded-xl shadow-xl p-6 w-full max-w-2xl max-h-[80vh] flex flex-col space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-900">{title}</h2>
          <Button variant="ghost" onClick={onClose} className="p-1">
            <X className="w-5 h-5 text-gray-600" />
          </Button>
        </div>
        {history.length === 0 ? (
          <p className="text-gray-700 text-sm">No edit history available.</p>
        ) : (
          <div className="overflow-y-auto max-h-[60vh]">
            <table className="min-w-full text-sm text-left border-collapse">
              <thead className="bg-gray-50 text-gray-700 sticky top-0">
                <tr>
                  <th className="px-4 py-2 font-semibold">Row ID</th>
                  <th className="px-4 py-2 font-semibold">Field</th>
                  <th className="px-4 py-2 font-semibold">Old Value</th>
                  <th className="px-4 py-2 font-semibold">New Value</th>
                  <th className="px-4 py-2 font-semibold">Timestamp</th>
                </tr>
              </thead>
              <tbody className="text-gray-800">
                {history.map((log, index) => (
                  <tr key={index} className="border-t">
                    <td className="px-4 py-2">{log.row_id}</td>
                    <td className="px-4 py-2">{log.field}</td>
                    <td className="px-4 py-2">{log.old_value || 'N/A'}</td>
                    <td className="px-4 py-2">{log.new_value || 'N/A'}</td>
                    <td className="px-4 py-2">{new Date(log.timestamp).toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        <div className="flex justify-end mt-auto">
          <Button
            onClick={onClose}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  );
}

