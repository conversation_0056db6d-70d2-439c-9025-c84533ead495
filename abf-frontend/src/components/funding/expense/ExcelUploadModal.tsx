import { useState, useRef, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { X, Upload, FileSpreadsheet, CheckCircle, Sparkles, Download } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface ExcelUploadModalProps {
    isOpen: boolean;
    onClose: () => void;
    onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

export default function ExcelUploadModal({ isOpen, onClose, onFileUpload }: ExcelUploadModalProps) {
    const [isDragOver, setIsDragOver] = useState(false);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadSuccess, setUploadSuccess] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleDragOver = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(true);
    }, []);

    const handleDragLeave = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);
    }, []);

    const handleDrop = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                setSelectedFile(file);
                // Create a synthetic event for the onFileUpload callback
                const syntheticEvent = {
                    target: { files: [file] } as any
                } as React.ChangeEvent<HTMLInputElement>;
                onFileUpload(syntheticEvent);
            }
        }
    }, [onFileUpload]);

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setSelectedFile(file);
            onFileUpload(event);
        }
    };

    const handleUploadClick = () => {
        if (selectedFile) {
            setIsUploading(true);
            setTimeout(() => {
                setIsUploading(false);
                setUploadSuccess(true);
                setTimeout(() => {
                    onClose();
                    setUploadSuccess(false);
                    setSelectedFile(null);
                }, 1500);
            }, 2000);
        }
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    if (!isOpen) return null;

    return (
        <AnimatePresence>
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 flex items-center justify-center bg-black/60 backdrop-blur-md z-50"
                onClick={onClose}
            >
                <motion.div
                    initial={{ scale: 0.9, opacity: 0, y: 20 }}
                    animate={{ scale: 1, opacity: 1, y: 0 }}
                    exit={{ scale: 0.9, opacity: 0, y: 20 }}
                    transition={{ type: "spring", duration: 0.5 }}
                    className="bg-white rounded-3xl shadow-2xl w-full max-w-md mx-4 overflow-hidden"
                    onClick={(e) => e.stopPropagation()}
                >
                    {/* Header with gradient */}
                    <div className="relative bg-gradient-to-r from-teal-600 via-teal-700 to-emerald-600 p-4 text-white">
                        <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
                        <div className="relative z-10 flex justify-between items-center">
                            <div className="flex items-center gap-2">
                                <div className="p-1.5 bg-white/20 rounded-lg backdrop-blur-sm">
                                    <FileSpreadsheet className="w-5 h-5 text-white" />
                                </div>
                                <div>
                                    <h2 className="text-lg font-bold tracking-tight">Upload Excel File</h2>
                                    <p className="text-teal-100 text-xs mt-0.5">Import your expense data</p>
                                </div>
                            </div>
                            <Button
                                variant="ghost"
                                onClick={onClose}
                                className="p-1.5 hover:bg-white/20 rounded-full transition-colors"
                            >
                                <X className="w-5 h-5 text-white" />
                            </Button>
                        </div>

                        {/* Decorative elements */}
                        <div className="absolute top-2 right-12 w-12 h-12 bg-white/10 rounded-full blur-lg"></div>
                        <div className="absolute bottom-2 left-12 w-8 h-8 bg-emerald-300/20 rounded-full blur-md"></div>
                    </div>

                   
                    <div className="p-4 space-y-4">
                      
                        <div
                            className={`relative border-2 border-dashed rounded-xl p-6 text-center transition-all duration-300 ${
                                isDragOver
                                    ? 'border-teal-400 bg-gradient-to-br from-teal-50 to-emerald-50 scale-105'
                                    : selectedFile
                                        ? 'border-emerald-400 bg-gradient-to-br from-emerald-50 to-teal-50'
                                        : 'border-gray-300 bg-gradient-to-br from-gray-50 to-white hover:border-teal-300 hover:bg-gradient-to-br hover:from-teal-50 hover:to-emerald-50'
                            }`}
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDrop}
                        >
                            <input
                                ref={fileInputRef}
                                type="file"
                                accept=".xlsx,.xls"
                                onChange={handleFileSelect}
                                className="hidden"
                            />

                            <AnimatePresence mode="wait">
                                {selectedFile ? (
                                    <motion.div
                                        key="file-selected"
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, y: -10 }}
                                        className="space-y-4"
                                    >
                                        <div className="flex items-center justify-center">
                                            <div className="p-2 bg-emerald-100 rounded-full">
                                                <CheckCircle className="w-8 h-8 text-emerald-600" />
                                            </div>
                                        </div>
                                        <div>
                                            <h3 className="text-sm font-semibold text-gray-900">{selectedFile.name}</h3>
                                            <p className="text-xs text-gray-500 mt-0.5">
                                                {formatFileSize(selectedFile.size)} • Excel
                                            </p>
                                        </div>
                                        <div className="flex items-center justify-center gap-1 text-emerald-600">
                                            <Sparkles className="w-3 h-3" />
                                            <span className="text-xs font-medium">Ready</span>
                                        </div>
                                    </motion.div>
                                ) : (
                                    <motion.div
                                        key="no-file"
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, y: -10 }}
                                        className="space-y-3"
                                    >
                                        <div className="flex items-center justify-center">
                                            <div className={`p-3 rounded-full transition-all duration-300 ${
                                                isDragOver
                                                    ? 'bg-teal-100 scale-110'
                                                    : 'bg-gray-100'
                                            }`}>
                                                <Upload className={`w-10 h-10 transition-colors duration-300 ${
                                                    isDragOver ? 'text-teal-600' : 'text-gray-400'
                                                }`} />
                                            </div>
                                        </div>
                                        <div>
                                            <h3 className="text-base font-semibold text-gray-900 mb-1">
                                                {isDragOver ? 'Drop file here' : 'Upload Excel'}
                                            </h3>
                                            <p className="text-gray-500 mb-2 text-xs">
                                                Drag & drop or click to browse
                                            </p>
                                            <Button
                                                onClick={() => fileInputRef.current?.click()}
                                                className="bg-gradient-to-r from-teal-600 to-emerald-600 hover:from-teal-700 hover:to-emerald-700 text-white px-4 py-2 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 text-sm"
                                            >
                                                <FileSpreadsheet className="w-3 h-3 mr-1" />
                                                Choose File
                                            </Button>
                                        </div>
                                        <div className="text-xs text-gray-400 space-y-1">
                                            <p>Supported formats: .xlsx, .xls</p>
                                        
                                        </div>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </div>

                        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-3 border border-blue-200/50">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <div className="p-1.5 bg-blue-100 rounded-md">
                                        <Download className="w-4 h-4 text-blue-600" />
                                    </div>
                                    <div>
                                        <h4 className="font-semibold text-gray-900 text-xs">Need a Excel Template?</h4>
                                        <p className="text-xs text-gray-600">Download Template</p>
                                    </div>
                                </div>
                                <Button
                                    variant="outline"
                                    className="border-blue-300 text-blue-700 hover:bg-blue-100 rounded-md text-xs px-2 py-1.5"
                                >
                                    <Download className="w-3 h-3 mr-1" />
                                    Download
                                </Button>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex justify-end gap-2 pt-2">
                            <Button
                                onClick={onClose}
                                variant="outline"
                                className="px-4 py-2 rounded-lg border-gray-300 text-gray-700 hover:bg-gray-50 font-semibold transition-all duration-300 text-xs"
                            >
                                Cancel
                            </Button>
                            <Button
                                onClick={handleUploadClick}
                                disabled={!selectedFile || isUploading}
                                className={`px-4 py-2 rounded-lg font-semibold transition-all duration-300 transform text-xs ${
                                    selectedFile && !isUploading
                                        ? 'bg-gradient-to-r from-teal-600 to-emerald-600 hover:from-teal-700 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl hover:scale-105'
                                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                }`}
                            >
                                {isUploading ? (
                                    <div className="flex items-center gap-1">
                                        <div className="w-3 h-3 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                        Uploading...
                                    </div>
                                ) : uploadSuccess ? (
                                    <div className="flex items-center gap-1">
                                        <CheckCircle className="w-3 h-3" />
                                        Success!
                                    </div>
                                ) : (
                                    <div className="flex items-center gap-1">
                                        <Upload className="w-3 h-3" />
                                        Upload
                                    </div>
                                )}
                            </Button>
                        </div>
                    </div>

                    {/* Success Animation Overlay */}
                    <AnimatePresence>
                        {uploadSuccess && (
                            <motion.div
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0 }}
                                className="absolute inset-0 bg-gradient-to-br from-emerald-500/90 to-teal-600/90 backdrop-blur-sm flex items-center justify-center"
                            >
                                <motion.div
                                    initial={{ scale: 0, rotate: -180 }}
                                    animate={{ scale: 1, rotate: 0 }}
                                    transition={{ type: "spring", duration: 0.8 }}
                                    className="text-center text-white"
                                >
                                    <div className="p-4 bg-white/20 rounded-full mb-4 mx-auto w-fit">
                                        <CheckCircle className="w-12 h-12" />
                                    </div>
                                    <h3 className="text-xl font-bold mb-2">Upload Successful!</h3>
                                    <p className="text-emerald-100 text-sm">File processed successfully</p>
                                </motion.div>
                            </motion.div>
                        )}
                    </AnimatePresence>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
}