export default function RemarksInput({
  value,
  onChange,
  rowId,
  hasError = false,
  errorMessage = '',
}: {
  value: string;
  onChange: (rowId: number, value: string) => void;
  rowId: number;
  hasError?: boolean;
  errorMessage?: string;
}) {
  return (

    <div className="space-y-4">

      <div
        className={`rounded-xl bg-white border transition-all duration-200 ${
          hasError
            ? 'border-red-300 bg-red-50/50'
            : 'border-gray-200 hover:border-gray-300 focus-within:border-teal-400 focus-within:shadow-sm focus-within:shadow-teal-200/30'
        }`}
      >

        <div className="p-4 space-y-3">
          <label className={`block text-sm font-semibold ${
            hasError ? 'text-red-600' : 'text-gray-700'
          }`}>
            Remarks
          </label>
          <textarea
            value={value}
            onChange={(e) => {
              console.log(`Remarks input for row ${rowId}:`, e.target.value);
              onChange(rowId, e.target.value);
            }}
            className={`w-full bg-transparent text-sm text-gray-800 focus:outline-none placeholder:text-gray-400 resize-none leading-relaxed ${
              hasError ? 'text-red-600' : 'focus:text-gray-900'

            }`}
            placeholder="Enter any remarks or comments regarding this expense item..."
            rows={3}
          />
          {hasError && (
            <div className="pt-3 border-t border-red-200">
              <p className="text-red-600 text-sm font-medium flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-red-500 rounded-full mt-1.5 flex-shrink-0"></span>
                <span>{errorMessage}</span>
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

