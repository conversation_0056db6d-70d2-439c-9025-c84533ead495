import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileSpreadsheet, Download, X } from 'lucide-react';
import { fundingService } from '@/services/funding-service';

interface ExcelAttachmentViewerProps {
  attachment: string;
  onClose: () => void;
  data?: any[];
}

export function ExcelAttachmentViewer({ attachment, data, onClose }: ExcelAttachmentViewerProps) {
  // Use provided data or fallback to mock data
  const excelData = data || [
    {
      particulars: 'Program Supplies',
      main_header: 'Programs',
      sub_headers: 'Materials',
      budget_quarterly: { Q1: 15000, Q2: 15000, Q3: 15000, Q4: 15000 },
      actuals_quarterly: { Q1: 14500, Q2: 14800, Q3: 14700, Q4: 14000 }
    },
    {
      particulars: 'Training Materials',
      main_header: 'Programs',
      sub_headers: 'Training',
      budget_quarterly: { Q1: 10000, Q2: 10000, Q3: 10000, Q4: 10000 },
      actuals_quarterly: { Q1: 9800, Q2: 9900, Q3: 9700, Q4: 9600 }
    }
  ];

  // Calculate totals for each column
  const totals = {
    budget: {
      Q1: 0,
      Q2: 0,
      Q3: 0,
      Q4: 0,
      total: 0
    },
    actual: {
      Q1: 0,
      Q2: 0,
      Q3: 0,
      Q4: 0,
      total: 0
    }
  };

  // Calculate totals
  excelData.forEach(row => {
    totals.budget.Q1 += row.budget_quarterly?.Q1 || 0;
    totals.budget.Q2 += row.budget_quarterly?.Q2 || 0;
    totals.budget.Q3 += row.budget_quarterly?.Q3 || 0;
    totals.budget.Q4 += row.budget_quarterly?.Q4 || 0;

    totals.actual.Q1 += row.actuals_quarterly?.Q1 || 0;
    totals.actual.Q2 += row.actuals_quarterly?.Q2 || 0;
    totals.actual.Q3 += row.actuals_quarterly?.Q3 || 0;
    totals.actual.Q4 += row.actuals_quarterly?.Q4 || 0;
  });

  // Calculate total budget and actual
  totals.budget.total = totals.budget.Q1 + totals.budget.Q2 + totals.budget.Q3 + totals.budget.Q4;
  totals.actual.total = totals.actual.Q1 + totals.actual.Q2 + totals.actual.Q3 + totals.actual.Q4;

  // Function to handle download (in a real implementation, this would download the actual file)
  const handleDownload = () => {
    // In a real implementation, this would trigger a download of the Excel file
    alert('Downloading file: ' + attachment);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto shadow-lg">
      <CardHeader className="flex flex-row items-center justify-between bg-gray-50 border-b">
        <div className="flex items-center gap-2">
          <FileSpreadsheet className="h-5 w-5 text-green-600" />
          <CardTitle className="text-lg">{attachment}</CardTitle>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="text-blue-600 hover:text-blue-800"
            onClick={handleDownload}
          >
            <Download className="h-4 w-4 mr-1" />
            Download
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full border-collapse bg-white text-sm">
            <thead>
              <tr className="bg-gray-100">
                <th className="p-3 border text-left font-semibold">Sr no.</th>
                <th className="p-3 border text-left font-semibold">Particulars</th>
                <th className="p-3 border text-left font-semibold">Main Header</th>
                <th className="p-3 border text-left font-semibold">Sub-Headers</th>
                <th className="p-3 border text-left font-semibold">Units</th>
                <th className="p-3 border text-left font-semibold">Frequency</th>
                <th className="p-3 border text-left font-semibold">Cost per unit</th>
                <th className="p-3 border text-center font-semibold" colSpan={4}>Budget quarterly breakup</th>
                <th className="p-3 border text-center font-semibold">Total Budget</th>
                <th className="p-3 border text-center font-semibold" colSpan={4}>Actuals quarterly breakup</th>
                <th className="p-3 border text-center font-semibold">Total Actual</th>
              </tr>
              <tr className="bg-gray-50">
                <th className="p-3 border" colSpan={7}></th>
                <th className="p-3 border text-center">Q1</th>
                <th className="p-3 border text-center">Q2</th>
                <th className="p-3 border text-center">Q3</th>
                <th className="p-3 border text-center">Q4</th>
                <th className="p-3 border"></th>
                <th className="p-3 border text-center">Q1</th>
                <th className="p-3 border text-center">Q2</th>
                <th className="p-3 border text-center">Q3</th>
                <th className="p-3 border text-center">Q4</th>
                <th className="p-3 border"></th>
              </tr>
            </thead>
            <tbody>
              {excelData && excelData.length > 0 ? (
                <>
                  {excelData.map((row, index) => {
                    // Calculate row totals
                    const rowBudgetTotal = Object.values(row.budget_quarterly || {}).reduce((sum: number, val: any) => sum + (val || 0), 0);
                    const rowActualTotal = Object.values(row.actuals_quarterly || {}).reduce((sum: number, val: any) => sum + (val || 0), 0);

                    return (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="p-3 border">{index + 1}</td>
                        <td className="p-3 border">{row.particulars}</td>
                        <td className="p-3 border">{row.main_header}</td>
                        <td className="p-3 border">{row.sub_headers}</td>
                        <td className="p-3 border">{row.units || '-'}</td>
                        <td className="p-3 border">{row.frequency || '-'}</td>
                        <td className="p-3 border text-right">{row.cost_per_unit ? fundingService.formatCurrency(row.cost_per_unit) : '-'}</td>
                        <td className="p-3 border text-right">{fundingService.formatCurrency(row.budget_quarterly?.Q1 || 0)}</td>
                        <td className="p-3 border text-right">{fundingService.formatCurrency(row.budget_quarterly?.Q2 || 0)}</td>
                        <td className="p-3 border text-right">{fundingService.formatCurrency(row.budget_quarterly?.Q3 || 0)}</td>
                        <td className="p-3 border text-right">{fundingService.formatCurrency(row.budget_quarterly?.Q4 || 0)}</td>
                        <td className="p-3 border text-right font-medium">{fundingService.formatCurrency(rowBudgetTotal)}</td>
                        <td className="p-3 border text-right">{fundingService.formatCurrency(row.actuals_quarterly?.Q1 || 0)}</td>
                        <td className="p-3 border text-right">{fundingService.formatCurrency(row.actuals_quarterly?.Q2 || 0)}</td>
                        <td className="p-3 border text-right">{fundingService.formatCurrency(row.actuals_quarterly?.Q3 || 0)}</td>
                        <td className="p-3 border text-right">{fundingService.formatCurrency(row.actuals_quarterly?.Q4 || 0)}</td>
                        <td className="p-3 border text-right font-medium">{fundingService.formatCurrency(rowActualTotal)}</td>
                      </tr>
                    );
                  })}

                  {/* Totals row */}
                  <tr className="bg-gray-100 font-semibold">
                    <td colSpan={7} className="p-3 border text-right">Total</td>
                    <td className="p-3 border text-right">{fundingService.formatCurrency(totals.budget.Q1)}</td>
                    <td className="p-3 border text-right">{fundingService.formatCurrency(totals.budget.Q2)}</td>
                    <td className="p-3 border text-right">{fundingService.formatCurrency(totals.budget.Q3)}</td>
                    <td className="p-3 border text-right">{fundingService.formatCurrency(totals.budget.Q4)}</td>
                    <td className="p-3 border text-right">{fundingService.formatCurrency(totals.budget.total)}</td>
                    <td className="p-3 border text-right">{fundingService.formatCurrency(totals.actual.Q1)}</td>
                    <td className="p-3 border text-right">{fundingService.formatCurrency(totals.actual.Q2)}</td>
                    <td className="p-3 border text-right">{fundingService.formatCurrency(totals.actual.Q3)}</td>
                    <td className="p-3 border text-right">{fundingService.formatCurrency(totals.actual.Q4)}</td>
                    <td className="p-3 border text-right">{fundingService.formatCurrency(totals.actual.total)}</td>
                  </tr>
                </>
              ) : (
                <tr>
                  <td colSpan={17} className="p-8 text-center text-gray-500">
                    No data available in this Excel file.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}