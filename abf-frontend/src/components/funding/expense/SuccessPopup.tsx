import { Button } from "@/components/ui/button";
import { X, CheckCir<PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface SuccessPopupProps {
    successPopup: string | null;
    setSuccessPopup: (message: string | null) => void;
}

export default function SuccessPopup({ successPopup, setSuccessPopup }: SuccessPopupProps) {
    if (!successPopup) return null;

    return (
        <AnimatePresence>
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 flex items-center justify-center bg-black/60 backdrop-blur-md z-50 p-4"
                onClick={() => setSuccessPopup(null)}
            >
                <motion.div
                    initial={{ scale: 0.9, opacity: 0, y: 20 }}
                    animate={{ scale: 1, opacity: 1, y: 0 }}
                    exit={{ scale: 0.9, opacity: 0, y: 20 }}
                    transition={{ type: "spring", duration: 0.5 }}
                    className="bg-white rounded-3xl shadow-2xl w-full max-w-lg overflow-hidden"
                    onClick={(e) => e.stopPropagation()}
                >
                    {/* Header */}
                    <div className="relative bg-teal-600 p-6 text-white">
                        <div className="flex justify-between items-center">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-white/20 rounded-lg">
                                    <CheckCircle className="w-6 h-6 text-white" />
                                </div>
                                <div>
                                    <h2 className="text-xl font-semibold">Success</h2>
                                    <p className="text-white/90 text-sm mt-1">Operation completed successfully</p>
                                </div>
                            </div>
                            <Button
                                variant="ghost"
                                onClick={() => setSuccessPopup(null)}
                                className="p-2 hover:bg-white/20 rounded-lg transition-colors"
                            >
                                <X className="w-5 h-5 text-white" />
                            </Button>
                        </div>
                    </div>

                    {/* Content */}
                    <div className="p-6">
                        <div className="bg-teal-50 border border-teal-200 rounded-lg p-4">
                            <div className="flex items-start gap-3">
                                <CheckCircle className="w-5 h-5 text-teal-600 mt-0.5 flex-shrink-0" />
                                <div className="text-teal-800 text-sm leading-relaxed font-medium">
                                    {successPopup}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Footer */}
                    <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                        <div className="flex justify-end">
                            <Button
                                onClick={() => setSuccessPopup(null)}
                                className="bg-teal-600 hover:bg-teal-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                            >
                                Close
                            </Button>
                        </div>
                    </div>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
}