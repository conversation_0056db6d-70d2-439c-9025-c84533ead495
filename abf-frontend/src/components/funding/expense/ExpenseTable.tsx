import { Fragment, useEffect, useRef } from 'react';
import { ChevronDown, ChevronUp, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import QuarterInlineInput from './QuarterInlineInput';
import RemarksInput from './RemarksInput';

interface ExpenseTableProps {
  updatedExcelData: any[];
  budgetValues: Record<string, number>;
  actualValues: Record<string, number>;
  remarksValues: Record<number, string>;
  rowErrors: Record<number, Record<string, string>>;
  budgetSumErrors: Record<number, Record<string, string>>;
  remarksErrors: Record<number, string>;
  submissionErrors: Record<number, Record<string, string>>;
  totalGrantBudgets: Record<number, number>;
  currentQuarter: number;
  quarters: string[];
  expandedRows: number[];
  editHistory: any[];
  handleBudgetChange: (rowId: number, quarter: string, value: number) => void;
  handleActualChange: (rowId: number, quarter: string, value: number) => void;
  handleRemarksChange: (rowId: number, value: string) => void;
  isBudgetEditable: (quarter: string) => boolean;
  isActualEditable: (quarter: string) => boolean;
  validateNumber: (value: number, label: string) => { isValid: boolean; message: string };
  calculateTotal: (rowId: number, type: 'budget' | 'actual') => string;
  toggleRow: (rowId: number) => void;
  onSubmit?: () => void;
  isSubmitting?: boolean;
}

export default function ExpenseTable({
  updatedExcelData,
  budgetValues,
  actualValues,
  remarksValues,
  rowErrors,
  budgetSumErrors,
  remarksErrors,
  submissionErrors,
  totalGrantBudgets,
  currentQuarter,
  quarters,
  expandedRows,
  editHistory,
  handleBudgetChange,
  handleActualChange,
  handleRemarksChange,
  isBudgetEditable,
  isActualEditable,
  validateNumber,
  calculateTotal,
  toggleRow,
  onSubmit,
  isSubmitting = false,
}: ExpenseTableProps) {
  const expandedRowRefs = useRef<Record<number, HTMLDivElement | null>>({});

  useEffect(() => {
    expandedRows.forEach(rowId => {
      const rowRef = expandedRowRefs.current[rowId];
      if (rowRef) {
        setTimeout(() => {
          rowRef.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
          });
        }, 100);
      }
    });
  }, [expandedRows]);

  // Calculate totals for the top component
  const grandTotals = updatedExcelData.reduce((acc, row) => {
    const rowId = row.id;
    return {
      q1Budget: acc.q1Budget + (budgetValues[`${rowId}-Apr-Jun`] || 0),
      q2Budget: acc.q2Budget + (budgetValues[`${rowId}-Jul-Sep`] || 0),
      q3Budget: acc.q3Budget + (budgetValues[`${rowId}-Oct-Dec`] || 0),
      q4Budget: acc.q4Budget + (budgetValues[`${rowId}-Jan-Mar`] || 0),
      q1Actual: acc.q1Actual + (actualValues[`${rowId}-Apr-Jun`] || 0),
      q2Actual: acc.q2Actual + (actualValues[`${rowId}-Jul-Sep`] || 0),
      q3Actual: acc.q3Actual + (actualValues[`${rowId}-Oct-Dec`] || 0),
      q4Actual: acc.q4Actual + (actualValues[`${rowId}-Jan-Mar`] || 0),
    };
  }, {
    q1Budget: 0, q2Budget: 0, q3Budget: 0, q4Budget: 0,
    q1Actual: 0, q2Actual: 0, q3Actual: 0, q4Actual: 0,
  });

  const totalBudget = grandTotals.q1Budget + grandTotals.q2Budget + grandTotals.q3Budget + grandTotals.q4Budget;
  const totalActual = grandTotals.q1Actual + grandTotals.q2Actual + grandTotals.q3Actual + grandTotals.q4Actual;

  return (
    <div className="mt-8 space-y-6">
      {/* Quarterly Totals Summary - Compact, Cool & Professional */}
      <Card className="rounded-xl shadow-lg bg-gradient-to-br from-white to-teal-50/20 border border-teal-200/50 overflow-hidden">
        <div className="bg-gradient-to-r from-teal-600 to-teal-700 px-4 py-3">
          <h3 className="text-base font-bold text-white text-center">Quarterly Totals</h3>
        </div>

        <div className="p-4 bg-white">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Budget Section */}
            <div className="space-y-3">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-0.5 h-5 bg-teal-600 rounded-full"></div>
                <h4 className="text-sm font-bold text-teal-800">Budget</h4>
              </div>
              <div className="grid grid-cols-4 gap-2">
                <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-2.5 rounded-lg border border-teal-200/60 hover:shadow-md transition-all duration-200">
                  <div className="text-center">
                    <p className="text-xs font-semibold text-teal-600 mb-0.5">Q1</p>
                    <p className="text-sm font-bold text-teal-800">₹{grandTotals.q1Budget.toLocaleString()}</p>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-2.5 rounded-lg border border-teal-200/60 hover:shadow-md transition-all duration-200">
                  <div className="text-center">
                    <p className="text-xs font-semibold text-teal-600 mb-0.5">Q2</p>
                    <p className="text-sm font-bold text-teal-800">₹{grandTotals.q2Budget.toLocaleString()}</p>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-2.5 rounded-lg border border-teal-200/60 hover:shadow-md transition-all duration-200">
                  <div className="text-center">
                    <p className="text-xs font-semibold text-teal-600 mb-0.5">Q3</p>
                    <p className="text-sm font-bold text-teal-800">₹{grandTotals.q3Budget.toLocaleString()}</p>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-2.5 rounded-lg border border-teal-200/60 hover:shadow-md transition-all duration-200">
                  <div className="text-center">
                    <p className="text-xs font-semibold text-teal-600 mb-0.5">Q4</p>
                    <p className="text-sm font-bold text-teal-800">₹{grandTotals.q4Budget.toLocaleString()}</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-teal-600 to-teal-700 p-3 rounded-lg shadow-md">
                <div className="text-center">
                  <p className="text-xs font-semibold text-teal-100 mb-0.5">Total Budget</p>
                  <p className="text-lg font-bold text-white">₹{totalBudget.toLocaleString()}</p>
                </div>
              </div>
            </div>

            {/* Actual Section */}
            <div className="space-y-3">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-0.5 h-5 bg-teal-600 rounded-full"></div>
                <h4 className="text-sm font-bold text-teal-800">Actual</h4>
              </div>
              <div className="grid grid-cols-4 gap-2">
                <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-2.5 rounded-lg border border-teal-200/60 hover:shadow-md transition-all duration-200">
                  <div className="text-center">
                    <p className="text-xs font-semibold text-teal-600 mb-0.5">Q1</p>
                    <p className="text-sm font-bold text-teal-800">₹{grandTotals.q1Actual.toLocaleString()}</p>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-2.5 rounded-lg border border-teal-200/60 hover:shadow-md transition-all duration-200">
                  <div className="text-center">
                    <p className="text-xs font-semibold text-teal-600 mb-0.5">Q2</p>
                    <p className="text-sm font-bold text-teal-800">₹{grandTotals.q2Actual.toLocaleString()}</p>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-2.5 rounded-lg border border-teal-200/60 hover:shadow-md transition-all duration-200">
                  <div className="text-center">
                    <p className="text-xs font-semibold text-teal-600 mb-0.5">Q3</p>
                    <p className="text-sm font-bold text-teal-800">₹{grandTotals.q3Actual.toLocaleString()}</p>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-2.5 rounded-lg border border-teal-200/60 hover:shadow-md transition-all duration-200">
                  <div className="text-center">
                    <p className="text-xs font-semibold text-teal-600 mb-0.5">Q4</p>
                    <p className="text-sm font-bold text-teal-800">₹{grandTotals.q4Actual.toLocaleString()}</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-teal-600 to-teal-700 p-3 rounded-lg shadow-md">
                <div className="text-center">
                  <p className="text-xs font-semibold text-teal-100 mb-0.5">Total Actual</p>
                  <p className="text-lg font-bold text-white">₹{totalActual.toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Main Expense Table */}
      <Card className="rounded-3xl shadow-2xl bg-gradient-to-br from-white via-slate-50/80 to-teal-50/40 border border-teal-200/30 backdrop-blur-sm">
        <div className="p-1">
          <div className="overflow-x-auto rounded-2xl border border-teal-200/30">
            <table className="w-full text-sm border-collapse">
              <thead className="sticky top-0 z-20">
                <tr className="bg-teal-600 text-white border-b border-teal-700">
                {[
                  { label: 'Sr.', width: 'w-12' },
                  { label: 'Main Header', width: 'w-28' },
                  { label: 'Sub Header', width: 'w-28' },
                  { label: 'Particulars', width: 'w-36' },
                  { label: 'Units', width: 'w-20' },
                  { label: 'Frequency', width: 'w-20' },
                  { label: 'Cost per Unit', width: 'w-24' },
                  { label: 'Total Budget', width: 'w-28' },
                  { label: 'Current Quarter', width: 'w-32' },
                  { label: '', width: 'w-16' },
                ].map((head, idx) => (
                  <th key={idx} className={`${head.width} px-4 py-4 font-semibold text-sm text-white text-center border-r border-teal-500/30 last:border-r-0`}>
                    <div className="flex items-center justify-center">
                      <span>{head.label}</span>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white">
              {updatedExcelData.map((row, index) => {
                const rowId = row.id;
                const rowError = rowErrors[rowId] || {};
                const budgetSumError = budgetSumErrors[rowId] || {};
                const remarkError = remarksErrors[rowId] || '';
                const submissionError = submissionErrors[rowId] || {};
                const hasRowError =
                  Object.keys(rowError).length > 0 ||
                  Object.keys(budgetSumError).length > 0 ||
                  remarkError ||
                  Object.keys(submissionError).length > 0;
                const currentQuarterLabel = quarters[currentQuarter - 1];
                const totalGrantBudget = totalGrantBudgets[rowId] || 0;
                const isExpanded = expandedRows.includes(rowId);

                return (
                  <Fragment key={rowId}>
                    <tr
                      className={`group transition-all duration-200 border-b border-gray-200/60 ${
                        hasRowError
                          ? 'bg-gradient-to-r from-red-50/90 via-red-25/50 to-orange-50/70 hover:from-red-100/90 hover:via-red-50/60 hover:to-orange-100/80'
                          : isExpanded
                            ? 'bg-gradient-to-r from-teal-50/80 via-white to-emerald-50/60'
                            : 'bg-white hover:bg-gradient-to-r hover:from-teal-50/40 hover:via-white hover:to-emerald-50/30'
                      }`}
                    >
                      <td className="w-12 px-3 py-4 text-center border-r border-gray-200/40">
                        <div className="inline-flex items-center justify-center w-7 h-7 bg-gradient-to-br from-teal-600 to-emerald-600 text-white text-xs font-bold rounded-full shadow-sm">
                          {String(index + 1).padStart(2, '0')}
                        </div>
                      </td>
                      <td className="w-28 px-3 py-4 border-r border-gray-200/40">
                        <div className="font-semibold text-gray-900 text-xs leading-tight">
                          {row.main_header || 'N/A'}
                        </div>
                      </td>
                      <td className="w-28 px-3 py-4 border-r border-gray-200/40">
                        <div className="text-gray-700 text-xs leading-tight">
                          {row.sub_header || 'N/A'}
                        </div>
                      </td>
                      <td className="w-36 px-3 py-4 border-r border-gray-200/40">
                        <div className="text-gray-700 text-xs leading-tight">
                          {row.particulars || 'N/A'}
                        </div>
                      </td>
                      <td className="w-20 px-3 py-4 text-center border-r border-gray-200/40">
                        <div className={`text-xs font-medium ${rowError['Units'] ? 'text-red-600' : 'text-gray-800'}`}>
                          {row.units || 'N/A'}
                        </div>
                        {rowError['Units'] && (
                          <div className="mt-1 text-xs text-red-500 font-medium">
                            {rowError['Units']}
                          </div>
                        )}
                      </td>
                      <td className="w-20 px-3 py-4 text-center border-r border-gray-200/40">
                        <div className={`text-xs font-medium ${rowError['Frequency'] ? 'text-red-600' : 'text-gray-800'}`}>
                          {row.frequency || 'N/A'}
                        </div>
                        {rowError['Frequency'] && (
                          <div className="mt-1 text-xs text-red-500 font-medium">
                            {rowError['Frequency']}
                          </div>
                        )}
                      </td>
                      <td className="w-24 px-3 py-4 text-center border-r border-gray-200/40">
                        <div className={`text-xs font-semibold ${rowError['Cost per Unit'] ? 'text-red-600' : 'text-gray-900'}`}>
                          {row.cost_per_unit
                            ? `₹${parseFloat(row.cost_per_unit).toFixed(2)}`
                            : 'N/A'}
                        </div>
                        {rowError['Cost per Unit'] && (
                          <div className="mt-1 text-xs text-red-500 font-medium">
                            {rowError['Cost per Unit']}
                          </div>
                        )}
                      </td>
                      <td className="w-28 px-3 py-4 text-center border-r border-gray-200/40">
                        <div className="bg-gradient-to-r from-teal-50 to-emerald-50 px-2 py-1.5 rounded-lg border border-teal-200/50">
                          <div className="text-xs font-bold text-teal-700">
                            {row.total_grant_budget.toLocaleString('en-IN', {
                              style: 'currency',
                              currency: 'INR',
                              minimumFractionDigits: 0,
                              maximumFractionDigits: 0,
                            })}
                          </div>
                        </div>
                      </td>
                      <td className="w-32 px-3 py-4 border-r border-gray-200/40">
                        <QuarterInlineInput
                          label={currentQuarterLabel}
                          value={actualValues[`${rowId}-${currentQuarterLabel}`]}
                          onChange={(val) =>
                            handleActualChange(rowId, currentQuarterLabel, val)
                          }
                          readOnly={!isActualEditable(currentQuarterLabel)}
                          rowId={rowId}
                          type="actual"
                          totalGrantBudget={totalGrantBudget}
                          validate={(val) => validateNumber(val, currentQuarterLabel)}
                        />
                      </td>
                      <td className="w-16 px-3 py-4 text-center">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleRow(rowId)}
                          className={`h-7 w-7 rounded-full transition-all duration-200 ${
                            isExpanded
                              ? 'bg-teal-100 text-teal-700 hover:bg-teal-200'
                              : 'hover:bg-teal-50 text-teal-600 hover:text-teal-700'
                          }`}
                        >
                          {isExpanded ? (
                            <ChevronUp className="w-3 h-3" />
                          ) : (
                            <ChevronDown className="w-3 h-3" />
                          )}
                        </Button>
                      </td>
                    </tr>

                    {isExpanded && (
                      <tr className="bg-gradient-to-br from-slate-50/90 via-white to-teal-50/60">
                        <td colSpan={10} className="p-0">
                          <div
                            ref={(el) => {
                              expandedRowRefs.current[rowId] = el;
                            }}
                            className="border-t border-teal-200/40 bg-gradient-to-br from-white via-slate-50/50 to-teal-50/30"
                          >
                            <div className="p-8 space-y-8">
                              {/* Enhanced error display */}
                              {(() => {
                                const combinedRowLevelErrors: Record<string, string> = {
                                  ...rowError,
                                  ...submissionErrors[rowId],
                                };
                                const seenKeys = new Set<string>();
                                const uniqueRowLevelErrors = Object.entries(combinedRowLevelErrors)
                                  .filter(([key]) => !key.startsWith('budget-') && !key.startsWith('actual-'))
                                  .filter(([key]) => {
                                    if (seenKeys.has(key)) return false;
                                    seenKeys.add(key);
                                    return true;
                                  });
                                return uniqueRowLevelErrors.length > 0 ? (
                                  <div className="bg-gradient-to-r from-red-50 to-orange-50 border-l-4 border-red-400 rounded-r-xl p-6 shadow-sm">
                                    <div className="flex items-center gap-3 mb-3">
                                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                      <h4 className="text-red-700 font-bold text-base">Validation Issues</h4>
                                    </div>
                                    <div className="space-y-2">
                                      {uniqueRowLevelErrors.map(([key, message]) => (
                                        <p key={key} className="text-red-600 text-sm flex items-start gap-2">
                                          <span className="w-1.5 h-1.5 bg-red-500 rounded-full mt-1.5 flex-shrink-0"></span>
                                          <span>{message}</span>
                                        </p>
                                      ))}
                                    </div>
                                  </div>
                                ) : null;
                              })()}
                              <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                             
                                <div className="bg-white rounded-2xl border border-gray-200/60 shadow-sm">
                                  <div className="p-6 border-b border-gray-200/60">
                                    <div className="flex items-center gap-3">
                                      <div className="w-1 h-8 bg-gradient-to-b from-teal-600 to-emerald-600 rounded-full"></div>
                                      <h3 className="text-lg font-bold text-gray-900">
                                        Budget Quarterly Breakup
                                      </h3>
                                    </div>
                                  </div>
                                  <div className="p-6 space-y-6">

                                    {budgetSumError.initialSumError && (
                                      <div className="bg-amber-50 border-l-4 border-amber-400 rounded-r-lg p-4">
                                        <p className="text-amber-700 text-sm font-semibold flex items-center gap-2">
                                          <span className="w-1.5 h-1.5 bg-amber-500 rounded-full animate-pulse"></span>
                                          {budgetSumError.initialSumError}
                                        </p>
                                      </div>
                                    )}
                                    <div className="grid grid-cols-2 gap-4">
                                      {quarters.map((q) => (
                                        <QuarterInlineInput
                                          key={`budget-${rowId}-${q}`}
                                          label={q}
                                          value={budgetValues[`${rowId}-${q}`]}
                                          onChange={(val) => handleBudgetChange(rowId, q, val)}
                                          readOnly={!isBudgetEditable(q)}
                                          rowId={rowId}
                                          type="budget"
                                          totalGrantBudget={totalGrantBudget}
                                          hasError={!!rowError[`budget-${q}`]}
                                          errorMessage={rowError[`budget-${q}`] || ''}
                                          validate={(val) => validateNumber(val, `${q} Budget`)}
                                        />
                                      ))}
                                    </div>
                                  </div>
                                  <div className="px-4 py-3 bg-gradient-to-r from-teal-50 to-emerald-50 border-t border-gray-200/60">
                                    <div className="text-center space-y-2">
                                      <div className="flex items-center justify-center gap-1.5">
                                        <div className="w-1.5 h-1.5 bg-teal-600 rounded-full"></div>
                                        <p className="text-xs text-teal-700 font-semibold uppercase tracking-wide">Budget Total</p>
                                      </div>
                                      <p className={`text-lg font-bold ${budgetSumError.initialSumError ? 'text-red-600' : 'text-teal-700'}`}>
                                        {calculateTotal(rowId, 'budget')}
                                      </p>
                                      {budgetSumError.initialSumError && (
                                        <div className="flex items-center justify-center gap-1">
                                          <div className="w-1 h-1 bg-red-500 rounded-full animate-ping"></div>
                                          <span className="text-xs text-red-600 font-medium">Error</span>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>

                        
                                <div className="bg-white rounded-2xl border border-gray-200/60 shadow-sm">
                                  <div className="p-6 border-b border-gray-200/60">
                                    <div className="flex items-center gap-3">
                                      <div className="w-1 h-8 bg-gradient-to-b from-emerald-600 to-teal-600 rounded-full"></div>
                                      <h3 className="text-lg font-bold text-gray-900">
                                        Actual Quarterly Breakup
                                      </h3>
                                    </div>
                                  </div>
                                  <div className="p-6 space-y-6">

                                    <div className="grid grid-cols-2 gap-4">
                                      {quarters.map((q) => (
                                        <QuarterInlineInput
                                          key={`actual-${rowId}-${q}`}
                                          label={q}
                                          value={actualValues[`${rowId}-${q}`]}
                                          onChange={(val) => handleActualChange(rowId, q, val)}
                                          readOnly={!isActualEditable(q)}
                                          rowId={rowId}
                                          type="actual"
                                          totalGrantBudget={totalGrantBudget}
                                          hasError={!!rowError[`actual-${q}`]}
                                          errorMessage={rowError[`actual-${q}`] || ''}
                                          validate={(val) => validateNumber(val, `${q} Actual`)}
                                        />
                                      ))}
                                    </div>
                                  </div>
                                  <div className="px-4 py-3 bg-gradient-to-r from-emerald-50 to-teal-50 border-t border-gray-200/60">
                                    <div className="text-center space-y-2">
                                      <div className="flex items-center justify-center gap-1.5">
                                        <div className="w-1.5 h-1.5 bg-emerald-600 rounded-full"></div>
                                        <p className="text-xs text-emerald-700 font-semibold uppercase tracking-wide">Actual Total</p>
                                      </div>
                                      <p className="text-lg font-bold text-emerald-700">
                                        {calculateTotal(rowId, 'actual')}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div className="bg-white rounded-2xl border border-gray-200/60 shadow-sm">
                                <div className="p-4 border-b border-gray-200/60">
                                  <div className="flex items-center gap-3">
                                    <div className="w-1 h-6 bg-gradient-to-b from-gray-600 to-gray-700 rounded-full"></div>
                                    <h3 className="text-base font-bold text-gray-900">
                                      Remarks
                                    </h3>
                                  </div>
                                </div>
                                <div className="p-4">
                                  <RemarksInput
                                    value={remarksValues[rowId] ?? row.remarks}
                                    onChange={handleRemarksChange}
                                    rowId={rowId}
                                    hasError={!!remarksErrors[rowId]}
                                    errorMessage={remarksErrors[rowId]}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}
                  </Fragment>
                );
              })}
            </tbody>
            </table>
          </div>
        </div>

        {onSubmit && updatedExcelData.length > 0 && (
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div className="flex justify-center">
              <Button
                onClick={onSubmit}
                disabled={isSubmitting}
                className="bg-teal-600 hover:bg-teal-700 text-white px-6 py-2.5 rounded-lg font-medium transition-colors"
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    Submitting...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Send className="w-4 h-4" />
                    Submit Expenses
                  </div>
                )}
              </Button>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
  
}
