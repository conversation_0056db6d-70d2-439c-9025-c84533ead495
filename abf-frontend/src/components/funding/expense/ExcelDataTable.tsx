import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { motion } from "framer-motion";

interface ExpenseRow {
  sr_no: number;
  particulars: string;
  main_header: string;
  sub_headers: string;
  units?: string;
  frequency?: string;
  cost_per_unit?: number;
  budget_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  actuals_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  total_budget?: number;
  total_actual?: number;
}

interface ExcelDataTableProps {
  data: ExpenseRow[];
  onInputChange?: (index: number, field: string, value: string | number) => void;
  editable?: boolean;
}

interface CategoryTotals {
  q1Budget: number;
  q2Budget: number;
  q3Budget: number;
  q4Budget: number;
  q1Actual: number;
  q2Actual: number;
  q3Actual: number;
  q4Actual: number;
  totalBudget: number;
  totalActual: number;
}

export function ExcelDataTable({ data, onInputChange, editable = false }: ExcelDataTableProps) {
  // Categorize expenses into Program Cost and Non-Program Cost
  const programCostData = data.filter(row =>
    row.main_header.toLowerCase().includes('program')
  );

  const nonProgramCostData = data.filter(row =>
    !row.main_header.toLowerCase().includes('program')
  );

  // Calculate category totals
  const calculateCategoryTotals = (categoryData: ExpenseRow[]): CategoryTotals => {
    return categoryData.reduce((acc, row) => ({
      q1Budget: acc.q1Budget + (row.budget_quarterly.Q1 || 0),
      q2Budget: acc.q2Budget + (row.budget_quarterly.Q2 || 0),
      q3Budget: acc.q3Budget + (row.budget_quarterly.Q3 || 0),
      q4Budget: acc.q4Budget + (row.budget_quarterly.Q4 || 0),
      q1Actual: acc.q1Actual + (row.actuals_quarterly.Q1 || 0),
      q2Actual: acc.q2Actual + (row.actuals_quarterly.Q2 || 0),
      q3Actual: acc.q3Actual + (row.actuals_quarterly.Q3 || 0),
      q4Actual: acc.q4Actual + (row.actuals_quarterly.Q4 || 0),
      totalBudget: acc.totalBudget + (row.total_budget || Object.values(row.budget_quarterly).reduce((sum, val) => sum + val, 0)),
      totalActual: acc.totalActual + (row.total_actual || Object.values(row.actuals_quarterly).reduce((sum, val) => sum + val, 0))
    }), {
      q1Budget: 0, q2Budget: 0, q3Budget: 0, q4Budget: 0,
      q1Actual: 0, q2Actual: 0, q3Actual: 0, q4Actual: 0,
      totalBudget: 0, totalActual: 0
    });
  };

  const programTotals = calculateCategoryTotals(programCostData);
  const nonProgramTotals = calculateCategoryTotals(nonProgramCostData);
  const grandTotals = calculateCategoryTotals(data);

  // Render category section
  const renderCategorySection = (categoryData: ExpenseRow[], categoryName: string, categoryTotals: CategoryTotals, startIndex: number) => {
    if (categoryData.length === 0) return null;

    return (
      <>
        {/* Category Header */}
        <tr className="bg-gradient-to-r from-teal-600 to-teal-700 text-white">
          <td colSpan={15} className="p-4 text-center font-bold text-lg tracking-wide">
            {categoryName}
          </td>
        </tr>

        {/* Category Data Rows */}
        {categoryData.map((row, index) => {
          const actualIndex = startIndex + index;
          return (
            <tr key={actualIndex} className="hover:bg-teal-50/50 transition-colors duration-200 border-b border-teal-100">
              <td className="p-3 border border-teal-200/40 text-center font-medium text-teal-800">
                {row.sr_no}
              </td>
              <td className="p-3 border border-teal-200/40">
                {editable ? (
                  <Input
                    type="text"
                    value={row.particulars}
                    onChange={(e) => onInputChange && onInputChange(actualIndex, 'particulars', e.target.value)}
                    className="w-full min-w-[120px] h-9 text-sm px-3 border-teal-200 focus:border-teal-500 focus:ring-teal-500"
                  />
                ) : (
                  <span className="text-gray-800 font-medium">{row.particulars}</span>
                )}
              </td>
              <td className="p-3 border border-teal-200/40">
                {editable ? (
                  <Input
                    type="text"
                    value={row.main_header}
                    onChange={(e) => onInputChange && onInputChange(actualIndex, 'main_header', e.target.value)}
                    className="w-full min-w-[120px] h-9 text-sm px-3 border-teal-200 focus:border-teal-500 focus:ring-teal-500"
                  />
                ) : (
                  <span className="text-gray-800 font-medium">{row.main_header}</span>
                )}
              </td>
              <td className="p-3 border border-teal-200/40">
                {editable ? (
                  <Input
                    type="text"
                    value={row.sub_headers}
                    onChange={(e) => onInputChange && onInputChange(actualIndex, 'sub_headers', e.target.value)}
                    className="w-full min-w-[120px] h-9 text-sm px-3 border-teal-200 focus:border-teal-500 focus:ring-teal-500"
                  />
                ) : (
                  <span className="text-gray-700">{row.sub_headers}</span>
                )}
              </td>
              <td className="p-3 border border-teal-200/40">
                {editable ? (
                  <Input
                    type="text"
                    value={row.units || ''}
                    onChange={(e) => onInputChange && onInputChange(actualIndex, 'units', e.target.value)}
                    className="w-full min-w-[80px] h-9 text-sm px-3 border-teal-200 focus:border-teal-500 focus:ring-teal-500"
                  />
                ) : (
                  <span className="text-gray-700">{row.units || ''}</span>
                )}
              </td>
              <td className="p-3 border border-teal-200/40">
                {editable ? (
                  <Input
                    type="text"
                    value={row.frequency || ''}
                    onChange={(e) => onInputChange && onInputChange(actualIndex, 'frequency', e.target.value)}
                    className="w-full min-w-[80px] h-9 text-sm px-3 border-teal-200 focus:border-teal-500 focus:ring-teal-500"
                  />
                ) : (
                  <span className="text-gray-700">{row.frequency || ''}</span>
                )}
              </td>
              <td className="p-3 border border-teal-200/40">
                {editable ? (
                  <Input
                    type="number"
                    value={row.cost_per_unit || ''}
                    onChange={(e) => onInputChange && onInputChange(actualIndex, 'cost_per_unit', parseFloat(e.target.value))}
                    className="w-full min-w-[80px] h-9 text-sm px-3 text-right border-teal-200 focus:border-teal-500 focus:ring-teal-500"
                  />
                ) : (
                  <span className="text-gray-800 font-medium text-right block">
                    {row.cost_per_unit?.toFixed(2) || ''}
                  </span>
                )}
              </td>
              {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                <td key={`budget_${quarter}`} className="p-3 border border-teal-200/40">
                  {editable ? (
                    <Input
                      type="number"
                      value={row.budget_quarterly[quarter as keyof typeof row.budget_quarterly]}
                      onChange={(e) => onInputChange && onInputChange(actualIndex, `budget_quarterly.${quarter}`, e.target.value)}
                      className="w-full min-w-[80px] h-9 text-sm px-3 text-right border-teal-200 focus:border-teal-500 focus:ring-teal-500"
                    />
                  ) : (
                    <span className="text-gray-800 font-medium text-right block">
                      {parseFloat(row.budget_quarterly[quarter as keyof typeof row.budget_quarterly].toString()).toFixed(2)}
                    </span>
                  )}
                </td>
              ))}
              <td className="p-3 border border-teal-200/40 text-center font-bold text-teal-800 bg-teal-50/30">
                {(row.total_budget || Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0)).toFixed(2)}
              </td>
              {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                <td key={`actual_${quarter}`} className="p-3 border border-teal-200/40">
                  {editable ? (
                    <Input
                      type="number"
                      value={row.actuals_quarterly[quarter as keyof typeof row.actuals_quarterly]}
                      onChange={(e) => onInputChange && onInputChange(actualIndex, `actuals_quarterly.${quarter}`, e.target.value)}
                      className="w-full min-w-[80px] h-9 text-sm px-3 text-right border-teal-200 focus:border-teal-500 focus:ring-teal-500"
                    />
                  ) : (
                    <span className="text-gray-800 font-medium text-right block">
                      {parseFloat(row.actuals_quarterly[quarter as keyof typeof row.actuals_quarterly].toString()).toFixed(2)}
                    </span>
                  )}
                </td>
              ))}
              <td className="p-3 border border-teal-200/40 text-center font-bold text-teal-800 bg-teal-50/30">
                {(row.total_actual || Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0)).toFixed(2)}
              </td>
            </tr>
          );
        })}

        {/* Category Subtotal */}
        <tr className="bg-gradient-to-r from-teal-100 to-teal-200 font-bold text-teal-900 border-t-2 border-teal-400">
          <td className="p-3 border border-teal-300" colSpan={7}>
            <span className="text-sm font-bold">{categoryName} Subtotal</span>
          </td>
          <td className="p-3 border border-teal-300 text-right font-bold">
            {categoryTotals.q1Budget.toFixed(2)}
          </td>
          <td className="p-3 border border-teal-300 text-right font-bold">
            {categoryTotals.q2Budget.toFixed(2)}
          </td>
          <td className="p-3 border border-teal-300 text-right font-bold">
            {categoryTotals.q3Budget.toFixed(2)}
          </td>
          <td className="p-3 border border-teal-300 text-right font-bold">
            {categoryTotals.q4Budget.toFixed(2)}
          </td>
          <td className="p-3 border border-teal-300 text-right font-bold bg-teal-200">
            {categoryTotals.totalBudget.toFixed(2)}
          </td>
          <td className="p-3 border border-teal-300 text-right font-bold">
            {categoryTotals.q1Actual.toFixed(2)}
          </td>
          <td className="p-3 border border-teal-300 text-right font-bold">
            {categoryTotals.q2Actual.toFixed(2)}
          </td>
          <td className="p-3 border border-teal-300 text-right font-bold">
            {categoryTotals.q3Actual.toFixed(2)}
          </td>
          <td className="p-3 border border-teal-300 text-right font-bold">
            {categoryTotals.q4Actual.toFixed(2)}
          </td>
          <td className="p-3 border border-teal-300 text-right font-bold bg-teal-200">
            {categoryTotals.totalActual.toFixed(2)}
          </td>
        </tr>
      </>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="overflow-x-auto rounded-xl shadow-lg"
    >
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white via-slate-50/80 to-teal-50/40">
        <CardContent className="p-0">
          <table className="w-full border-collapse bg-white text-sm rounded-xl overflow-hidden">
            <thead className="sticky top-0 z-20">
              <tr className="bg-gradient-to-r from-teal-700 to-teal-800 text-white">
                <th className="p-4 border border-teal-600 text-left font-bold text-sm tracking-wide">Sr</th>
                <th className="p-4 border border-teal-600 text-left font-bold text-sm tracking-wide">Particulars</th>
                <th className="p-4 border border-teal-600 text-left font-bold text-sm tracking-wide">Main Header</th>
                <th className="p-4 border border-teal-600 text-left font-bold text-sm tracking-wide">Sub-Headers</th>
                <th className="p-4 border border-teal-600 text-left font-bold text-sm tracking-wide">Units</th>
                <th className="p-4 border border-teal-600 text-left font-bold text-sm tracking-wide">Freq.</th>
                <th className="p-4 border border-teal-600 text-left font-bold text-sm tracking-wide">Cost/Unit</th>
                <th className="p-4 border border-teal-600 text-center font-bold text-sm tracking-wide" colSpan={4}>Budget Quarterly</th>
                <th className="p-4 border border-teal-600 text-center font-bold text-sm tracking-wide">Total</th>
                <th className="p-4 border border-teal-600 text-center font-bold text-sm tracking-wide" colSpan={4}>Actuals Quarterly</th>
                <th className="p-4 border border-teal-600 text-center font-bold text-sm tracking-wide">Total</th>
              </tr>
              <tr className="bg-gradient-to-r from-teal-600 to-teal-700 text-white">
                <th className="p-3 border border-teal-500" colSpan={7}></th>
                <th className="p-3 border border-teal-500 text-center font-semibold text-sm">Q1</th>
                <th className="p-3 border border-teal-500 text-center font-semibold text-sm">Q2</th>
                <th className="p-3 border border-teal-500 text-center font-semibold text-sm">Q3</th>
                <th className="p-3 border border-teal-500 text-center font-semibold text-sm">Q4</th>
                <th className="p-3 border border-teal-500"></th>
                <th className="p-3 border border-teal-500 text-center font-semibold text-sm">Q1</th>
                <th className="p-3 border border-teal-500 text-center font-semibold text-sm">Q2</th>
                <th className="p-3 border border-teal-500 text-center font-semibold text-sm">Q3</th>
                <th className="p-3 border border-teal-500 text-center font-semibold text-sm">Q4</th>
                <th className="p-3 border border-teal-500"></th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {/* Program Cost Section */}
              {renderCategorySection(programCostData, "Program Cost", programTotals, 0)}

              {/* Non-Program Cost Section */}
              {renderCategorySection(nonProgramCostData, "Non-Program Cost", nonProgramTotals, programCostData.length)}

              {/* Grand Total Row */}
              <tr className="bg-gradient-to-r from-teal-800 to-teal-900 text-white font-bold text-base border-t-4 border-teal-600">
                <td className="p-4 border border-teal-700" colSpan={7}>
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-bold tracking-wide">GRAND TOTAL</span>
                    <div className="h-0.5 flex-1 bg-white/30 rounded"></div>
                  </div>
                </td>
                <td className="p-4 border border-teal-700 text-right font-bold text-base">
                  {grandTotals.q1Budget.toFixed(2)}
                </td>
                <td className="p-4 border border-teal-700 text-right font-bold text-base">
                  {grandTotals.q2Budget.toFixed(2)}
                </td>
                <td className="p-4 border border-teal-700 text-right font-bold text-base">
                  {grandTotals.q3Budget.toFixed(2)}
                </td>
                <td className="p-4 border border-teal-700 text-right font-bold text-base">
                  {grandTotals.q4Budget.toFixed(2)}
                </td>
                <td className="p-4 border border-teal-700 text-right font-bold text-lg bg-teal-700">
                  {grandTotals.totalBudget.toFixed(2)}
                </td>
                <td className="p-4 border border-teal-700 text-right font-bold text-base">
                  {grandTotals.q1Actual.toFixed(2)}
                </td>
                <td className="p-4 border border-teal-700 text-right font-bold text-base">
                  {grandTotals.q2Actual.toFixed(2)}
                </td>
                <td className="p-4 border border-teal-700 text-right font-bold text-base">
                  {grandTotals.q3Actual.toFixed(2)}
                </td>
                <td className="p-4 border border-teal-700 text-right font-bold text-base">
                  {grandTotals.q4Actual.toFixed(2)}
                </td>
                <td className="p-4 border border-teal-700 text-right font-bold text-lg bg-teal-700">
                  {grandTotals.totalActual.toFixed(2)}
                </td>
              </tr>
            </tbody>
          </table>
        </CardContent>
      </Card>

      {/* Quarterly Totals Summary - Main Feature */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="mt-6"
      >
        <Card className="border-2 border-teal-300 shadow-xl bg-gradient-to-br from-white to-teal-50/30">
          <CardContent className="p-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-teal-800 mb-2">Quarterly Totals Summary</h2>
              <p className="text-teal-600">Individual quarterly totals for the entire uploaded table</p>
            </div>

            {/* Budget Totals */}
            <div className="mb-8">
              <h3 className="text-lg font-bold text-teal-700 mb-4 text-center">📊 Budget Totals by Quarter</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 text-center">
                  <h4 className="text-sm font-semibold text-blue-700 mb-2">Q1 Budget</h4>
                  <p className="text-xl font-bold text-blue-800">₹{grandTotals.q1Budget.toLocaleString()}</p>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-xl border border-green-200 text-center">
                  <h4 className="text-sm font-semibold text-green-700 mb-2">Q2 Budget</h4>
                  <p className="text-xl font-bold text-green-800">₹{grandTotals.q2Budget.toLocaleString()}</p>
                </div>
                <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-xl border border-orange-200 text-center">
                  <h4 className="text-sm font-semibold text-orange-700 mb-2">Q3 Budget</h4>
                  <p className="text-xl font-bold text-orange-800">₹{grandTotals.q3Budget.toLocaleString()}</p>
                </div>
                <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-xl border border-purple-200 text-center">
                  <h4 className="text-sm font-semibold text-purple-700 mb-2">Q4 Budget</h4>
                  <p className="text-xl font-bold text-purple-800">₹{grandTotals.q4Budget.toLocaleString()}</p>
                </div>
              </div>
            </div>

            {/* Actual Totals */}
            <div className="mb-8">
              <h3 className="text-lg font-bold text-teal-700 mb-4 text-center">💰 Actual Totals by Quarter</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-gradient-to-br from-cyan-50 to-cyan-100 p-4 rounded-xl border border-cyan-200 text-center">
                  <h4 className="text-sm font-semibold text-cyan-700 mb-2">Q1 Actual</h4>
                  <p className="text-xl font-bold text-cyan-800">₹{grandTotals.q1Actual.toLocaleString()}</p>
                </div>
                <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 p-4 rounded-xl border border-emerald-200 text-center">
                  <h4 className="text-sm font-semibold text-emerald-700 mb-2">Q2 Actual</h4>
                  <p className="text-xl font-bold text-emerald-800">₹{grandTotals.q2Actual.toLocaleString()}</p>
                </div>
                <div className="bg-gradient-to-br from-amber-50 to-amber-100 p-4 rounded-xl border border-amber-200 text-center">
                  <h4 className="text-sm font-semibold text-amber-700 mb-2">Q3 Actual</h4>
                  <p className="text-xl font-bold text-amber-800">₹{grandTotals.q3Actual.toLocaleString()}</p>
                </div>
                <div className="bg-gradient-to-br from-pink-50 to-pink-100 p-4 rounded-xl border border-pink-200 text-center">
                  <h4 className="text-sm font-semibold text-pink-700 mb-2">Q4 Actual</h4>
                  <p className="text-xl font-bold text-pink-800">₹{grandTotals.q4Actual.toLocaleString()}</p>
                </div>
              </div>
            </div>

            {/* Overall Summary */}
            <div className="border-t-2 border-teal-200 pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gradient-to-br from-teal-100 to-teal-200 p-6 rounded-xl border border-teal-300 text-center">
                  <h3 className="text-lg font-bold text-teal-800 mb-3">📈 Total Budget</h3>
                  <p className="text-3xl font-bold text-teal-900">₹{grandTotals.totalBudget.toLocaleString()}</p>
                  <p className="text-sm text-teal-700 mt-2">Sum of all quarterly budgets</p>
                </div>
                <div className="bg-gradient-to-br from-emerald-100 to-emerald-200 p-6 rounded-xl border border-emerald-300 text-center">
                  <h3 className="text-lg font-bold text-emerald-800 mb-3">💵 Total Actual</h3>
                  <p className="text-3xl font-bold text-emerald-900">₹{grandTotals.totalActual.toLocaleString()}</p>
                  <p className="text-sm text-emerald-700 mt-2">Sum of all quarterly actuals</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Category Breakdown */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6"
      >
        <Card className="border border-teal-200 shadow-md hover:shadow-lg transition-shadow duration-300">
          <CardContent className="p-4">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-teal-700 mb-3">🎯 Program Cost Total</h3>
              <div className="space-y-2">
                <p className="text-lg font-bold text-teal-800">
                  Budget: ₹{programTotals.totalBudget.toLocaleString()}
                </p>
                <p className="text-lg font-bold text-teal-600">
                  Actual: ₹{programTotals.totalActual.toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-teal-200 shadow-md hover:shadow-lg transition-shadow duration-300">
          <CardContent className="p-4">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-teal-700 mb-3">🏢 Non-Program Cost Total</h3>
              <div className="space-y-2">
                <p className="text-lg font-bold text-teal-800">
                  Budget: ₹{nonProgramTotals.totalBudget.toLocaleString()}
                </p>
                <p className="text-lg font-bold text-teal-600">
                  Actual: ₹{nonProgramTotals.totalActual.toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
