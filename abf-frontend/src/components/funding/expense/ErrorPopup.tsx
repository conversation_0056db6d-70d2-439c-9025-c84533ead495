import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, AlertCircle, ShieldAlert } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface ErrorPopupProps {
  errorPopup: string | null;
  setErrorPopup: (message: string | null) => void;
}

export default function ErrorPopup({ errorPopup, setErrorPopup }: ErrorPopupProps) {
  if (!errorPopup) return null;

  const isValidationError = errorPopup.includes('validation errors');
  const isBackendError = errorPopup.includes('Backend validation errors');
  const errorLines = errorPopup.split('\n').filter(line => line.trim());

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 flex items-center justify-center bg-black/60 backdrop-blur-md z-50 p-4"
        onClick={() => setErrorPopup(null)}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0, y: 20 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.9, opacity: 0, y: 20 }}
          transition={{ type: "spring", duration: 0.5 }}
          className="bg-white rounded-3xl shadow-2xl w-full max-w-lg max-h-[85vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className={`relative p-6 text-white ${
            isValidationError
              ? 'bg-teal-600'
              : 'bg-red-600'
          }`}>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-white/20 rounded-lg">
                  {isValidationError ? (
                    <ShieldAlert className="w-6 h-6 text-white" />
                  ) : isBackendError ? (
                    <AlertCircle className="w-6 h-6 text-white" />
                  ) : (
                    <AlertTriangle className="w-6 h-6 text-white" />
                  )}
                </div>
                <div>
                  <h2 className="text-xl font-semibold">
                    {isValidationError ? 'Validation Required' : isBackendError ? 'Server Error' : 'Error Occurred'}
                  </h2>
                  <p className="text-white/90 text-sm mt-1">
                    {isValidationError ? 'Please review and fix the issues below' : 'Something went wrong with your request'}
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                onClick={() => setErrorPopup(null)}
                className="p-2 hover:bg-white/20 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-white" />
              </Button>
            </div>
          </div>

          <div className="p-6 space-y-6 max-h-[60vh] overflow-y-auto">
            {isValidationError || isBackendError ? (
              <div className="space-y-4">
                {errorLines.map((line, index) => {
                  if (line.includes('validation errors') || line.includes('Backend validation errors') || line.includes('Please fix the following validation errors:')) {
                    return null;
                  }

                  if (line.trim() && !line.includes('Please fix')) {
                    const cleanedLine = line.replace(/^Row \d+:\s*/, '');

                    return (
                      <div key={index} className={`border rounded-lg p-4 ${
                        isValidationError
                          ? 'bg-teal-50 border-teal-200'
                          : 'bg-red-50 border-red-200'
                      }`}>
                        <div className="flex items-start gap-3">
                          <div className={`w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0 ${
                            isValidationError ? 'bg-teal-500' : 'bg-red-500'
                          }`}></div>
                          <p className={`text-sm leading-relaxed font-medium ${
                            isValidationError ? 'text-teal-800' : 'text-red-800'
                          }`}>{cleanedLine}</p>
                        </div>
                      </div>
                    );
                  }

                  return null;
                })}
              </div>
            ) : (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
                  <div className="text-red-800 text-sm leading-relaxed font-medium whitespace-pre-wrap">
                    {errorPopup?.replace(/Row \d+:\s*/g, '')}
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div className="flex justify-end">
              <Button
                onClick={() => setErrorPopup(null)}
                className={`text-white px-6 py-2 rounded-lg font-medium transition-colors ${
                  isValidationError
                    ? 'bg-teal-600 hover:bg-teal-700'
                    : 'bg-gray-600 hover:bg-gray-700'
                }`}
              >
                Close
              </Button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}