"use client";

import { ReactNode, useState, useEffect } from "react";
import { Header } from "./Header";
import { Sidebar } from "./Sidebar";
import { motion } from "framer-motion";
import { Menu } from "lucide-react";

interface LayoutProps {
  children: ReactNode;
  title?: string;
}

export function Layout({ children, title = "Dashboard" }: LayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for mobile devices
    const checkMobile = () => {
      if (window.innerWidth < 1024) {
        setSidebarOpen(false);
      } else {
        setSidebarOpen(true);
      }
    };

    // Initial check
    checkMobile();

    // Add event listener
    window.addEventListener('resize', checkMobile);

    // Simulate loading state for smoother transitions
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 300);

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkMobile);
      clearTimeout(timer);
    };
  }, []);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
    // Force a reflow to ensure smooth transitions
    window.requestAnimationFrame(() => {
      window.requestAnimationFrame(() => {
        document.body.style.overflow = sidebarOpen ? 'auto' : 'hidden';
        setTimeout(() => {
          document.body.style.overflow = '';
        }, 150);
      });
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-[#6366F1] border-r-[#6366F1] border-b-indigo-200 border-l-indigo-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      {/* Fixed header */}
      <Header toggleSidebar={toggleSidebar}/>


      {/* Sidebar */}
      <Sidebar isOpen={sidebarOpen} toggleSidebar={toggleSidebar} />

      {/* Main content with proper spacing */}
      <div
        className={`transition-all duration-150 ${sidebarOpen ? 'lg:ml-64' : 'ml-0'}`}
        style={{ paddingTop: 'var(--header-height, 5rem)' }}
      >
        <main className="px-4 md:px-6 pb-12 pt-4 bg-white min-h-screen">
          {children}
        </main>
      </div>
    </div>
  );
}