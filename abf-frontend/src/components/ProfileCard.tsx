"use client";

import { ReactNode } from "react";
import { Card, CardContent } from "@/components/ui/card";

interface ProfileCardProps {
  title: string;
  children: ReactNode;
  editButton?: boolean;
  onEdit?: () => void;
}

export function ProfileCard({ title, children, editButton, onEdit }: ProfileCardProps) {
  return (
    <div className="mb-8">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-800">{title}</h2>
        {editButton && (
          <button
            onClick={onEdit}
            className="flex items-center gap-2 text-sm font-medium text-teal-500 hover:text-teal-600"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
              <path d="m15 5 4 4" />
            </svg>
            Edit {title}
          </button>
        )}
      </div>
      <Card className="border-gray-200 shadow-sm rounded-md">
        <CardContent className="p-6">{children}</CardContent>
      </Card>
    </div>
  );
}