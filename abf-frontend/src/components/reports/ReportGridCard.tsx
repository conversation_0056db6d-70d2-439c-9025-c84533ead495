import { motion } from "framer-motion";
import { format } from "date-fns";
import Link from "next/link";
import {
  MessageSquare,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

// Types
type ReportStatus = 'PENDING' | 'APPROVED' | 'REJECTED';

interface Grant {
  id: string;
  grant_name: string;
  annual_budget?: number;
}

interface Report {
  id: number;
  quarter: number;
  year: string;
  created_at: Date;
  updated_at: Date;
  grant_id: number;
  remarks?: string;
  status: ReportStatus;
  grant?: Grant;
}

interface ReportGridCardProps {
  report: Report;
  index: number;
  onRemarksClick: (remarks: string) => void;
}

// Constants
const STATUS_CONFIG = {
  REJECTED: {
    icon: AlertTriangle,
    gradientClassName: "bg-gradient-to-r from-red-50 to-rose-100 text-red-700 border-red-200"
  },
  APPROVED: {
    icon: CheckCircle,
    gradientClassName: "bg-gradient-to-r from-emerald-50 to-green-100 text-emerald-700 border-emerald-200"
  },
  PENDING: {
    icon: Clock,
    gradientClassName: "bg-gradient-to-r from-amber-50 to-yellow-100 text-amber-700 border-amber-200"
  }
} as const;

export const ReportGridCard = ({ 
  report, 
  index, 
  onRemarksClick 
}: ReportGridCardProps) => {
  const config = STATUS_CONFIG[report.status];
  const StatusIcon = config.icon;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.1 + index * 0.05 }}
      className="group"
    >
      <div className="bg-white rounded-xl border border-slate-200 shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden h-full flex flex-col">
        
        {/* Card Header */}
        <div className="p-6 pb-1">
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg text-slate-800 line-clamp-2 group-hover:text-slate-900 transition-colors duration-300">
                {report.grant?.grant_name || `Grant ID: ${report.grant_id}`}
              </h3>
            </div>
            
            {/* Status Badge - Top Right */}
            <div className="ml-3 flex items-center gap-2">
              <Badge className={`${config.gradientClassName} flex items-center gap-1 shadow-sm`}>
                <StatusIcon className="h-3 w-3" />
                {report.status}
              </Badge>
              
              {/* Remarks Button for Rejected Reports */}
              {report.remarks && report.status === "REJECTED" && (
                <Button
                  onClick={() => onRemarksClick(report.remarks!)}
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 bg-red-100 hover:bg-red-200 text-red-600 rounded-full transition-all duration-300 hover:scale-110"
                >
                  <MessageSquare className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Card Content */}
        <div className="px-6 space-y-4 flex-1 pt-2">
          {/* Quarter and Year Info */}
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-1 text-slate-400" />
              <span className="font-medium text-slate-700">
                Q{report.quarter} {report.year}
              </span>
            </div>
          </div>
        </div>

        {/* Card Footer */}
        <div className="px-6 py-4 border-t border-slate-100 bg-slate-50/50 mt-auto">
          <div className="flex justify-between items-center">
            <span className="text-xs text-slate-500">
              Created: {format(new Date(report.created_at), "MMM dd, yyyy")}
            </span>
            
            <Link href={`/milestones-reports/narrative/${report.id}`}>
              <Button variant="outline" size="sm">
                View Report
              </Button>
            </Link>
          </div>
        </div>

        {/* Status Color Accent Bar */}
        <div className={`h-1 w-full ${
          report.status === 'APPROVED' ? 'bg-emerald-500' :
          report.status === 'PENDING' ? 'bg-amber-500' :
          'bg-red-500'
        }`}></div>
      </div>
    </motion.div>
  );
};