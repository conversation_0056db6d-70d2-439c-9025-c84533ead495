import { motion } from "framer-motion";
import { format } from "date-fns";
import Link from "next/link";
import {
  MessageSquare,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileSpreadsheetIcon,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

// Types
type ReportStatus = 'PENDING' | 'APPROVED' | 'REJECTED';

interface Grant {
  id: string;
  grant_name: string;
  annual_budget?: number;
}

interface Report {
  id: number;
  quarter: number;
  year: string;
  created_at: Date;
  updated_at: Date;
  grant_id: number;
  remarks?: string;
  status: ReportStatus;
  grant?: Grant;
}

interface ReportListItemProps {
  report: Report;
  index: number;
  onRemarksClick: (remarks: string) => void;
}

// Constants
const STATUS_CONFIG = {
  REJECTED: {
    icon: AlertTriangle,
    className: "bg-red-100 text-red-800 border-red-200",
    color: "text-red-500",
    label: "Rejected"
  },
  APPROVED: {
    icon: CheckCircle,
    className: "bg-green-100 text-green-800 border-green-200", 
    color: "text-green-500",
    label: "Approved"
  },
  PENDING: {
    icon: Clock,
    className: "bg-yellow-100 text-yellow-800 border-yellow-200",
    color: "text-yellow-500",
    label: "Pending"
  }
} as const;

export const ReportListItem = ({ 
  report, 
  index, 
  onRemarksClick 
}: ReportListItemProps) => {
  const config = STATUS_CONFIG[report.status];
  const StatusIcon = config.icon;

  return (
    <motion.div
      className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm hover:shadow-md transition-all duration-200"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.05 * index }}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-10 h-10 rounded-full bg-emerald-100 text-emerald-600 flex-shrink-0">
            <FileSpreadsheetIcon className="h-5 w-5" />
          </div>
          <div>
            <div className="font-medium text-gray-800">
              {report.grant?.grant_name || `Grant ID: ${report.grant_id}`}
            </div>
            <div className="text-sm text-gray-500 flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>Q{report.quarter} {report.year}</span>
              <span className="mx-1">•</span>
              <span>{format(new Date(report.created_at), "MMM dd, yyyy")}</span>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          {report.remarks && report.status === "REJECTED" && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 bg-red-100 hover:bg-red-200 text-red-600 rounded-full transition-all duration-300 hover:scale-110"
              onClick={() => onRemarksClick(report.remarks!)}
            >
              <MessageSquare className="h-4 w-4" />
            </Button>
          )}
          
          <Badge className={`${config.className} flex items-center gap-1.5`}>
            <StatusIcon className="w-3.5 h-3.5" />
            {config.label}
          </Badge>
          
          <Link href={`/milestones-reports/narrative/${report.id}`}>
            <Button variant="outline" size="sm">
              View Report
            </Button>
          </Link>
        </div>
      </div>
    </motion.div>
  );
};