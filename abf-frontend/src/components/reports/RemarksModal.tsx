import {
  MessageSquare,
  AlertTriangleIcon,
} from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface RemarksModalProps {
  isOpen: boolean;
  onClose: () => void;
  remarks: string;
}

export const RemarksModal = ({
  isOpen,
  onClose,
  remarks,
}: RemarksModalProps) => (
  <Dialog open={isOpen} onOpenChange={onClose}>
    <DialogContent className="max-w-2xl max-h-96 overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex items-center gap-3">
          <div className="p-2 bg-red-100 rounded-lg">
            <MessageSquare className="h-5 w-5 text-red-600" />
          </div>
          Rejection Remarks
        </DialogTitle>
      </DialogHeader>
      <Alert className="border-red-200 bg-red-50">
        <AlertTriangleIcon className="h-4 w-4 text-red-600" />
        <AlertDescription className="text-red-700">
          <div className="whitespace-pre-wrap">{remarks}</div>
        </AlertDescription>
      </Alert>
    </DialogContent>
  </Dialog>
);