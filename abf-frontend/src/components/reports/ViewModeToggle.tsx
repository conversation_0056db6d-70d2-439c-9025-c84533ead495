import {
  Grid3X3,
  List
} from "lucide-react";
import { Button } from "@/components/ui/button";


const VIEW_MODES = [
  { id: "grid", icon: Grid3X3, label: "Grid View" },
  { id: "list", icon: List, label: "List View" }
] as const;


export const ViewModeToggle = ({ 
  viewMode,
  setViewMode
}: {
  viewMode: "grid" | "list";
  setViewMode: (mode: "grid" | "list") => void;
}) => (
  <div className="flex items-center bg-slate-100 rounded-lg p-1">
    {VIEW_MODES.map((mode) => {
      const Icon = mode.icon;
      return (
        <Button
          key={mode.id}
          onClick={() => setViewMode(mode.id as "grid" | "list")}
          variant="ghost"
          size="sm"
          className={`px-3 py-2 transition-all duration-300 ${
            viewMode === mode.id 
              ? 'bg-white shadow-sm text-teal-600' 
              : 'text-slate-600 hover:text-slate-800'
          }`}
        >
          <Icon className="h-4 w-4" />
        </Button>
      );
    })}
  </div>
);