import { motion } from "framer-motion";

interface SkeletonCardProps {
  index: number;
}

export const SkeletonCard = ({ index }: SkeletonCardProps) => {
  return (
    <motion.div
      className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.1 * index }}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {/* Icon Skeleton */}
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-teal-100 to-teal-200 flex-shrink-0 relative overflow-hidden">
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-60"
              animate={{ x: [-40, 40] }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
            />
          </div>
          
          <div className="space-y-2">
            {/* Title Skeleton */}
            <div className="h-4 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-32 relative overflow-hidden">
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-60"
                animate={{ x: [-128, 128] }}
                transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
              />
            </div>
            
            {/* Subtitle Skeleton */}
            <div className="h-3 bg-gradient-to-r from-gray-100 to-gray-200 rounded w-20 relative overflow-hidden">
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-60"
                animate={{ x: [-80, 80] }}
                transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut", delay: 0.3 }}
              />
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Status Badge Skeleton */}
          <div className="h-6 w-16 bg-gradient-to-r from-yellow-100 to-yellow-200 rounded-full relative overflow-hidden">
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-60"
              animate={{ x: [-64, 64] }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut", delay: 0.6 }}
            />
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Wrapper component for multiple skeleton cards
export const SkeletonLoading = () => {
  return (
    <div className="space-y-4">
      {[...Array(3)].map((_, index) => (
        <SkeletonCard key={index} index={index} />
      ))}
    </div>
  );
};