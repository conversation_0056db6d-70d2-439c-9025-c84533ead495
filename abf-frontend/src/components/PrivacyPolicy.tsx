"use client";

import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";

interface PrivacyPolicyProps {
  children: React.ReactNode;
  onAccept?: () => void;
  onReject?: () => void;
}

export function PrivacyPolicy({ children, onAccept, onReject }: PrivacyPolicyProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleAccept = () => {
    onAccept?.();
    setIsOpen(false);
  };

  const handleReject = () => {
    onReject?.();
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[85vh]">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl font-bold text-gray-800">
            Privacy Policy
          </DialogTitle>
          <DialogDescription className="text-sm text-gray-600">
            Please review our privacy policy before proceeding.
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="h-[50vh] pr-4">
          <div className="space-y-6 text-sm text-gray-700">
            <section>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">
                1. Information We Collect
              </h3>
              <p className="mb-3">
                We collect information you provide directly to us, such as when you create an account,
                submit grant applications, or communicate with us. This may include:
              </p>
              <ul className="list-disc pl-6 space-y-1">
                <li>Personal identification information (name, email address, phone number)</li>
                <li>Organization details and documentation</li>
                <li>Financial information related to grant applications</li>
                <li>Communication records and correspondence</li>
              </ul>
            </section>

            <section>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">
                2. How We Use Your Information
              </h3>
              <p className="mb-3">
                We use the information we collect to:
              </p>
              <ul className="list-disc pl-6 space-y-1">
                <li>Process and evaluate grant applications</li>
                <li>Communicate with you about your applications and account</li>
                <li>Provide customer support and respond to inquiries</li>
                <li>Improve our services and user experience</li>
                <li>Comply with legal obligations and regulatory requirements</li>
              </ul>
            </section>

            <section>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">
                3. Information Sharing and Disclosure
              </h3>
              <p className="mb-3">
                We do not sell, trade, or otherwise transfer your personal information to third parties
                without your consent, except as described in this policy. We may share information:
              </p>
              <ul className="list-disc pl-6 space-y-1">
                <li>With authorized personnel involved in grant evaluation</li>
                <li>When required by law or legal process</li>
                <li>To protect our rights, property, or safety</li>
                <li>With service providers who assist in our operations</li>
              </ul>
            </section>

            <section>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">
                4. Data Security
              </h3>
              <p>
                We implement appropriate technical and organizational measures to protect your personal
                information against unauthorized access, alteration, disclosure, or destruction. However,
                no method of transmission over the internet or electronic storage is 100% secure.
              </p>
            </section>

            <section>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">
                5. Data Retention
              </h3>
              <p>
                We retain your personal information for as long as necessary to fulfill the purposes
                outlined in this policy, comply with legal obligations, resolve disputes, and enforce
                our agreements.
              </p>
            </section>

            <section>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">
                6. Your Rights
              </h3>
              <p className="mb-3">
                You have the right to:
              </p>
              <ul className="list-disc pl-6 space-y-1">
                <li>Access and update your personal information</li>
                <li>Request deletion of your data (subject to legal requirements)</li>
                <li>Object to processing of your personal information</li>
                <li>Request data portability</li>
                <li>Withdraw consent where applicable</li>
              </ul>
            </section>

            <section>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">
                7. Contact Information
              </h3>
              <p>
                If you have any questions about this Privacy Policy or our data practices,
                please contact <NAME_EMAIL> or through our support channels.
              </p>
            </section>

            <section>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">
                8. Changes to This Policy
              </h3>
              <p>
                We may update this Privacy Policy from time to time. We will notify you of any
                material changes by posting the new policy on this page and updating the
                "Last Updated" date.
              </p>
            </section>

            <div className="mt-6 p-3 bg-gray-50 rounded-lg">
              <p className="text-xs text-gray-600">
                <strong>Last Updated:</strong> {new Date().toLocaleDateString()}
              </p>
            </div>
          </div>
        </ScrollArea>

        <DialogFooter className="pt-4 border-t border-gray-200">
          <div className="flex gap-3 w-full">
            <Button
              type="button"
              variant="outline"
              onClick={handleReject}
              className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Reject
            </Button>
            <Button
              type="button"
              onClick={handleAccept}
              className="flex-1 bg-teal-600 hover:bg-teal-700 text-white"
            >
              Accept Policy
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
