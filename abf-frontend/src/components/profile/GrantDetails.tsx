/*
  One thing to note is documents are hardcoded.
  Also only first grant is displayed.
*/

"use client";
import { useEffect, useState } from "react";
import { Card, CardTitle, CardDescription, CardHeader, CardContent } from "../ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { getMyGrants } from "@/services/grantmaker/grantee-service";
import { transformGrantsAPIResponseToOrganizationGrants } from "@/services/profile-service";
import { Organization, OrganizationGrant } from "@/types/profile";

interface GrantsListProps {
  organization: Organization | null;
}

export function GrantsList({organization}: GrantsListProps) {
  const [grants, setGrants] = useState<OrganizationGrant[]>(organization?.grants || [])
  const defaultGrantData = {
    grantName: "PF-2025-DCRS-001",
    grantId: "PF-2025-DCRS-001",
    projectStartDate: "1st April 2025",
    projectEndDate: "31st March 2026",
    purpose: "Setting up digital classrooms for government schools in rural Karnataka.",
    annualBudget: "90,00,000",
    fundingSources: "NIL",
    documents: [
      { type: "MOU X CSR", name: "Final_id_proof", verified: true, url: 'https://temp-abf-data.s3.ap-south-1.amazonaws.com/MOU%20Ashadeep-SV%20CSR%20Agreement-full%20agreement.pdf?response-content-disposition=inline&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEOD%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCmFwLXNvdXRoLTEiRzBFAiAcEdqIjQDnglS9kYA3ShpTAmAo%2BSEOTDOs4Sp8fXFDRQIhAOW6NFhbiPgrEMcUFsnRWztQCj%2BLWp8lTb8miM2KncJyKsIDCIn%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEQABoMNTg4NzM4NTk3NDIyIgwHOHZ%2FU9fZH0Y41XUqlgNxmDIUsJEQWkLGLq7FzC3YK%2BbmaDnFAP4540eQ8w8hwz%2Fx0T02hdbrLMZlEpFSyvew1pNRXz13zPYoYRM%2F68xiwQxkT9mI2W5nc3mXt7jpvdhZMxv8N8AyuqLeSImphRCEpCuhOsXlLgjTQ4TUzxYCgPs46iztzD97zRIoSfvG18RqAzT7gec1%2BgUmlHs%2F4rnZeqSjTjZvGhtWiVBQ6tD7uc2x9D92Td0BLQHT%2BYYXreWdkj2%2F75TAkGm9URmgA3F7XPAMzLaU8FfvPYsZrgFOfLww1B1AgD7XFQQ8EIoU1KX34JOwZOUuLo3wGFLMbtuUHSONorz5dG%2FRAMN9csa9Vd1%2BVTgzBoDn2v65x6f274ZESK3IR0EY2T2rXPvTPv4BMGE5mgZm1vw8bnuV7LmrKhKtrSEmCx4gYtxdOeEejILGXOlSBBgNStvVohovgZjXo%2FlQS0D5u4TG6XNgdTVLXXjcAo4YDzEwnVIijEcFXBJLJ56rllwj31uG198zuNAO2ufN0CJFn87kfpddQ%2BXzQUP%2BDnEnMNbe9sAGOt4CGJfEn%2FqhplyBdIJ518Mynvx0%2BcypYGTcXrmOV%2FiSeu5hPn%2FGFi9nqSGeLgVbzXqtcief3EKXSs9iIAu0rdZrbaWODfwuqBgC09CYfbOh8L1v090xkXy7vs0l0iwEgMAugJnlKOU2ClhfCuOyNHdvuQgtUZbBoooJkM0JpLJkIi53rsg4X%2BedQqen2f7QF7aOpbBLP1A3jdBfVdUOUT%2B1b3RnxndLpLwpWSq1mSam6eLIuHlcRMEY7owZOCQepvcDolyg2zHy%2Bn%2FtxAHglZcu%2FTt1aJ%2Br%2B2qPManYHVrTq%2FePR3iQG30yUaGqPOdeCeG8PPKfcsOFkh7AvGCy4AYPbSk24YGrzq07mFiSwPH0ROx7zcigEHXouMhYvq1XT2LkR1bgVev9vL0Nx%2BV1K00qAHSKXrDYocjMG1B0p%2FdwZOxCAEczNfP1LsTzAhlcLp63EnUSdYf0FQkpZxec7jg%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAYSE4OCIXGVOX32V4%2F20250509%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20250509T075721Z&X-Amz-Expires=43200&X-Amz-SignedHeaders=host&X-Amz-Signature=42a3260a30e060e87f193d0e2d23b86a0736afae7523dde5af181e41a711121f' },
      { type: "M&E Framework", name: "Certificate_id_proof", verified: true, url: 'https://temp-abf-data.s3.ap-south-1.amazonaws.com/Ashadeep%20M%26E%20Plan.xlsx?response-content-disposition=inline&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEOH%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCmFwLXNvdXRoLTEiRzBFAiEA4uzV8wVuJweI9OJpA3uANVdpy2MehvccAkD7kU%2BAw4QCIE%2BJ4Mr%2FComlY583aUjI%2FrXHq1yrxXuhaXFmeH8YtHpuKsIDCIr%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEQABoMNTg4NzM4NTk3NDIyIgwiV2wrEcZ4CKz8Ea4qlgNDs1XXjJZAPF5mpNnIzitXu1jZqYJUsftAW6Ib0qKRXSpbHZqo2JUipIMt31x9DzyTs1uXCrka6UN8iaUkV9tO1Gtzl9tf7SwbrsqrYY%2B0Lq4lwUCqCW%2FH34fVGumiKfC%2BDNcdhGwjOIl1vTNiGh%2Fi%2B19%2Fz47cl0UDoTol5WY3jHfbx47%2Fuh9KoNRoM4ypt4kM16nI62gJpB%2ByIuZz4xUzPg2juhkZX%2FNmAneBNYTyfe07SoDYJgubkg91EPo1KRmwEBvcdLRpYIk0eeN42Rhj1g0R8rLA1JWUUVEOTm7CUxaS2vBEtydY0G5E3p100hjQthn4Xysjb5YL7hEft9tRpYvfrvoEA9qRojDK%2BTsULMPt7j4iEZ33ldGnX1s7ofg4lbfoDMa8wzZNdD3hZ43LQ%2Fxr5sp%2FyzsDoSTMaOlyKBzZ%2BAMFHbPW45C8Zzsb%2BYnug6THwPSbtkF1XWzWu8Bb%2BOPkb4KY5pX2PO0oJwSR41hsI8ZrBvBWXR2Zir9O5hdv6vOtOS5BZFremgYGJB5txeG%2FvWSTMNbe9sAGOt4C7%2FIxNYEaWUfRfpUmj11cDia8ya4Q%2FI8Pii4j45GEzzc7YSgbG8lCUS%2BMrpv18x8EaocGUh6aMjCgf8Bsv2luqPpRAC0lJTmOZNU307ygGWEjRahSXv99CJkNdZMOKkfdmmU3QmKlRPybkmtgbprYW%2FAKHw2guSAhrO9Pfyt9eechCyAJaVJ%2FvX2HPgrKnqwkfQibcDlvl8emSG33J8rCrZel3233qfJVL1NVeIGOfts05%2BHs1O1ZUiAsKwd1PPkYH5Gu%2BO2Oee%2F8OIGCgVLbWCtcX2SPJBtKixliJbHd7qL%2BsNpsloVlF9l4Ez7bvioqsi7sy3aTCulp3H3OaWMq6imI4gzWuU%2FhRZSY5NH2kOST21ZDAXuY7vL5%2B6J9UM2M2j64%2FADRCzzoiyNPXLqE3JujTfLvHrFZc9cilbrOgeMN3Dasker6xUhgFfpUjWOTJ3AZeM3xB2IujZh4t3U%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAYSE4OCIXK6GKCMYT%2F20250509%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20250509T084426Z&X-Amz-Expires=43200&X-Amz-SignedHeaders=host&X-Amz-Signature=74321645b51ea3e06893982b8ea85a490ad371efd651f53b971cdac935e9aec7' },
      { type: "Program Proposal", name: "Trust Reg_id_proof", verified: true, url: 'https://temp-abf-data.s3.ap-south-1.amazonaws.com/Ashadeep%20Proposal.docx?response-content-disposition=inline&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEOD%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCmFwLXNvdXRoLTEiRzBFAiAcEdqIjQDnglS9kYA3ShpTAmAo%2BSEOTDOs4Sp8fXFDRQIhAOW6NFhbiPgrEMcUFsnRWztQCj%2BLWp8lTb8miM2KncJyKsIDCIn%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEQABoMNTg4NzM4NTk3NDIyIgwHOHZ%2FU9fZH0Y41XUqlgNxmDIUsJEQWkLGLq7FzC3YK%2BbmaDnFAP4540eQ8w8hwz%2Fx0T02hdbrLMZlEpFSyvew1pNRXz13zPYoYRM%2F68xiwQxkT9mI2W5nc3mXt7jpvdhZMxv8N8AyuqLeSImphRCEpCuhOsXlLgjTQ4TUzxYCgPs46iztzD97zRIoSfvG18RqAzT7gec1%2BgUmlHs%2F4rnZeqSjTjZvGhtWiVBQ6tD7uc2x9D92Td0BLQHT%2BYYXreWdkj2%2F75TAkGm9URmgA3F7XPAMzLaU8FfvPYsZrgFOfLww1B1AgD7XFQQ8EIoU1KX34JOwZOUuLo3wGFLMbtuUHSONorz5dG%2FRAMN9csa9Vd1%2BVTgzBoDn2v65x6f274ZESK3IR0EY2T2rXPvTPv4BMGE5mgZm1vw8bnuV7LmrKhKtrSEmCx4gYtxdOeEejILGXOlSBBgNStvVohovgZjXo%2FlQS0D5u4TG6XNgdTVLXXjcAo4YDzEwnVIijEcFXBJLJ56rllwj31uG198zuNAO2ufN0CJFn87kfpddQ%2BXzQUP%2BDnEnMNbe9sAGOt4CGJfEn%2FqhplyBdIJ518Mynvx0%2BcypYGTcXrmOV%2FiSeu5hPn%2FGFi9nqSGeLgVbzXqtcief3EKXSs9iIAu0rdZrbaWODfwuqBgC09CYfbOh8L1v090xkXy7vs0l0iwEgMAugJnlKOU2ClhfCuOyNHdvuQgtUZbBoooJkM0JpLJkIi53rsg4X%2BedQqen2f7QF7aOpbBLP1A3jdBfVdUOUT%2B1b3RnxndLpLwpWSq1mSam6eLIuHlcRMEY7owZOCQepvcDolyg2zHy%2Bn%2FtxAHglZcu%2FTt1aJ%2Br%2B2qPManYHVrTq%2FePR3iQG30yUaGqPOdeCeG8PPKfcsOFkh7AvGCy4AYPbSk24YGrzq07mFiSwPH0ROx7zcigEHXouMhYvq1XT2LkR1bgVev9vL0Nx%2BV1K00qAHSKXrDYocjMG1B0p%2FdwZOxCAEczNfP1LsTzAhlcLp63EnUSdYf0FQkpZxec7jg%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAYSE4OCIXGVOX32V4%2F20250509%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20250509T075744Z&X-Amz-Expires=43200&X-Amz-SignedHeaders=host&X-Amz-Signature=4fff8eb6d29d3eae3a8ede089c43bd87aa80ffe5eeae83a508b3ee92b38e29f3'},
      { type: "Program Budgets", name: "Final_tax_proof", verified: true, url: 'https://temp-abf-data.s3.ap-south-1.amazonaws.com/Ashadeep%20Budget.xlsx?response-content-disposition=inline&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEOD%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCmFwLXNvdXRoLTEiRzBFAiAcEdqIjQDnglS9kYA3ShpTAmAo%2BSEOTDOs4Sp8fXFDRQIhAOW6NFhbiPgrEMcUFsnRWztQCj%2BLWp8lTb8miM2KncJyKsIDCIn%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEQABoMNTg4NzM4NTk3NDIyIgwHOHZ%2FU9fZH0Y41XUqlgNxmDIUsJEQWkLGLq7FzC3YK%2BbmaDnFAP4540eQ8w8hwz%2Fx0T02hdbrLMZlEpFSyvew1pNRXz13zPYoYRM%2F68xiwQxkT9mI2W5nc3mXt7jpvdhZMxv8N8AyuqLeSImphRCEpCuhOsXlLgjTQ4TUzxYCgPs46iztzD97zRIoSfvG18RqAzT7gec1%2BgUmlHs%2F4rnZeqSjTjZvGhtWiVBQ6tD7uc2x9D92Td0BLQHT%2BYYXreWdkj2%2F75TAkGm9URmgA3F7XPAMzLaU8FfvPYsZrgFOfLww1B1AgD7XFQQ8EIoU1KX34JOwZOUuLo3wGFLMbtuUHSONorz5dG%2FRAMN9csa9Vd1%2BVTgzBoDn2v65x6f274ZESK3IR0EY2T2rXPvTPv4BMGE5mgZm1vw8bnuV7LmrKhKtrSEmCx4gYtxdOeEejILGXOlSBBgNStvVohovgZjXo%2FlQS0D5u4TG6XNgdTVLXXjcAo4YDzEwnVIijEcFXBJLJ56rllwj31uG198zuNAO2ufN0CJFn87kfpddQ%2BXzQUP%2BDnEnMNbe9sAGOt4CGJfEn%2FqhplyBdIJ518Mynvx0%2BcypYGTcXrmOV%2FiSeu5hPn%2FGFi9nqSGeLgVbzXqtcief3EKXSs9iIAu0rdZrbaWODfwuqBgC09CYfbOh8L1v090xkXy7vs0l0iwEgMAugJnlKOU2ClhfCuOyNHdvuQgtUZbBoooJkM0JpLJkIi53rsg4X%2BedQqen2f7QF7aOpbBLP1A3jdBfVdUOUT%2B1b3RnxndLpLwpWSq1mSam6eLIuHlcRMEY7owZOCQepvcDolyg2zHy%2Bn%2FtxAHglZcu%2FTt1aJ%2Br%2B2qPManYHVrTq%2FePR3iQG30yUaGqPOdeCeG8PPKfcsOFkh7AvGCy4AYPbSk24YGrzq07mFiSwPH0ROx7zcigEHXouMhYvq1XT2LkR1bgVev9vL0Nx%2BV1K00qAHSKXrDYocjMG1B0p%2FdwZOxCAEczNfP1LsTzAhlcLp63EnUSdYf0FQkpZxec7jg%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAYSE4OCIXGVOX32V4%2F20250509%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20250509T075328Z&X-Amz-Expires=43200&X-Amz-SignedHeaders=host&X-Amz-Signature=814d6f026ca6534a29d40bda40657e4ec8b637a55013af5d9a6c777ea774adde'},
      { type: "Consolidated Sheet", name: "Final_id_tax", verified: true, url: 'https://temp-abf-data.s3.ap-south-1.amazonaws.com/Ashadeep%20Consolidated%20Data%20Report_FY%2024-25.xlsx?response-content-disposition=inline&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEOP%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCmFwLXNvdXRoLTEiRjBEAiAOk4ixMw6lMlvwgVmZ24058oaEO%2BZSo%2FTK1ilPaOXfUQIgf%2BuTGr4EQrdjad2XifvboK36SddUi2bi3RpoK%2FbDtoUqwgMIjP%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FARAAGgw1ODg3Mzg1OTc0MjIiDPMgN%2BqILV8Mly8SJSqWA0o3kG4gXPHW2t8OFg%2BGTCUe4FdRjmcQpml3OEmfn4ixToX%2BDbRsWYkrp4V8k%2BPdSA89%2FA06E%2B6NyhclTu9DE9%2BmGD7ZvOAqJ4Cb2lDuMMolocR708xBhJcAGJGkeUOl1cHEOhUb2qV3GIBNU7XJQlAmH96aRm9IlYMc0ho9LFBjWG2kJqJgUFN49LDXXg3YhZ3%2Fzl%2F75WwOX5nKUZWikh2L6nEcZGXWFkSaRUkhS8ff4maTUoosWdMtvIRXR7bYg6HDT07FPuC%2BjOELlRP2lE23e7I3JcdBzj9MH6pQDCrBiifO%2BHb7BlW%2Bo18M2SEFT%2FQp7Cv9U9I%2F3SmZmRVZUnYHBaUuDzD5335wUCvIq3XcENcXQKqHGeL1L5xiZxoTuZw95Cq2RV7PnMPCaGnJA%2FycCrYi36AcMkh2G6a3Uv8PY7WhwHNfT%2FcgL5LjFfSOTmMctk%2Flw8OHUs%2FVL4o7UOtTiwLRzqsRhEuAnGpB18nGNIl4%2BmQ5k2rID4uHbYhn9AH1UoUEESoNbMbIGDRFdQgp%2BochBDAw1t72wAY63wKR4kmMj8IPTVhP%2Fq%2BfEeyWcBSJUWL%2FsZa7AoKN%2FG%2FrRBUAk2uzpRtLHMXhbzfxZHHFuVQDDDzbcc9kdgVvjKbz%2Fhd5x2hi%2FlDEx3YDuRKnEgKMHf3l03svk3tDJIW0HWmencyPTBggdUhdijMeLx%2FcP%2FOmQbLc0s6%2BI8monVoaep%2FUsnwqlryxvKKpZsdmJwIqQtyc41kkRcrpLZnnXhs7jLNhqdtk9S0rIX5vYdqjtYtLyMOlLSZdxFEfZRtmxGLLQySuZFQGDfnM9VQ1vF7WclEz%2F7Frts17l2paXp%2B%2FIWw%2BA5cOtCPiz9JIURe00Jt82T1I6t0IS89uhAS89fQyyU5OOwE5OI2Csn3abwqbN0V9j6gioYOfhATnbsCYzwGtzTAJiv29Xt8my1kY2JR5m11NcvX3b4kQViDPnn4TkH231YgEvi%2BL7kPkKsWKbnp5E3Qk3z1K3zVC2hZ8Qnc%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAYSE4OCIXO66ULSDF%2F20250509%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20250509T103510Z&X-Amz-Expires=43200&X-Amz-SignedHeaders=host&X-Amz-Signature=9cf078bd9a758c3964abc27461c919c83ad3253d78d0b84eac390cc9cc31459e' },
    ],
  };

  const grant = grants?.[0];

  // Refined UI layout with Card and gradient border, styled tiles, and documents inside the Card
  return (
    <div className="space-y-6">
      <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden">
        <div className="h-1.5 bg-gradient-to-r from-teal-300 via-teal-500 to-teal-700"></div>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-semibold text-gray-800">Grant Overview</CardTitle>
              <CardDescription>Core information about this grant</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
            <div className="bg-gray-50 p-4 rounded-lg mb-4">
              <h3 className="text-sm font-medium text-gray-500">Grant Name</h3>
              <p className="mt-1 text-gray-900 font-medium">{grant?.name}</p>
            </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-500">Grant Annual Budget</h3>
              <p className="mt-1 text-gray-900 font-medium">₹{grant?.amount}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-500">Program Start Date</h3>
              <p className="mt-1 text-gray-900 font-medium">{grant?.startDate}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-500">Program End Date</h3>
              <p className="mt-1 text-gray-900 font-medium">{grant?.endDate}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg col-span-3">
              <h3 className="text-sm font-medium text-gray-500">Funding Entity</h3>
              <p className="mt-1 text-gray-900">{grant?.fundingSources || "NIL"}</p>
            </div>
          </div>

          <div className="mt-10">
            <h2 className="text-lg font-medium text-gray-700 mb-4">Grant Specific Documents</h2>
            <div className="space-y-4">
              {defaultGrantData.documents.map((doc, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-md bg-white shadow-sm hover:shadow transition">
                  <div className="flex items-center space-x-3">
                    <div className="text-red-500">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium">{doc.type}</p>
                      <p className="text-sm text-gray-500">{doc.name}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm" className="flex items-center space-x-1"
                      onClick={() => window.open(doc.url, "_blank")}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7 10 12 15 17 10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                      </svg>
                      <span>Download</span>
                    </Button>
                    {doc.verified && (
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        <svg className="mr-1" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M20 6L9 17l-5-5"></path>
                        </svg>
                        Verified
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}