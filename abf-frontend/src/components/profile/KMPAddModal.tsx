

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { KMP, KMPUpdateRequest } from "@/types/profile";

interface KMPAddModalProps {
    onClose: () => void;
    onSave: (data: Partial<KMP>) => void;
}

export function KMPAddModal({ onClose, onSave }: KMPAddModalProps) {
    const [formData, setFormData] = useState<Partial<KMP>>({
        name: "",
        designation: "",
        din: "",
        phoneNumber: "",
        email: "",
    });

    const handleChange = (field: keyof Omit<KMP, "id">, value: string) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
    };

    const handleSubmit = () => {
        onSave(formData);
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-md shadow-md w-full max-w-lg">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-lg font-semibold">Add Key Management Personnel</h2>
                    <Button variant="ghost" onClick={onClose}>X</Button>
                </div>
                <div className="space-y-4">
                    {["name", "designation", "din", "phoneNumber", "email"].map((field) => (
                        <div key={field}>
                            <label className="block text-sm font-medium text-gray-700 capitalize">{field}</label>
                            <Input
                                type="text"
                                value={formData[field as keyof Omit<KMP, "id">]}
                                onChange={(e) => handleChange(field as keyof Omit<KMP, "id">, e.target.value)}
                            />
                        </div>
                    ))}
                    <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={onClose}>Cancel</Button>
                        <Button onClick={handleSubmit}>Save</Button>
                    </div>
                </div>
            </div>
        </div>
    );
}