"use client"

import { useState, useEffect, useRef } from "react"
import { Pencil } from "lucide-react"

interface ProfilePicUploaderProps {
  imageSrc?: string | null
  onChange: (file: File) => void
}

export function ProfilePicUploader({ imageSrc, onChange }: ProfilePicUploaderProps) {
  const [preview, setPreview] = useState<string | null>(imageSrc || null)
  const fileInputRef = useRef<HTMLInputElement | null>(null)

  useEffect(() => {
    console.log("Setting preview from imageSrc: ", imageSrc)
    if (imageSrc) {
      setPreview(imageSrc)
    }
  }, [imageSrc])

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const fileURL = URL.createObjectURL(file)
      setPreview(fileURL)
      onChange(file)
    }
  }

  const handleEditClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="w-full flex justify-center">
      <div className="relative w-32 h-32">
        <div className="w-full h-full rounded-full overflow-hidden border shadow">
          {preview ? (
            <img
              src={preview}
              alt="Profile Preview"
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-400 text-sm">
              No Image
            </div>
          )}
        </div>

        {/* Pencil Icon Button */}
        <button
          type="button"
          onClick={handleEditClick}
          className="absolute bottom-0 right-0 p-1 bg-white border rounded-full shadow hover:bg-gray-100"
        >
          <Pencil className="w-4 h-4 text-gray-600" />
        </button>

        {/* Hidden File Input */}
        <input
          type="file"
          accept="image/*"
          ref={fileInputRef}
          onChange={handleFileChange}
          className="hidden"
        />
      </div>
    </div>
  )
}