"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardDescription, CardContent } from "@/components/ui/card";
import React, { useEffect, useState } from "react";
import { Building, Pencil, Trash2, Plus } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import type { ReactElement } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import {
  updateProfile,
  transformOrganizationDetailsFormToOrganizationAPIRequestPayload,
  transformOrganizationAPIResponseToOrganization,
  addKMP,
  transformKMPAPIResponse<PERSON>oKMP,
  transformKMPToKMPUpdateRequest,
  transformHistoricalGrantFormTypeToHistoricalGrantAPIPayload,
  addHistoricalGrant,
  transformHistoricalGrantAPIResponseToHistoricalGrant,
} from "@/services/profile-service";
import { HistoricalGrant, Organization, KMP, KMPUpdateRequest, APIErrorResponse } from "@/types/profile";
import { organizationDetailsFormSchema, organizationDetailsFormDefaultValues, OrganizationDetailsFormType } from "@/schemas/OrganizationDetailsSchema";
import { motion } from "framer-motion";
import { toast } from "sonner";
import { KMPList } from "./KMPList";
import { KMPAddModal } from "./KMPAddModal";
import { AxiosError } from "axios";
import { extractErrors } from "@/lib/axios-error-utils";
import { HistoricalGrantsList } from "./HistoricalGrantsList";
import { HistoricalGrantAddForm } from "./AddHistoricalGrant";
import { HistoricalGrantFormType } from "@/schemas/AddHistoricalGrantSchema";
import { totalmem } from "os";



interface OrganizationDetailsProps {
  organization: Organization | null;
}

export function OrganizationDetails({
  organization,
}: OrganizationDetailsProps): ReactElement {
  // State for tracking editing mode
  const [editing, setEditing] = useState(false);
  // Unified organization data state
  const [organizationData, setOrganizationData] = useState<Organization | null>(organization);

  // Modal state for Add KMP
  const [showAddKMPModal, setShowAddKMPModal] = useState(false);
  // Modal state for Add Grant
  const [showAddGrantModal, setShowAddGrantModal] = useState(false);

  const [kmps, setKmps] = useState<KMP[]>(organization?.kmps || []);

  const [historialGrants, setHistoricalGrants] = useState(organization?.previousGrants || []);

  // KMP editing states
  const [editingKMPs, setEditingKMPs] = useState<Record<number, boolean>>({});
  const [editedKMPs, setEditedKMPs] = useState<Record<number, KMP>>({});

  useEffect(() => {

  }, [kmps])

  useEffect(() => {
    console.log("Resetting org data");
    setOrganizationData(organization);
    setKmps(organization?.kmps || []);
    setHistoricalGrants(organization?.previousGrants || []);
  }, [organization])

  const form = useForm<OrganizationDetailsFormType>({
    resolver: zodResolver(organizationDetailsFormSchema),
    defaultValues: organizationDetailsFormDefaultValues,
    mode: "onChange",
  });

  useEffect(() => {
    form.reset({
      organizationName: organizationData?.organizationName || "",
      organizationLegalType: organizationData?.organizationLegalType || "",
      taxRegistrationNumber: organizationData?.taxRegistrationNumber || "",
      panNumber: organizationData?.panNumber || "",
      teamMembers: organizationData?.numberOfTeamMembers.toString() || "",
      websiteUrl: organizationData?.websiteUrl || "",
      budget: organizationData?.budget?.toString() || "",
      registeredYear: organizationData?.registeredYear?.toString() || "",
      mission: organizationData?.mission || "",
      vision: organizationData?.vision || "",
      backgroundHistory: organizationData?.backgroundHistory || "",

    });
  }, [organizationData]);

  const handleSubmit = async (values: OrganizationDetailsFormType) => {
    const payload = transformOrganizationDetailsFormToOrganizationAPIRequestPayload(values);
    setEditing(false);
    try {
      const response = await updateProfile(payload);

      if (response.status === "SUCCESS") {
        setOrganizationData(transformOrganizationAPIResponseToOrganization(response.data));
        toast.success("Profile information updated successfully");
      }

    } catch (error) {
      toast.error("Unable to update profile")
    }
  }


  async function handleKMPAdd(data: Partial<KMP>): Promise<void> {
    setShowAddKMPModal(false);
    const payload = transformKMPToKMPUpdateRequest(data)
    try {
      const response = await addKMP(payload);
      console.log("Response = ", JSON.stringify(response))
      if (response.status === "SUCCESS") {
        const newKmp = transformKMPAPIResponseToKMP(response.data);
        setKmps((prev) => [newKmp, ...(prev || [])]);
        toast.success("KMP added successfully")
      }

    } catch (error) {
      const errorMessages = extractErrors(error);
      errorMessages.forEach((msg) => toast.error(msg));
    }
  }

  const handleAddHistoricalGrant = async (values: HistoricalGrantFormType) => {
    const backendData = transformHistoricalGrantFormTypeToHistoricalGrantAPIPayload(values);
    try {
      const response = await addHistoricalGrant(backendData);
      console.log("Response = ", JSON.stringify(response));
      if (response.status === 'SUCCESS') {
        console.log("Successs")
        const historicalGrant = transformHistoricalGrantAPIResponseToHistoricalGrant(response.data);
        setHistoricalGrants((prev) => [historicalGrant, ...(prev || [])])
        toast.success("Historical Grant added successfully!");
      }
    } catch (error) {
      toast.error("Unable to add historical grant!")
    }

  }

  return (
    <div>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
        >
          {/* Edit button at the top right */}
          <div className="flex justify-end mb-4">
            {editing ? (
              <>
                <button
                  type="button"
                  className="bg-red-400 text-white mr-2 px-4 py-2 rounded-md hover:bg-red-600 transition"
                  onClick={() => setEditing(false)}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-[#00998F] text-white px-4 py-2 rounded-md hover:bg-teal-600 transition"
                >
                  Save
                </button>
              </>
            ) : (
              <button
                type="button"
                className="bg-[#00998F] text-white px-4 py-2 rounded-md hover:bg-teal-600 transition"

                onClick={() => {
                  setEditing(true);
                }}
              >
                Edit
              </button>
            )}

          </div>

          <motion.div
            key="organization-details-content"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4 }}
          >
            <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden bg-white hover:shadow-md transition-shadow duration-300">
              <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
              <CardHeader className="pb-2">
                <CardTitle className="text-xl font-semibold text-gray-800 flex items-center">
                  <Building className="h-5 w-5 mr-2 text-[#00998F]" />
                  Organization Details
                </CardTitle>
                <CardDescription>Key details of the organization</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Organization Name</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="organizationName"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Organization Name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.organizationName}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Organization Legal Type</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="organizationLegalType"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Organization Legal Type" {...field} disabled />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.organizationLegalTypeName}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Tax Registration Number</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="taxRegistrationNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Tax Registration Number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.taxRegistrationNumber}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">PAN Number</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="panNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="PAN Number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.panNumber}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Number of Team Members</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="teamMembers"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Number of Team Members" type="number" min="1" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.numberOfTeamMembers}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Website URL</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="websiteUrl"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Website URL" type="url" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.websiteUrl}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Budget</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="budget"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Budget" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.budget}</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Registered Year</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="registeredYear"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input placeholder="Registered Year" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.registeredYear}</p>
                    )}
                  </div>
                  <div className="md:col-span-2">
                    <h3 className="text-sm font-medium text-gray-500">Mission Statement</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="mission"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Textarea placeholder="Mission Statement" rows={3} {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.mission}</p>
                    )}
                  </div>
                  <div className="md:col-span-2">
                    <h3 className="text-sm font-medium text-gray-500">Vision</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="vision"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Textarea placeholder="Vision" rows={2} {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium">{organizationData?.vision}</p>
                    )}
                  </div>
                  <div className="md:col-span-2">
                    <h3 className="text-sm font-medium text-gray-500">Background & History</h3>
                    {editing ? (
                      <FormField
                        control={form.control}
                        name="backgroundHistory"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Textarea placeholder="Background & History" rows={5} {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 font-medium whitespace-pre-line">{organizationData?.backgroundHistory}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

        </form>
      </Form>
      <div className="mt-8">
        <div className="flex justify-end mb-4">
          <Button onClick={() => setShowAddKMPModal(true)} className="bg-[#00998F] text-white hover:bg-teal-600">
            <Plus className="w-4 h-4 mr-2" />
            Add KMP
          </Button>
        </div>
        <KMPList kmps={kmps} />

      </div>

      {/* Grant History Section */}
      <div className="mt-8">
        <div className="flex justify-end mb-4">
          <Button onClick={() => setShowAddGrantModal(true)} className="bg-[#00998F] text-white hover:bg-teal-600">
            <Plus className="w-4 h-4 mr-2" />
            Add Grant
          </Button>
        </div>
        <HistoricalGrantsList historicalGrants={historialGrants}/>

      </div>
      {showAddKMPModal && (
        <KMPAddModal
          onSave={(data) => handleKMPAdd(data)}
          onClose={() => setShowAddKMPModal(false)}
        />
      )}

      {showAddGrantModal && (
       <HistoricalGrantAddForm isOpen={showAddGrantModal} onClose={() => setShowAddGrantModal(false)} onSubmit={handleAddHistoricalGrant}/> 
      )}
    </div>
  );
}
