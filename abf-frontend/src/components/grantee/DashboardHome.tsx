"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { Layout } from "@/components/Layout";
import { getProfile } from "@/services/profile-service";
import axios, { AxiosError } from "axios";

const options = [

  { title: "Profile", link: "/profile" },
  // { title: "Reports", link: "/reports" },
  { title: "Funding", link: "/funding" },
  // { title: "Milestones", link: "/milestones" },
  { title: "Help Center", link: "/support-ticket" },

  // { title: "About", link: "/about" },
];

export default function DashboardHome() {
  const router = useRouter();

  useEffect(() => {
    const isLoggedIn = localStorage.getItem("isLoggedIn");
    if (isLoggedIn !== "true") {
      router.push("/login");
      return;
    }

    // Redirect to funding page instead of showing dashboard
    router.push("/funding");

    const fetchProfileData = async () => {
      try {
        const organizationData = await getProfile();

      } catch (error: any) {

        if (axios.isAxiosError(error)) {
          const err = error as AxiosError<{ message: string }>;

          if (err.status == 400) {
            // User must create a new organization
            router.replace('/create-organization');
          }


        } else {
          console.log("Unexpected error in fetchProfileData: ", error);
        }

      }
    }

    fetchProfileData()
  }, [router]);

  // Return null since we're redirecting to the funding page
  return null;
}
