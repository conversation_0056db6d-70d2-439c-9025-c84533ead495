import { motion } from "framer-motion";
import { ReactNode } from "react";

interface EditableTextFieldProps {
  children: ReactNode;
}

const EditableTextField = ({ children }: EditableTextFieldProps) => {
  return (
    <motion.div
      className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors duration-200"
      whileHover={{ y: -2 }}
      transition={{ duration: 0.2 }}
    >
      {children}
    </motion.div>
  );
};

export default EditableTextField;
