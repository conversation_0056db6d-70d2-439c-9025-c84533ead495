"use client";

import { useState, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { motion, AnimatePresence } from "framer-motion";
import { format } from "date-fns";
import { toast } from "sonner";
import {
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  FileSpreadsheetIcon,
  FileX,
  AlertTriangleIcon,
  Loader2,
  RefreshCw,
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { formatCurrency } from "@/lib/utils";
import { getQuarterDateRange } from "@/components/funding/QuarterlyDateSelector";
import apiClient from "@/lib/apiClient";

// Types
interface Organization {
  id: number;
  organization_name: string;
}

interface Grant {
  id: string;
  grant_name: string;
  grant_purpose: string;
  start_date: string;
  end_date: string;
  annual_budget: number;
  funding_sources: string;
}

interface QuestionAnswer {
  id: number;
  question: string;
  answer: string;
}

type ReportStatus = "PENDING" | "APPROVED" | "REJECTED";

interface NarrativeReport {
  id: number;
  title?: string;
  organization: Organization;
  grant?: Grant;
  quarter: number;
  year: number;
  status: ReportStatus;
  created_at: string;
  updated_at?: string;
  remarks?: string;
  question_answers?: QuestionAnswer[];
}

interface UpdateReportStatusPayload {
  status: ReportStatus;
  remarks?: string;
}

interface GranteeReportsTabProps {
  granteeId: string;
  grantId?: string;
}

// Constants
const STATUS_CONFIG = {
  APPROVED: {
    className: "bg-green-100 text-green-800 border-green-200",
    label: "Approved",
    icon: CheckCircle,
    color: "text-green-500",
  },
  PENDING: {
    className: "bg-yellow-100 text-yellow-800 border-yellow-200",
    label: "Pending",
    icon: Clock,
    color: "text-yellow-500",
  },
  REJECTED: {
    className: "bg-red-100 text-red-800 border-red-200",
    label: "Rejected",
    icon: XCircle,
    color: "text-red-500",
  },
} as const;

// API Functions
const fetchReports = async (
  granteeId: string,
  grantId?: string,
): Promise<NarrativeReport[]> => {
  const params = new URLSearchParams();
  params.append("grantee_id", granteeId);
  if (grantId) params.append("grant", grantId);

  const response = await apiClient.get(
    `/api/reports/grantmaker/narrative?${params.toString()}`,
  );
  return response.data || [];
};

const updateReportStatus = async (
  reportId: number,
  payload: UpdateReportStatusPayload,
): Promise<NarrativeReport> => {
  const response = await apiClient.patch(
    `/api/reports/grantmaker/narrative/${reportId}/`,
    payload,
  );
  return response.data;
};

// Components
const StatusIcon = ({ status }: { status: ReportStatus }) => {
  const config = STATUS_CONFIG[status];
  const IconComponent = config.icon;
  return <IconComponent className={`w-4 h-4 ${config.color}`} />;
};

const StatusBadge = ({ status }: { status: ReportStatus }) => {
  const config = STATUS_CONFIG[status];
  const IconComponent = config.icon;
  return (
    <Badge className={`${config.className} flex items-center gap-1.5`}>
      <IconComponent className="w-3 h-3" />
      {config.label}
    </Badge>
  );
};

const ReportCard = ({
  report,
  index,
  onView,
  onApprove,
  onReject,
  isUpdating,
}: {
  report: NarrativeReport;
  index: number;
  onView: (report: NarrativeReport) => void;
  onApprove: (reportId: number) => void;
  onReject: (report: NarrativeReport) => void;
  isUpdating: boolean;
}) => (
  <motion.div
    className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm hover:shadow-md transition-all duration-200"
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3, delay: 0.05 * index }}
  >
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-emerald-100 text-emerald-600 flex-shrink-0">
          <FileSpreadsheetIcon className="h-5 w-5" />
        </div>
        <div>
          <div className="font-medium text-gray-800 flex items-center gap-2">
            {report.grant?.grant_name || report.title || "Untitled Report"}
            <StatusBadge status={report.status} />
          </div>
          <div className="text-sm text-gray-500 flex items-center gap-1">
            <span>{report.year}</span>
            <span className="mx-1">•</span>
            <span>Q{report.quarter}</span>
            <span className="mx-1">•</span>
            <span>{report.organization.organization_name}</span>
            <span className="mx-1">•</span>
            <span>{format(new Date(report.created_at), "MMM dd, yyyy")}</span>
          </div>
        </div>
      </div>

      <div className="flex items-center gap-3">
        <div className="flex gap-2 ml-auto">
          {/* View Button */}
          <Button
            variant="outline"
            size="sm"
            className="text-blue-600 hover:text-blue-800 border-blue-200 hover:bg-blue-50/50 transition-all duration-200"
            onClick={() => onView(report)}
          >
            <Eye className="h-4 w-4 mr-1.5" />
            View
          </Button>
          
          {/* Approve/Reject only if status is PENDING */}
          {report.status === "PENDING" && (
            <>
              <Button
                variant="outline"
                size="sm"
                className="text-red-600 hover:text-red-800 border-red-200 hover:bg-red-50/50 transition-all duration-200"
                onClick={() => onReject(report)}
                disabled={isUpdating}
              >
                <XCircle className="h-4 w-4 mr-1.5" />
                Reject
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="text-green-600 hover:text-green-800 border-green-200 hover:bg-green-50/50 transition-all duration-200"
                onClick={() => onApprove(report.id)}
                disabled={isUpdating}
              >
                <CheckCircle className="h-4 w-4 mr-1.5" />
                Approve
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  </motion.div>
);

const EmptyState = ({ selectedTab }: { selectedTab: string }) => (
  <div className="flex flex-col items-center justify-center py-12 text-gray-500">
    <FileX className="h-12 w-12 mb-4 text-gray-300" />
    <p className="font-medium text-lg">No Reports Found</p>
    <p className="text-sm text-gray-400 mt-1">
      {selectedTab === "all"
        ? "No reports available for this grantee"
        : `No ${selectedTab} reports found`}
    </p>
  </div>
);

const ErrorState = ({ onRetry }: { onRetry: () => void }) => (
  <Alert variant="destructive">
    <AlertTriangleIcon className="h-4 w-4" />
    <AlertDescription className="flex justify-between items-center">
      <span>Failed to load reports. Please try again.</span>
      <Button variant="outline" size="sm" onClick={onRetry} className="ml-4">
        <RefreshCw className="h-4 w-4 mr-2" />
        Retry
      </Button>
    </AlertDescription>
  </Alert>
);

const ViewReportDialog = ({
  report,
  isOpen,
  onClose,
}: {
  report: NarrativeReport | null;
  isOpen: boolean;
  onClose: () => void;
}) => (
  <Dialog open={isOpen} onOpenChange={onClose}>
    <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <div className="flex items-center justify-between">
          <div>
            <DialogTitle className="text-xl">
              {report?.grant?.grant_name || report?.title || "Report Details"}
            </DialogTitle>
            <DialogDescription>
              Q{report?.quarter} {report?.year} •{" "}
              {report?.quarter &&
                report?.year &&
                getQuarterDateRange(`Q${report.quarter}`, `${report.year}`)}
            </DialogDescription>
          </div>
          {report && <StatusBadge status={report.status} />}
        </div>
      </DialogHeader>

      {report && (
        <div className="space-y-6">
          {/* Rejection Notes */}
          {report.status === "REJECTED" && report.remarks && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangleIcon className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-700">
                <div className="font-medium mb-2">Rejection Remarks</div>
                <div className="whitespace-pre-wrap">{report.remarks}</div>
              </AlertDescription>
            </Alert>
          )}

          {/* Grant Details */}
          {report.grant && (
            <Card>
              <CardContent className="space-y-4 pt-6">
                <div>
                  <h4 className="font-medium text-sm text-gray-500 mb-1">
                    Purpose
                  </h4>
                  <p className="text-sm">{report.grant.grant_purpose}</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-sm text-gray-500 mb-1">
                      Grant Period
                    </h4>
                    <p className="text-sm">
                      {format(
                        new Date(report.grant.start_date),
                        "MMM dd, yyyy",
                      )}{" "}
                      to{" "}
                      {format(new Date(report.grant.end_date), "MMM dd, yyyy")}
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-gray-500 mb-1">
                      Annual Budget
                    </h4>
                    <p className="text-sm font-medium">
                      {formatCurrency(report.grant.annual_budget)}
                    </p>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-sm text-gray-500 mb-1">
                    Funding Sources
                  </h4>
                  <p className="text-sm">{report.grant.funding_sources}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Report Q&A */}
          <Card>
            <CardContent className="space-y-6 pt-6">
              <div>
                <h3 className="font-medium mb-2">Report Content</h3>
                <p className="text-sm text-gray-500">
                  Created on {format(new Date(report.created_at), "PPPP")}
                  {report.updated_at &&
                    report.updated_at !== report.created_at && (
                      <span>
                        {" "}
                        • Updated on{" "}
                        {format(new Date(report.updated_at), "PPPP")}
                      </span>
                    )}
                </p>
              </div>
              {report.question_answers && report.question_answers.length > 0 ? (
                report.question_answers.map((qa, index) => (
                  <div key={qa.id}>
                    <h4 className="font-medium text-gray-800 mb-3">
                      {qa.question}
                    </h4>
                    <p className="text-gray-600 whitespace-pre-wrap text-sm leading-relaxed">
                      {qa.answer}
                    </p>
                    {index < report.question_answers!.length - 1 && (
                      <Separator className="mt-6" />
                    )}
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-sm">
                  No questions and answers available for this report.
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      <DialogFooter>
        <Button variant="outline" onClick={onClose}>
          Close
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
);

const RejectReportDialog = ({
  isOpen,
  onClose,
  onSubmit,
  rejectionNotes,
  setRejectionNotes,
  isSubmitting,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: () => void;
  rejectionNotes: string;
  setRejectionNotes: (notes: string) => void;
  isSubmitting: boolean;
}) => (
  <Dialog open={isOpen} onOpenChange={onClose}>
    <DialogContent className="max-w-md">
      <DialogHeader>
        <DialogTitle>Reject Report</DialogTitle>
        <DialogDescription>
          Please provide a reason for rejecting this report. The grantee will be
          able to see these notes and resubmit the report.
        </DialogDescription>
      </DialogHeader>

      <div className="space-y-4">
        <div>
          <Label htmlFor="rejection-notes">Rejection Notes</Label>
          <Textarea
            id="rejection-notes"
            placeholder="Enter the reason for rejection..."
            value={rejectionNotes}
            onChange={(e) => setRejectionNotes(e.target.value)}
            rows={4}
            className="mt-1"
          />
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button
          variant="destructive"
          onClick={onSubmit}
          disabled={isSubmitting || !rejectionNotes.trim()}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Rejecting...
            </>
          ) : (
            "Reject Report"
          )}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
);

// Main Component
export function GranteeReportsTab({
  granteeId,
  grantId,
}: GranteeReportsTabProps) {
  const [selectedTab, setSelectedTab] = useState("all");
  const [selectedReport, setSelectedReport] = useState<NarrativeReport | null>(
    null,
  );
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [rejectionNotes, setRejectionNotes] = useState("");

  const queryClient = useQueryClient();
  const queryKey = ["grantee-reports", granteeId, grantId];

  // Queries
  const {
    data: reports = [],
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey,
    queryFn: () => fetchReports(granteeId, grantId),
  });

  // Mutations
  const updateStatusMutation = useMutation({
    mutationFn: ({
      reportId,
      payload,
    }: {
      reportId: number;
      payload: UpdateReportStatusPayload;
    }) => updateReportStatus(reportId, payload),
    onSuccess: (updatedReport, { payload }) => {
      queryClient.setQueryData(
        queryKey,
        (oldData: NarrativeReport[] | undefined) => {
          if (!oldData) return [updatedReport];
          return oldData.map((report) =>
            report.id === updatedReport.id
              ? { ...updatedReport, updated_at: new Date().toISOString() }
              : report,
          );
        },
      );

      const statusText =
        payload.status === "APPROVED" ? "approved" : "rejected";
      toast.success(`Report ${statusText} successfully`);
    },
    onError: () => {
      toast.error("Failed to update report status");
    },
  });

  // Filter reports
  const filteredReports = reports.filter((report) => {
    if (selectedTab === "all") return true;
    return report.status.toLowerCase() === selectedTab.toLowerCase();
  });

  // Handlers
  const handleViewReport = useCallback((report: NarrativeReport) => {
    setSelectedReport(report);
    setIsViewDialogOpen(true);
  }, []);

  const handleApproveReport = useCallback(
    (reportId: number) => {
      updateStatusMutation.mutate({
        reportId,
        payload: { status: "APPROVED" },
      });
    },
    [updateStatusMutation],
  );

  const handleRejectReport = useCallback((report: NarrativeReport) => {
    setSelectedReport(report);
    setRejectionNotes(report.remarks || "");
    setIsRejectDialogOpen(true);
  }, []);

  const submitRejection = useCallback(() => {
    if (selectedReport && rejectionNotes.trim()) {
      updateStatusMutation.mutate(
        {
          reportId: selectedReport.id,
          payload: {
            status: "REJECTED",
            remarks: rejectionNotes.trim(),
          },
        },
        {
          onSuccess: () => {
            setIsRejectDialogOpen(false);
            setRejectionNotes("");
            setSelectedReport(null);
          },
        },
      );
    }
  }, [selectedReport, rejectionNotes, updateStatusMutation]);

  if (isError) {
    return (
      <Card className="shadow-sm border border-gray-100">
        <CardContent className="p-6">
          <ErrorState onRetry={refetch} />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <CardContent className="p-2">
        <Tabs
          value={selectedTab}
          onValueChange={setSelectedTab}
          className="w-full"
        >
          <TabsList className="mb-6">
            <TabsTrigger value="all">All Reports</TabsTrigger>
            <TabsTrigger value="approved">Approved</TabsTrigger>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="rejected">Rejected</TabsTrigger>
          </TabsList>

          <TabsContent value={selectedTab} className="mt-0">
            {isLoading ? (
              <div className="py-12 text-center text-gray-500">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                <p>Loading reports data...</p>
              </div>
            ) : filteredReports.length === 0 ? (
              <EmptyState selectedTab={selectedTab} />
            ) : (
              <AnimatePresence mode="wait">
                <motion.div
                  key={`${selectedTab}-${filteredReports.length}`}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="space-y-6"
                >
                  {filteredReports.map((report, index) => (
                    <ReportCard
                      key={report.id}
                      report={report}
                      index={index}
                      onView={handleViewReport}
                      onApprove={handleApproveReport}
                      onReject={handleRejectReport}
                      isUpdating={updateStatusMutation.isPending}
                    />
                  ))}
                </motion.div>
              </AnimatePresence>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* View Report Dialog */}
      <ViewReportDialog
        report={selectedReport}
        isOpen={isViewDialogOpen}
        onClose={() => setIsViewDialogOpen(false)}
      />

      {/* Reject Report Dialog */}
      <RejectReportDialog
        isOpen={isRejectDialogOpen}
        onClose={() => {
          setIsRejectDialogOpen(false);
          setRejectionNotes("");
        }}
        onSubmit={submitRejection}
        rejectionNotes={rejectionNotes}
        setRejectionNotes={setRejectionNotes}
        isSubmitting={updateStatusMutation.isPending}
      />
    </div>
  );
}
