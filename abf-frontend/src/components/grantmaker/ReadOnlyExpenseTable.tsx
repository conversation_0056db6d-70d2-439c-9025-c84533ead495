import { Card } from "@/components/ui/card";

export interface ExpenseRow {
  id: number;
  particulars: string;
  main_header: string;
  sub_header: string;
  units: number;
  frequency: number;
  cost_per_unit: number;
  total_grant_budget: number;
  actual_q1: number;
  actual_q2: number;
  actual_q3: number;
  actual_q4: number;
  activity_description: string;
}

interface Props {
  expenses: ExpenseRow[];
}

export default function ReadOnlyExpenseTable({ expenses }: Props) {
  return (
    <Card className="rounded-2xl shadow-md bg-white overflow-x-auto">
      <table className="min-w-full text-sm text-left border-collapse">
        <thead className="bg-gray-50 text-gray-700">
          <tr>
            {[
              "Sr.",
              "Particulars",
              "Main Header",
              "Sub Header",
              "Units",
              "Frequency",
              "Cost per Unit",
              "Total Grant Budget",
              "Actual Q1",
              "Actual Q2",
              "Actual Q3",
              "Actual Q4",
              "Activity Description",
            ].map((head, idx) => (
              <th key={idx} className="px-4 py-3 font-semibold text-sm">
                {head}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="text-gray-800">
          {expenses.map((row, index) => (
            <tr key={row.id} className="hover:bg-gray-50 transition">
              <td className="px-4 py-3 font-semibold">
                {String(index + 1).padStart(2, "0")}
              </td>
              <td className="px-4 py-3">{row.particulars || "N/A"}</td>
              <td className="px-4 py-3">{row.main_header|| "N/A"}</td>
              <td className="px-4 py-3">{row.sub_header || "N/A"}</td>
              <td className="px-4 py-3">{row.units}</td>
              <td className="px-4 py-3">{row.frequency}</td>
              <td className="px-4 py-3">
                ₹{parseFloat(row.cost_per_unit.toString()).toFixed(2)}
              </td>
              <td className="px-4 py-3">
                ₹
                {parseFloat(row.total_grant_budget.toString()).toLocaleString(
                  "en-IN",
                  {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  }
                )}
              </td>
              <td className="px-4 py-3">{row.actual_q1}</td>
              <td className="px-4 py-3">{row.actual_q2}</td>
              <td className="px-4 py-3">{row.actual_q3}</td>
              <td className="px-4 py-3">{row.actual_q4}</td>
              <td className="px-4 py-3">{row.activity_description || "N/A"}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </Card>
  );
}