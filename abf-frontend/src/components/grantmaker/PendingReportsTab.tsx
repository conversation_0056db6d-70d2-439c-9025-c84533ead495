"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardT<PERSON>le, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Check,
  X,
  FileSpreadsheetIcon,
  FileX,
  AlertTriangleIcon
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { motion } from "framer-motion";
import { formatCurrency } from '@/lib/utils';
import { format } from "date-fns";
import { getQuarterDateRange } from "@/components/funding/QuarterlyDateSelector";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import apiClient from "@/lib/apiClient";

interface Organization {
    id: number;
    organization_name: string;
}

interface Grant {
    id: string;
    grant_name: string;
    grant_purpose: string;
    start_date: string;
    end_date: string;
    annual_budget: number;
    funding_sources: string;
}

interface QuestionAnswer {
    id: number;
    question: string;
    answer: string;
}

type ReportStatus = 'PENDING' | 'APPROVED' | 'REJECTED';

interface NarrativeReportListItem {
    id: number;
    title?: string;
    organization: Organization;
    grant?: Grant;
    quarter?: number;
    year?: number;
    status: ReportStatus;
    grant_id?: number | string;
    created_at: string;
    updated_at?: string;
    remarks?: string;
    question_answers?: QuestionAnswer[];
}

interface GranteeReportsTabProps {
  granteeId: string;
  grantId?: string;
}

export function PendingReportsTab({ granteeId, grantId }: GranteeReportsTabProps) {
  const [reports, setReports] = useState<NarrativeReportListItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedReport, setSelectedReport] = useState<NarrativeReportListItem | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [rejectionNotes, setRejectionNotes] = useState('');

  useEffect(() => {
    const fetchReports = async () => {
      setIsLoading(true);
      try {
        const params = new URLSearchParams();
        params.append('grantee_id', granteeId);
        if (grantId) {
          params.append('grant', grantId);
        }

        const queryString = params.toString();
        const response = await apiClient.get(`/api/reports/grantmaker/narrative?${queryString}`);
        // Filter to show only pending reports
        const pendingReports = (response.data || []).filter((report: NarrativeReportListItem) => 
          report.status === 'PENDING'
        );
        setReports(pendingReports);
      } catch (error) {
        console.error('Error fetching reports:', error);
        toast.error('Failed to load reports data');
        setReports([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchReports();
  }, [granteeId, grantId]);

  // Update report status (approve/reject)
  const updateReportStatus = async (reportId: number, newStatus: ReportStatus, remarks?: string) => {
    setIsUpdatingStatus(true);
    try {
      const payload: any = { status: newStatus };
      if (newStatus === 'REJECTED' && remarks) {
        payload.remarks = remarks;
      }

      await apiClient.patch(`/api/reports/grantmaker/narrative/${reportId}/`, payload);
      
      // Remove the report from the list since it's no longer pending
      setReports(reports.filter(report => report.id !== reportId));
      
      const statusText = newStatus === 'APPROVED' ? 'approved' : 'rejected';
      toast.success(`Report ${statusText} successfully`);

      // Close dialogs
      setIsRejectDialogOpen(false);
      setRejectionNotes('');
    } catch (error) {
      console.error('Error updating report status:', error);
      toast.error('Failed to update report status');
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Handle approve report
  const handleApproveReport = (reportId: number) => {
    updateReportStatus(reportId, 'APPROVED');
  };

  // Handle reject report
  const handleRejectReport = (report: NarrativeReportListItem) => {
    setSelectedReport(report);
    setRejectionNotes(report.remarks || '');
    setIsRejectDialogOpen(true);
  };

  // Submit rejection with notes
  const submitRejection = () => {
    if (selectedReport) {
      updateReportStatus(selectedReport.id, 'REJECTED', rejectionNotes);
    }
  };

  // Handle view report details
  const handleViewReport = (report: NarrativeReportListItem) => {
    setSelectedReport(report);
    setIsViewDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <Card className="shadow-sm border border-gray-100">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-yellow-500" />
            Pending Reports
          </CardTitle>
          <CardDescription>
            Reports waiting for review and approval
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="py-8 text-center text-gray-500">Loading pending reports...</div>
          ) : reports.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-gray-500">
              <FileX className="h-12 w-12 mb-2 text-gray-300" />
              <p className="font-medium">No pending reports</p>
              <p className="text-sm text-gray-400 mt-1">
                All reports have been reviewed
              </p>
            </div>
          ) : (
            <div className="flex flex-col gap-4">
              {reports.map((report, index) => (
                <motion.div
                  key={`report-${report.id}`}
                  className="flex items-center justify-between bg-white p-4 rounded-lg border border-gray-100 shadow-sm hover:shadow-md transition-all duration-200"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.05 * index }}
                >
                  <div className="flex items-center gap-3">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-yellow-100 text-yellow-600 flex-shrink-0">
                      <FileSpreadsheetIcon className="h-5 w-5" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-800">
                        {report.grant?.grant_name || report.title || 'Untitled Report'}
                      </div>
                      <div className="text-sm text-gray-500 flex items-center gap-1">
                        <span>{report.year}</span>
                        <span className="mx-1">•</span>
                        <span>Q{report.quarter}</span>
                        {report.organization && (
                          <>
                            <span className="mx-1">•</span>
                            <span>{report.organization.organization_name}</span>
                          </>
                        )}
                        <span className="mx-1">•</span>
                        <span>{format(new Date(report.created_at), "MMM dd, yyyy")}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Clock className="w-4 h-4 text-yellow-500" />
                    <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
                    <div className="flex gap-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="h-8 px-2"
                        onClick={() => handleViewReport(report)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="h-8 px-2 text-green-600 border-green-200 hover:bg-green-50"
                        onClick={() => handleApproveReport(report.id)}
                        disabled={isUpdatingStatus}
                      >
                        <Check className="h-4 w-4 mr-1" />
                        Approve
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="h-8 px-2 text-red-600 border-red-200 hover:bg-red-50"
                        onClick={() => handleRejectReport(report)}
                        disabled={isUpdatingStatus}
                      >
                        <X className="h-4 w-4 mr-1" />
                        Reject
                      </Button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Report Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <div>
                <DialogTitle className="text-xl">
                  {selectedReport?.grant?.grant_name || selectedReport?.title || 'Report Details'}
                </DialogTitle>
                <DialogDescription>
                  Q{selectedReport?.quarter} {selectedReport?.year} • {' '}
                  {selectedReport?.quarter && selectedReport?.year && 
                    getQuarterDateRange(`Q${selectedReport.quarter}`, `${selectedReport.year}`)
                  }
                </DialogDescription>
              </div>
              <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
            </div>
          </DialogHeader>
          
          {selectedReport && (
            <div className="space-y-6">
              {/* Grant Details */}
              {selectedReport.grant && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Grant Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">Purpose</h4>
                      <p className="text-sm">{selectedReport.grant.grant_purpose}</p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium text-sm text-gray-500 mb-1">Grant Period</h4>
                        <p className="text-sm">
                          {format(new Date(selectedReport.grant.start_date), "MMM dd, yyyy")} to{" "}
                          {format(new Date(selectedReport.grant.end_date), "MMM dd, yyyy")}
                        </p>
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-gray-500 mb-1">Annual Budget</h4>
                        <p className="text-sm font-medium">{formatCurrency(selectedReport.grant.annual_budget)}</p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">Funding Sources</h4>
                      <p className="text-sm">{selectedReport.grant.funding_sources}</p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Report Q&A */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Report Content</CardTitle>
                  <CardDescription>
                    Created on {format(new Date(selectedReport.created_at), "PPPP")}
                    {selectedReport.updated_at && selectedReport.updated_at !== selectedReport.created_at && (
                      <span> • Updated on {format(new Date(selectedReport.updated_at), "PPPP")}</span>
                    )}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {selectedReport.question_answers && selectedReport.question_answers.length > 0 ? (
                    selectedReport.question_answers.map((qa, index) => (
                      <div key={qa.id}>
                        <h4 className="font-medium text-gray-800 mb-2">{qa.question}</h4>
                        <p className="text-gray-600 whitespace-pre-wrap text-sm leading-relaxed">
                          {qa.answer}
                        </p>
                        {index < selectedReport.question_answers!.length - 1 && (
                          <Separator className="mt-4" />
                        )}
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-sm">No questions and answers available for this report.</p>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
          
          <DialogFooter>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                Close
              </Button>
              <Button 
                className="text-green-600 border-green-200 hover:bg-green-50"
                variant="outline"
                onClick={() => {
                  if (selectedReport) {
                    handleApproveReport(selectedReport.id);
                    setIsViewDialogOpen(false);
                  }
                }}
                disabled={isUpdatingStatus}
              >
                <Check className="h-4 w-4 mr-1" />
                Approve
              </Button>
              <Button 
                variant="outline"
                className="text-red-600 border-red-200 hover:bg-red-50"
                onClick={() => {
                  if (selectedReport) {
                    setIsViewDialogOpen(false);
                    handleRejectReport(selectedReport);
                  }
                }}
                disabled={isUpdatingStatus}
              >
                <X className="h-4 w-4 mr-1" />
                Reject
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Report Dialog */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Reject Report</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this report. The grantee will be able to see these notes and resubmit the report.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="rejection-notes">Rejection Notes</Label>
              <Textarea
                id="rejection-notes"
                placeholder="Enter the reason for rejection..."
                value={rejectionNotes}
                onChange={(e) => setRejectionNotes(e.target.value)}
                rows={4}
                className="mt-1"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => {
                setIsRejectDialogOpen(false);
                setRejectionNotes('');
              }}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive"
              onClick={submitRejection}
              disabled={isUpdatingStatus || !rejectionNotes.trim()}
            >
              {isUpdatingStatus ? 'Rejecting...' : 'Reject Report'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}