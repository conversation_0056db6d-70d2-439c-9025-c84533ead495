import { GalleryItem } from "@/app/milestones-reports/_components/gallery/attachment-card";
import GalleryGrid from "@/app/milestones-reports/_components/gallery/gallery-grid";
import apiClient from "@/lib/apiClient";
import { useQuery } from "@tanstack/react-query";
import React from "react";

function GranteeGalleryTab({ granteeId }: { granteeId: number }) {
  const gallery = useQuery({
    queryKey: ["gallery"],
    queryFn: () =>
      apiClient
        .get(`/api/reports/grantmaker/gallery/?organization=${granteeId}`)
        .then((res) => res.data),
  });

  return (
    <div>
      <GalleryGrid
        title={"Images"}
        filter={(item: GalleryItem) => item.file_type.startsWith("image/")}
        gallery={gallery}
      />

      <GalleryGrid
        title={"Videos"}
        gallery={gallery}
        filter={(item: GalleryItem) => item.file_type.startsWith("video/")}
      />

      <GalleryGrid
        title={"Files"}
        gallery={gallery}
        filter={(item: GalleryItem) =>
          !(
            item.file_type.startsWith("video/") ||
            item.file_type.startsWith("image/")
          )
        }
      />
    </div>
  );
}

export default GranteeGalleryTab;
