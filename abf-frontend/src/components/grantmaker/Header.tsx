"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  Menu,
  X,
  LogOut,
  LayoutDashboard,
  Building2,
  FileText,
  DollarSign,
  BarChart3,
  User,
  InfoIcon
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { motion, AnimatePresence } from "framer-motion";
import { useAuthContext } from "@/contexts/AuthContext";

interface HeaderProps {
  // Keeping title prop for backward compatibility, but not displaying it as requested
  title?: string;
  toggleSidebar: () => void;
}

export function Header({
  title = "Dashboard",
  toggleSidebar
}: HeaderProps) {
  const router = useRouter();

  const { user, isLoading, logout } = useAuthContext();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Using title in a way that doesn't affect the UI to avoid unused variable warning
  useEffect(() => {
    // This is just to use the title prop without displaying it
    document.title = title ? `ABF - ${title}` : "ABF Dashboard";
  }, [title]);

  // Handle scroll effect and update header height CSS variable
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      setScrolled(isScrolled);

      // Update CSS variable for header height
      document.documentElement.style.setProperty(
        '--header-height',
        isScrolled ? '4rem' : '5rem'
      );
    };

    // Set initial header height
    document.documentElement.style.setProperty('--header-height', '5rem');

    // Run once on mount to ensure the variable is set
    handleScroll();

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  const handleLogout = () => {
    localStorage.clear();
    router.push("/login");
  };

  const formattedDate = new Intl.DateTimeFormat('en-US', {
    weekday: 'long',
    month: 'short',
    day: 'numeric',
  }).format(currentTime);

  const formattedTime = new Intl.DateTimeFormat('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true
  }).format(currentTime);

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-40 w-full transition-all duration-150 ${scrolled ? 'h-16' : 'h-20'}`}
    >
      {/* Backdrop blur and gradient overlay */}
      <div
        className={`absolute inset-0 bg-white backdrop-blur-md border-b border-gray-200 transition-all duration-150 ${scrolled ? 'shadow-md' : 'shadow-sm'}`}
      ></div>

      {/* Header content */}
      <div className="relative flex items-center justify-between h-full px-4 md:px-6">
        <div className="flex items-center gap-3">
          {/* Sidebar toggle with animation */}
          <div className="hidden md:flex">
          <motion.button
            onClick={toggleSidebar}
            className="rounded-full p-2 text-gray-600 hover:bg-teal-100 hover:text-teal-600 transition-all duration-200 group"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Menu className="h-5 w-5" />
          </motion.button>

          </div>


          {/* Logo with animation */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Link href="/grantmaker" className="flex items-center">
              <img src="/OAB-logo.svg" alt="OAB Logo" width="42" height="22" className="transition-transform duration-300 hover:scale-105" />
              <div className="h-6 w-[1px] bg-gray-300 mx-2"></div>
              <img src="/AB_Foundation.png" alt="Logo" className="ml-1 transition-transform duration-300 hover:scale-105" width="82" height="34"  />
            </Link>
          </motion.div>

          {/* Title removed as requested */}
        </div>

        {/* Right side elements */}
        <div className="flex items-center gap-3">
          {/* Date and time display - desktop only */}
          <div className="hidden lg:flex flex-col items-end mr-4">
            <p className="text-sm font-medium text-gray-700">{formattedDate}</p>
            <p className="text-xs text-gray-500">{formattedTime}</p>
          </div>



          {/* Settings button removed as requested */}

          {/* User dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <motion.button
                className="flex items-center gap-2 rounded-full pl-2 pr-3 py-1.5 bg-white border border-gray-200 hover:border-teal-200 shadow-sm hover:shadow transition-all duration-200"
                whileHover={{ scale: 1.02 }}
              >
                <div className="relative">
                  <div className="h-8 w-8 rounded-full bg-gradient-to-br from-teal-600 to-teal-500 flex items-center justify-center shadow-md transition-all duration-300 border-2 border-white">
                    <span className="text-white font-bold text-sm">
                      {user?.firstName?.[0] || ""}
                      {user?.lastName?.[0] || ""}
                    </span>
                  </div>
                </div>
                <div className="hidden md:block text-left">
                  <p className="text-sm font-medium text-gray-800">
                    {user?.firstName && user.firstName !== "null" ? user.firstName : ""}
                    {user?.lastName && user.lastName !== "null" ? ` ${user.lastName}` : ""}
                  </p>
                  <p className="text-xs text-gray-500">{user?.role}</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400 ml-1"><path d="m6 9 6 6 6-6"/></svg>
              </motion.button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-64 p-2 bg-white shadow-xl border border-gray-100 rounded-xl">
              <div className="mb-2 pb-2 border-b border-gray-100">
                <div className="flex items-center gap-3 mb-2 p-2">
                  <div className="h-8 w-8 rounded-full bg-gradient-to-br from-teal-600 to-teal-500 flex items-center justify-center shadow-md border-2 border-white">
                    <span className="text-white font-bold text-sm">
                      {user?.firstName?.[0] || ""}
                      {user?.lastName?.[0] || " "}
                    </span>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-800">
                      {user?.firstName && user.firstName !== "null" ? user.firstName : ""}
                      {user?.lastName && user.lastName !== "null" ? ` ${user.lastName}` : " "}
                    </p>
                    <span className="text-xs text-gray-500 break-words whitespace-normal block w-48 overflow-hidden text-ellipsis">
                      {user?.email}
                    </span>
                  </div>
                </div>
              </div>

              <DropdownMenuItem
                className="cursor-pointer hover:bg-teal-50 hover:text-teal-600 transition-colors duration-200 rounded-lg flex items-center gap-2 p-2 mt-1"
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4 text-teal-500" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden relative group"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <div className="absolute -inset-0.5 bg-gradient-to-r from-teal-200 to-teal-400 rounded-md opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            {isMobileMenuOpen ?
              <X className="h-5 w-5 transition-transform duration-300 rotate-0" /> :
              <Menu className="h-5 w-5 transition-transform duration-300 group-hover:scale-110" />
            }
          </Button>
        </div>
      </div>

      {/* Mobile Menu with animation */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            key="mobile-menu"
            className="absolute left-0 right-0 top-full z-20 bg-white shadow-lg md:hidden border-b border-gray-200"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.15 }}
          >
            <div className="p-4">
              <nav className="space-y-2">
                <Link
                  href="/"
                  className="flex items-center rounded-md px-3 py-2 text-sm text-gray-700 hover:bg-teal-50 hover:text-teal-600 transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <LayoutDashboard className="mr-2 h-4 w-4 text-teal-500" />
                  Dashboard
                </Link>
                <Link
                  href="/grantmaker/grantees"
                  className="flex items-center rounded-md px-3 py-2 text-sm text-gray-700 hover:bg-teal-50 hover:text-teal-600 transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Building2 className="mr-2 h-4 w-4 text-teal-500" />
                  Grant Partners
                </Link>
                {/* <Link
                  href="/grantmaker/milestones"
                  className="flex items-center rounded-md px-3 py-2 text-sm text-gray-700 hover:bg-teal-50 hover:text-teal-600 transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <FileText className="mr-2 h-4 w-4 text-teal-500" />
                  Milestones
                </Link> */}
                <Link
                  href="/finance"
                  className="flex items-center rounded-md px-3 py-2 text-sm text-gray-700 hover:bg-teal-50 hover:text-teal-600 transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <DollarSign className="mr-2 h-4 w-4 text-teal-500" />
                  Finance
                </Link>
                <Link
                  href="#"
                  className="flex items-center rounded-md px-3 py-2 text-sm text-gray-700 hover:bg-teal-50 hover:text-teal-600 transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <FileText className="mr-2 h-4 w-4 text-teal-500" />
                  Grants
                </Link>
                <Link
                  href="/grantmaker/support/my-tickets/support-ticket"
                  className="flex items-center rounded-md px-3 py-2 text-sm text-gray-700 hover:bg-teal-50 hover:text-teal-600 transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <InfoIcon className="mr-2 h-4 w-4 text-teal-500" />
                  Help Center
                </Link>
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
}
