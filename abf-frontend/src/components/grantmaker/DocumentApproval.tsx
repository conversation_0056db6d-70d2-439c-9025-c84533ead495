"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { verifyDocument, Document } from "@/services/document-service";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, AlertCircle, FileText, Eye } from "lucide-react";

interface DocumentApprovalProps {
  documents: Document[];
  onDocumentStatusChange?: (documentId: string, status: string) => void;
}

interface DocumentReviewDialogProps {
  document: Document;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onApprove: (documentId: string) => void;
  onReject: (documentId: string, comments: string) => void;
}

const getStatusBadge = (status: string) => {
  switch (status) {
    case "verified":
      return (
        <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          Verified
        </Badge>
      );
    case "pending":
      return (
        <Badge className="bg-yellow-100 text-yellow-800 flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Pending
        </Badge>
      );
    case "update-required":
      return (
        <Badge className="bg-red-100 text-red-800 flex items-center gap-1">
          <XCircle className="h-3 w-3" />
          Update Required
        </Badge>
      );
    case "not-uploaded":
      return (
        <Badge className="bg-gray-100 text-gray-800 flex items-center gap-1">
          Not Uploaded
        </Badge>
      );
    default:
      return null;
  }
};

const DocumentReviewDialog = ({
  document,
  open,
  onOpenChange,
  onApprove,
  onReject
}: DocumentReviewDialogProps) => {
  const [comments, setComments] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleApprove = async () => {
    setIsSubmitting(true);
    try {
      await onApprove(document.id);
      onOpenChange(false);
    } catch (error) {
      console.error("Error approving document:", error);
      toast.error("Failed to approve document");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReject = async () => {
    if (!comments.trim()) {
      toast.error("Please provide comments explaining why the document is rejected");
      return;
    }

    setIsSubmitting(true);
    try {
      await onReject(document.id, comments);
      onOpenChange(false);
    } catch (error) {
      console.error("Error rejecting document:", error);
      toast.error("Failed to reject document");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Review Document</DialogTitle>
          <DialogDescription>
            Review and approve or reject the document with comments.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-md">
            <FileText className="h-8 w-8 text-orange-500" />
            <div>
              <h3 className="font-medium">{document.name}</h3>
              <p className="text-sm text-gray-500">Uploaded on {new Date(document.uploadedAt).toLocaleDateString()}</p>
            </div>
            {getStatusBadge(document.status)}
          </div>

          {document.url && (
            <div className="flex justify-center">
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={() => window.open(document.url, "_blank")}
              >
                <Eye className="h-4 w-4" />
                View Document
              </Button>
            </div>
          )}

          <div className="space-y-2">
            <h4 className="text-sm font-medium">Comments</h4>
            <Textarea
              placeholder="Provide comments about this document, especially if rejecting"
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              rows={4}
            />
          </div>
        </div>

        <DialogFooter className="flex justify-between sm:justify-between">
          <Button
            type="button"
            variant="destructive"
            onClick={handleReject}
            disabled={isSubmitting}
          >
            Reject
          </Button>
          <Button
            type="button"
            variant="default"
            className="bg-green-600 hover:bg-green-700"
            onClick={handleApprove}
            disabled={isSubmitting}
          >
            Approve
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export function DocumentApproval({ documents, onDocumentStatusChange }: DocumentApprovalProps) {
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);

  const handleReviewClick = (document: Document) => {
    setSelectedDocument(document);
    setIsReviewDialogOpen(true);
  };

  const handleApproveDocument = async (documentId: string) => {
    try {
      const result = await verifyDocument(documentId, "verified");
      if (result.success) {
        toast.success("Document approved successfully");
        if (onDocumentStatusChange) {
          onDocumentStatusChange(documentId, "verified");
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error approving document:", error);
      toast.error("Failed to approve document");
    }
  };

  const handleRejectDocument = async (documentId: string, comments: string) => {
    try {
      const result = await verifyDocument(documentId, "update-required", comments);
      if (result.success) {
        toast.success("Document rejected with comments");
        if (onDocumentStatusChange) {
          onDocumentStatusChange(documentId, "update-required");
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error rejecting document:", error);
      toast.error("Failed to reject document");
    }
  };

  return (
    <div className="space-y-4">
      {documents.length > 0 ? (
        documents.map((document) => (
          <div key={document.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-md">
            <div className="flex items-center">
              <FileText className="h-5 w-5 mr-3 text-orange-500" />
              <div>
                <div className="flex items-center gap-2">
                  <p className="font-medium">{document.name}</p>
                  {getStatusBadge(document.status)}
                </div>
                <p className="text-sm text-gray-500">
                  {document.uploadedAt ? `Uploaded on ${new Date(document.uploadedAt).toLocaleDateString()}` : "Not uploaded yet"}
                </p>
                {document.status === "update-required" && document.comments && (
                  <p className="text-sm text-red-500 mt-1">{document.comments}</p>
                )}
              </div>
            </div>
            {document.status !== "not-uploaded" && (
              <Button
                onClick={() => handleReviewClick(document)}
                variant="outline"
                className="text-orange-500 border-orange-500 hover:bg-orange-50"
              >
                Review
              </Button>
            )}
          </div>
        ))
      ) : (
        <div className="text-center p-6 bg-gray-50 rounded-md">
          <p className="text-gray-500">No documents available for review</p>
        </div>
      )}

      {selectedDocument && (
        <DocumentReviewDialog
          document={selectedDocument}
          open={isReviewDialogOpen}
          onOpenChange={setIsReviewDialogOpen}
          onApprove={handleApproveDocument}
          onReject={handleRejectDocument}
        />
      )}
    </div>
  );
}