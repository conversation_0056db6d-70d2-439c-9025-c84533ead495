"use client";

import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Search, Filter, ArrowUpDown, X, ChevronLeft, ChevronRight, User as UserIcon, Building, Mail } from "lucide-react";
import { motion } from "framer-motion";
import { User } from "@/services/grantmaker/user-service";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Organization } from "@/types/profile";

interface GranteeUserTableProps {
  users: Organization[];
  onViewDetails: (userId: number) => void;
  title?: string;
  description?: string;
}

export function GranteeUserTable({
  users: grantees,
  onViewDetails,
  title = "Grantees",
  description = "Click on a grantee to view details"
}: GranteeUserTableProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState<keyof Organization>("organizationName");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [organizationFilter, setOrganizationFilter] = useState<string[]>([]);

  // Get unique organizations
  const organizations = [...new Set(grantees.map(user => user.organizationName || 'Unknown'))];

  const handleSort = (field: keyof Organization) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const toggleOrganizationFilter = (organization: string) => {
    setOrganizationFilter(prev =>
      prev.includes(organization)
        ? prev.filter(o => o !== organization)
        : [...prev, organization]
    );
  };

  const clearFilters = () => {
    setOrganizationFilter([]);
    setSearchTerm("");
  };

  const filteredGrantees = grantees.filter(grantee => {
    const organizationName = grantee.organizationName.toLowerCase();
    const matchesSearch = 
      organizationName.includes(searchTerm.toLowerCase()) ||
      grantee.emailAddress.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  const sortedGrantees = [...filteredGrantees].sort((a, b) => {
    let aValue: any = a[sortField];
    let bValue: any = b[sortField];
    
    if (sortField === 'organizationName') {
      aValue = a.organizationName
      bValue = b.organizationName
    }
    
    if (aValue === undefined) return 1;
    if (bValue === undefined) return -1;
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }
    
    return sortDirection === 'asc'
      ? (aValue > bValue ? 1 : -1)
      : (bValue > aValue ? 1 : -1);
  });

  // Calculate pagination
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const totalPages = Math.ceil(sortedGrantees.length / itemsPerPage);
  const paginatedGrantees = sortedGrantees.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="bg-white shadow-md hover:shadow-xl transition-all duration-300 border-0 overflow-hidden">
        <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
        <CardHeader className="pb-2">
          <CardTitle className="text-xl font-bold bg-gradient-to-r from-[#00998F] to-teal-600 bg-clip-text text-transparent">
            {title}
          </CardTitle>
          <CardDescription className="text-gray-600">{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
            <div className="relative w-full md:w-auto">
              <div className="relative group">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-[#00998F] to-teal-500 rounded-lg opacity-0 group-hover:opacity-20 transition-all duration-300"></div>
                <div className="relative flex items-center">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400 group-hover:text-[#00998F] transition-colors duration-200" />
                  <input
                    type="text"
                    placeholder="Search grantees..."
                    className="h-10 w-full md:w-64 rounded-lg border border-gray-200 bg-white pl-10 pr-4 text-sm focus:border-[#00998F] focus:outline-none focus:ring-1 focus:ring-[#00998F] shadow-sm group-hover:shadow transition-all duration-200"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  {searchTerm && (
                    <button
                      className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      onClick={() => setSearchTerm("")}
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="border-gray-200 hover:border-teal-200 hover:bg-teal-50 transition-all duration-200">
                    <Filter className="h-4 w-4 mr-2 text-teal-500" />
                    Filter
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56 p-2 bg-white shadow-xl border border-gray-100 rounded-xl overflow-hidden">
                  <DropdownMenuLabel className="text-gray-800 font-medium">Filter Grantees</DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-gray-100" />
                  <DropdownMenuLabel className="text-xs font-medium text-gray-500 mt-1">Organization</DropdownMenuLabel>
                  {organizations.map(organization => (
                    <DropdownMenuCheckboxItem
                      key={organization}
                      checked={organizationFilter.includes(organization)}
                      onCheckedChange={() => toggleOrganizationFilter(organization)}
                      className="rounded-md my-0.5 focus:bg-teal-50 focus:text-[#00998F]"
                    >
                      {organization}
                    </DropdownMenuCheckboxItem>
                  ))}
                  {(organizationFilter.length > 0 || searchTerm) && (
                    <>
                      <DropdownMenuSeparator className="bg-gray-100 my-2" />
                      <DropdownMenuItem
                        className="rounded-md text-center text-teal-600 hover:text-teal-700 hover:bg-teal-50 cursor-pointer"
                        onClick={clearFilters}
                      >
                        Clear Filters
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className="overflow-x-auto rounded-lg border border-gray-100 shadow-sm">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gradient-to-r from-gray-50 to-white border-b border-gray-200">
                  <th className="py-3 px-4 text-sm font-medium text-gray-600">
                    <button
                      onClick={() => handleSort("organizationName")}
                      className="flex items-center gap-1 hover:text-[#00998F] transition-colors duration-200"
                    >
                      Name
                      <ArrowUpDown className="h-4 w-4" />
                    </button>
                  </th>
                  <th className="py-3 px-4 text-sm font-medium text-gray-600">
                    <button
                      onClick={() => handleSort("emailAddress")}
                      className="flex items-center gap-1 hover:text-[#00998F] transition-colors duration-200"
                    >
                      Email
                      <ArrowUpDown className="h-4 w-4" />
                    </button>
                  </th>
                  <th className="py-3 px-4 text-sm font-medium text-gray-600">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {paginatedGrantees.length === 0 ? (
                  <tr>
                    <td colSpan={4} className="py-8 text-center text-gray-500">
                      <div className="flex flex-col items-center justify-center">
                        <UserIcon className="h-12 w-12 text-gray-300 mb-2" />
                        <p className="text-gray-500 font-medium">No grantees found</p>
                        <p className="text-gray-400 text-sm mt-1">Try adjusting your search or filters</p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  paginatedGrantees.map((grantee, index) => (
                    <tr
                      key={grantee.id}
                      className={`border-b border-gray-100 hover:bg-teal-50/30 cursor-pointer transition-colors duration-150 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}
                      onClick={() => onViewDetails(grantee.id)}
                    >
                      <td className="py-3 px-4 font-medium text-gray-800">
                        <div className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-teal-100 flex items-center justify-center text-teal-600 font-medium mr-3">
                             {grantee.organizationName.charAt(0)}{grantee.organizationName.charAt(1)}
                          </div>
                          {grantee.organizationName}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          <Mail className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="text-gray-700">{grantee.emailAddress}</span>
                        </div>
                      </td>

                      <td className="py-3 px-4">
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-teal-200 hover:bg-teal-100 hover:text-[#00998F] transition-all duration-200"
                          onClick={(e) => {
                            e.stopPropagation();
                            onViewDetails(grantee.id);
                          }}
                        >
                          View Profile
                        </Button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-gray-500">
                Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, sortedGrantees.length)} of {sortedGrantees.length} grantees
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="h-8 w-8 p-0 flex items-center justify-center"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                  <Button
                    key={page}
                    variant={page === currentPage ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(page)}
                    className={`h-8 w-8 p-0 ${page === currentPage ? 'bg-gradient-to-r from-[#00998F] to-teal-500 text-white' : ''}`}
                  >
                    {page}
                  </Button>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="h-8 w-8 p-0 flex items-center justify-center"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
