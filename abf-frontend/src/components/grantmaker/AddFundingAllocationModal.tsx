

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover";
import { FundingEntity} from "@/types/finance";
import { OrganizationGrant } from "@/types/profile";
import { Dispatch, SetStateAction, useState } from "react";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface AddFundingAllocationModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (values: {
    fundingEntityId: number;
    grantId: number;
  }) => void;
  grants: OrganizationGrant[];
  fundingEntities: FundingEntity[];
}

export const AddFundingAllocationModal = ({
  open,
  onClose,
  onSave,
  grants,
  fundingEntities
}: AddFundingAllocationModalProps) => {
  const [selectedFundingEntity, setSelectedFundingEntity] = useState<number | null>(null);
  const [selectedGrant, setSelectedGrant] = useState<number | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>("");

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add Funding Allocation</DialogTitle>
          <DialogDescription>Select funding entity and grant.</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label>Funding Entity</Label>
            <Select onValueChange={val => setSelectedFundingEntity(Number(val))}>
              <SelectTrigger>
                <SelectValue placeholder="Select Funding Entity" />
              </SelectTrigger>
              <SelectContent>
                {fundingEntities.map(entity => (
                  <SelectItem key={entity.id} value={entity.id.toString()}>
                    {entity.organizationName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Grant</Label>
            <Select onValueChange={val => setSelectedGrant(Number(val))}>
              <SelectTrigger>
                <SelectValue placeholder="Select Grant" />
              </SelectTrigger>
              <SelectContent>
                {grants.map(grant => (
                  <SelectItem key={grant.id} value={grant.id.toString()}>
                    {grant.recipientOrganizationName + " - " + grant.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* <div>
            <Label>Date Allocated</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start">
                  {selectedDate || "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={selectedDate ? new Date(selectedDate) : undefined}
                  onSelect={date => {
                    if (date) setSelectedDate(date.toISOString().slice(0, 10));
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div> */}

          <div className="flex justify-end gap-2 pt-4">
            <Button
              variant="outline"
              onClick={() => {
                setSelectedFundingEntity(null);
                setSelectedGrant(null);
                setSelectedDate("");
                onClose();
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                console.log("selected funding entity", selectedFundingEntity)
                console.log("selected Grant ", selectedGrant)
                if (selectedFundingEntity && selectedGrant) {
                    console.log("I am here");
                  onSave({
                    fundingEntityId: selectedFundingEntity,
                    grantId: selectedGrant,
                  });
                }
              }}
              className="bg-gradient-to-r from-[#00998F] to-teal-600 hover:from-teal-700 hover:to-teal-800 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-lg px-6 py-2.5 font-medium"
            >
              Add Allocation
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};