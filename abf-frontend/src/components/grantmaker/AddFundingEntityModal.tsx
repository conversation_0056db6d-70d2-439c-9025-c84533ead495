import { Button } from "../ui/button";
import { useState } from "react";

import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  Di<PERSON>Footer,
  DialogTitle,
} from "../ui/dialog";
import { Input } from "../ui/input";
import { Label } from "../ui/label";

export interface FundsReceived {
  funderName: string;
  recipientName: string;
  addedBy: string;
  amount: number;
  referenceNumber: string;
  currency: string;
  remarks: string;
  dateReceived: string;
  paymentMode: string;
}

interface AddFundingEntityModalProps {
  isOpen: boolean,
  onClose: () => void,
  handleAdd: (entityName: string, panNumber: string) => void;
}

export default function AndFundingEntityModal({ isOpen, onClose, handleAdd: handleAddFundingEntity }: AddFundingEntityModalProps) {


  const [panNumber, setPanNumber] = useState("");
  const [entityName, setEntityName] = useState("");

  return (
    <div>

      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Funding Entity</DialogTitle>
          </DialogHeader>
          <div>
            <div className="pb-4">
              <Label className="pb-2">
                Entity Name
                <span className="text-red-500">*</span>

              </Label>
              <Input
                placeholder="Entity Name"
                value={entityName}
                onChange={(e) => setEntityName(e.target.value)}
              />
            </div>
            <div>
              <Label className="pb-2">Pan Number
                <span className="text-red-500">*</span>

              </Label>
              <Input
                placeholder="PAN Number"
                value={panNumber}
                onChange={(e) => setPanNumber(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>

            <Button
              onClick={() => handleAddFundingEntity(entityName, panNumber)}
              className="bg-gradient-to-r from-[#00998F] to-teal-600 hover:from-teal-700 hover:to-teal-800 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-lg px-6 py-2.5 font-medium"
            >Add Entity</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
