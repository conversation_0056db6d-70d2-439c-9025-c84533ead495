"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { ExpenseRecord } from '@/services/funding-service';
import { getGranteeExpenses, updateExpenseStatus } from '@/services/grantmaker/grantee-expense-service';
import { formatCurrency } from '@/services/grantmaker/grantmaker-service';


interface GranteeExpenseTabProps {
  granteeId: string;
  grantId?: string;
}

export function GranteeExpenseTab({ granteeId, grantId }: GranteeExpenseTabProps) {
  const [expenses, setExpenses] = useState<ExpenseRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('all');
  const [selectedExpense, setSelectedExpense] = useState<ExpenseRecord | null>(null);
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [rejectionNotes, setRejectionNotes] = useState('');
  // Handle viewing expense details in dedicated page
  const handleViewExpense = (expense: ExpenseRecord) => {
    // Store expense data for the detail page
    localStorage.setItem('selectedExpense', JSON.stringify(expense));
    localStorage.setItem('granteeId', granteeId);
    localStorage.setItem('returnTab', 'expense'); // Store current tab
    localStorage.setItem('returnUrl', window.location.pathname + window.location.search); // Store current URL

    // Navigate to expense detail page
    window.location.href = `/grantmaker/expense-detail/${expense.id}`;
  };

  // Fetch expenses when component mounts or when granteeId/grantId changes
  useEffect(() => {
    const fetchExpenses = async () => {
      setIsLoading(true);
      try {
        const data = await getGranteeExpenses(granteeId);
        setExpenses(data);
      } catch (error) {
        console.error('Error fetching expenses:', error);
        toast.error('Failed to load expense data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchExpenses();
  }, [granteeId, grantId]);

  // Filter expenses based on selected tab
  const filteredExpenses = expenses.filter(expense => {
    if (selectedTab === 'all') return true;
    return expense.status === selectedTab;
  });

  // Handle expense approval
  const handleApprove = async () => {
    if (!selectedExpense) return;

    try {
      await updateExpenseStatus(selectedExpense.id, 'approved');

      // Update local state
      setExpenses(expenses.map(expense =>
        expense.id === selectedExpense.id
          ? { ...expense, status: 'approved' }
          : expense
      ));

      toast.success('Expense approved successfully');
      setIsApproveDialogOpen(false);
    } catch (error) {
      console.error('Error approving expense:', error);
      toast.error('Failed to approve expense');
    }
  };

  // Handle expense rejection
  const handleReject = async () => {
    if (!selectedExpense) return;

    if (!rejectionNotes.trim()) {
      toast.error('Please provide rejection notes');
      return;
    }

    try {
      await updateExpenseStatus(selectedExpense.id, 'rejected', rejectionNotes);

      // Update local state
      setExpenses(expenses.map(expense =>
        expense.id === selectedExpense.id
          ? { ...expense, status: 'rejected', rejection_notes: rejectionNotes }
          : expense
      ));

      toast.success('Expense rejected successfully');
      setIsRejectDialogOpen(false);
      setRejectionNotes('');
    } catch (error) {
      console.error('Error rejecting expense:', error);
      toast.error('Failed to reject expense');
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800">Rejected</Badge>;
      case 'pending':
      default:
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <Card className="shadow-sm border border-gray-100">
        <CardHeader className="pb-4">
          <div>
            <CardTitle className="text-xl font-bold text-gray-900">Expenses Approval History</CardTitle>
            <CardDescription className="text-gray-600">Review and manage grantee expense submissions</CardDescription>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" value={selectedTab} onValueChange={setSelectedTab} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="all">All Expenses</TabsTrigger>
              <TabsTrigger value="pending">Pending</TabsTrigger>
              <TabsTrigger value="approved">Approved</TabsTrigger>
              <TabsTrigger value="rejected">Rejected</TabsTrigger>
            </TabsList>

            <TabsContent value={selectedTab} className="mt-0">
              {isLoading ? (
                <div className="py-8 text-center text-gray-500">Loading expense data...</div>
              ) : filteredExpenses.length === 0 ? (
                <div className="py-8 text-center text-gray-500">No expenses found</div>
              ) : (
                <div className="w-full overflow-hidden">
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    {/* Table Container */}
                    <div className="overflow-x-auto">
                      <table className="w-full min-w-[1100px]">
                        <thead>
                          <tr className="bg-gray-50 border-b border-gray-200">
                            <th className="px-6 py-4 text-center text-xs font-bold text-gray-900 uppercase tracking-wider" style={{width: '80px'}}>
                              Sr.
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider" style={{width: '140px'}}>
                              Date
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider" style={{width: '200px'}}>
                              Headers
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider" style={{width: '280px'}}>
                              Particulars
                            </th>
                            <th className="px-6 py-4 text-right text-xs font-bold text-gray-900 uppercase tracking-wider" style={{width: '130px'}}>
                              Budget
                            </th>
                            <th className="px-6 py-4 text-right text-xs font-bold text-gray-900 uppercase tracking-wider" style={{width: '130px'}}>
                              Actual
                            </th>
                            <th className="px-6 py-4 text-right text-xs font-bold text-gray-900 uppercase tracking-wider" style={{width: '130px'}}>
                              Variance
                            </th>
                            <th className="px-6 py-4 text-center text-xs font-bold text-gray-900 uppercase tracking-wider" style={{width: '120px'}}>
                              Status
                            </th>
                            <th className="px-6 py-4 text-center text-xs font-bold text-gray-900 uppercase tracking-wider" style={{width: '160px'}}>
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {filteredExpenses.map((expense, index) => {
                            const variance = expense.totalActualSpent - expense.totalBudget;

                            // Check for validation errors that require approval
                            const validationErrors = [];
                            const hasErrors = [];

                            if (variance > 0) {
                              validationErrors.push(`Budget exceeded by ${formatCurrency(variance)}`);
                              hasErrors.push('budget');
                            }
                            if (!expense.description || expense.description.trim() === '') {
                              validationErrors.push('Missing expense description');
                              hasErrors.push('description');
                            }
                            if (!expense.category || expense.category.trim() === '') {
                              validationErrors.push('Missing category information');
                              hasErrors.push('category');
                            }
                            if (expense.totalActualSpent === 0) {
                              validationErrors.push('No actual expenses recorded');
                              hasErrors.push('actual');
                            }

                            return (
                              <tr
                                key={expense.id}
                                className="hover:bg-gray-50"
                              >
                                {/* Serial Number */}
                                <td className="px-6 py-4 text-center" style={{width: '80px'}}>
                                  <span className="text-sm font-medium text-gray-900">
                                    {index + 1}
                                  </span>
                                </td>

                                {/* Date */}
                                <td className="px-6 py-4" style={{width: '140px'}}>
                                  <div className="text-sm text-gray-900">{expense.loggedDate}</div>
                                </td>

                                {/* Headers */}
                                <td className="px-6 py-4" style={{width: '200px'}}>
                                  <div className="text-sm">
                                    <div className={`font-medium ${hasErrors.includes('category') ? 'text-red-600' : 'text-gray-900'}`}>
                                      {expense.category || 'N/A'}
                                    </div>
                                  </div>
                                </td>

                                {/* Particulars */}
                                <td className="px-6 py-4" style={{width: '280px'}}>
                                  <div className="text-sm">
                                    <div className={`${hasErrors.includes('description') ? 'text-red-600' : 'text-gray-900'}`}
                                         title={expense.description}>
                                      {expense.description ?
                                        (expense.description.length > 50 ?
                                          `${expense.description.substring(0, 50)}...` :
                                          expense.description
                                        ) : 'No description provided'
                                      }
                                    </div>
                                    {validationErrors.length > 0 && expense.status === 'pending' && (
                                      <div className="flex items-center gap-1 mt-1">
                                        <AlertCircle className="h-3 w-3 text-red-500" />
                                        <span className="text-xs text-red-600">
                                          {validationErrors.length} issue{validationErrors.length > 1 ? 's' : ''}
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                </td>

                                {/* Budget */}
                                <td className="px-6 py-4 text-right" style={{width: '130px'}}>
                                  <div className="text-sm font-medium text-gray-900">
                                    {formatCurrency(expense.totalBudget)}
                                  </div>
                                </td>

                                {/* Actual */}
                                <td className="px-6 py-4 text-right" style={{width: '130px'}}>
                                  <div className={`text-sm font-medium ${hasErrors.includes('actual') ? 'text-red-600' : 'text-gray-900'}`}>
                                    {formatCurrency(expense.totalActualSpent)}
                                  </div>
                                </td>

                                {/* Variance */}
                                <td className="px-6 py-4 text-right" style={{width: '130px'}}>
                                  <div className={`text-sm font-medium ${
                                    variance > 0 ? 'text-red-600' :
                                    variance < 0 ? 'text-green-600' : 'text-gray-900'
                                  }`}>
                                    {variance > 0 ? '+' : ''}{formatCurrency(Math.abs(variance))}
                                  </div>
                                </td>

                                {/* Status */}
                                <td className="px-6 py-4 text-center" style={{width: '120px'}}>
                                  <div className="space-y-2">
                                    {getStatusBadge(expense.status)}
                                    {expense.status === 'rejected' && expense.rejection_notes && (
                                      <div className="text-xs text-red-600 truncate" title={expense.rejection_notes}>
                                        {expense.rejection_notes}
                                      </div>
                                    )}
                                  </div>
                                </td>

                                {/* Actions */}
                                <td className="px-6 py-4 text-center" style={{width: '160px'}}>
                                  <div className="flex items-center justify-center gap-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-xs"
                                      onClick={() => handleViewExpense(expense)}
                                    >
                                      <Eye className="h-3 w-3 mr-1" />
                                      View
                                    </Button>
                                    {expense.status === 'pending' && (
                                      <div className="flex gap-1">
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="text-xs text-green-600 border-green-200 hover:bg-green-50"
                                          onClick={() => {
                                            setSelectedExpense(expense);
                                            setIsApproveDialogOpen(true);
                                          }}
                                        >
                                          <CheckCircle className="h-3 w-3" />
                                        </Button>
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="text-xs text-red-600 border-red-200 hover:bg-red-50"
                                          onClick={() => {
                                            setSelectedExpense(expense);
                                            setIsRejectDialogOpen(true);
                                          }}
                                        >
                                          <XCircle className="h-3 w-3" />
                                        </Button>
                                      </div>
                                    )}
                                  </div>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>



      {/* Approve Dialog */}
      <Dialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Expense</DialogTitle>
            <DialogDescription>
              Review validation issues and confirm approval
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedExpense && (() => {
              const variance = selectedExpense.totalActualSpent - selectedExpense.totalBudget;
              const validationErrors = [];
              if (variance > 0) {
                validationErrors.push(`Budget exceeded by ${formatCurrency(variance)}`);
              }
              if (!selectedExpense.description || selectedExpense.description.trim() === '') {
                validationErrors.push('Missing expense description');
              }
              if (!selectedExpense.category || selectedExpense.category.trim() === '') {
                validationErrors.push('Missing category information');
              }
              if (selectedExpense.totalActualSpent === 0) {
                validationErrors.push('No actual expenses recorded');
              }

              return (
                <>
                  {validationErrors.length > 0 && (
                    <div className="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
                      <h4 className="font-medium text-amber-800 mb-2">Validation Issues:</h4>
                      <ul className="space-y-1">
                        {validationErrors.map((error, index) => (
                          <li key={index} className="text-sm text-amber-700 flex items-center gap-2">
                            <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
                            {error}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="font-medium text-gray-500">Description</p>
                      <p>{selectedExpense.description || 'N/A'}</p>
                    </div>
                    <div>
                      <p className="font-medium text-gray-500">Category</p>
                      <p>{selectedExpense.category || 'N/A'}</p>
                    </div>
                    <div>
                      <p className="font-medium text-gray-500">Budget</p>
                      <p className="font-medium">{formatCurrency(selectedExpense.totalBudget || 0)}</p>
                    </div>
                    <div>
                      <p className="font-medium text-gray-500">Actual</p>
                      <p className="font-medium">{formatCurrency(selectedExpense.totalActualSpent || 0)}</p>
                    </div>
                  </div>
                </>
              );
            })()}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsApproveDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleApprove} className="bg-green-600 hover:bg-green-700">Approve</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Expense</DialogTitle>
            <DialogDescription>
              Review validation issues and provide rejection reason
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedExpense && (() => {
              const variance = selectedExpense.totalActualSpent - selectedExpense.totalBudget;
              const validationErrors = [];
              if (variance > 0) {
                validationErrors.push(`Budget exceeded by ${formatCurrency(variance)}`);
              }
              if (!selectedExpense.description || selectedExpense.description.trim() === '') {
                validationErrors.push('Missing expense description');
              }
              if (!selectedExpense.category || selectedExpense.category.trim() === '') {
                validationErrors.push('Missing category information');
              }
              if (selectedExpense.totalActualSpent === 0) {
                validationErrors.push('No actual expenses recorded');
              }

              return (
                <>
                  {validationErrors.length > 0 && (
                    <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                      <h4 className="font-medium text-red-800 mb-2">Validation Issues Found:</h4>
                      <ul className="space-y-1">
                        {validationErrors.map((error, index) => (
                          <li key={index} className="text-sm text-red-700 flex items-center gap-2">
                            <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                            {error}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                    <div>
                      <p className="font-medium text-gray-500">Description</p>
                      <p>{selectedExpense.description || 'N/A'}</p>
                    </div>
                    <div>
                      <p className="font-medium text-gray-500">Category</p>
                      <p>{selectedExpense.category || 'N/A'}</p>
                    </div>
                    <div>
                      <p className="font-medium text-gray-500">Budget</p>
                      <p className="font-medium">{formatCurrency(selectedExpense.totalBudget || 0)}</p>
                    </div>
                    <div>
                      <p className="font-medium text-gray-500">Actual</p>
                      <p className="font-medium">{formatCurrency(selectedExpense.totalActualSpent || 0)}</p>
                    </div>
                  </div>
                </>
              );
            })()}
            <div className="space-y-2">
              <Label htmlFor="rejection-notes">Rejection Notes</Label>
              <Textarea
                id="rejection-notes"
                placeholder="Enter reason for rejection"
                value={rejectionNotes}
                onChange={(e) => setRejectionNotes(e.target.value)}
                className="min-h-[100px]"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRejectDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleReject} className="bg-red-600 hover:bg-red-700">Reject</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>


    </div>
  );
}
