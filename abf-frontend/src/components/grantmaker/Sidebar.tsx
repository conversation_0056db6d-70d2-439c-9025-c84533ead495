"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  FileText,
  Users,
  InfoIcon,
  DollarSign,
  FolderArchive,
  LockIcon,
  BarChart3
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface SidebarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
}

export function Sidebar({ isOpen, toggleSidebar }: SidebarProps) {
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  // No floating button when sidebar is closed
  if (!isOpen) return null;

  const navItems = [
    { name: "Dashboard", href: "/", icon: LayoutDashboard, badge: "" },
    // { name: "Organizations", href: "/grantmaker/organizations", icon: Building2, badge: "" },
    { name: "Grant Partners", href: "/grantmaker/grantees", icon: Users, badge: "" },
    // { name: "Milestones", href: "/grantmaker/milestones", icon: FileText, badge: "" },
    { name: "Finance", href: "/finance", icon: DollarSign, badge: "" },
    { name: "Grants", href: "#", icon: FileText, badge: "" },
    { name: "Archive", href: "#", icon: FolderArchive, badge: "" },
    { name: "Help Center", href: "/grantmaker/support/my-tickets/support-ticket", icon: InfoIcon, badge: "" },
    // { name: "Reports", href: "/grantmaker/reports", icon: BarChart3, badge: "" },
    // { name: "Analytics", href: "/grantmaker/analytics", icon: PieChart, badge: "" },
    // { name: "Calendar", href: "/grantmaker/calendar", icon: Calendar, badge: "" },
    // { name: "Settings", href: "/grantmaker/settings", icon: Settings, badge: "" },
    // { name: "Help", href: "/grantmaker/help", icon: HelpCircle, badge: "" },
  ];

  return (
    <AnimatePresence>
      {/* Mobile overlay with animation */}
      {isOpen && (
        <motion.div
          key="sidebar-overlay"
          className="fixed inset-0 bg-black/30 backdrop-blur-sm z-[35] lg:hidden"
          onClick={toggleSidebar}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.1 }}
        />
      )}

      {/* Sidebar with animation */}
      <motion.aside
        key="sidebar-menu"
        className={`fixed left-0 z-30 w-64 bg-white shadow-sm transition-all`}
        style={{
          top: 'var(--header-height, 5rem)',
          height: 'calc(100vh - var(--header-height, 5rem))'
        }}
        initial={{ x: -280 }}
        animate={{ x: isOpen ? 0 : -280 }}
        transition={{ duration: 0.15 }}
      >
        <div className="flex h-full flex-col overflow-y-auto border-r border-gray-200">
          {/* No extra padding needed */}

          {/* Navigation section - simplified */}
          <div className="flex-1 py-6 px-4">
            <nav className="space-y-1.5">
              {navItems.map((item) => {
                const isActive = item.href === '/grantmaker'
                  ? pathname === '/grantmaker'
                  : pathname === item.href ||
                    (pathname.startsWith(`${item.href}/`) && item.href !== '/grantmaker');
                const Icon = item.icon;
                // Use a div instead of Link for non-functional tabs
                return item.href === "#" ? (
                  <div
                    key={item.name}
                    className={`group flex items-center justify-between rounded-lg px-4 py-2.5 text-sm font-medium transition-all duration-200 opacity-50 cursor-not-allowed text-gray-700`}
                    onMouseEnter={() => setHoveredItem(item.name)}
                    onMouseLeave={() => setHoveredItem(null)}
                  >
                    <div className="flex items-center">
                      <Icon
                        className={`mr-3 h-5 w-5 flex-shrink-0 transition-transform duration-300 text-gray-500 ${hoveredItem === item.name ? "scale-110" : ""}`}
                      />
                      <span className="transition-all duration-200 flex items-center gap-1">
                        {item.name}
                        <LockIcon className="w-4 h-4" />
                      </span>
                    </div>
                  </div>
                ) : (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`group flex items-center justify-between rounded-lg px-4 py-2.5 text-sm font-medium transition-all duration-200 ${isActive
                      ? "bg-gradient-to-r from-teal-600/90 to-teal-500/90 text-white shadow-md"
                      : "text-gray-700 hover:bg-teal-50 hover:text-teal-600"
                      }`}
                    onMouseEnter={() => setHoveredItem(item.name)}
                    onMouseLeave={() => setHoveredItem(null)}
                  >
                    <div className="flex items-center">
                      <Icon
                        className={`mr-3 h-5 w-5 flex-shrink-0 transition-transform duration-300 ${isActive ? "text-white" : "text-gray-500 group-hover:text-[#6366F1]"} ${hoveredItem === item.name ? "scale-110" : ""}`}
                      />
                      <span className={`transition-all duration-200 ${hoveredItem === item.name && !isActive ? "font-semibold" : ""}`}>
                        {item.name}
                      </span>
                    </div>
                  </Link>
                );
              })}
            </nav>
          </div>

          {/* No close button */}
        </div>
      </motion.aside>
    </AnimatePresence>
  );
}
