"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Calendar,
  IndianRupee,
  Clock,
  CheckCircle,
  AlertCircle,
  Target,
  FileText,
  ChevronUp,
  ChevronDown,
} from "lucide-react";
import { getGrantDetails } from "@/services/grantmaker/grantee-service";
import { toast } from "sonner";

interface GrantDetailsDropdownProps {
  grant: any;
  isExpanded: boolean;
  onToggle: () => void;
}

interface GrantDetails {
  id: number;
  grant_name: string;
  grant_purpose: string;
  start_date: string;
  end_date: string;
  annual_budget: string;
  funding_sources: string;
  organization: any;
  grant_maker_organization: any;
  created_at: string;
  updated_at: string;
}

export default function GrantDetailsDropdown({
  grant,
  isExpanded,
  onToggle,
}: GrantDetailsDropdownProps) {
  const [grantDetails, setGrantDetails] = useState<GrantDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Format currency
  const formatCurrency = (amount: number | string) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(numAmount);
  };

  // Calculate grant status based on dates
  const getGrantStatus = (startDate: string, endDate: string) => {
    const today = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (today < start) {
      return { status: 'pending', label: 'Pending', color: 'bg-amber-100 text-amber-800' };
    } else if (today > end) {
      return { status: 'completed', label: 'Completed', color: 'bg-blue-100 text-blue-800' };
    } else {
      return { status: 'active', label: 'Active', color: 'bg-green-100 text-green-800' };
    }
  };

  // Fetch grant details when expanded
  useEffect(() => {
    if (isExpanded && grant?.id) {
      fetchGrantDetails();
    }
  }, [isExpanded, grant?.id]);

  const fetchGrantDetails = async () => {
    if (!grant?.id) return;

    setLoading(true);
    setError(null);

    try {
      const details = await getGrantDetails(grant.id.toString());
      setGrantDetails(details);
    } catch (err) {
      console.error('Error fetching grant details:', err);
      setError('Failed to load grant details');
      toast.error('Failed to load grant details');
      
      // Use fallback data if available
      if (grant) {
        setGrantDetails(grant);
      }
    } finally {
      setLoading(false);
    }
  };

  // Use fallback data if no API data is available
  const displayData = grantDetails || grant;

  return (
    <div className="w-full">
      {/* Toggle Button */}
      <div className="mt-4 flex justify-end">
        <Button
          variant="outline"
          size="sm"
          className="text-[#00998F] border-teal-200 hover:bg-teal-50 transition-all duration-200 flex items-center gap-1.5"
          onClick={onToggle}
        >
          <FileText className="h-4 w-4" />
          View Details
          {isExpanded ? (
            <ChevronUp className="h-4 w-4 ml-1" />
          ) : (
            <ChevronDown className="h-4 w-4 ml-1" />
          )}
        </Button>
      </div>

      {/* Expandable Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="overflow-hidden"
          >
            <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
              {loading && (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#00998F]"></div>
                  <span className="ml-2 text-gray-600 text-sm">Loading details...</span>
                </div>
              )}

              {error && !displayData && (
                <div className="flex items-center justify-center py-4">
                  <div className="text-center">
                    <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
                    <p className="text-red-600 text-sm">{error}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={fetchGrantDetails}
                      className="mt-2"
                    >
                      Try Again
                    </Button>
                  </div>
                </div>
              )}

              {displayData && !loading && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2, delay: 0.1 }}
                  className="space-y-4"
                >
                  {/* Header with Status */}
                  <div className="flex items-center justify-between">
                    <h4 className="text-lg font-semibold text-gray-900">
                      {displayData.grant_name || displayData.name}
                    </h4>
                    {displayData.start_date && displayData.end_date && (
                      <Badge className={getGrantStatus(displayData.start_date, displayData.end_date).color}>
                        {getGrantStatus(displayData.start_date, displayData.end_date).status === 'active' && <CheckCircle className="h-3 w-3 mr-1" />}
                        {getGrantStatus(displayData.start_date, displayData.end_date).status === 'pending' && <Clock className="h-3 w-3 mr-1" />}
                        {getGrantStatus(displayData.start_date, displayData.end_date).status === 'completed' && <CheckCircle className="h-3 w-3 mr-1" />}
                        {getGrantStatus(displayData.start_date, displayData.end_date).label}
                      </Badge>
                    )}
                  </div>

                  <Separator />

                  {/* Grant Information Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Basic Information */}
                    <div className="space-y-3">
                      <h5 className="font-medium text-gray-800 flex items-center">
                        <Target className="h-4 w-4 mr-2 text-[#00998F]" />
                        Grant Information
                      </h5>
                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="font-medium text-gray-600">Purpose:</span>
                          <p className="text-gray-900 mt-1">{displayData.grant_purpose || displayData.purpose || 'Not specified'}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-600">Funding Sources:</span>
                          <p className="text-gray-900 mt-1">{displayData.funding_sources || 'Not specified'}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-600">Grant Amount:</span>
                          <p className="text-[#00998F] font-semibold mt-1">
                            {formatCurrency(displayData.annual_budget || displayData.amount || 0)}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Timeline Information */}
                    <div className="space-y-3">
                      <h5 className="font-medium text-gray-800 flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-[#00998F]" />
                        Timeline
                      </h5>
                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="font-medium text-gray-600">Start Date:</span>
                          <p className="text-gray-900 mt-1">
                            {displayData.start_date ? new Date(displayData.start_date).toLocaleDateString() : displayData.startDate ? new Date(displayData.startDate).toLocaleDateString() : 'Not specified'}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-600">End Date:</span>
                          <p className="text-gray-900 mt-1">
                            {displayData.end_date ? new Date(displayData.end_date).toLocaleDateString() : displayData.endDate ? new Date(displayData.endDate).toLocaleDateString() : 'Not specified'}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-600">Duration:</span>
                          <p className="text-gray-900 mt-1">
                            {displayData.start_date && displayData.end_date ? (
                              `${Math.ceil((new Date(displayData.end_date).getTime() - new Date(displayData.start_date).getTime()) / (1000 * 60 * 60 * 24 * 30))} months`
                            ) : (
                              'Not calculated'
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Additional Information */}
                  {(displayData.created_at || displayData.updated_at) && (
                    <>
                      <Separator />
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        {displayData.created_at && (
                          <div>
                            <span className="font-medium text-gray-600">Created:</span>
                            <p className="text-gray-900 mt-1">
                              {new Date(displayData.created_at).toLocaleDateString()}
                            </p>
                          </div>
                        )}
                        {displayData.updated_at && (
                          <div>
                            <span className="font-medium text-gray-600">Last Updated:</span>
                            <p className="text-gray-900 mt-1">
                              {new Date(displayData.updated_at).toLocaleDateString()}
                            </p>
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </motion.div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
