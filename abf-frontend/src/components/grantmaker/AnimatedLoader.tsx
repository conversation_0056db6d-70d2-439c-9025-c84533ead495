import React from 'react';
import { motion } from 'framer-motion';
import { CurrencyDollarIcon, ChartBarIcon, WalletIcon } from '@/components/ui/icons';
import { Card, CardContent } from '@/components/ui/card';

interface AnimatedLoaderProps {
  type?: 'grantee' | 'expense' | 'disbursement' | 'document';
  message?: string;
}

export function AnimatedLoader({ type = 'grantee', message }: AnimatedLoaderProps) {
  const defaultMessages = {
    grantee: 'Loading grantee data...',
    expense: 'Loading expense details...',
    disbursement: 'Loading disbursement data...',
    document: 'Loading documents...'
  };

  const displayMessage = message || defaultMessages[type];
  
  // Shimmer animation for skeleton loading
  const shimmer = {
    hidden: { opacity: 0.3 },
    visible: { 
      opacity: 0.8,
      transition: {
        repeat: Infinity,
        repeatType: "reverse" as const,
        duration: 1.5
      }
    }
  };

  // Pulse animation for the main container
  const pulse = {
    scale: [1, 1.01, 1],
    opacity: [0.9, 1, 0.9],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    }
  };

  // Staggered card appearance
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const item = {
    hidden: { y: 20, opacity: 0 },
    show: { y: 0, opacity: 1 }
  };

  // Rotating icons animation
  const rotate = {
    rotate: [0, 360],
    transition: {
      duration: 3,
      repeat: Infinity,
      ease: "linear"
    }
  };

  // Floating animation
  const float = {
    y: [0, -10, 0],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    }
  };

  return (
    <div className="space-y-8">
      {/* Animated header */}
      <motion.div 
        className="bg-gradient-to-r from-orange-50 to-amber-50 p-6 rounded-lg shadow-sm"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center justify-between">
          <div>
            <motion.h1 
              className="text-2xl font-bold text-gray-800"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              {displayMessage}
            </motion.h1>
            <div className="flex items-center mt-2">
              <motion.div 
                className="h-1 bg-gradient-to-r from-orange-500 to-amber-400 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: "100%" }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </div>
          </div>
          <motion.div 
            className="text-orange-500"
            animate={rotate}
          >
            {type === 'grantee' && <WalletIcon className="w-8 h-8" />}
            {type === 'expense' && <ChartBarIcon className="w-8 h-8" />}
            {type === 'disbursement' && <CurrencyDollarIcon className="w-8 h-8" />}
            {type === 'document' && (
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14 2 14 8 20 8" />
              </svg>
            )}
          </motion.div>
        </div>
      </motion.div>

      {/* Animated cards */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
        variants={container}
        initial="hidden"
        animate="show"
      >
        {[1, 2, 3].map((index) => (
          <motion.div key={index} variants={item}>
            <Card className="border-0 shadow-md rounded-lg overflow-hidden h-full">
              <div className="h-1 bg-gradient-to-r from-orange-500 to-amber-400"></div>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <motion.div 
                    className="h-4 w-24 bg-gray-200 rounded"
                    variants={shimmer}
                    initial="hidden"
                    animate="visible"
                  />
                  <motion.div 
                    className="bg-orange-100 p-2 rounded-full"
                    animate={float}
                  >
                    {index === 1 && <CurrencyDollarIcon className="w-5 h-5 text-orange-500" />}
                    {index === 2 && <WalletIcon className="w-5 h-5 text-amber-500" />}
                    {index === 3 && <ChartBarIcon className="w-5 h-5 text-orange-500" />}
                  </motion.div>
                </div>
                
                <motion.div 
                  className="h-8 w-32 bg-gray-200 rounded mb-4"
                  variants={shimmer}
                  initial="hidden"
                  animate="visible"
                />
                
                <motion.div 
                  className="h-3 w-full bg-gray-200 rounded mt-4"
                  variants={shimmer}
                  initial="hidden"
                  animate="visible"
                />
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {/* Animated charts placeholder */}
      <motion.div 
        className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8"
        animate={pulse}
      >
        <Card className="shadow-md border-0 rounded-lg overflow-hidden">
          <div className="h-1 bg-gradient-to-r from-orange-500 to-amber-400"></div>
          <CardContent className="p-6">
            <div className="flex justify-between items-center mb-4">
              <motion.div 
                className="h-6 w-40 bg-gray-200 rounded"
                variants={shimmer}
                initial="hidden"
                animate="visible"
              />
            </div>
            <div className="h-60 flex items-center justify-center">
              <motion.div
                className="w-40 h-40 rounded-full border-4 border-orange-200"
                animate={{
                  borderColor: ['#FFEDD5', '#FED7AA', '#FFEDD5'],
                  rotate: 360
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "linear"
                }}
              >
                <motion.div
                  className="w-full h-full rounded-full border-4 border-amber-300"
                  animate={{
                    borderColor: ['#FEF3C7', '#FDE68A', '#FEF3C7'],
                    rotate: -360
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                />
              </motion.div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-md border-0 rounded-lg overflow-hidden">
          <div className="h-1 bg-gradient-to-r from-amber-500 to-orange-400"></div>
          <CardContent className="p-6">
            <div className="flex justify-between items-center mb-4">
              <motion.div 
                className="h-6 w-48 bg-gray-200 rounded"
                variants={shimmer}
                initial="hidden"
                animate="visible"
              />
            </div>
            <div className="h-60 flex flex-col justify-center space-y-4">
              {[1, 2, 3, 4, 5].map((index) => (
                <div key={index} className="flex items-center">
                  <motion.div 
                    className="w-12 bg-gray-200 rounded h-4"
                    variants={shimmer}
                    initial="hidden"
                    animate="visible"
                  />
                  <div className="flex-1 ml-4">
                    <motion.div 
                      className="h-6 bg-orange-200 rounded"
                      initial={{ width: 0 }}
                      animate={{ width: `${20 * index}%` }}
                      transition={{ 
                        duration: 1.5, 
                        repeat: Infinity,
                        repeatType: "reverse"
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
