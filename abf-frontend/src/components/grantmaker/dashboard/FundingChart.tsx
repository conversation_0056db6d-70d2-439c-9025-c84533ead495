"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell, ComposedChart } from "recharts";
import { formatCurrency } from "@/services/grantmaker/grantmaker-service";
import { motion } from "framer-motion";
import { TrendingUp } from "lucide-react";

interface FundingData {
  month?: string;
  quarter?: string;
  amount: number;
}

interface FundingChartProps {
  data: FundingData[];
  onBarClick: (label: string, amount: number) => void;
  selectedQuarter?: string;
}

export function FundingChart({ data, onBarClick, selectedQuarter = 'All' }: FundingChartProps) {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [animationComplete, setAnimationComplete] = useState(false);
  const [enhancedData, setEnhancedData] = useState<FundingData[]>([]);
  const [chartTitle, setChartTitle] = useState("Quarterly Disbursements");

  useEffect(() => {
    // No longer need to calculate trend values
    setEnhancedData(data);

    // Always use Quarterly Disbursements as the title
    setChartTitle("Quarterly Disbursements");

    // Set animation complete after a delay
    const timer = setTimeout(() => setAnimationComplete(true), 2000);
    return () => clearTimeout(timer);
  }, [data]);

  const handleMouseEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };

  const handleMouseLeave = () => {
    setActiveIndex(null);
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white/95 backdrop-blur-sm p-4 border border-teal-100 shadow-xl rounded-lg animate-fadeIn">
          <div className="flex items-center mb-2">
            <div className="w-3 h-3 rounded-full bg-[#00998F] mr-2"></div>
            <p className="font-semibold text-gray-800">{label}</p>
          </div>
          <p className="text-[#00998F] font-medium text-lg">
            {formatCurrency(payload[0].value)}
          </p>
          <div className="h-1 w-full bg-gradient-to-r from-[#00998F] to-emerald-500 mt-2 rounded-full"></div>
        </div>
      );
    }
    return null;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="h-full"
    >
      <Card className="bg-white shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border-0 h-full">
        <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
        <CardHeader className="pb-2">
          <div className="flex items-start">
            <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-sm group-hover:shadow-md transition-all duration-300">
              <TrendingUp className="h-6 w-6 text-[#00998F] group-hover:text-teal-600 transition-colors" />
            </div>
            <div>
              <CardTitle className="text-xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                {chartTitle}
              </CardTitle>
              <CardDescription className="text-gray-600">
                Funding disbursed per quarter
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {selectedQuarter === 'All' ? (
            <div className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <ComposedChart
                  data={enhancedData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                  onClick={(data) => {
                    if (data && data.activePayload) {
                      const payload = data.activePayload[0].payload;
                      const label = payload.quarter || payload.month;
                      onBarClick(label, payload.amount);
                    }
                  }}
                  onMouseMove={(data) => {
                    if (data && data.activeTooltipIndex !== undefined) {
                      handleMouseEnter(null, data.activeTooltipIndex);
                    }
                  }}
                  onMouseLeave={handleMouseLeave}
                >
                  <defs>
                    <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="#00998F" stopOpacity={1} />
                      <stop offset="100%" stopColor="#10B981" stopOpacity={0.8} />
                    </linearGradient>
                    <linearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="#10B981" stopOpacity={0.3} />
                      <stop offset="100%" stopColor="#10B981" stopOpacity={0} />
                    </linearGradient>
                    <filter id="barShadow" height="130%">
                      <feDropShadow dx="0" dy="3" stdDeviation="5" floodColor="#00998F" floodOpacity="0.3"/>
                    </filter>
                    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
                      <feGaussianBlur stdDeviation="6" result="blur" />
                      <feComposite in="SourceGraphic" in2="blur" operator="over" />
                    </filter>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" opacity={0.2} vertical={false} />
                  <XAxis
                    dataKey={data[0]?.quarter ? "quarter" : "month"}
                    tick={{ fill: '#4B5563', fontSize: 12 }}
                    axisLine={{ stroke: '#E5E7EB' }}
                    tickLine={false}
                    dy={10}
                  />
                  <YAxis
                    tickFormatter={(value) => `₹${value / 1000}k`}
                    tick={{ fill: '#4B5563', fontSize: 12 }}
                    axisLine={{ stroke: '#E5E7EB' }}
                    tickLine={false}
                    dx={-10}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  {/* Legend removed as we don't need it anymore */}

                  {/* Trend line and area removed as requested */}

                  {/* Main bars */}
                  <Bar
                    dataKey="amount"
                    name=""
                    radius={[8, 8, 0, 0]}
                    cursor="pointer"
                    isAnimationActive={true}
                    animationDuration={1500}
                    animationEasing="ease-in-out"
                    barSize={30}
                  >
                    {enhancedData.map((_, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={`url(#barGradient)`}
                        style={{
                          filter: activeIndex === index ? 'url(#barShadow)' : 'none',
                          opacity: activeIndex === null || activeIndex === index ? 1 : 0.7,
                          transition: 'all 0.3s ease-in-out',
                          transform: activeIndex === index ? 'scaleY(1.05)' : 'scaleY(1)',
                          transformOrigin: 'bottom'
                        }}
                      />
                    ))}
                  </Bar>
                </ComposedChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="h-[400px] flex flex-col items-center justify-center">
              <div className="w-full max-w-md">
                <div className="flex items-center justify-center mb-8">
                  <div className="p-6 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-full shadow-lg border border-teal-100 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-teal-500/10 to-transparent rounded-full"></div>
                    <div className="absolute inset-0 bg-gradient-to-tr from-emerald-500/10 to-transparent rounded-full"></div>
                    <TrendingUp className="h-16 w-16 text-[#00998F] relative z-10" />
                    <div className="absolute inset-0 bg-white/10 rounded-full backdrop-blur-sm"></div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-white to-teal-50/30 p-8 rounded-xl shadow-xl border-0 relative overflow-hidden"
                  style={{
                    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                    backdropFilter: 'blur(10px)'
                  }}>
                  {/* Background decorative elements */}
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-500/10 to-transparent rounded-bl-full"></div>
                  <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-teal-500/10 to-transparent rounded-tr-full"></div>
                  <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>

                  <div className="flex items-center gap-4 mb-8">
                    <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl shadow-md border border-teal-50">
                      <TrendingUp className="h-6 w-6 text-[#00998F]" />
                    </div>
                    <h3 className="text-2xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                      {selectedQuarter}
                    </h3>
                  </div>

                  <div className="flex flex-col gap-6">
                    <div className="flex items-center justify-center p-6 bg-gradient-to-r from-teal-50 to-emerald-50 rounded-xl shadow-md border border-teal-100">
                      <span className="text-3xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                        {formatCurrency(data.find(item => item.quarter === selectedQuarter)?.amount || 0)}
                      </span>
                    </div>

                    <div className="h-2 w-full bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400 rounded-full shadow-sm"></div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Total amount display */}
          <div className="mt-4 pt-4 border-t border-gray-100">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="p-2.5 bg-gradient-to-br from-teal-50 to-emerald-50 rounded-lg mr-3 shadow-sm border border-teal-100">
                  <TrendingUp className="h-5 w-5 text-[#00998F]" />
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-700">Total Disbursements</span>
                  <div className="text-xs text-gray-500">Across all quarters</div>
                </div>
              </div>
              <div className="flex items-center bg-gradient-to-r from-teal-50 to-emerald-50 px-5 py-2.5 rounded-lg shadow-sm border border-teal-100">
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 20
                  }}
                  className="text-lg font-bold text-[#00998F]"
                >
                  {formatCurrency(data.reduce((sum, item) => sum + item.amount, 0))}
                </motion.div>
              </div>
            </div>
          </div>

          {/* Animated hint text */}
          {animationComplete && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="text-center mt-4 text-xs text-gray-500 italic"
            >
              
            </motion.div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
