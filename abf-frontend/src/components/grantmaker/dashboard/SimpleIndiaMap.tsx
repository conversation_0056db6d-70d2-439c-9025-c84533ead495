"use client";

import React, { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import {
  Globe,
  MapPin,
  Users,
  BookOpen,
  HeartPulse,
  Leaf,
  PenTool,
  Baby,
  Home,
  Tractor,
  GraduationCap,
  Heart,
  TreePine,
  UserRound,
  Building,
  Palette,
  X
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { HoverableIndiaMap } from "./HoverableIndiaMap";

// Program types and their colors (professional gradient colors)
const programTypeColors = {
  "Economic Advancement": {
    main: "#4361EE", // Main color
    gradient: "linear-gradient(135deg, #4361EE, #3A56D4)", // Gradient
    light: "#EEF1FF", // Light background
    hover: "rgba(67, 97, 238, 0.8)" // Hover color
  },
  "Promotion of Well being": {
    main: "#F72585", // Main color
    gradient: "linear-gradient(135deg, #F72585, #D81B76)", // Gradient
    light: "#FEE9F2", // Light background
    hover: "rgba(247, 37, 133, 0.8)" // Hover color
  },
  "Social Advancement": {
    main: "#06D6A0", // Main color
    gradient: "linear-gradient(135deg, #06D6A0, #05C090)", // Gradient
    light: "#E6F9F4", // Light background
    hover: "rgba(6, 214, 160, 0.8)" // Hover color
  }
};

// Mock data for the states with focus on program types
const stateData = [
  {
    state: "Karnataka",
    projects: 3,
    livesImpacted: 7500,
    primaryProgram: "Economic Advancement", // Main program type for coloring
    programDetails: [
      {
        type: "Economic Advancement",
        name: "Rural Livelihood Initiative",
        beneficiaries: 3200,
        description: "Supporting rural livelihoods and employment"
      },
      {
        type: "Promotion of Well being",
        name: "Mental Health Program",
        beneficiaries: 2800,
        description: "Mental health services for rural communities"
      },
      {
        type: "Social Advancement",
        name: "Community Empowerment",
        beneficiaries: 1500,
        description: "Supporting community development initiatives"
      }
    ]
  },
  {
    state: "Maharashtra",
    projects: 2,
    livesImpacted: 5000,
    primaryProgram: "Promotion of Well being",
    programDetails: [
      {
        type: "Promotion of Well being",
        name: "Mental Health Camps",
        beneficiaries: 3000,
        description: "Mobile mental health camps in remote villages"
      },
      {
        type: "Social Advancement",
        name: "Community Centers",
        beneficiaries: 2000,
        description: "Building community centers for social activities"
      }
    ]
  },
  {
    state: "Tamil Nadu",
    projects: 2,
    livesImpacted: 4500,
    primaryProgram: "Social Advancement",
    programDetails: [
      {
        type: "Economic Advancement",
        name: "Coastal Livelihood Program",
        beneficiaries: 2500,
        description: "Livelihood support for coastal communities"
      },
      {
        type: "Social Advancement",
        name: "Social Inclusion Initiative",
        beneficiaries: 2000,
        description: "Promoting social inclusion in communities"
      }
    ]
  },
  {
    state: "Delhi",
    projects: 2,
    livesImpacted: 3000,
    primaryProgram: "Social Advancement",
    programDetails: [
      {
        type: "Social Advancement",
        name: "Urban Community Centers",
        beneficiaries: 1800,
        description: "Community centers in urban areas"
      },
      {
        type: "Promotion of Well being",
        name: "Urban Mental Health",
        beneficiaries: 1200,
        description: "Mental health support in urban communities"
      }
    ]
  },
  {
    state: "Gujarat",
    projects: 1,
    livesImpacted: 2500,
    primaryProgram: "Promotion of Well being",
    programDetails: [
      {
        type: "Promotion of Well being",
        name: "Rural Mental Health Initiative",
        beneficiaries: 2500,
        description: "Comprehensive mental health services in rural areas"
      }
    ]
  },
  {
    state: "West Bengal",
    projects: 1,
    livesImpacted: 1500,
    primaryProgram: "Economic Advancement",
    programDetails: [
      {
        type: "Economic Advancement",
        name: "Artisan Livelihood Support",
        beneficiaries: 1500,
        description: "Supporting traditional artisans' livelihoods"
      }
    ]
  },
  {
    state: "Uttar Pradesh",
    projects: 2,
    livesImpacted: 3000,
    primaryProgram: "Social Advancement",
    programDetails: [
      {
        type: "Economic Advancement",
        name: "Rural Livelihood Program",
        beneficiaries: 1500,
        description: "Promoting sustainable livelihoods in rural areas"
      },
      {
        type: "Social Advancement",
        name: "Community Development",
        beneficiaries: 1500,
        description: "Community development initiatives"
      }
    ]
  },
  {
    state: "Rajasthan",
    projects: 1,
    livesImpacted: 2000,
    primaryProgram: "Economic Advancement",
    programDetails: [
      {
        type: "Economic Advancement",
        name: "Desert Livelihood Development",
        beneficiaries: 2000,
        description: "Sustainable livelihoods in desert rural areas"
      }
    ]
  },
  {
    state: "Madhya Pradesh",
    projects: 1,
    livesImpacted: 1800,
    primaryProgram: "Promotion of Well being",
    programDetails: [
      {
        type: "Promotion of Well being",
        name: "Tribal Mental Health Program",
        beneficiaries: 1800,
        description: "Mental health support for tribal communities"
      }
    ]
  }
];

export function SimpleIndiaMap() {
  const [activeState, setActiveState] = useState<string | null>(null);
  const [hoverState, setHoverState] = useState<string | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [hoverTooltipPosition, setHoverTooltipPosition] = useState({ x: 0, y: 0 });

  // Function to get program icon with color based on program type
  const getProgramIcon = (programType: string, size: number = 3) => {
    const colorObj = programTypeColors[programType as keyof typeof programTypeColors];
    const color = colorObj ? colorObj.main : "#6E6E6E";

    switch (programType) {
      case "Economic Advancement":
        return <Tractor className={`h-${size} w-${size}`} style={{ color }} />;
      case "Promotion of Well being":
        return <HeartPulse className={`h-${size} w-${size}`} style={{ color }} />;
      case "Social Advancement":
        return <Users className={`h-${size} w-${size}`} style={{ color }} />;
      default:
        return <MapPin className={`h-${size} w-${size}`} style={{ color }} />;
    }
  };



  // Handle state selection
  const handleStateSelect = (state: string | null) => {
    if (state) {
      setActiveState(state);

      // Calculate tooltip position - center of the viewport
      // Position it higher up to avoid cutting off at the bottom
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      setTooltipPosition({
        x: viewportWidth / 2,
        y: viewportHeight / 4 // Position it higher (1/4 of viewport height instead of 1/3)
      });
    } else {
      setActiveState(null);
    }
  };

  // Add event listeners to close the tooltip when clicking outside or scrolling
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if the click is outside the tooltip
      const tooltipElement = document.querySelector('.map-tooltip');
      if (activeState && tooltipElement && !tooltipElement.contains(event.target as Node)) {
        // Don't close if clicking on the map itself (let the map handle its own clicks)
        const mapElement = document.querySelector('.india-map-container');
        if (!mapElement || !mapElement.contains(event.target as Node)) {
          setActiveState(null);
        }
      }
    };

    const handleScroll = () => {
      if (activeState) {
        setActiveState(null);
      }
    };

    // Add event listeners
    document.addEventListener('click', handleClickOutside);
    window.addEventListener('scroll', handleScroll);

    // Clean up
    return () => {
      document.removeEventListener('click', handleClickOutside);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [activeState]);

  // Get active state data
  const activeStateData = activeState ? stateData.find(item => item.state === activeState) : null;

  // Get hover state data
  const hoverStateData = hoverState ? stateData.find(item => item.state === hoverState) : null;

  // Create city colors map based on program types
  const cityColors: Record<string, string> = {};

  // Set up colors for each state based on primary program
  stateData.forEach(state => {
    const programType = state.primaryProgram as keyof typeof programTypeColors;
    const colorObj = programTypeColors[programType];

    if (colorObj) {
      // Use gradient colors for better visibility
      cityColors[state.state] = colorObj.main;
    } else {
      cityColors[state.state] = "#6E6E6E";
    }
  });

  // Get all program types from the data
  const getAllProgramTypes = () => {
    const programTypes = new Set<string>();

    stateData.forEach(state => {
      state.programDetails.forEach(program => {
        programTypes.add(program.type);
      });
    });

    return Array.from(programTypes);
  };

  return (
    <Card className="bg-white shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border-0 rounded-xl">
      <div className="h-1 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
      <CardHeader className="pb-2">
        <div className="flex items-start">
          <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-sm group-hover:shadow-md transition-all duration-300">
            <Globe className="h-6 w-6 text-[#00998F] group-hover:text-teal-600 transition-colors" />
          </div>
          <div>
            <CardTitle className="text-xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
              Geographical Spread
            </CardTitle>
            <CardDescription className="text-gray-600">
              Program Distribution across India (hover over states for details)
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="relative h-[500px] w-full">
          <div className="w-full h-full flex items-center justify-center relative">
            <div className="relative">
              {/* Background glow effect */}
              <div
                className="absolute inset-0 rounded-full blur-3xl transition-all duration-500"
                style={{
                  background: activeState ?
                    `radial-gradient(circle, ${programTypeColors[stateData.find(s => s.state === activeState)?.primaryProgram as keyof typeof programTypeColors || "Education"]} 0%, transparent 70%)` :
                    'none',
                  opacity: activeState ? 0.4 : 0,
                }}
              />

              {/* Main map */}
              <div className="india-map-container">
                <HoverableIndiaMap
                  cityColors={cityColors}
                  onHover={(state, event) => {
                    setHoverState(state);
                    if (event) {
                      setHoverTooltipPosition({
                        x: event.clientX,
                        y: event.clientY
                      });
                    }
                  }}
                  onSelect={handleStateSelect}
                  size={450}
                />
              </div>
            </div>

            {/* No program icons on the map - using colors instead */}
          </div>

          {/* Hover Tooltip */}
          {hoverState && hoverStateData && !activeState && (
            <motion.div
              key={`hover-${hoverState}`}
              initial={{ opacity: 0, y: 5, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ type: "spring", stiffness: 500, damping: 30 }}
              className="fixed bg-white p-2 rounded-lg shadow-lg border border-gray-200 z-50 max-w-[220px] transition-all duration-200 pointer-events-none"
              style={{
                left: `${hoverTooltipPosition.x}px`,
                top: `${hoverTooltipPosition.y - 30}px`,
                transform: 'translate(-50%, -100%)',
                backdropFilter: 'blur(8px)',
                background: `linear-gradient(135deg, ${programTypeColors[hoverStateData.primaryProgram as keyof typeof programTypeColors]?.light || 'rgba(255, 255, 255, 0.95)'}, white)`,
                borderColor: programTypeColors[hoverStateData.primaryProgram as keyof typeof programTypeColors]?.main || '#e2e8f0',
                boxShadow: `0 10px 25px -5px ${programTypeColors[hoverStateData.primaryProgram as keyof typeof programTypeColors]?.main}20 || rgba(0,0,0,0.1)`
              }}
            >
              <div className="flex items-center gap-2 mb-1">
                <div
                  className="p-1 rounded-md"
                  style={{
                    background: programTypeColors[hoverStateData.primaryProgram as keyof typeof programTypeColors]?.gradient || 'linear-gradient(135deg, #00998F, #00887F)'
                  }}
                >
                  {getProgramIcon(hoverStateData.primaryProgram, 3)}
                </div>
                <div>
                  <div
                    className="font-bold text-xs"
                    style={{
                      color: programTypeColors[hoverStateData.primaryProgram as keyof typeof programTypeColors]?.main || '#00998F'
                    }}
                  >
                    {hoverState}
                  </div>
                  <div className="text-[10px] text-gray-500">
                    <span>{hoverStateData.programDetails.length} programs • {hoverStateData.livesImpacted.toLocaleString()} lives</span>
                  </div>
                </div>
              </div>
              <div className="text-[10px] text-gray-600 flex items-center gap-1">
                <span
                  className="font-medium px-1.5 py-0.5 rounded-sm"
                  style={{
                    background: programTypeColors[hoverStateData.primaryProgram as keyof typeof programTypeColors]?.light || '#f0f9ff',
                    color: programTypeColors[hoverStateData.primaryProgram as keyof typeof programTypeColors]?.main || '#00998F'
                  }}
                >
                  {hoverStateData.primaryProgram}
                </span>
              </div>
              <div className="flex flex-wrap gap-1 mt-1">
                {hoverStateData.programDetails.slice(0, 2).map((program, index) => (
                  <div
                    key={index}
                    className="text-[10px] px-1.5 py-0.5 rounded-sm"
                    style={{
                      background: programTypeColors[program.type as keyof typeof programTypeColors]?.light || '#f0f9ff',
                      color: programTypeColors[program.type as keyof typeof programTypeColors]?.main || '#0ea5e9'
                    }}
                  >
                    {program.name}
                  </div>
                ))}
                {hoverStateData.programDetails.length > 2 && (
                  <div className="text-[10px] text-gray-500">+{hoverStateData.programDetails.length - 2} more</div>
                )}
              </div>
            </motion.div>
          )}

          {/* Enhanced Tooltip with better animations and styling */}
          <AnimatePresence>
            {activeState && activeStateData && (
              <motion.div
                initial={{ opacity: 0, y: 10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                className="map-tooltip fixed bg-white p-5 rounded-xl shadow-xl border border-gray-200 z-50 w-96 transition-all duration-300 animate-fadeIn max-h-[80vh] overflow-y-auto"
              style={{
                left: `${tooltipPosition.x}px`,
                top: `${tooltipPosition.y}px`,
                transform: 'translate(-50%, -50%)', // Center the tooltip instead of positioning it above
                backdropFilter: 'blur(8px)',
                background: 'rgba(255, 255, 255, 0.95)',
                maxHeight: '80vh',
                overflowY: 'auto',
                marginTop: '20px' // Add some margin to ensure it's not too close to the top
              }}
            >
              {/* Close button */}
              <button
                onClick={() => setActiveState(null)}
                className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-100 transition-colors"
                aria-label="Close"
              >
                <X className="h-4 w-4 text-gray-500" />
              </button>

              <div className="flex items-center gap-3 mb-3">
                <div
                  className="p-2 rounded-lg shadow-sm animate-pulse-slow"
                  style={{
                    background: programTypeColors[activeStateData.primaryProgram as keyof typeof programTypeColors]?.gradient || 'linear-gradient(135deg, #00998F, #00887F)'
                  }}
                >
                  {getProgramIcon(activeStateData.primaryProgram, 5)}
                </div>
                <div className="flex flex-col">
                  <div
                    className="font-bold text-gray-800 text-xl bg-clip-text text-transparent"
                    style={{
                      backgroundImage: programTypeColors[activeStateData.primaryProgram as keyof typeof programTypeColors]?.gradient || 'linear-gradient(to right, #00998F, #00887F)'
                    }}
                  >
                    {activeState}
                  </div>
                  <div className="text-xs text-gray-500 flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    <span>State in India • {activeStateData.programDetails.length} programs</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 }}
                  className="p-3 rounded-lg shadow-sm border hover:shadow-md transition-all duration-300"
                  style={{
                    background: programTypeColors[activeStateData.primaryProgram as keyof typeof programTypeColors]?.light || '#f0f9ff',
                    borderColor: `${programTypeColors[activeStateData.primaryProgram as keyof typeof programTypeColors]?.main}30` || '#e2e8f0'
                  }}
                >
                  <div
                    className="text-xs font-medium"
                    style={{ color: programTypeColors[activeStateData.primaryProgram as keyof typeof programTypeColors]?.main || '#00998F' }}
                  >
                    Primary Program
                  </div>
                  <div className="text-lg font-bold text-gray-700 flex items-center gap-2">
                    {getProgramIcon(activeStateData.primaryProgram, 4)}
                    <span>{activeStateData.primaryProgram}</span>
                  </div>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: 10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                  className="p-3 rounded-lg shadow-sm border hover:shadow-md transition-all duration-300"
                  style={{
                    background: programTypeColors[activeStateData.primaryProgram as keyof typeof programTypeColors]?.light || '#f0f9ff',
                    borderColor: `${programTypeColors[activeStateData.primaryProgram as keyof typeof programTypeColors]?.main}30` || '#e2e8f0'
                  }}
                >
                  <div
                    className="text-xs font-medium"
                    style={{ color: programTypeColors[activeStateData.primaryProgram as keyof typeof programTypeColors]?.main || '#00998F' }}
                  >
                    Lives Impacted
                  </div>
                  <div className="text-xl font-bold text-gray-700">{activeStateData.livesImpacted.toLocaleString()}</div>
                </motion.div>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="mb-4"
              >
                <div
                  className="text-sm font-medium mb-2 flex items-center"
                  style={{ color: programTypeColors[activeStateData.primaryProgram as keyof typeof programTypeColors]?.main || '#00998F' }}
                >
                  <div
                    className="w-1.5 h-1.5 rounded-full mr-2"
                    style={{ background: programTypeColors[activeStateData.primaryProgram as keyof typeof programTypeColors]?.main || '#00998F' }}
                  ></div>
                  All Programs in {activeState}
                </div>
                <div className="flex flex-wrap gap-2">
                  {activeStateData.programDetails.map((program, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.3 + (index * 0.1) }}
                      className="flex items-center px-3 py-1.5 rounded-full text-xs font-medium shadow-sm border hover:shadow-md transition-all duration-300"
                      style={{
                        background: programTypeColors[program.type as keyof typeof programTypeColors]?.light || '#f0f9ff',
                        borderColor: `${programTypeColors[program.type as keyof typeof programTypeColors]?.main}30` || '#e2e8f0',
                        color: programTypeColors[program.type as keyof typeof programTypeColors]?.main || '#00998F'
                      }}
                    >
                      {getProgramIcon(program.type)}
                      <span className="ml-1.5">{program.name}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Program Details */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="mb-2"
              >
                <div
                  className="text-sm font-medium mb-2 flex items-center"
                  style={{ color: programTypeColors[activeStateData.primaryProgram as keyof typeof programTypeColors]?.main || '#00998F' }}
                >
                  <div
                    className="w-1.5 h-1.5 rounded-full mr-2"
                    style={{ background: programTypeColors[activeStateData.primaryProgram as keyof typeof programTypeColors]?.main || '#00998F' }}
                  ></div>
                  Program Details:
                </div>
                <div className="space-y-2">
                  {activeStateData.programDetails.map((program, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 5 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 + (index * 0.1) }}
                      className="p-3 rounded-md border"
                      style={{
                        background: programTypeColors[program.type as keyof typeof programTypeColors]?.light || '#f0f9ff',
                        borderColor: `${programTypeColors[program.type as keyof typeof programTypeColors]?.main}30` || '#e2e8f0'
                      }}
                    >
                      <div className="flex items-center gap-2 mb-1">
                        {getProgramIcon(program.type, 4)}
                        <span
                          className="font-medium text-sm"
                          style={{ color: programTypeColors[program.type as keyof typeof programTypeColors]?.main || '#00998F' }}
                        >
                          {program.name}
                        </span>
                      </div>
                      <div className="text-xs text-gray-600">{program.description}</div>
                      <div
                        className="text-xs font-medium mt-1"
                        style={{ color: programTypeColors[program.type as keyof typeof programTypeColors]?.main || '#00998F' }}
                      >
                        Beneficiaries: {program.beneficiaries.toLocaleString()}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </motion.div>
          )}
          </AnimatePresence>
        </div>

        <div className="mt-6 pt-4 border-t border-gray-100">
          <div className="grid grid-cols-1 gap-4">
            <div>
              <div className="text-sm font-medium text-[#00998F] mb-3 flex items-center">
                <div className="w-1.5 h-1.5 bg-[#00998F] rounded-full mr-2"></div>
                Program Types Color
              </div>
              <div className="flex flex-wrap gap-2">
                {Object.entries(programTypeColors).map(([program, colorObj]) => (
                  <div
                    key={program}
                    className="flex items-center px-3 py-2 rounded-lg text-xs font-medium shadow-sm border transition-all duration-300 hover:shadow-md"
                    style={{
                      background: colorObj.gradient,
                      borderColor: colorObj.main,
                      color: 'white'
                    }}
                  >
                    <div className="flex items-center gap-2">
                      {getProgramIcon(program, 4)}
                      <span>{program}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
