"use client";

import { Card, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import { Users, Target, Heart, TrendingUp } from "lucide-react";

interface ProjectMetricsData {
  totalProjects: number;
  livesImpacted: number;
  sectorDistribution: Array<{
    name: string;
    count: number;
    color: string;
  }>;
}

interface ProjectMetricsCardProps {
  data: ProjectMetricsData;
  onCardClick?: (section: string) => void;
}

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 shadow-md rounded-md border border-gray-100">
        <p className="font-medium text-gray-800">{label}</p>
        <p className="text-sm text-gray-600">
          {payload[0].value} projects
        </p>
      </div>
    );
  }
  return null;
};

export function ProjectMetricsCard({ data, onCardClick }: ProjectMetricsCardProps) {
  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { y: 20, opacity: 0 },
    show: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 80,
        damping: 12
      }
    }
  };

  // Calculate total projects by sector for the donut chart
  const sectorData = data.sectorDistribution.map(sector => ({
    name: sector.name,
    value: sector.count,
    color: sector.color
  }));

  return (
    <motion.div
      variants={container}
      initial="hidden"
      animate="show"
      className="mb-8"
    >
      <div className="flex justify-between items-center mb-4">
        <CardTitle className="text-2xl font-bold text-gray-800">
          Project Impact
        </CardTitle>
        <div className="flex items-center space-x-2">
          {data.sectorDistribution.slice(0, 5).map((sector, index) => (
            <div
              key={index}
              className="flex items-center"
              title={`${sector.name}: ${sector.count} projects`}
            >
              <div
                className="w-3 h-3 rounded-full mr-1"
                style={{ backgroundColor: sector.color }}
              ></div>
              <span className="text-xs text-gray-600 hidden md:inline">{sector.name}</span>
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Total Projects Card */}
        <motion.div variants={item}>
          <Card className="bg-gradient-to-br from-white via-white to-emerald-50/30 shadow-md hover:shadow-xl transition-all duration-300 rounded-xl overflow-hidden border-0">
            <div className="h-1.5 bg-gradient-to-r from-emerald-600 via-emerald-500 to-teal-400"></div>
            <div className="p-5 flex items-center">
              <div className="mr-4 bg-gradient-to-br from-emerald-100 to-teal-50 p-3 rounded-xl shadow-sm">
                <Target className="h-8 w-8 text-emerald-600" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-800 mb-1">Total Projects</h3>
                <div className="flex items-center justify-between">
                  <p className="text-4xl font-bold bg-gradient-to-r from-emerald-600 to-teal-500 bg-clip-text text-transparent">
                    {data.totalProjects}
                  </p>
                  <div className="flex flex-col items-end">
                    <div className="flex items-center text-xs text-gray-500 mb-1">
                      <span className="w-2 h-2 rounded-full bg-emerald-500 mr-1"></span>
                      <span>Active: {Math.round(data.totalProjects * 0.7)}</span>
                    </div>
                    <div className="flex items-center text-xs text-gray-500">
                      <span className="w-2 h-2 rounded-full bg-gray-300 mr-1"></span>
                      <span>Completed: {Math.round(data.totalProjects * 0.3)}</span>
                    </div>
                  </div>
                </div>
                <div className="mt-3 h-1.5 bg-gray-100 rounded-full overflow-hidden">
                  <div className="h-full bg-emerald-500 rounded-full" style={{ width: '70%' }}></div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Lives Impacted Card */}
        <motion.div variants={item}>
          <Card className="bg-gradient-to-br from-white via-white to-rose-50/30 shadow-md hover:shadow-xl transition-all duration-300 rounded-xl overflow-hidden border-0">
            <div className="h-1.5 bg-gradient-to-r from-rose-600 via-rose-500 to-pink-400"></div>
            <div className="p-5 flex items-center">
              <div className="mr-4 bg-gradient-to-br from-rose-100 to-pink-50 p-3 rounded-xl shadow-sm">
                <Heart className="h-8 w-8 text-rose-600" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-800 mb-1">Lives Impacted</h3>
                <div className="flex items-center justify-between">
                  <p className="text-4xl font-bold bg-gradient-to-r from-rose-600 to-pink-500 bg-clip-text text-transparent">
                    {data.livesImpacted.toLocaleString()}
                  </p>
                  <div className="flex flex-col items-end">
                    <div className="flex items-center text-xs text-gray-500">
                      <Users className="h-3 w-3 mr-1 text-rose-500" />
                      <span>Across {data.sectorDistribution.length} sectors</span>
                    </div>
                    <div className="flex items-center text-xs text-green-600 mt-1">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      <span>+12% from last year</span>
                    </div>
                  </div>
                </div>
                <div className="mt-3 grid grid-cols-5 gap-1">
                  {data.sectorDistribution.slice(0, 5).map((sector, index) => (
                    <div
                      key={index}
                      className="h-1.5 rounded-full"
                      style={{ backgroundColor: sector.color }}
                      title={sector.name}
                    ></div>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </motion.div>
  );
}
