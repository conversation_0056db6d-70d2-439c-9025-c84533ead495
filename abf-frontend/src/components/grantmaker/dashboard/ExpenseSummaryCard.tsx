"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import { FileSpreadsheet, TrendingUp, ChevronRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { Responsive<PERSON>ontaine<PERSON>, <PERSON><PERSON>hart, Pie, Cell, Tooltip } from "recharts";

interface ExpenseSummaryCardProps {
  totalBudget: number;
  totalActual: number;
  pendingCount: number;
  approvedCount: number;
  rejectedCount: number;
  onClick: () => void;
}

export function ExpenseSummaryCard({
  totalBudget,
  totalActual,
  pendingCount,
  approvedCount,
  rejectedCount,
  onClick
}: ExpenseSummaryCardProps) {
  const router = useRouter();

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Calculate utilization percentage
  const utilizationPercentage = totalBudget > 0
    ? Math.round((totalActual / totalBudget) * 100)
    : 0;

  // Data for pie chart
  const data = [
    { name: 'Approved', value: approvedCount, color: '#10b981' },
    { name: 'Pending', value: pendingCount, color: '#f59e0b' },
    { name: 'Rejected', value: rejectedCount, color: '#ef4444' }
  ];

  // Custom tooltip for pie chart
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-2 border border-gray-200 shadow-md rounded-md text-xs">
          <p className="font-medium">{`${payload[0].name}: ${payload[0].value}`}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="group cursor-pointer"
      onClick={onClick}
    >
      <Card className="bg-white shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border-0">
        <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
        <CardHeader className="pb-2">
          <div className="flex items-start">
            <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-sm group-hover:shadow-md transition-all duration-300">
              <FileSpreadsheet className="h-6 w-6 text-[#00998F] group-hover:text-teal-600 transition-colors" />
            </div>
            <div>
              <CardTitle className="text-xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                Expense Overview
              </CardTitle>
              <CardDescription className="text-gray-600">
                Budget utilization and expense status
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <p className="text-sm text-gray-500 font-medium">Total Budget</p>
                  <p className="text-sm text-gray-900 font-semibold">{formatCurrency(totalBudget)}</p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-sm text-gray-500 font-medium">Total Spent</p>
                  <p className="text-sm text-gray-900 font-semibold">{formatCurrency(totalActual)}</p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-sm text-gray-500 font-medium">Utilization</p>
                  <div className="flex items-center">
                    <p className="text-sm text-gray-900 font-semibold">{utilizationPercentage}%</p>
                    <TrendingUp className="h-3 w-3 ml-1 text-green-500" />
                  </div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-100">
                <div className="flex justify-between items-center">
                  <p className="text-sm text-gray-500 font-medium">Pending Expenses</p>
                  <p className="text-sm text-amber-600 font-semibold">{pendingCount}</p>
                </div>
              </div>
            </div>

            <div className="h-32">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data}
                    cx="50%"
                    cy="50%"
                    innerRadius={30}
                    outerRadius={50}
                    paddingAngle={2}
                    dataKey="value"
                  >
                    {data.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          <div className="text-right">
            <span className="text-xs text-[#00998F] font-medium flex items-center justify-end opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              View All Expenses
              <ChevronRight className="h-3 w-3 ml-1" />
            </span>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
