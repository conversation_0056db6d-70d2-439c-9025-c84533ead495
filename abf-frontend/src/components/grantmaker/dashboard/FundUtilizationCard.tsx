"use client";

import { useState, useEffect, useRef } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { motion, AnimatePresence } from "framer-motion";
import { formatCurrency } from "@/services/grantmaker/grantmaker-service";
import { BarChart3, ArrowUpRight, Wallet, TrendingUp, IndianRupee, Info, ChevronRight, DollarSign, CheckCircle2, Building2 } from "lucide-react";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell, Legend, LabelList } from "recharts";

interface FundUtilizationCardProps {
  totalBudget: number;
  totalDisbursed: number;
  totalUtilized: number;
}

export function FundUtilizationCard({
  totalBudget,
  totalDisbursed,
  totalUtilized
}: FundUtilizationCardProps) {
  // Calculate percentages
  const disbursedPercentage = Math.round((totalDisbursed / totalBudget) * 100);
  const utilizedPercentage = Math.round((totalUtilized / totalBudget) * 100);
  const remainingPercentage = 100 - utilizedPercentage;

  // State for animations
  const [animatedDisbursed, setAnimatedDisbursed] = useState(0);
  const [animatedUtilized, setAnimatedUtilized] = useState(0);
  const [activeBar, setActiveBar] = useState<string | null>(null);
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipData, setTooltipData] = useState({ title: "", value: 0, percentage: 0, color: "" });
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });

  // Animation for the values
  useEffect(() => {
    const disbursedInterval = setInterval(() => {
      setAnimatedDisbursed(prev => {
        const next = prev + totalDisbursed / 50;
        return next >= totalDisbursed ? totalDisbursed : next;
      });
    }, 20);

    const utilizedInterval = setInterval(() => {
      setAnimatedUtilized(prev => {
        const next = prev + totalUtilized / 50;
        return next >= totalUtilized ? totalUtilized : next;
      });
    }, 20);

    return () => {
      clearInterval(disbursedInterval);
      clearInterval(utilizedInterval);
    };
  }, [totalDisbursed, totalUtilized]);

  // Data for the project-wise chart with added spacing
  const projectData = [
    {
      name: "Ashadeep",
      budget: totalBudget * 0.25,
      disbursed: totalDisbursed * 0.27,
      utilized: totalUtilized * 0.26,
      budgetColor: "#4f46e5",
      disbursedColor: "#f59e0b",
      utilizedColor: "#10b981"
    },
    {
      name: "Tycia",
      budget: totalBudget * 0.20,
      disbursed: totalDisbursed * 0.22,
      utilized: totalUtilized * 0.21,
      budgetColor: "#4f46e5",
      disbursedColor: "#f59e0b",
      utilizedColor: "#10b981"
    },
    {
      name: "Saahas",
      budget: totalBudget * 0.20,
      disbursed: totalDisbursed * 0.18,
      utilized: totalUtilized * 0.19,
      budgetColor: "#4f46e5",
      disbursedColor: "#f59e0b",
      utilizedColor: "#10b981"
    },
    {
      name: "Slam Out Loud",
      budget: totalBudget * 0.18,
      disbursed: totalDisbursed * 0.17,
      utilized: totalUtilized * 0.18,
      budgetColor: "#4f46e5",
      disbursedColor: "#f59e0b",
      utilizedColor: "#10b981"
    },
    {
      name: "Positive Yuva Network",
      budget: totalBudget * 0.17,
      disbursed: totalDisbursed * 0.16,
      utilized: totalUtilized * 0.16,
      budgetColor: "#4f46e5",
      disbursedColor: "#f59e0b",
      utilizedColor: "#10b981"
    },
  ];

  // Handle bar hover
  const handleBarMouseEnter = (data: any, index: number, event: any) => {
    const barName = data.name;
    setActiveBar(barName);

    // Calculate tooltip position
    const rect = event.target.getBoundingClientRect();
    setTooltipPosition({
      x: rect.left + rect.width / 2,
      y: rect.top - 10
    });

    // Set tooltip data
    let percentage = 0;
    if (barName === "Disbursed") {
      percentage = disbursedPercentage;
    } else if (barName === "Utilized") {
      percentage = utilizedPercentage;
    } else {
      percentage = 100;
    }

    setTooltipData({
      title: barName,
      value: data.value,
      percentage,
      color: data.color
    });

    setShowTooltip(true);
  };

  const handleBarMouseLeave = () => {
    setActiveBar(null);
    setShowTooltip(false);
  };

  // Comprehensive tooltip showing all three values at once - more compact
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      const projectName = data.name;

      return (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-3 shadow-lg rounded-lg border border-teal-100"
          style={{
            boxShadow: '0 10px 25px -5px rgba(0, 153, 143, 0.15), 0 8px 10px -6px rgba(0, 153, 143, 0.1)',
            maxWidth: '220px',
            background: 'linear-gradient(135deg, white, #f8fffe)'
          }}
        >
          {/* Project name header - more compact */}
          <div className="flex items-center gap-2 mb-2 pb-1 border-b border-gray-100">
            <div className="w-6 h-6 rounded-full flex items-center justify-center bg-gradient-to-br from-teal-100 to-teal-50">
              <Building2 className="h-3.5 w-3.5 text-[#00998F]" />
            </div>
            <div>
              <p className="font-semibold text-gray-800 text-sm">{projectName}</p>
              <p className="text-[10px] text-gray-500">Fund allocation</p>
            </div>
          </div>

          {/* All three values displayed together - more compact */}
          <div className="space-y-1.5">
            {/* Budget row */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1.5">
                <div className="w-4 h-4 rounded-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-blue-50">
                  <DollarSign className="h-2.5 w-2.5 text-blue-600" />
                </div>
                <span className="text-xs font-medium text-gray-700">Budget</span>
              </div>
              <div className="flex items-center">
                <span className="text-xs font-semibold text-blue-600">{formatCurrency(data.budget)}</span>
              </div>
            </div>

            {/* Disbursed row */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1.5">
                <div className="w-4 h-4 rounded-full flex items-center justify-center bg-gradient-to-br from-amber-100 to-amber-50">
                  <Wallet className="h-2.5 w-2.5 text-amber-600" />
                </div>
                <span className="text-xs font-medium text-gray-700">Disbursed</span>
              </div>
              <div className="flex items-center">
                <span className="text-xs font-semibold text-amber-600">{formatCurrency(data.disbursed)}</span>
              </div>
            </div>

            {/* Utilized row */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1.5">
                <div className="w-4 h-4 rounded-full flex items-center justify-center bg-gradient-to-br from-emerald-100 to-emerald-50">
                  <CheckCircle2 className="h-2.5 w-2.5 text-emerald-600" />
                </div>
                <span className="text-xs font-medium text-gray-700">Utilized</span>
              </div>
              <div className="flex items-center">
                <span className="text-xs font-semibold text-emerald-600">{formatCurrency(data.utilized)}</span>
              </div>
            </div>
          </div>


        </motion.div>
      );
    }
    return null;
  };

  // Progress bar with hover effect
  const ProgressBar = ({
    percentage,
    color,
    label,
    value,
    icon
  }: {
    percentage: number;
    color: string;
    label: string;
    value: number;
    icon: React.ReactNode;
  }) => {
    const [isHovered, setIsHovered] = useState(false);

    return (
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <div className={`p-2 bg-${color}-50 rounded-lg mr-3`}>
              {icon}
            </div>
            <span className="text-sm font-medium text-gray-700">{label}</span>
          </div>
          <div className="flex items-center">
            <AnimatePresence mode="wait">
              <motion.span
                key={Math.floor(value)}
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
                className="text-sm font-semibold text-gray-900 mr-2"
              >
                {formatCurrency(Math.floor(value))}
              </motion.span>
            </AnimatePresence>
            <span className={`text-xs font-medium text-${color}-600 bg-${color}-50 px-2 py-0.5 rounded-full`}>
              {percentage}%
            </span>
          </div>
        </div>
        <div
          className="w-full bg-gray-100 rounded-full h-3 relative overflow-hidden"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <motion.div
            className={`bg-${color}-500 h-3 rounded-full transition-all duration-1000 ease-in-out relative`}
            style={{ width: `${percentage}%` }}
            initial={{ width: 0 }}
            animate={{ width: `${percentage}%` }}
            transition={{ duration: 1, ease: "easeOut" }}
          >
            {isHovered && (
              <motion.div
                className="absolute inset-0 bg-white opacity-30"
                initial={{ x: '-100%' }}
                animate={{ x: '100%' }}
                transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
              />
            )}
          </motion.div>
        </div>
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="h-full"
    >
      <Card className="bg-white shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border-0 h-full">
        <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
        <CardHeader className="pb-2">
          <div className="flex items-start">
            <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-sm group-hover:shadow-md transition-all duration-300">
              <BarChart3 className="h-6 w-6 text-[#00998F] group-hover:text-teal-600 transition-colors" />
            </div>
            <div>
              <CardTitle className="text-xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                Grant Expenditure
              </CardTitle>
              <CardDescription className="text-gray-600">
                Budget allocation and utilization
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[400px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={projectData}
                margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                barGap={12} // Further increased gap between bars in the same group
                barSize={10} // Slightly smaller bars for better spacing
                barCategoryGap={40} // Further increased gap between different project groups
                layout="vertical"
              >
                <defs>
                  {/* Teal-based gradients for primary color consistency */}
                  <linearGradient id="budgetGradient" x1="0" y1="0" x2="1" y2="0">
                    <stop offset="0%" stopColor="#4f46e5" stopOpacity={0.9} />
                    <stop offset="100%" stopColor="#818cf8" stopOpacity={1} />
                  </linearGradient>
                  <linearGradient id="disbursedGradient" x1="0" y1="0" x2="1" y2="0">
                    <stop offset="0%" stopColor="#f59e0b" stopOpacity={0.9} />
                    <stop offset="100%" stopColor="#fcd34d" stopOpacity={1} />
                  </linearGradient>
                  <linearGradient id="utilizedGradient" x1="0" y1="0" x2="1" y2="0">
                    <stop offset="0%" stopColor="#10b981" stopOpacity={0.9} />
                    <stop offset="100%" stopColor="#34d399" stopOpacity={1} />
                  </linearGradient>
                  <linearGradient id="tealGradient" x1="0" y1="0" x2="1" y2="0">
                    <stop offset="0%" stopColor="#00998F" stopOpacity={0.9} />
                    <stop offset="100%" stopColor="#0d9488" stopOpacity={1} />
                  </linearGradient>

                  {/* Enhanced shadow effects */}
                  <filter id="shadow" height="200%">
                    <feDropShadow dx="0" dy="3" stdDeviation="4" floodOpacity="0.25" />
                  </filter>
                  <filter id="glow" height="200%">
                    <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                    <feMerge>
                      <feMergeNode in="coloredBlur"/>
                      <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                  </filter>

                  {/* Shimmer effect for bars */}
                  <linearGradient id="shimmer" x1="0" y1="0" x2="1" y2="0">
                    <stop offset="0%" stopColor="rgba(255,255,255,0)" stopOpacity="0">
                      <animate
                        attributeName="offset"
                        values="0;1"
                        dur="2s"
                        repeatCount="indefinite"
                      />
                    </stop>
                    <stop offset="50%" stopColor="rgba(255,255,255,0.3)" stopOpacity="0.3">
                      <animate
                        attributeName="offset"
                        values="0;1"
                        dur="2s"
                        repeatCount="indefinite"
                      />
                    </stop>
                    <stop offset="100%" stopColor="rgba(255,255,255,0)" stopOpacity="0">
                      <animate
                        attributeName="offset"
                        values="0;1"
                        dur="2s"
                        repeatCount="indefinite"
                      />
                    </stop>
                  </linearGradient>
                </defs>

                <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} opacity={0.15} />

                <XAxis
                  type="number"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: '#6b7280', fontSize: 11 }}
                  tickFormatter={(value) => `₹${value / 1000}K`}
                />

                <YAxis
                  type="category"
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: '#6b7280', fontSize: 12 }}
                  width={150}
                />

                <Tooltip
                  content={<CustomTooltip />}
                  cursor={{ fill: 'rgba(224, 231, 255, 0.1)' }}
                  wrapperStyle={{ outline: 'none' }}
                />

                <Legend
                  verticalAlign="bottom"
                  align="center"
                  wrapperStyle={{ paddingTop: '20px' }}
                  formatter={(value) => {
                    let color = "#4f46e5";
                    if (value === "Disbursed") color = "#f59e0b";
                    if (value === "Utilized") color = "#10b981";

                    return (
                      <span className="text-sm font-medium flex items-center gap-1.5">
                        <span className="inline-block w-3 h-3 rounded-full" style={{ backgroundColor: color }}></span>
                        {value}
                      </span>
                    );
                  }}
                />

                <Bar
                  name="Budget"
                  dataKey="budget"
                  fill="url(#budgetGradient)"
                  radius={[0, 6, 6, 0]}
                  animationDuration={1500}
                  animationBegin={0}
                  style={{ filter: 'drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.15))' }}
                >
                  {/* Add shimmer effect overlay */}
                  <defs>
                    <linearGradient id="budgetShimmer" x1="0" y1="0" x2="1" y2="0">
                      <stop offset="0%" stopColor="#4f46e5" stopOpacity="0.9" />
                      <stop offset="100%" stopColor="#818cf8" stopOpacity="1" />
                    </linearGradient>
                  </defs>
                </Bar>

                <Bar
                  name="Disbursed"
                  dataKey="disbursed"
                  fill="url(#disbursedGradient)"
                  radius={[0, 6, 6, 0]}
                  animationDuration={1500}
                  animationBegin={300}
                  style={{ filter: 'drop-shadow(0px 3px 5px rgba(0, 0, 0, 0.12))' }}
                />

                <Bar
                  name="Utilized"
                  dataKey="utilized"
                  fill="url(#utilizedGradient)"
                  radius={[0, 6, 6, 0]}
                  animationDuration={1500}
                  animationBegin={600}
                  style={{ filter: 'drop-shadow(0px 3px 5px rgba(0, 0, 0, 0.12))' }}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>

          <div className="flex justify-center items-center mt-6">
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center bg-blue-50 px-2 py-1 rounded-md">
                <div className="w-3 h-3 rounded-full bg-indigo-600 mr-2"></div>
                <span className="text-xs font-medium text-gray-700">Budget: {formatCurrency(totalBudget)}</span>
              </div>
              <div className="flex items-center bg-amber-50 px-2 py-1 rounded-md">
                <div className="w-3 h-3 rounded-full bg-amber-500 mr-2"></div>
                <span className="text-xs font-medium text-gray-700">Disbursed: {formatCurrency(totalDisbursed)}</span>
              </div>
              <div className="flex items-center bg-emerald-50 px-2 py-1 rounded-md">
                <div className="w-3 h-3 rounded-full bg-emerald-500 mr-2"></div>
                <span className="text-xs font-medium text-gray-700">Utilized: {formatCurrency(totalUtilized)}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced floating tooltip with teal primary color */}
      {showTooltip && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="fixed bg-white p-3 shadow-lg rounded-lg border border-teal-100 z-50 pointer-events-none"
          style={{
            left: `${tooltipPosition.x + 20}px`, // Shifted to the right
            top: `${tooltipPosition.y - 10}px`,
            transform: 'translate(0, -100%)', // Changed to not center horizontally
            boxShadow: '0 10px 25px -5px rgba(0, 153, 143, 0.15), 0 8px 10px -6px rgba(0, 153, 143, 0.1)',
            maxWidth: '200px', // Smaller width
            background: `linear-gradient(135deg, white, ${
              tooltipData.title === "Budget" ? "#4f46e510" :
              tooltipData.title === "Disbursed" ? "#f59e0b10" : "#10b98110"
            })`
          }}
        >
          {/* Tooltip header with icon - more compact */}
          <div className="flex items-center gap-2 mb-2 pb-1 border-b border-gray-100">
            <div
              className="w-7 h-7 rounded-full flex items-center justify-center"
              style={{
                background: tooltipData.title === "Budget" ?
                  "linear-gradient(135deg, #4f46e520, #4f46e510)" :
                  tooltipData.title === "Disbursed" ?
                  "linear-gradient(135deg, #f59e0b20, #f59e0b10)" :
                  "linear-gradient(135deg, #10b98120, #10b98110)"
              }}
            >
              {tooltipData.title === "Budget" ? (
                <DollarSign className="h-4 w-4 text-[#00998F]" />
              ) : tooltipData.title === "Disbursed" ? (
                <Wallet className="h-4 w-4 text-[#00998F]" />
              ) : (
                <CheckCircle2 className="h-4 w-4 text-[#00998F]" />
              )}
            </div>
            <div>
              <p className="font-semibold text-gray-800 text-sm">{tooltipData.title}</p>
              <p className="text-[10px] text-gray-500">
                {tooltipData.title === "Budget" ? "Allocated funds" :
                 tooltipData.title === "Disbursed" ? "Funds released" :
                 "Funds utilized"}
              </p>
            </div>
          </div>

          {/* Amount display with teal gradient - more compact */}
          <div className="mb-2 flex items-center justify-between">
            <div className="bg-gradient-to-r from-[#00998F] to-teal-500 text-white px-2 py-1 rounded-md font-medium text-sm">
              <div className="flex items-center gap-1">
                <IndianRupee className="h-3 w-3" />
                <p>{formatCurrency(tooltipData.value)}</p>
              </div>
            </div>


          </div>

          {/* Tooltip arrow */}
          <div className="absolute -bottom-2 left-4 w-4 h-4 bg-white rotate-45 border-r border-b border-teal-100"></div>
        </motion.div>
      )}
    </motion.div>
  );
}
