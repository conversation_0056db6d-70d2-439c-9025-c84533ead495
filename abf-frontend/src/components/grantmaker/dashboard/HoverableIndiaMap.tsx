"use client";

import React, { useState, useEffect, useRef } from "react";
import India from "@react-map/india";

interface HoverableIndiaMapProps {
  cityColors: Record<string, string>;
  onHover: (state: string | null, event: React.MouseEvent | null) => void;
  onSelect: (state: string | null) => void;
  size?: number;
}

export function HoverableIndiaMap({
  cityColors,
  onHover,
  onSelect,
  size = 450
}: HoverableIndiaMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [hoveredState, setHoveredState] = useState<string | null>(null);

  // Use effect to add event listeners to SVG paths after component mounts
  useEffect(() => {
    if (!mapRef.current) return;

    const mapElement = mapRef.current;

    // Function to handle mouse movement over the map
    const handleMouseMove = (e: MouseEvent) => {
      const target = e.target as Element;

      // Check if we're hovering over a path element (state)
      if (target.tagName === 'path') {
        const stateName = target.getAttribute('name');
        if (stateName && stateName !== hoveredState) {
          setHoveredState(stateName);
          onHover(stateName, e as unknown as React.MouseEvent);
          console.log("Hovering over state:", stateName);
        }
      }
    };

    // Function to handle mouse leaving the map
    const handleMouseLeave = () => {
      setHoveredState(null);
      onHover(null, null);
    };

    // Add event listeners
    mapElement.addEventListener('mousemove', handleMouseMove);
    mapElement.addEventListener('mouseleave', handleMouseLeave);

    // Add event listeners to all SVG paths
    const observer = new MutationObserver((mutations) => {
      // Once the SVG is loaded, find all paths and add event listeners
      const svgElement = mapElement.querySelector('svg');
      if (svgElement) {
        const paths = svgElement.querySelectorAll('path');
        paths.forEach(path => {
          const stateName = path.getAttribute('name');
          if (stateName) {
            path.addEventListener('mouseenter', () => {
              setHoveredState(stateName);
              onHover(stateName, null);
              console.log("Mouse enter on state:", stateName);
            });

            path.addEventListener('mouseleave', () => {
              setHoveredState(null);
              onHover(null, null);
            });
          }
        });

        // Disconnect once we've processed the SVG
        observer.disconnect();
      }
    });

    // Start observing
    observer.observe(mapElement, { childList: true, subtree: true });

    // Cleanup
    return () => {
      mapElement.removeEventListener('mousemove', handleMouseMove);
      mapElement.removeEventListener('mouseleave', handleMouseLeave);
      observer.disconnect();
    };
  }, []);

  // Direct event handlers for React events
  const handleMouseOver = (e: React.MouseEvent) => {
    const target = e.target as Element;
    if (target.tagName === 'path') {
      const stateName = target.getAttribute('name');
      if (stateName) {
        setHoveredState(stateName);
        onHover(stateName, e);
        console.log("React mouse over on state:", stateName);
      }
    }
  };

  const handleMouseOut = () => {
    setHoveredState(null);
    onHover(null, null);
  };

  return (
    <div
      ref={mapRef}
      className="relative cursor-pointer"
      style={{ width: size, height: size }}
      onMouseOver={handleMouseOver}
      onMouseOut={handleMouseOut}
      onMouseMove={(e) => {
        // Update position on mouse move for tooltip positioning
        if (hoveredState) {
          onHover(hoveredState, e);
        }
      }}
    >
      <India
        type="select-single"
        size={size}
        mapColor="#f8fafc" // Lighter background
        strokeColor="#475569" // Darker stroke for better visibility
        strokeWidth={1} // Slightly thicker stroke
        hoverColor="rgba(0, 153, 143, 0.7)" // Semi-transparent hover color matching the header
        selectColor="rgba(0, 153, 143, 0.8)" // Semi-transparent select color matching the header
        hints={true}
        hintTextColor="#334155"
        hintBackgroundColor="white"
        hintPadding="10px"
        hintBorderRadius={12}
        onSelect={onSelect}
        cityColors={cityColors}
        disableClick={false}
        disableHover={false}
        borderStyle="solid"
        style={{
          filter: 'drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.1))',
          transition: 'all 0.3s ease-in-out',
        }}
      />
    </div>
  );
}
