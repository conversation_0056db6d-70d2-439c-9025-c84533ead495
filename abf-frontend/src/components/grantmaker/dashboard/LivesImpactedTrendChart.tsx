"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardT<PERSON>le, CardDescription } from "@/components/ui/card";
import { motion, AnimatePresence } from "framer-motion";
import { formatCurrency } from "@/lib/utils";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from "recharts";
import { TrendingUp, Users, Calendar } from "lucide-react";

interface LivesImpactedTrendChartProps {
  data: Array<{
    month: string;
    count: number;
  }>;
  totalLives: number;
}

export function LivesImpactedTrendChart({ data, totalLives }: LivesImpactedTrendChartProps) {
  const [animatedTotal, setAnimatedTotal] = useState(0);
  const [hoveredMonth, setHoveredMonth] = useState<string | null>(null);

  // Animation for the total value
  useEffect(() => {
    const interval = setInterval(() => {
      setAnimatedTotal(prev => {
        const next = prev + totalLives / 50;
        return next >= totalLives ? totalLives : next;
      });
    }, 20);

    return () => clearInterval(interval);
  }, [totalLives]);

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-3 shadow-lg rounded-lg border border-teal-100"
          style={{
            boxShadow: '0 10px 25px -5px rgba(0, 153, 143, 0.1), 0 8px 10px -6px rgba(0, 153, 143, 0.1)'
          }}
        >
          <div className="flex items-center gap-2 mb-1">
            <Calendar className="h-4 w-4 text-[#00998F]" />
            <p className="font-semibold text-gray-800">{label}</p>
          </div>
          <div className="flex items-center gap-2 text-[#00998F] font-medium">
            <Users className="h-4 w-4" />
            <p>{payload[0].value.toLocaleString()} lives reached</p>
          </div>
          <div className="flex items-center gap-1 text-gray-500 text-xs mt-1">
            <TrendingUp className="h-3 w-3" />
            <p>{((payload[0].value / totalLives) * 100).toFixed(1)}% of total</p>
          </div>
        </motion.div>
      );
    }
    return null;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="h-full"
    >
      <Card className="bg-white shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border-0 h-full">
        <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
        <CardHeader className="pb-2">
          <div className="flex items-start">
            <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-sm">
              <Users className="h-6 w-6 text-[#00998F]" />
            </div>
            <div>
              <CardTitle className="text-xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                Lives Reached
              </CardTitle>
              <CardDescription className="text-gray-600">
                Monthly trend of lives reached
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-80 relative">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={data}
                margin={{ top: 10, right: 30, left: 0, bottom: 30 }}
                onMouseMove={(e) => {
                  if (e.activeTooltipIndex !== undefined && data[e.activeTooltipIndex]) {
                    setHoveredMonth(data[e.activeTooltipIndex].month);
                  }
                }}
                onMouseLeave={() => setHoveredMonth(null)}
              >
                <defs>
                  <linearGradient id="livesImpactedGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#00998F" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#00998F" stopOpacity={0} />
                  </linearGradient>
                  <filter id="livesImpactedShadow" height="200%">
                    <feDropShadow dx="0" dy="3" stdDeviation="4" floodOpacity="0.15" />
                  </filter>
                </defs>
                <CartesianGrid strokeDasharray="3 3" vertical={false} opacity={0.2} />
                <XAxis
                  dataKey="month"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: '#6b7280', fontSize: 12 }}
                  dy={10}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: '#6b7280', fontSize: 12 }}
                  tickFormatter={(value) => value.toLocaleString()}
                />
                <Tooltip content={<CustomTooltip />} />
                <Area
                  type="monotone"
                  dataKey="count"
                  stroke="#00998F"
                  strokeWidth={3}
                  fillOpacity={1}
                  fill="url(#livesImpactedGradient)"
                  activeDot={{
                    r: 6,
                    fill: "#00998F",
                    stroke: "#ffffff",
                    strokeWidth: 2,
                    style: { filter: 'drop-shadow(0px 2px 3px rgba(0, 153, 143, 0.3))' }
                  }}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>

          {/* Total lives impacted display at bottom of card */}
          <div className="mt-4 pt-4 border-t border-gray-100">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="p-2 bg-teal-50 rounded-lg mr-2">
                  <Users className="h-5 w-5 text-[#00998F]" />
                </div>
                <span className="text-sm font-medium text-gray-700">Total Lives Reached</span>
              </div>
              <div className="flex items-center bg-gradient-to-r from-teal-50 to-emerald-50 px-4 py-2 rounded-lg shadow-sm">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={Math.floor(animatedTotal)}
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -5 }}
                    className="flex items-center"
                  >
                    <span className="text-lg font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                      {Math.floor(animatedTotal).toLocaleString()}
                    </span>
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
