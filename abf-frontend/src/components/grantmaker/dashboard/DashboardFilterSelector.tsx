'use client';

import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { motion } from 'framer-motion';
import { FileText } from 'lucide-react';

interface DashboardFilterSelectorProps {
  selectedQuarter: string;
  setSelectedQuarter: (quarter: string) => void;
  selectedYear: string;
  setSelectedYear: (year: string) => void;
  selectedGrant: string;
  setSelectedGrant: (grant: string) => void;
  // viewMode and setViewMode removed as they're no longer needed in the UI
  viewMode?: 'quarterly' | 'yearly'; // kept as optional for backward compatibility
  setViewMode?: (mode: 'quarterly' | 'yearly') => void; // kept as optional for backward compatibility
  grants: Array<{id: string, name: string}>;
}

export function DashboardFilterSelector({
  selectedQuarter,
  setSelectedQuarter,
  selectedYear,
  setSelectedYear,
  selectedGrant,
  setSelectedGrant,
  // viewMode and setViewMode are not used anymore
  viewMode,
  setViewMode,
  grants = [
    { id: 'all', name: 'All Grants' },
    { id: 'grant-1', name: 'Education Fund 2024' },
    { id: 'grant-2', name: 'Healthcare Initiative' },
    { id: 'grant-3', name: 'Community Development' }
  ]
}: DashboardFilterSelectorProps) {
  // Using end years of financial years (e.g., 2025 represents FY 2024-25)
  const years = ['2025', '2024', '2023'];
  const quarters = ['All', 'Q1 (Apr-Jun)', 'Q2 (Jul-Sep)', 'Q3 (Oct-Dec)', 'Q4 (Jan-Mar)'];

  const getQuarterDateRange = (quarter: string, endYear: string) => {
    // Financial year quarters - endYear is the end of financial year (e.g., 2025 for FY 2024-25)
    const startYear = parseInt(endYear) - 1;

    if (quarter === 'Q1 (Apr-Jun)') return `Apr 1 - Jun 30, ${startYear}`;
    if (quarter === 'Q2 (Jul-Sep)') return `Jul 1 - Sep 30, ${startYear}`;
    if (quarter === 'Q3 (Oct-Dec)') return `Oct 1 - Dec 31, ${startYear}`;
    if (quarter === 'Q4 (Jan-Mar)') {
      return `Jan 1 - Mar 31, ${endYear}`;
    }
    return `FY ${startYear}-${endYear.toString().slice(-2)}`; // Format as FY 2024-25
  };

  return (
    <motion.div
      className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6 mb-6 bg-white p-5 rounded-xl shadow-sm border border-gray-100"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Grant Selector */}
      <motion.div
        className="flex flex-col sm:flex-row items-start sm:items-center gap-3 w-full lg:w-auto"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center gap-2 min-w-[140px]">
          <div className="p-1.5 bg-teal-50 rounded-md">
            <FileText className="h-4 w-4 text-teal-500" />
          </div>
          <span className="text-sm font-medium text-gray-700">Funding Entity</span>
        </div>

        <Select value={selectedGrant} onValueChange={setSelectedGrant}>
          <SelectTrigger className="w-full sm:w-[250px] border-teal-200 focus:ring-teal-500 h-9">
            <SelectValue placeholder="Select grant" />
          </SelectTrigger>
          <SelectContent>
            {grants.map((grant) => (
              <SelectItem key={grant.id} value={grant.id}>
                {grant.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </motion.div>

      {/* Date Selector */}
      <motion.div
        className="flex flex-col w-full lg:w-auto"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <div className="flex flex-wrap items-center gap-3">
            <Select value={selectedQuarter} onValueChange={setSelectedQuarter}>
              <SelectTrigger className="w-[180px] border-teal-200 focus:ring-teal-500 h-9">
                <SelectValue placeholder="Select quarter" />
              </SelectTrigger>
              <SelectContent>
                {quarters.map((quarter) => (
                  <SelectItem key={quarter} value={quarter}>
                    {quarter === 'All' ? 'All Quarters' : quarter}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedYear} onValueChange={setSelectedYear}>
              <SelectTrigger className="w-[120px] border-teal-200 focus:ring-teal-500 h-9">
                <SelectValue placeholder="Select year" />
              </SelectTrigger>
              <SelectContent>
                {years.map((year) => (
                  <SelectItem key={year} value={year}>{year}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="px-3 py-1.5 bg-teal-50 text-teal-700 text-xs rounded-md border border-teal-100 font-medium">
              {selectedQuarter !== 'All'
                ? getQuarterDateRange(selectedQuarter, selectedYear)
                : getQuarterDateRange('All', selectedYear)}
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}
