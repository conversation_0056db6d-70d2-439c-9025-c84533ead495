"use client";

import { SimpleIndiaMap } from "./SimpleIndiaMap";

interface GeoData {
  state: string;
  projects: number;
  livesImpacted: number;
  programTypes: string[];
}

interface GeographicalSpreadMapProps {
  data: GeoData[];
}

export function GeographicalSpreadMap({ data: _ }: GeographicalSpreadMapProps) {
  // SimpleIndiaMap uses its own internal data, so we don't pass the data prop
  return (
    <SimpleIndiaMap />
  );
}
