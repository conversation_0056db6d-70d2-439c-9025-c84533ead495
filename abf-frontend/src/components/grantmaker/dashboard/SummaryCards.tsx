"use client";

import { Card } from "@/components/ui/card";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { IndianRupee, Users, Heart } from "lucide-react";

interface SummaryCardsProps {
  totalAmount: number;
  numberOfGrantees: number;
  activeGrantees: number;
  pendingGrantees: number;
  completedGrantees: number;
  livesImpacted: number; // Required again as we're using the dynamic value
}

export function SummaryCards({
  totalAmount,
  numberOfGrantees,
  activeGrantees = 12,
  pendingGrantees = 0,
  completedGrantees = 3,
  livesImpacted
}: SummaryCardsProps) {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { y: 30, opacity: 0 },
    show: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 80,
        damping: 12,
        mass: 0.8
      }
    }
  };

  // Format large numbers with commas
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-IN').format(num);
  };

  return (
    <motion.div
      className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-10"
      variants={container}
      initial="hidden"
      animate="show"
    >
      {/* Grant Budget Card (Swapped position) */}
      <motion.div variants={item}>
        <Card
          className={cn(
            "bg-gradient-to-br from-white via-white to-purple-50/30 shadow-lg hover:shadow-2xl",
            "transition-all duration-300 rounded-xl overflow-hidden group",
            "transform hover:scale-[1.03] hover:-translate-y-1 border-0 h-[220px]"
          )}
        >
          <div className="p-6 pb-12 relative">
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
            <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-teal-500/10 to-transparent rounded-bl-full"></div>
            <div className="flex items-start mb-4">
              <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-sm group-hover:shadow-md transition-all duration-300">
                <IndianRupee className="h-6 w-6 text-[#00998F] group-hover:text-teal-600 transition-colors" />
              </div>
              <h3 className="text-gray-800 font-semibold text-lg pt-1.5">Grant Corpus</h3>
            </div>
            <div className="mb-4">
              <p className="text-4xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent group-hover:from-teal-700 group-hover:to-emerald-600 transition-all duration-300">₹{(totalAmount * 1.5).toLocaleString()}</p>
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">All-time contributions</span>
            </div>

          </div>
        </Card>
      </motion.div>

      {/* Total Grant Allocated Card (Swapped position) */}
      <motion.div variants={item}>
        <Card
          className={cn(
            "bg-gradient-to-br from-white via-white to-teal-50/30 shadow-lg hover:shadow-2xl",
            "transition-all duration-300 rounded-xl overflow-hidden group",
            "transform hover:scale-[1.03] hover:-translate-y-1 border-0 h-[220px]"
          )}
        >
          <div className="p-6 pb-12 relative">
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
            <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-teal-500/10 to-transparent rounded-bl-full"></div>
            <div className="flex items-start mb-4">
              <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-sm group-hover:shadow-md transition-all duration-300">
                <IndianRupee className="h-6 w-6 text-[#00998F] group-hover:text-teal-600 transition-colors" />
              </div>
              <h3 className="text-gray-800 font-semibold text-lg pt-1.5">Grant Allocated</h3>
            </div>
            <div className="mb-4">
              <p className="text-4xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent group-hover:from-teal-700 group-hover:to-emerald-600 transition-all duration-300">₹{totalAmount.toLocaleString()}</p>
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Across all grants</span>
              <div className="ml-2 flex items-center gap-1">
                <span className="w-2 h-2 rounded-full bg-green-500 animate-pulse"></span>
                <span className="text-green-600 text-xs font-medium">Active</span>
              </div>
            </div>

          </div>
        </Card>
      </motion.div>

      {/* Total Grantees Card */}
      <motion.div variants={item}>
        <Card
          className={cn(
            "bg-gradient-to-br from-white via-white to-teal-50/30 shadow-lg hover:shadow-2xl",
            "transition-all duration-300 rounded-xl overflow-hidden group",
            "transform hover:scale-[1.03] hover:-translate-y-1 border-0 h-[220px]"
          )}
        >
          <div className="p-6 pb-12 relative">
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
            <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-teal-500/10 to-transparent rounded-bl-full"></div>
            <div className="flex items-start mb-4">
              <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-sm group-hover:shadow-md transition-all duration-300">
                <Users className="h-6 w-6 text-[#00998F] group-hover:text-teal-600 transition-colors" />
              </div>
              <h3 className="text-gray-800 font-semibold text-lg pt-1.5">Total Grant Partners</h3>
            </div>
            <div className="mb-4">
              <p className="text-4xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent group-hover:from-teal-700 group-hover:to-emerald-600 transition-all duration-300">{numberOfGrantees}</p>
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Total Number of Grant Partners</span>
            </div>

          </div>
        </Card>
      </motion.div>

      {/* Lives Impacted Card */}
      <motion.div variants={item}>
        <Card
          className={cn(
            "bg-gradient-to-br from-white via-white to-purple-50/30 shadow-lg hover:shadow-2xl",
            "transition-all duration-300 rounded-xl overflow-hidden group",
            "transform hover:scale-[1.03] hover:-translate-y-1 border-0 h-[220px]"
          )}
        >
          <div className="p-6 pb-12 relative">
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
            <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-teal-500/10 to-transparent rounded-bl-full"></div>
            <div className="flex items-start mb-4">
              <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-sm group-hover:shadow-md transition-all duration-300">
                <Heart className="h-6 w-6 text-[#00998F] group-hover:text-teal-600 transition-colors" />
              </div>
              <h3 className="text-gray-800 font-semibold text-lg pt-1.5">Lives Reached</h3>
            </div>
            <div className="mb-4">
              <p className="text-4xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent group-hover:from-teal-700 group-hover:to-emerald-600 transition-all duration-300">{formatNumber(livesImpacted)}</p>
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">People reached through programs</span>
            </div>

          </div>
        </Card>
      </motion.div>
    </motion.div>
  );
}
