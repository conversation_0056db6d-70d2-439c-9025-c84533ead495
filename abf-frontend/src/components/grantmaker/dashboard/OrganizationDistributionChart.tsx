"use client";

import { useState, useC<PERSON>back, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON>hart, Pie, Cell, Sector, ResponsiveContainer, Toolt<PERSON>, Legend } from "recharts";
import { motion, AnimatePresence } from "framer-motion";
import { Pie<PERSON>hart as PieChartIcon, TrendingUp, BarChart3 } from "lucide-react";
import { formatCurrency } from "@/services/grantmaker/grantmaker-service";

interface SectorDistribution {
  name: string;
  value: number;
}

interface OrganizationDistributionChartProps {
  data: SectorDistribution[];
}

export function OrganizationDistributionChart({ data }: OrganizationDistributionChartProps) {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  // State to track if we need to show additional UI elements
  const [animationComplete, setAnimationComplete] = useState(false);
  // Calculate total number of sectors
  const totalSectors = data.length;

  // Modern color palette with our primary color
  const COLORS = [
    '#00998F', // Primary teal
    '#10B981', // Emerald
    '#0EA5E9', // Sky blue
    '#8B5CF6', // Violet
    '#EC4899', // Pink
    '#F59E0B', // Amber
  ];

  useEffect(() => {
    // Set animation complete after a delay
    const timer = setTimeout(() => setAnimationComplete(true), 2000);
    return () => clearTimeout(timer);
  }, []);

  const onPieEnter = useCallback((_: any, index: number) => {
    setActiveIndex(index);
    setHoveredIndex(index);
  }, []);

  const onPieLeave = useCallback(() => {
    setActiveIndex(null);
    setHoveredIndex(null);
  }, []);

  const renderActiveShape = (props: any) => {
    const { cx, cy, innerRadius, outerRadius, startAngle, endAngle, fill, payload, percent } = props;

    return (
      <g>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius + 8}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
          className="drop-shadow-lg"
        />
        <Sector
          cx={cx}
          cy={cy}
          startAngle={startAngle}
          endAngle={endAngle}
          innerRadius={innerRadius - 4}
          outerRadius={innerRadius - 1}
          fill={fill}
        />
        <text
          x={cx}
          y={cy - 10}
          textAnchor="middle"
          fill="#4f46e5"
          className="text-xs font-medium"
        >
          {payload.name}
        </text>
        <text
          x={cx}
          y={cy + 10}
          textAnchor="middle"
          fill="#6366f1"
          className="text-xs font-semibold"
        >
          {`${(percent * 100).toFixed(1)}%`}
        </text>
      </g>
    );
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      const percentage = ((data.value / data.payload.value) * 100).toFixed(1);

      return (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-4 shadow-lg rounded-lg border border-indigo-100"
        >
          <div className="flex items-center gap-2 mb-1">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: data.payload.fill || data.color }}
            ></div>
            <p className="font-semibold text-gray-800">{data.name}</p>
          </div>
          <div className="flex items-center gap-2 text-indigo-600 font-medium">
            <PieChartIcon className="h-4 w-4" />
            <p>{data.value}%</p>
          </div>
          <div className="flex items-center gap-1 text-gray-500 text-sm mt-1">
            <TrendingUp className="h-3 w-3" />
            <p>Sector distribution</p>
          </div>
        </motion.div>
      );
    }
    return null;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ y: -5 }}
      className="h-full"
    >
      <Card className="bg-white shadow-md hover:shadow-xl transition-all duration-500 overflow-hidden border-0 h-full rounded-xl"
        style={{
          background: 'linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(249,250,251,0.95) 100%)',
          boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.05), 0 8px 10px -6px rgba(0, 0, 0, 0.03), 0 -2px 0 0 rgba(0, 153, 143, 0.1) inset',
          transform: 'perspective(1000px) rotateX(0deg)',
        }}>
        <div className="h-2 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400 rounded-t-xl"></div>
        <CardHeader className="pb-3 pt-5">
          <div className="flex items-start">
            <div className="p-3.5 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-md group-hover:shadow-lg transition-all duration-300 border border-teal-50">
              <PieChartIcon className="h-6 w-6 text-[#00998F] group-hover:text-teal-600 transition-colors" />
            </div>
            <div>
              <div className="flex items-center">
                <CardTitle className="text-xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                  Sector wise Contribution
                </CardTitle>

              </div>
              <CardDescription className="text-gray-600 mt-1">
                Grant allocation
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-80 relative">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <defs>
                  <filter id="sectorGlow" height="200%">
                    <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                    <feMerge>
                      <feMergeNode in="coloredBlur"/>
                      <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                  </filter>

                  {/* Add radial gradients for each color */}
                  {COLORS.map((color, index) => {
                    const lighterColor = color.replace(')', ', 0.7)').replace('rgb', 'rgba');
                    return (
                      <radialGradient
                        key={`gradient-${index}`}
                        id={`pieGradient-${index}`}
                        cx="50%"
                        cy="50%"
                        r="50%"
                        fx="50%"
                        fy="50%"
                      >
                        <stop offset="0%" stopColor={color} stopOpacity={1} />
                        <stop offset="100%" stopColor={lighterColor} stopOpacity={0.9} />
                      </radialGradient>
                    );
                  })}
                </defs>

                <Pie
                  activeIndex={activeIndex}
                  activeShape={renderActiveShape}
                  data={data.map((item, index) => ({
                    ...item,
                    fill: `url(#pieGradient-${index % COLORS.length})` // Add fill property for tooltip
                  }))}
                  cx="50%"
                  cy="50%"
                  innerRadius={70}
                  outerRadius={110}
                  paddingAngle={4}
                  dataKey="value"
                  onMouseEnter={onPieEnter}
                  onMouseLeave={onPieLeave}

                  className="drop-shadow-lg"
                  animationBegin={0}
                  animationDuration={1500}
                  isAnimationActive={true}
                  labelLine={false}
                >
                  {data.map((_, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={`url(#pieGradient-${index % COLORS.length})`}
                      stroke="#fff"
                      strokeWidth={2}
                      style={{
                        filter: activeIndex === index ? 'url(#sectorGlow)' : 'none',
                        opacity: hoveredIndex === null || hoveredIndex === index || activeIndex === index ? 1 : 0.7,
                        transition: 'all 0.3s ease-in-out',
                      }}
                    />
                  ))}
                </Pie>

                <Tooltip content={<CustomTooltip />} />

                <Legend
                  layout="horizontal"
                  verticalAlign="bottom"
                  align="center"
                  formatter={(value) => <span className="text-sm font-medium text-gray-700">{value}</span>}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>

          {/* Total distribution display */}
          <div className="mt-4 pt-4 border-t border-gray-100">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="p-2.5 bg-gradient-to-br from-teal-50 to-emerald-50 rounded-lg mr-3 shadow-sm border border-teal-100">
                  <BarChart3 className="h-5 w-5 text-[#00998F]" />
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-700">Total Grant Area</span>
                </div>
              </div>
              <div className="flex items-center bg-gradient-to-r from-teal-50 to-emerald-50 px-5 py-2.5 rounded-lg shadow-sm border border-teal-100">
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 20
                  }}
                  className="flex items-center"
                >
                  <span className="text-xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                    {totalSectors}
                  </span>
                </motion.div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
