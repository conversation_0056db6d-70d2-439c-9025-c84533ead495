"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { motion, AnimatePresence, useAnimation } from "framer-motion";
import { ChevronLeft, ChevronRight, Quote, Heart, Calendar, Building, ArrowRight, Star } from "lucide-react";
import { cn } from "@/lib/utils";

interface ImpactStory {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  organization: string;
  date: string;
}

interface ImpactStoriesCarouselProps {
  stories: ImpactStory[];
}

export function ImpactStoriesCarousel({ stories }: ImpactStoriesCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const autoplayRef = useRef<NodeJS.Timeout | null>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);

  // Format date with consistent server/client rendering to prevent hydration errors
  const formatDate = (dateString: string) => {
    // Using a fixed format approach instead of locale-specific formatting
    // This ensures consistent rendering between server and client
    const date = new Date(dateString);
    const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
    return `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`;
  };

  // Use client-side only rendering for date display
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);


  // Handle next slide with improved animation
  const nextSlide = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentIndex((prevIndex) =>
      prevIndex === stories.length - 1 ? 0 : prevIndex + 1
    );
    setTimeout(() => setIsTransitioning(false), 600);
  };

  // Handle previous slide with improved animation
  const prevSlide = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? stories.length - 1 : prevIndex - 1
    );
    setTimeout(() => setIsTransitioning(false), 600);
  };

  // Handle touch events for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (touchStart - touchEnd > 50) {
      // Swipe left, go to next slide
      nextSlide();
    }

    if (touchStart - touchEnd < -50) {
      // Swipe right, go to previous slide
      prevSlide();
    }
  };

  // Set up autoplay with enhanced pause functionality
  useEffect(() => {
    if (autoplayRef.current) {
      clearInterval(autoplayRef.current);
    }

    if (!isPaused) {
      autoplayRef.current = setInterval(() => {
        nextSlide();
      }, 8000);
    }

    return () => {
      if (autoplayRef.current) {
        clearInterval(autoplayRef.current);
      }
    };
  }, [currentIndex, isPaused]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') prevSlide();
      if (e.key === 'ArrowRight') nextSlide();
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isTransitioning]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="h-full animate-fadeIn"
      suppressHydrationWarning
    >
      <Card className="bg-white shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border-0 h-full rounded-2xl">
        <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
        <CardHeader className="pb-2 pt-4">
          <div className="flex items-start">
            <div className="p-3 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-2xl mr-4 shadow-sm group-hover:shadow-md transition-all duration-300 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-teal-500/10 to-emerald-500/10 animate-pulse"></div>
              <Star className="h-6 w-6 text-[#00998F] group-hover:text-teal-600 transition-colors relative z-10" />
            </div>
            <div>
              <CardTitle className="text-2xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                Impact Stories
              </CardTitle>
              <CardDescription className="text-gray-600 mt-1">
                Transforming lives through our initiatives
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0 relative h-[calc(100%-80px)] min-h-[600px] flex flex-col">
          <div
            className="relative overflow-hidden flex-grow"
            onMouseEnter={() => setIsPaused(true)}
            onMouseLeave={() => setIsPaused(false)}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            {/* Main content */}
            <div className="relative h-full flex flex-col">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentIndex}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                  className="h-full flex flex-col"
                >
                  <div className="flex flex-col flex-grow">
                    {/* Image Section - Enhanced with better animations */}
                    <div className="relative h-96 md:h-[400px] overflow-hidden rounded-xl mx-4 flex-shrink-0">
                      <motion.div
                        initial={{ scale: 1.1, opacity: 0.8 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ duration: 1.8, ease: "easeOut" }}
                        className="absolute inset-0"
                      >
                        <img
                          src={stories[currentIndex].imageUrl}
                          alt={stories[currentIndex].title}
                          className="w-full h-full object-cover transform hover:scale-105 transition-transform duration-3000 rounded-xl shadow-lg"
                        />
                        <div className="absolute inset-0 bg-gradient-to-b from-teal-900/5 via-teal-900/10 to-teal-900/60 animate-gradient rounded-xl"></div>
                      </motion.div>

                      {/* Floating quote icon with enhanced animation */}
                      <motion.div
                        initial={{ opacity: 0, y: -20, rotate: -10 }}
                        animate={{ opacity: 1, y: 0, rotate: 0 }}
                        transition={{ delay: 0.3, duration: 0.6, type: "spring", stiffness: 200 }}
                        className="absolute top-4 right-4 bg-white/90 p-3 rounded-full shadow-xl animate-float ring-2 ring-teal-100"
                      >
                        <Quote className="h-6 w-6 text-[#00998F]" />
                      </motion.div>

                      {/* Organization badge with enhanced styling */}
                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.4, duration: 0.5, type: "spring" }}
                        className="absolute bottom-4 left-4 bg-gradient-to-r from-white/95 to-teal-50/95 px-4 py-2 rounded-full shadow-xl flex items-center gap-2 border border-teal-100 ring-1 ring-white/50"
                      >
                        <Building className="h-4 w-4 text-[#00998F]" />
                        <span className="text-sm font-medium text-gray-800">{stories[currentIndex].organization}</span>
                      </motion.div>

                      {/* Date badge with enhanced styling */}
                      <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5, duration: 0.5, type: "spring" }}
                        className="absolute bottom-4 right-4 bg-gradient-to-r from-white/95 to-teal-50/95 px-4 py-2 rounded-full shadow-xl flex items-center gap-2 border border-teal-100 ring-1 ring-white/50"
                      >
                        <Calendar className="h-4 w-4 text-[#00998F]" />
                        <span className="text-sm font-medium text-gray-800">
                          {isClient ? formatDate(stories[currentIndex].date) : ""}
                        </span>
                      </motion.div>
                    </div>

                    {/* Content Section with enhanced styling and animations */}
                    <div className="px-6 py-4 flex-grow flex flex-col justify-between">
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3, duration: 0.6, type: "spring", stiffness: 100 }}
                        className="flex-grow"
                      >
                        <h3 className="text-2xl font-bold text-gray-800 mb-4 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-500 bg-clip-text text-transparent animate-gradient">
                          {stories[currentIndex].title}
                        </h3>
                        <div className="relative">
                          <div className="absolute top-0 left-0 w-1.5 h-full bg-gradient-to-b from-teal-400 to-emerald-400 rounded-full shadow-lg"></div>
                          <p className="text-gray-600 line-clamp-4 leading-relaxed pl-6 animate-fadeIn text-lg">
                            {stories[currentIndex].description}
                          </p>
                        </div>
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Enhanced Navigation controls */}
            <div className="absolute bottom-4 left-0 right-0 flex justify-between items-center px-6">
              {/* Enhanced Pagination dots with animations */}
              <div className="flex space-x-2">
                {stories.map((_, index) => (
                  <motion.button
                    key={index}
                    onClick={() => {
                      setCurrentIndex(index);
                    }}
                    className={`transition-all duration-300 ${
                      index === currentIndex
                        ? "w-12 h-3 bg-gradient-to-r from-[#00998F] to-emerald-500 shadow-lg"
                        : "w-3 h-3 bg-gray-200 hover:bg-teal-200"
                    } rounded-full`}
                    whileHover={{ scale: 1.2 }}
                    whileTap={{ scale: 0.9 }}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.3 }}
                    aria-label={`Go to slide ${index + 1}`}
                  />
                ))}
              </div>

              {/* Enhanced Navigation buttons with better styling */}
              <div className="flex space-x-3">
                <motion.button
                  onClick={prevSlide}
                  className="p-3 rounded-full bg-gradient-to-r from-teal-50 to-emerald-50 hover:from-teal-100 hover:to-emerald-100 transition-all duration-300 border border-teal-100 shadow-lg ring-1 ring-teal-100"
                  whileHover={{ scale: 1.1, x: -2 }}
                  whileTap={{ scale: 0.95 }}
                  disabled={isTransitioning}
                  aria-label="Previous slide"
                >
                  <ChevronLeft className="h-5 w-5 text-[#00998F]" />
                </motion.button>
                <motion.button
                  onClick={nextSlide}
                  className="p-3 rounded-full bg-gradient-to-r from-teal-50 to-emerald-50 hover:from-teal-100 hover:to-emerald-100 transition-all duration-300 border border-teal-100 shadow-lg ring-1 ring-teal-100"
                  whileHover={{ scale: 1.1, x: 2 }}
                  whileTap={{ scale: 0.95 }}
                  disabled={isTransitioning}
                  aria-label="Next slide"
                >
                  <ChevronRight className="h-5 w-5 text-[#00998F]" />
                </motion.button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
