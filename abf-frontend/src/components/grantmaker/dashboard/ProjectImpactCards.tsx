"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { motion, AnimatePresence } from "framer-motion";
import { Users, BarChart2, Building2, Globe, Heart, BookOpen, HeartPulse, Leaf, PenTool, Target } from "lucide-react";
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip, Legend, RadialBarChart, RadialBar, AreaChart, Area, XAxis, YAxis, CartesianGrid } from "recharts";

// Custom tooltip for the area chart with teal primary color
const AreaChartTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white p-3 border border-teal-100 rounded-lg shadow-md"
        style={{ boxShadow: '0 4px 12px rgba(0, 153, 143, 0.1)' }}
      >
        <p className="text-sm font-semibold text-gray-700 mb-1">{`${label}`}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center gap-2 mt-1">
            <div className="w-2 h-2 rounded-full" style={{ backgroundColor: entry.color }}></div>
            <p className="text-sm text-gray-700">
              <span className="font-medium">{entry.name === "actual" ? "Actual" : "Projected"}: </span>
              <span className="font-semibold">{formatNumber(entry.value)}</span>
            </p>
          </div>
        ))}
      </motion.div>
    );
  }
  return null;
};

interface ProjectImpactCardsProps {
  totalProjects: number;
  livesImpacted: number;
  projectsByType: Array<{ type: string; count: number }>;
}

export function ProjectImpactCards({
  totalProjects,
  livesImpacted,
  projectsByType
}: ProjectImpactCardsProps) {
  // State for animations
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [hoveredSector, setHoveredSector] = useState<string | null>(null);

  // Format large numbers with commas
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-IN').format(num);
  };

  // No longer need animation for lives impacted counter

  // Colors for the charts
  const COLORS = ['#f59e0b', '#10b981', '#3b82f6', '#8b5cf6', '#ec4899'];

  // Get icon for project type
  const getProjectIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'education':
        return <BookOpen className="h-4 w-4 text-blue-500" />;
      case 'healthcare':
        return <HeartPulse className="h-4 w-4 text-red-500" />;
      case 'environment':
        return <Leaf className="h-4 w-4 text-green-500" />;
      case 'arts':
        return <PenTool className="h-4 w-4 text-purple-500" />;
      case 'community':
        return <Users className="h-4 w-4 text-amber-500" />;
      default:
        return <Target className="h-4 w-4 text-gray-500" />;
    }
  };

  // Data for the radial chart
  const radialData = projectsByType.map((item, index) => ({
    name: item.type,
    value: item.count,
    fill: COLORS[index % COLORS.length]
  }));

  // Data for the area chart (simulated impact growth)
  const areaData = [
    { month: 'Jan', value: livesImpacted * 0.5 },
    { month: 'Feb', value: livesImpacted * 0.6 },
    { month: 'Mar', value: livesImpacted * 0.65 },
    { month: 'Apr', value: livesImpacted * 0.7 },
    { month: 'May', value: livesImpacted * 0.75 },
    { month: 'Jun', value: livesImpacted * 0.8 },
    { month: 'Jul', value: livesImpacted * 0.85 },
    { month: 'Aug', value: livesImpacted * 0.9 },
    { month: 'Sep', value: livesImpacted * 0.95 },
    { month: 'Oct', value: livesImpacted },
    { month: 'Nov', value: livesImpacted * 1.05 },
    { month: 'Dec', value: livesImpacted * 1.1 },
  ];

  // Enhanced trend data with projected growth
  const trendData = [
    { month: 'Q1', actual: livesImpacted * 0.6, projected: livesImpacted * 0.55 },
    { month: 'Q2', actual: livesImpacted * 0.75, projected: livesImpacted * 0.7 },
    { month: 'Q3', actual: livesImpacted * 0.9, projected: livesImpacted * 0.85 },
    { month: 'Q4', actual: livesImpacted * 1.1, projected: livesImpacted },
  ];

  // Custom tooltip for the pie chart
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-3 shadow-lg rounded-lg border border-teal-100"
          style={{ boxShadow: '0 4px 12px rgba(0, 153, 143, 0.1)' }}
        >
          <div className="flex items-center gap-2 mb-1">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: payload[0].payload.fill }}
            ></div>
            <p className="font-semibold text-gray-800">{payload[0].name}</p>
          </div>
          <div className="flex items-center gap-2 text-[#00998F] font-medium">
            <Target className="h-3.5 w-3.5" />
            <p>{payload[0].value} Projects</p>
          </div>
          <div className="flex items-center gap-1 text-gray-500 text-xs mt-1">
            <div className="flex items-center">
              {getProjectIcon(payload[0].name)}
              <span className="ml-1">{Math.round((payload[0].value / totalProjects) * 100)}% of total</span>
            </div>
          </div>
        </motion.div>
      );
    }
    return null;
  };

  // Custom tooltip for the area chart
  const AreaChartTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-3 shadow-lg rounded-lg border border-teal-100"
          style={{ boxShadow: '0 4px 12px rgba(0, 153, 143, 0.1)' }}
        >
          <p className="font-semibold text-gray-800">{label}</p>
          <div className="flex items-center gap-1 text-[#00998F] font-medium">
            <Users className="h-3.5 w-3.5" />
            <p>{formatNumber(payload[0].value)} Lives Impacted</p>
          </div>
        </motion.div>
      );
    }
    return null;
  };

  return (
    <div>
      {/* Projects Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        whileHover={{ y: -5 }}
        className="h-full"
      >
        <Card className="bg-white shadow-md hover:shadow-xl transition-all duration-500 overflow-hidden border-0 h-full rounded-xl"
          style={{
            background: 'linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(249,250,251,0.95) 100%)',
            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.05), 0 8px 10px -6px rgba(0, 0, 0, 0.03), 0 -2px 0 0 rgba(0, 153, 143, 0.1) inset',
            transform: 'perspective(1000px) rotateX(0deg)',
          }}>
          <div className="h-2 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400 rounded-t-xl"></div>
          <CardHeader className="pb-3 pt-5">
            <div className="flex items-start">
              <div className="p-3.5 bg-gradient-to-br from-teal-100 to-emerald-50 rounded-xl mr-4 shadow-md group-hover:shadow-lg transition-all duration-300 border border-teal-50">
                <BarChart2 className="h-6 w-6 text-[#00998F] group-hover:text-teal-600 transition-colors" />
              </div>
              <div>
                <div className="flex items-center">
                  <CardTitle className="text-xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">
                    Projects Overview
                  </CardTitle>
                  <div className="ml-3 px-2 py-1 bg-teal-50 rounded-full text-xs font-medium text-teal-700 flex items-center">
                    <span className="w-1.5 h-1.5 rounded-full bg-teal-500 mr-1.5 animate-pulse"></span>
                    {totalProjects} Projects
                  </div>
                </div>
                <CardDescription className="text-gray-600 mt-1">
                  Distribution of active projects
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center">
              <div className="h-64 w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <defs>
                      <filter id="glow" height="200%">
                        <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                        <feMerge>
                          <feMergeNode in="coloredBlur"/>
                          <feMergeNode in="SourceGraphic"/>
                        </feMerge>
                      </filter>

                      {/* Add gradients for each color */}
                      {COLORS.map((color, index) => {
                        const lighterColor = color.replace(')', ', 0.7)').replace('rgb', 'rgba');
                        return (
                          <radialGradient
                            key={`gradient-${index}`}
                            id={`projectGradient-${index}`}
                            cx="50%"
                            cy="50%"
                            r="50%"
                            fx="50%"
                            fy="50%"
                          >
                            <stop offset="0%" stopColor={color} stopOpacity={1} />
                            <stop offset="100%" stopColor={lighterColor} stopOpacity={0.9} />
                          </radialGradient>
                        );
                      })}
                    </defs>

                    <Pie
                      data={projectsByType}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={90}
                      paddingAngle={4}
                      dataKey="count"
                      nameKey="type"
                      onMouseEnter={(_, index) => {
                        setActiveIndex(index);
                        setHoveredSector(projectsByType[index].type);
                      }}
                      onMouseLeave={() => {
                        setActiveIndex(null);
                        setHoveredSector(null);
                      }}
                      animationBegin={0}
                      animationDuration={1500}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      labelLine={{ stroke: '#f59e0b', strokeWidth: 1, strokeOpacity: 0.5 }}
                    >
                      {projectsByType.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={`url(#projectGradient-${index % COLORS.length})`}
                          stroke="#fff"
                          strokeWidth={2}
                          style={{
                            filter: activeIndex === index || hoveredSector === entry.type ? 'url(#glow)' : 'none',
                            opacity: activeIndex === null && hoveredSector === null || activeIndex === index || hoveredSector === entry.type ? 1 : 0.7,
                            transition: 'all 0.3s ease-in-out',
                          }}
                        />
                      ))}
                    </Pie>

                    <Tooltip content={<CustomTooltip />} />
                    <Legend
                      layout="horizontal"
                      verticalAlign="bottom"
                      align="center"
                      formatter={(value) => (
                        <span className="text-sm font-medium text-gray-700">{value}</span>
                      )}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              {/* Stats in center */}
              <div className="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-md mt-4 border border-teal-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="p-2 bg-teal-50 rounded-lg">
                      <Target className="h-5 w-5 text-[#00998F]" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Active Projects</p>
                      <p className="text-xl font-bold bg-gradient-to-r from-[#00998F] to-emerald-500 bg-clip-text text-transparent">{Math.round(totalProjects)}</p>
                    </div>
                  </div>
                  <div className="h-10 w-px bg-gray-200 mx-2"></div>

                </div>
              </div>


            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
