"use client";

import { useState } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Search, Filter, ArrowUpDown, X, ChevronLeft, ChevronRight } from "lucide-react";
import { motion } from "framer-motion";
import { formatCurrency, UnifiedOrganization } from "@/services/grantmaker/grantee-service";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";

// Using UnifiedOrganization interface from grantee-service

interface GranteeTableProps {
  organizations: UnifiedOrganization[];
  onViewDetails: (organizationId: string) => void;
  title?: string;
  description?: string;
}

export function GranteeTable({
  organizations,
  onViewDetails,
  title = "Grantees",
  description = "Click on a grantee to view details"
}: GranteeTableProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState<keyof UnifiedOrganization>("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [sectorFilter, setSectorFilter] = useState<string[]>([]);

  // Get unique sectors
  const sectors = [...new Set(organizations.map(org => org.sector))];

  const handleSort = (field: keyof UnifiedOrganization) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const toggleSectorFilter = (sector: string) => {
    setSectorFilter(prev =>
      prev.includes(sector)
        ? prev.filter(s => s !== sector)
        : [...prev, sector]
    );
  };

  const clearFilters = () => {
    setSectorFilter([]);
    setSearchTerm("");
  };

  const filteredOrganizations = organizations.filter(org => {
    const matchesSearch = org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      org.sector.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSector = sectorFilter.length === 0 || sectorFilter.includes(org.sector);
    return matchesSearch && matchesSector;
  });

  const sortedOrganizations = [...filteredOrganizations].sort((a, b) => {
    if (sortField === "totalBudget" || sortField === "totalDisbursed" ||
        sortField === "remainingBalance") {
      return sortDirection === "asc"
        ? Number(a[sortField]) - Number(b[sortField])
        : Number(b[sortField]) - Number(a[sortField]);
    }

    const aValue = String(a[sortField]).toLowerCase();
    const bValue = String(b[sortField]).toLowerCase();
    return sortDirection === "asc" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
  });

  const isFiltered = sectorFilter.length > 0 || searchTerm !== "";

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="bg-white shadow-md hover:shadow-xl transition-all duration-300 border-0 overflow-hidden">
        <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
        <CardHeader className="pb-2">
          <CardTitle className="text-xl font-bold bg-gradient-to-r from-[#00998F] to-teal-600 bg-clip-text text-transparent">
            {title}
          </CardTitle>
          <CardDescription className="text-gray-600">{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
            <div className="relative w-full md:w-auto">
              <div className="relative group">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-[#00998F] to-teal-500 rounded-lg opacity-0 group-hover:opacity-20 transition-all duration-300"></div>
                <div className="relative flex items-center">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400 group-hover:text-[#00998F] transition-colors duration-200" />
                  <input
                    type="text"
                    placeholder="Search grantees..."
                    className="h-10 w-full md:w-64 rounded-lg border border-gray-200 bg-white pl-10 pr-4 text-sm focus:border-[#00998F] focus:outline-none focus:ring-1 focus:ring-[#00998F] shadow-sm group-hover:shadow transition-all duration-200"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  {searchTerm && (
                    <button
                      className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400 hover:text-[#00998F] transition-colors duration-200"
                      onClick={() => setSearchTerm("")}
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 w-full md:w-auto">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="flex items-center gap-2 border-gray-200 hover:border-teal-200 hover:bg-teal-50 hover:text-[#00998F] transition-all duration-200 shadow-sm hover:shadow"
                  >
                    <Filter className="h-4 w-4" />
                    Filter
                    {isFiltered && (
                      <span className="ml-1 rounded-full bg-teal-100 px-2 py-0.5 text-xs font-medium text-[#00998F]">
                        {sectorFilter.length}
                      </span>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56 p-2 bg-white shadow-xl border border-gray-100 rounded-xl overflow-hidden">
                  <DropdownMenuLabel className="text-gray-800 font-medium">Filter Grantees</DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-gray-100" />
                  <DropdownMenuLabel className="text-xs font-medium text-gray-500 mt-1">Sector</DropdownMenuLabel>
                  {sectors.map(sector => (
                    <DropdownMenuCheckboxItem
                      key={sector}
                      checked={sectorFilter.includes(sector)}
                      onCheckedChange={() => toggleSectorFilter(sector)}
                      className="rounded-md my-0.5 focus:bg-teal-50 focus:text-[#00998F]"
                    >
                      {sector}
                    </DropdownMenuCheckboxItem>
                  ))}

                  <DropdownMenuSeparator className="bg-gray-100 my-2" />
                  <DropdownMenuItem
                    className="justify-center text-[#00998F] focus:text-[#00998F] cursor-pointer rounded-md hover:bg-teal-50"
                    onClick={clearFilters}
                  >
                    Clear Filters
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {isFiltered && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="text-[#00998F] hover:text-teal-700 hover:bg-teal-50 transition-all duration-200"
                >
                  Clear Filters
                </Button>
              )}
            </div>
          </div>

          <div className="overflow-x-auto rounded-lg border border-gray-100 shadow-sm">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gradient-to-r from-gray-50 to-white border-b border-gray-200">
                  <th className="py-3 px-4 text-sm font-medium text-gray-600">
                    <button
                      onClick={() => handleSort("name")}
                      className="flex items-center gap-1 hover:text-[#00998F] transition-colors duration-200"
                    >
                      Grantee
                      <ArrowUpDown className="h-4 w-4" />
                    </button>
                  </th>
                  <th className="py-3 px-4 text-sm font-medium text-gray-600">
                    <button
                      onClick={() => handleSort("sector")}
                      className="flex items-center gap-1 hover:text-[#00998F] transition-colors duration-200"
                    >
                      Sector
                      <ArrowUpDown className="h-4 w-4" />
                    </button>
                  </th>
                  <th className="py-3 px-4 text-sm font-medium text-gray-600">
                    <button
                      onClick={() => handleSort("totalBudget")}
                      className="flex items-center gap-1 hover:text-[#00998F] transition-colors duration-200"
                    >
                      Total Budget
                      <ArrowUpDown className="h-4 w-4" />
                    </button>
                  </th>
                  <th className="py-3 px-4 text-sm font-medium text-gray-600">
                    <button
                      onClick={() => handleSort("totalDisbursed")}
                      className="flex items-center gap-1 hover:text-[#00998F] transition-colors duration-200"
                    >
                      Disbursed
                      <ArrowUpDown className="h-4 w-4" />
                    </button>
                  </th>
                  <th className="py-3 px-4 text-sm font-medium text-gray-600">
                    <button
                      onClick={() => handleSort("remainingBalance")}
                      className="flex items-center gap-1 hover:text-[#00998F] transition-colors duration-200"
                    >
                      Remaining
                      <ArrowUpDown className="h-4 w-4" />
                    </button>
                  </th>
                  <th className="py-3 px-4 text-sm font-medium text-gray-600">Actions</th>
                </tr>
              </thead>
              <tbody>
                {sortedOrganizations.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="py-8 text-center text-gray-500">
                      <div className="flex flex-col items-center justify-center">
                        <Search className="h-8 w-8 text-gray-300 mb-2" />
                        <p>No grantees found matching your search criteria</p>
                        {isFiltered && (
                          <Button
                            variant="link"
                            size="sm"
                            onClick={clearFilters}
                            className="text-[#00998F] mt-2"
                          >
                            Clear filters and try again
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ) : (
                  sortedOrganizations.map((org, index) => (
                    <tr
                      key={org.id}
                      className={`border-b border-gray-100 hover:bg-teal-50/30 cursor-pointer transition-colors duration-150 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}
                      onClick={() => onViewDetails(org.id)}
                    >
                      <td className="py-3 px-4 font-medium text-gray-800">{org.name}</td>
                      <td className="py-3 px-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-[#00998F]">
                          {org.sector}
                        </span>
                      </td>
                      <td className="py-3 px-4 font-medium">{formatCurrency(org.totalBudget)}</td>
                      <td className="py-3 px-4">{formatCurrency(org.totalDisbursed)}</td>
                      <td className="py-3 px-4">
                        <span className={`font-medium ${org.remainingBalance > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {formatCurrency(org.remainingBalance)}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-teal-200 hover:bg-teal-100 hover:text-[#00998F] transition-all duration-200"
                          onClick={(e) => {
                            e.stopPropagation();
                            onViewDetails(org.id);
                          }}
                        >
                          View Details
                        </Button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Table footer with pagination placeholder */}
          {sortedOrganizations.length > 0 && (
            <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
              <div>
                Showing <span className="font-medium">{sortedOrganizations.length}</span> grantees
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" className="h-8 w-8 p-0" disabled>
                  <span className="sr-only">Go to previous page</span>
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm" className="h-8 w-8 p-0 bg-teal-50 border-teal-200">
                  <span className="sr-only">Page 1</span>
                  1
                </Button>
                <Button variant="outline" size="sm" className="h-8 w-8 p-0" disabled>
                  <span className="sr-only">Go to next page</span>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
