"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { updateExpenseStatus } from "@/services/grantmaker/expense-service";

interface Expense {
  id: string;
  status: string;
  remarks?: string;
}

interface ExpenseApprovalProps {
  expense: Expense;
  onStatusChange?: (expenseId: string, status: string) => void;
}

interface ExpenseReviewDialogProps {
  expense: Expense;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onApprove: (expenseId: string) => void;
  onReject: (expenseId: string, comments: string) => void;
}

const ExpenseReviewDialog = ({
  expense,
  open,
  onOpenChange,
  onApprove,
  onReject
}: ExpenseReviewDialogProps) => {
  const [comments, setComments] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleApprove = async () => {
    setIsSubmitting(true);
    try {
      await onApprove(expense.id);
      onOpenChange(false);
    } catch (error) {
      console.error("Error approving expense:", error);
      toast.error("Failed to approve expense");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReject = async () => {
    if (!comments.trim()) {
      toast.error("Please provide comments explaining why the expense is rejected");
      return;
    }

    setIsSubmitting(true);
    try {
      await onReject(expense.id, comments);
      onOpenChange(false);
    } catch (error) {
      console.error("Error rejecting expense:", error);
      toast.error("Failed to reject expense");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Review Expense</DialogTitle>
          <DialogDescription>
            Review and approve or reject the expense with comments.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Expense Status</h4>
            <div className="flex items-center space-x-2">
              {expense.status === "pending" ? (
                <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
              ) : expense.status === "approved" ? (
                <Badge className="bg-green-100 text-green-800">Approved</Badge>
              ) : (
                <Badge className="bg-red-100 text-red-800">Rejected</Badge>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="text-sm font-medium">Comments</h4>
            <Textarea
              placeholder="Provide comments about this expense, especially if rejecting. For rejected expenses, explain what revisions are needed."
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              rows={4}
            />
          </div>
        </div>

        <DialogFooter className="flex justify-between sm:justify-between">
          <Button
            type="button"
            variant="destructive"
            onClick={handleReject}
            disabled={isSubmitting}
          >
            Reject
          </Button>
          <Button
            type="button"
            variant="default"
            className="bg-green-600 hover:bg-green-700"
            onClick={handleApprove}
            disabled={isSubmitting}
          >
            Approve
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export function ExpenseApproval({ expense, onStatusChange }: ExpenseApprovalProps) {
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);

  const handleApproveExpense = async (expenseId: string) => {
    try {
      await updateExpenseStatus(expenseId, 'approved');
      toast.success("Expense approved successfully");
      if (onStatusChange) {
        onStatusChange(expenseId, "approved");
      }
    } catch (error) {
      console.error("Error approving expense:", error);
      toast.error("Failed to approve expense");
    }
  };

  const handleRejectExpense = async (expenseId: string, comments: string) => {
    try {
      await updateExpenseStatus(expenseId, 'rejected', comments);
      toast.success("Expense rejected with comments");
      if (onStatusChange) {
        onStatusChange(expenseId, "rejected");
      }
    } catch (error) {
      console.error("Error rejecting expense:", error);
      toast.error("Failed to reject expense");
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "rejected":
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          {getStatusIcon(expense.status)}
          <span className="font-medium">
            {expense.status === "pending" ? "Pending Review" : 
             expense.status === "approved" ? "Approved" : "Rejected"}
          </span>
        </div>
        <Button
          onClick={() => setIsReviewDialogOpen(true)}
          variant="outline"
          className="text-green-600 border-green-600 hover:bg-green-50"
        >
          Review
        </Button>
      </div>

      <ExpenseReviewDialog
        expense={expense}
        open={isReviewDialogOpen}
        onOpenChange={setIsReviewDialogOpen}
        onApprove={handleApproveExpense}
        onReject={handleRejectExpense}
      />
    </div>
  );
}
