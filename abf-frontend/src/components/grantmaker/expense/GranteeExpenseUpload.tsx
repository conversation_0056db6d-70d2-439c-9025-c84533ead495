"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { Upload, FileSpreadsheet, AlertCircle } from "lucide-react";
import { ExcelDataTable } from "@/app/expense/components/ExcelDataTable";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { motion, AnimatePresence } from "framer-motion";
import { getGrantees } from "@/services/grantmaker/grantee-service";
import { uploadGranteeExpense } from "@/services/grantmaker/expense-service";

interface ExpenseRow {
  sr_no: number;
  particulars: string;
  main_header: string;
  sub_headers: string;
  units?: string;
  frequency?: string;
  cost_per_unit?: number;
  budget_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  actuals_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  total_budget?: number;
  total_actual?: number;
  remarks?: string;
}

interface Grantee {
  id: string;
  name: string;
}

export function GranteeExpenseUpload() {
  const [file, setFile] = useState<File | null>(null);
  const [excelData, setExcelData] = useState<ExpenseRow[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedGrantee, setSelectedGrantee] = useState<string>("");
  const [grantees, setGrantees] = useState<Grantee[]>([]);
  const [validationErrors, setValidationErrors] = useState<{[key: string]: boolean}>({});
  const [remarks, setRemarks] = useState<{[key: string]: string}>({});
  const [showValidationAlert, setShowValidationAlert] = useState(false);

  useEffect(() => {
    const fetchGrantees = async () => {
      try {
        const granteesData = await getGrantees();
        setGrantees(granteesData);
      } catch (error) {
        console.error("Error fetching grantees:", error);
        toast.error("Failed to load grantees. Please try again.");
      }
    };

    fetchGrantees();
  }, []);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Check file type
      const fileType = selectedFile.name.split('.').pop()?.toLowerCase();
      if (fileType !== 'xlsx' && fileType !== 'xls' && fileType !== 'csv') {
        toast.error('Please upload a valid Excel or CSV file');
        return;
      }
      setFile(selectedFile);
      setExcelData([]);
    }
  };

  const handleUpload = async () => {
    if (!file) {
      toast.error('Please select a file to upload');
      return;
    }

    if (!selectedGrantee) {
      toast.error('Please select a grantee');
      return;
    }

    setIsUploading(true);

    try {
      // In a real implementation, we would send the file to the backend for processing
      // For now, we'll simulate processing with mock data
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Mock data for demonstration
      const mockProcessedData: ExpenseRow[] = [
        {
          sr_no: 1,
          particulars: "Program Manager Salary",
          main_header: "Personnel",
          sub_headers: "Staff",
          units: "Months",
          frequency: "12",
          cost_per_unit: 50000,
          budget_quarterly: { Q1: 150000, Q2: 150000, Q3: 150000, Q4: 150000 },
          actuals_quarterly: { Q1: 160000, Q2: 150000, Q3: 150000, Q4: 150000 },
          total_budget: 600000,
          total_actual: 610000
        },
        {
          sr_no: 2,
          particulars: "Office Rent",
          main_header: "Administrative",
          sub_headers: "Rent",
          units: "Months",
          frequency: "12",
          cost_per_unit: 25000,
          budget_quarterly: { Q1: 75000, Q2: 75000, Q3: 75000, Q4: 75000 },
          actuals_quarterly: { Q1: 75000, Q2: 75000, Q3: 75000, Q4: 75000 },
          total_budget: 300000,
          total_actual: 300000
        }
      ];

      // Check for budget validation errors
      const errors: {[key: string]: boolean} = {};
      mockProcessedData.forEach((row, index) => {
        const budgetSum = Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0);
        const actualsSum = Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0);

        if (actualsSum > budgetSum) {
          errors[index.toString()] = true;
        }
      });

      setValidationErrors(errors);
      setExcelData(mockProcessedData);

      if (Object.keys(errors).length > 0) {
        setShowValidationAlert(true);
      }

      toast.success(`Excel file processed successfully`);
    } catch (error) {
      console.error('Error processing Excel file:', error);
      toast.error('Failed to process Excel file. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleExcelInputChange = (index: number, field: string, value: string | number) => {
    const updatedData = [...excelData];

    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      (updatedData[index] as any)[parent][child] = typeof value === 'string' ? parseFloat(value) : value;
    } else {
      (updatedData[index] as any)[field] = value;
    }

    // Recalculate totals
    const row = updatedData[index];
    row.total_budget = Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0);
    row.total_actual = Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0);

    // Check for budget validation errors
    const errors = {...validationErrors};
    if (row.total_actual > row.total_budget) {
      errors[index.toString()] = true;
    } else {
      delete errors[index.toString()];
    }

    setValidationErrors(errors);
    setExcelData(updatedData);

    if (Object.keys(errors).length > 0) {
      setShowValidationAlert(true);
    } else {
      setShowValidationAlert(false);
    }
  };

  const handleSubmit = async () => {

    setIsProcessing(true);

    try {
      // Prepare data for submission
      const dataToSubmit = excelData.map((row, index) => ({
        ...row,
        remarks: validationErrors[index.toString()] ? remarks[index.toString()] : undefined
      }));

      // Call the service to upload the expense data
      await uploadGranteeExpense(selectedGrantee, dataToSubmit, file);

      toast.success(
        <div className="font-semibold">
          <div className="text-xl mb-2">Success! 🎉</div>
          <div>Expense data has been uploaded for the grantee</div>
        </div>,
        {
          duration: 6000,
          position: 'top-center',
          style: {
            background: '#00998F',
            color: '#fff',
            fontSize: '16px',
            padding: '16px',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
            maxWidth: '400px',
            width: '100%'
          }
        }
      );

      // Reset form
      setFile(null);
      setExcelData([]);
      setSelectedGrantee("");
      setValidationErrors({});
      setRemarks({});
      setShowValidationAlert(false);

    } catch (error) {
      console.error('Error submitting expense data:', error);
      toast.error('Failed to submit expense data. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card className="shadow-md border-0 rounded-lg overflow-hidden">
        <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-gray-800">Upload Expense Data for Grantee</CardTitle>
          <CardDescription>Upload an Excel file with expense details for a specific grantee</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="grantee-select">Select Grantee</Label>
              <Select value={selectedGrantee} onValueChange={setSelectedGrantee}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select a grantee" />
                </SelectTrigger>
                <SelectContent>
                  {grantees.map((grantee) => (
                    <SelectItem key={grantee.id} value={grantee.id}>
                      {grantee.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="excel-upload">Upload Excel File</Label>
              <div className="flex items-center gap-4">
                <input
                  id="excel-upload"
                  type="file"
                  accept=".xlsx,.xls,.csv"
                  onChange={handleFileChange}
                  className="hidden"
                />
                <Button
                  disabled={isUploading}
                  onClick={() => document.getElementById("excel-upload")?.click()}
                  className="bg-[#00998F] hover:bg-[#007b73] text-white"
                >
                  Select File
                </Button>
                <span className="text-sm text-gray-500">
                  {file ? file.name : "Only .xlsx, .xls, or .csv files are allowed"}
                </span>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              onClick={handleUpload}
              disabled={!file || !selectedGrantee || isUploading}
              className="bg-[#00998F] hover:bg-[#007b73] text-white"
            >
              {isUploading ? (
                <span className="flex items-center gap-2">
                  <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
                  Processing...
                </span>
              ) : (
                <span className="flex items-center gap-2">
                  <Upload className="h-4 w-4" />
                  Process Excel File
                </span>
              )}
            </Button>
          </div>

          <AnimatePresence>
            {showValidationAlert && (
              <motion.div
                key="validation-alert"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
              >
                <Alert variant="destructive" className="bg-amber-50 border-amber-200 text-amber-800">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Validation Warning</AlertTitle>
                  <AlertDescription>
                    Some expense items have actual expenses that exceed the budget.
                    Please provide remarks for these items to proceed.
                  </AlertDescription>
                </Alert>
              </motion.div>
            )}
          </AnimatePresence>

          {excelData.length > 0 && (
            <div className="space-y-6">
              <Card className="border border-gray-200">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Expense Data</CardTitle>
                </CardHeader>
                <CardContent>
                  <ExcelDataTable
                    data={excelData}
                    onInputChange={handleExcelInputChange}
                    editable={false}
                  />

                  {Object.keys(validationErrors).length > 0 && (
                    <div className="mt-6 space-y-4">
                      <h3 className="text-lg font-medium text-gray-800">Validation Warning</h3>
                      <p className="text-sm text-gray-600">
                        The following items have actual expenses that exceed budget. Grantees will need to provide remarks for these items:
                      </p>

                      <div className="space-y-4">
                        {Object.keys(validationErrors).map((index) => {
                          const rowIndex = parseInt(index);
                          const row = excelData[rowIndex];
                          return (
                            <div key={index} className="p-4 border border-amber-200 rounded-lg bg-amber-50">
                              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-3">
                                <div>
                                  <h4 className="font-medium text-amber-800">
                                    {row.particulars} ({row.main_header} - {row.sub_headers})
                                  </h4>
                                  <p className="text-sm text-amber-700">
                                    Budget: {row.total_budget?.toFixed(2)} |
                                    Actual: {row.total_actual?.toFixed(2)} |
                                    Difference: {(row.total_actual! - row.total_budget!).toFixed(2)}
                                  </p>
                                </div>
                                <div className="flex items-center gap-2">
                                  <AlertCircle className="h-5 w-5 text-amber-500" />
                                  <span className="text-sm font-medium text-amber-700">
                                    Actual exceeds budget
                                  </span>
                                </div>
                              </div>

                              <div>
                                <p className="text-sm text-gray-600 mt-2">
                                  Grantees will be required to provide remarks explaining this discrepancy.
                                </p>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end mt-6">
                    <Button
                      onClick={handleSubmit}
                      disabled={isProcessing || (Object.keys(validationErrors).length > 0 &&
                        Object.keys(validationErrors).some(index => !remarks[index] || remarks[index].trim() === ''))}
                      className="bg-[#00998F] hover:bg-[#007b73] text-white"
                    >
                      {isProcessing ? (
                        <span className="flex items-center gap-2">
                          <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
                          Submitting...
                        </span>
                      ) : (
                        <span className="flex items-center gap-2">
                          <FileSpreadsheet className="h-4 w-4" />
                          Submit Expense Data
                        </span>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
