// src/components/funding/expense/ReadOnlyExpenseTable.tsx

import { Card } from '@/components/ui/card';

interface ReadOnlyExpenseTableProps {
  excelRecords: any[]; // You can replace `any[]` with a stricter type if needed
}

export default function ReadOnlyExpenseTable({ excelRecords }: ReadOnlyExpenseTableProps) {
  return (
    <Card className="rounded-2xl shadow-md bg-white overflow-x-auto">
      <table className="min-w-full text-sm text-left border-collapse">
        <thead className="bg-gray-50 text-gray-700">
          <tr>
            {[
              'Sr.',
              'Main Header',
              'Sub Header',
              'Particulars',
              'No. of Units',
              'Frequency',
              'Cost per Unit',
              'Total Grant Budget',
              'Activity Description',
            ].map((head, idx) => (
              <th key={idx} className="px-4 py-3 font-semibold text-sm">
                {head}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="text-gray-800">
          {excelRecords.map((row, index) => (
            <tr key={row.id} className="hover:bg-gray-50 transition">
              <td className="px-4 py-3 font-semibold">{String(index + 1).padStart(2, '0')}</td>
              <td className="px-4 py-3">{row.particulars || 'N/A'}</td>
              <td className="px-4 py-3">{row.main_header || 'N/A'}</td>
              <td className="px-4 py-3">{row.sub_header || 'N/A'}</td>
              <td className="px-4 py-3">{row.units || 'N/A'}</td>
              <td className="px-4 py-3">{row.frequency || 'N/A'}</td>
              <td className="px-4 py-3">
                {row.cost_per_unit
                  ? `₹${parseFloat(row.cost_per_unit).toFixed(2)}`
                  : 'N/A'}
              </td>
              <td className="px-4 py-3">
                {row.total_grant_budget?.toLocaleString('en-IN', {
                  style: 'currency',
                  currency: 'INR',
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }) || 'N/A'}
              </td>
              <td className="px-4 py-3">{row.activity_description || 'N/A'}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </Card>
  );
}