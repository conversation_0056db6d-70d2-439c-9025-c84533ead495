"use client";

import { useAuth<PERSON>ontext, UserRole, UserType } from "@/contexts/AuthContext";
import { Spinner } from "./Spinner";
import { useRouter } from "next/navigation";
import { LockIcon } from "lucide-react";
import { Button, buttonVariants } from "./ui/button";
import Link from "next/link";
import { cn } from "@/lib/utils";

export function AuthGuard({
  user_roles,
  user_types,
  children,
}: {
  user_roles?: UserRole[];
  user_types?: UserType[];
  children: React.ReactNode;
}) {
  const router = useRouter();
  const { user, isLoading } = useAuthContext();

  if (isLoading) return <Spinner />;

  if (!user) {
    router.push("/login");
    return null;
  }

  const isTypeAllowed = !user_types || user_types.includes(user.type);
  const isRoleAllowed = !user_roles || user_roles.includes(user.role);

  // If both are specified, user must match both
  const hasAccess = isTypeAllowed && isRoleAllowed;

  if (hasAccess) return <>{children}</>;

  return <Unauthorized />;
}

export function Unauthorized() {
  return (
    <div className="flex min-h-[100dvh] flex-col items-center justify-center bg-background px-4 py-12 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-md text-center">
        <LockIcon className="mx-auto h-12 w-12 text-primary" />
        <h1 className="mt-4 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
          Unauthorized Access
        </h1>
        <p className="mt-4 text-muted-foreground">
          You do not have the necessary permissions to access this resource.
          Please contact your administrator for assistance.
        </p>

        <Link href="/" className={cn(buttonVariants(), "mt-8")}>
          Back To Homepage
        </Link>
        {/* <div className="mt-6"> */}
        {/*   <img */}
        {/*     src="/placeholder.svg" */}
        {/*     alt="Unauthorized access illustration" */}
        {/*     className="mx-auto" */}
        {/*     width="300" */}
        {/*     height="300" */}
        {/*     style={{ aspectRatio: "300/300", objectFit: "cover" }} */}
        {/*   /> */}
        {/* </div> */}
      </div>
    </div>
  );
}
