"use client";

export default function TicketFormSkeleton() {
  return (
    <div className="max-w-3xl mx-auto py-12 animate-pulse">
      <div className="bg-white shadow-xl rounded-3xl border border-gray-100 p-10 space-y-8">
        <div className="h-8 w-1/3 bg-gray-200 rounded" />
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <div className="h-3 w-1/4 bg-gray-200 rounded" />
            <div className="h-10 bg-gray-100 rounded" />
          </div>
        ))}
        <div className="h-3 w-1/4 bg-gray-200 rounded" />
        <div className="h-32 bg-gray-100 rounded" />
        <div className="flex justify-end gap-4 pt-4">
          <div className="h-10 w-24 bg-gray-200 rounded-xl" />
          <div className="h-10 w-36 bg-gray-300 rounded-xl" />
        </div>
      </div>
    </div>
  );
}
