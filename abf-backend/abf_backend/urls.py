"""
URL configuration for abf_backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import include, path

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/support/', include('support.urls')),
    path('api/resources/', include('resources.urls')),
    path('api/profiles/', include('profiles.urls')),
    path('api/auth/', include('auth.urls')),
    path('api/funding/', include('funding.urls')),
    path('api/aws/', include('aws_services.urls')),
    path('api/users/', include('users.urls')),
    path('api/milestones/', include('milestones.urls')),
    path('api/v1/', include('grants.urls')),
    path('api/grantmaker/', include('grantmaker.urls')),
    path('api/reports/', include('reports.urls'))
]
