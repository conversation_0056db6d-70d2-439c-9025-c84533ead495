from abf_backend import settings
from django.db import models
from django.core.exceptions import ValidationError
import re

# Create your models here.
class UserRole(models.Model):
    # Can be either ADMIN, VIEWER, GUEST or other
    code = models.CharField(max_length=50, null=False, blank=False, unique=True)
    name = models.CharField(max_length=50, null=False, blank=False, unique=True)
    description = models.CharField(max_length=256, null=False, blank=False)

    @classmethod
    def seed_defaults(cls):
        cls.objects.get_or_create(code="ADMIN", defaults={"name": "Admin"})

    def clean(self):

        if not re.fullmatch(r'[A-Z_]+', self.code):
            raise ValidationError({
                'code': 'Code must only contain uppercase letters and underscore'
            })

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.code} - {self.name}"

class UserType(models.Model):
    # Can be either grantee or grant maker

    code = models.CharField(max_length=50, null=False, blank=False, unique=True) # GRANTEE & GRANT_MAKER
    name = models.CharField(max_length=50, null=False, blank=False, unique=True)
    description = models.CharField(max_length=256, null=False, blank=False)

    @classmethod
    def seed_defaults(cls):
        roles = [
            {"code": "GRANTEE", "name": "Grantee"},
            {"code": "GRANT_MAKER", "name": "Grant Maker"},
        ]
        for role in roles:
            cls.objects.get_or_create(code=role["code"], defaults={"name": role["name"]})

    def clean(self):

        if not re.fullmatch(r'[A-Z_]+', self.code):
            raise ValidationError({
                'code': 'Code must only contain uppercase letters and underscore'
            })

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.code} - {self.name}"

class User(models.Model):
    cognito_sub = models.CharField(max_length=100, null=False, blank=False, unique=True)
    first_name = models.CharField(max_length=50, null=False, blank=False)
    last_name = models.CharField(max_length=50, null=False, blank=False)
    email = models.EmailField(max_length=128, null=False, blank=False, unique=True)
    role = models.ForeignKey(
        UserRole,
        on_delete=models.CASCADE,
        related_name='users_with_role')
    type = models.ForeignKey(
        UserType,
        on_delete=models.CASCADE,
        related_name='users_with_type')
    organization = models.ForeignKey(
        settings.ORGANIZATION_MODEL,
        on_delete=models.CASCADE,
        related_name='users_in_organization',
        null=True,
        blank=True
    )

    @classmethod
    def seed_defaults(cls):
        users = [
            {
                "cognito_sub": "31f39d9a-d0e1-70b1-922d-92c4897327ff",
                "first_name": "XYZ",
                "last_name": "ABC",
                "email": "<EMAIL>",
                "role_code": "ADMIN",
                "type_code": "GRANTEE",
            },
            {
                "cognito_sub": "b103ed5a-e031-70fe-5028-ef20effd460c",
                "first_name": "Sarvesh",
                "last_name": "Atalkar",
                "email": "<EMAIL>",
                "role_code": "ADMIN",
                "type_code": "GRANTEE",
            },
            {
                "cognito_sub": "01f3dd2a-8051-70d8-e478-d9c899f47eba",
                "first_name": "Dhananjay",
                "last_name": "Panage",
                "email": "<EMAIL>",
                "role_code": "ADMIN",
                "type_code": "GRANT_MAKER",
            }
        ]

        for user in users:
            cls.objects.get_or_create(
                cognito_sub=user["cognito_sub"],
                defaults={
                    "first_name": user["first_name"],
                    "last_name": user["last_name"],
                    "email": user["email"],
                    "role": UserRole.objects.get(code=user["role_code"]),
                    "type": UserType.objects.get(code=user["type_code"]),
                }
            )

    def clean(self):
        pass
   #     if self.type.type.lower() == 'grantee' and not self.organization:
   #         raise ValidationError("Grantee users must have an organisation.")
   #     if self.type.type.lower() == 'grant_maker' and self.organization:
   #         raise ValidationError("Grant maker users should not be linked to an organisation.")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.cognito_sub} - {self.type}"
