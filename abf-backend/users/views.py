import jwt
import logging
from django.shortcuts import render
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from users.models import User, UserR<PERSON>, UserType


class UserView(APIView):

    def post(self, request):

        decoded_token = getattr(request, 'decoded_token', None)
        if not decoded_token:
            return Response({
                'status': 'ERROR',
                'message': 'Unauthorized: Token is missing or invalid.'
            }, status=status.HTTP_401_UNAUTHORIZED)

        logging.debug(f'unverified decoded token = {decoded_token}')

        cognito_sub = decoded_token.get('sub')
        email = decoded_token.get('email')

        # Check if the user is a part of any group, user must be a part of a group
        try:
            groups = decoded_token.get('cognito:groups', [])
            if not groups or not isinstance(groups, list) or not isinstance(groups[0], str):
                raise ValueError("User is not part of any valid group.")
            group = groups[0].upper()
        except (<PERSON><PERSON>rror, ValueError, IndexError, AttributeError) as e:
            logging.error(f"Error while fetching group: {e}")
            return Response({
                'status': 'ERROR',
                'message': 'User is not part of any group.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Setting admin role by default
        role = 'ADMIN'  # TODO: For time being setting role as ADMIN, needs to changed later on

        # Confirm if both first name and last name is set in cognito
        first_name = decoded_token.get('name')
        last_name = decoded_token.get('family_name')
        if not first_name or not last_name:
            logging.error('Unable to create user as name or family_name is not set in cognito')
            return Response({
                'status': 'ERROR',
                'message': 'Please set first name and last name for the user.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Confirm if the role is present in the UserRole table
        try:
            role = UserRole.objects.get(code=role)
        except UserRole.DoesNotExist:
            return Response(
                {
                    "status": "ERROR",
                    "message": f"Role '{role}' not found."
                }, status=status.HTTP_400_BAD_REQUEST)

        # Confirm if the group is present in the UserGroup table
        try:
            group = UserType.objects.get(code=group)
        except UserType.DoesNotExist:
            return Response(
                {
                    "status": "ERROR",
                    "message": f"Type '{group}' not found."
                }, status=status.HTTP_400_BAD_REQUEST)

        logging.debug(f"group = {group}, role = {role}")

        user, created = User.objects.get_or_create(
            cognito_sub=cognito_sub,
            defaults={
                'email': email,
                'first_name': first_name,
                'last_name': last_name,
                'type': group,
                'role': role
            }
        )

        return Response({
            'status': 'SUCCESS',
            'message': 'User created successfully'
        }, status=status.HTTP_200_OK)

    def delete(self, request):

        decoded_token = getattr(request, 'decoded_token', None)
        if not decoded_token:
            return Response({
                'status': 'ERROR',
                'message': 'Unauthorized: Token is missing or invalid.'
            }, status=status.HTTP_401_UNAUTHORIZED)

        cognito_sub = decoded_token.get('sub')

        try:
            user = User.objects.get(cognito_sub=cognito_sub)
        except User.DoesNotExist:
            return Response({
                'status': 'ERROR',
                'message': 'User not found, unable to delete the user'
            }, status=status.HTTP_404_NOT_FOUND)

        user.delete()

        return Response({
            'status': 'SUCCESS',
            'message': 'User deleted successfully'
        }, status=status.HTTP_204_NO_CONTENT)

