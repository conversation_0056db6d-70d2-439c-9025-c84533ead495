from rest_framework.permissions import BasePermission
import logging


class IsGrantMaker(BasePermission):
    """
        Allows access only to grant maker users.
    """

    def has_permission(self, request, view):
        decoded_token = getattr(request, 'decoded_token', None)

        if not decoded_token:
            return False

        groups = decoded_token.get('cognito:groups', [])
        if not groups:
            return False

        user_type = groups[0]
        return user_type.lower() == 'grant_maker'


class IsGrantee(BasePermission):
    """
        Allows access only to grantee users.
    """

    def has_permission(self, request, view):
        decoded_token = getattr(request, 'decoded_token', None)

        if not decoded_token:
            return False

        groups = decoded_token.get('cognito:groups', [])
        if not groups:
            return False

        user_type = groups[0]
        return user_type.lower() == 'grantee'



