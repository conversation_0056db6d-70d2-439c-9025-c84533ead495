# Generated by Django 4.2.20 on 2025-06-17 07:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('profiles', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.Char<PERSON>ield(max_length=50, unique=True)),
                ('name', models.Char<PERSON>ield(max_length=50, unique=True)),
                ('description', models.CharField(max_length=256)),
            ],
        ),
        migrations.CreateModel(
            name='UserType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True)),
                ('name', models.Char<PERSON><PERSON>(max_length=50, unique=True)),
                ('description', models.CharField(max_length=256)),
            ],
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cognito_sub', models.CharField(max_length=100, unique=True)),
                ('first_name', models.CharField(max_length=50)),
                ('last_name', models.CharField(max_length=50)),
                ('email', models.EmailField(max_length=128, unique=True)),
                ('organization', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='users_in_organization', to='profiles.organization')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='users_with_role', to='users.userrole')),
                ('type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='users_with_type', to='users.usertype')),
            ],
        ),
    ]
