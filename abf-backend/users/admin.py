from django.contrib import admin
from .models import UserRole, UserType, User

@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'description')
    search_fields = ('code', 'name')

@admin.register(UserType)
class UserTypeAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'description')
    search_fields = ('code', 'name')

@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'last_name', 'role', 'type', 'organization')
    search_fields = ('first_name', 'last_name', 'cognito_sub')
    list_filter = ('role', 'type')
