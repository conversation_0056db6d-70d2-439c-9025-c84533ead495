# aws_services/views.py
import logging
import mimetypes
import uuid

from cffi.model import const_voidp_type
from rest_framework import status
from rest_framework.response import Response
from rest_framework.status import (HTTP_400_BAD_REQUEST, HTTP_401_UNAUTHORIZED,
                                   HTTP_403_FORBIDDEN)
from rest_framework.views import APIView

from abf_backend import settings
from common.decorators import handle_org_exceptions
from common.exceptions import UserNotRegistered, UserOrganizationNotFound
from common.mixins.org_aware import OrgAwareMixin
from common.mixins.s3 import S3PresignedURLMixin

mimetypes.add_type("video/x-matroska", ".mkv")
mimetypes.add_type("application/octet-stream", ".db")
mimetypes.add_type("application/sql", ".sql")
mimetypes.add_type("application/x-sqlite3", ".sqlite")

class GeneratePresignedURL(S3PresignedURLMixin, APIView):
    def post(self, request):
        file_name = request.data.get('file_name')
        file_type = request.data.get('file_type')

        if not file_type:
            file_type, _ = mimetypes.guess_type(file_name)

        if not file_name or not file_type:
            return Response({'error': 'Missing file_name or file_type'}, status=status.HTTP_400_BAD_REQUEST)

        bucket = settings.S3_BUCKET_NAME
        unique_key = uuid.uuid4()
        key = f"uploads/{unique_key}_{file_name}"
        try:
            url = self.generate_presigned_url(
                s3_key=key,
                method="put_object",
                expires_in=3600,
                content_type=file_type
            )
        except RuntimeError as e:
            # Handle runtime errors thrown by generate_presigned_url
            logging.error(f"Error generating S3 presigned URL: {str(e)}")
            return Response(
                {
                    "status": "ERROR",
                    "message": str(e),
                    "data": None
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            # Catch any other unexpected issues
            logging.error(f"Unexpected error while generating S3 presigned URL: {e}")
            return Response(
                {
                    "status": "ERROR",
                    "message": "Something went wrong while generating the presigned URL.",
                    "data": None
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        return Response({
            "status": "SUCCESS",
            "message": "Presigned URL generated successfully",
            "data": {
                'upload_url': url,
                'file_url': f'https://{bucket}.s3.amazonaws.com/{key}',
                "object_key": key,
                'file_type': file_type
            }
        })


class GenerateProfileLogoPresignedURL(S3PresignedURLMixin, OrgAwareMixin, APIView):
    @handle_org_exceptions
    def post(self, request):
        user_org = self.get_user_org(request)
        organization_id = user_org.id

        file_name = request.data.get('file_name')
        file_type = request.data.get('file_type')

        if not file_name or not file_type:
            return Response({'error': 'Missing file_name or file_type'}, status=status.HTTP_400_BAD_REQUEST)

        bucket = settings.S3_PROFILE_BUCKET
        print(f"bucket = {bucket}")

        key = f"logos/org_{organization_id}_logo.png"
        print(f"file type = {file_type}")
        try:
            url = self.generate_presigned_url(
                s3_key=key,
                method="put_object",
                expires_in=3600,
                content_type=file_type,
                bucket=bucket
            )
            print(f"Url = {url}")
        except RuntimeError as e:
            logging.error(f"Error generating S3 presigned URL for profile logo: {str(e)}")
            return Response(
                {
                    "status": "ERROR",
                    "message": str(e),
                    "data": None
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            logging.error(f"Unexpected error while generating profile logo presigned URL: {e}")
            return Response(
                {
                    "status": "ERROR",
                    "message": "Something went wrong while generating the profile logo presigned URL.",
                    "data": None
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        return Response({
            "status": "SUCCESS",
            "message": "Profile logo presigned URL generated successfully",
            "data": {
                'upload_url': url,
                'file_url': f'https://{bucket}.s3.amazonaws.com/{key}',
                "object_key": key
            }
        })
