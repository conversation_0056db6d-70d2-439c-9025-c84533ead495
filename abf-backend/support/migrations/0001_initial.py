# Generated by Django 4.2.20 on 2025-06-17 07:33

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import phonenumber_field.modelfields
import simple_history.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('users', '0001_initial'),
        ('grants', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Ticket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('point_of_contact_name', models.CharField(max_length=255)),
                ('category', models.CharField(choices=[('financial', 'Financial'), ('reporting', 'Reporting'), ('documents', 'Documents'), ('technical support', 'Technical Support'), ('applications', 'Applications'), ('account management', 'Account Management')], default='general', max_length=100)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='low', max_length=50)),
                ('title', models.CharField(default='', max_length=255)),
                ('description', models.TextField()),
                ('email', models.EmailField(max_length=254)),
                ('phone', phonenumber_field.modelfields.PhoneNumberField(max_length=128, region=None)),
                ('file', models.FileField(blank=True, null=True, upload_to='support-ticket-attachment/')),
                ('status', models.CharField(choices=[('open', 'Open'), ('under review', 'Under Review'), ('resolved', 'Resolved')], default='open', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('grant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grants', to='grants.grant')),
            ],
        ),
        migrations.CreateModel(
            name='TicketChatHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('update_text', models.TextField(blank=True)),
                ('file', models.FileField(blank=True, null=True, upload_to='support-update-attachment/')),
                ('updated_at', models.DateTimeField(auto_now_add=True)),
                ('ticket', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='updates', to='support.ticket')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ticket_updates', to='users.user')),
            ],
        ),
        migrations.CreateModel(
            name='HistoricalTicket',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('point_of_contact_name', models.CharField(max_length=255)),
                ('category', models.CharField(choices=[('financial', 'Financial'), ('reporting', 'Reporting'), ('documents', 'Documents'), ('technical support', 'Technical Support'), ('applications', 'Applications'), ('account management', 'Account Management')], default='general', max_length=100)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='low', max_length=50)),
                ('title', models.CharField(default='', max_length=255)),
                ('description', models.TextField()),
                ('email', models.EmailField(max_length=254)),
                ('phone', phonenumber_field.modelfields.PhoneNumberField(max_length=128, region=None)),
                ('file', models.TextField(blank=True, max_length=100, null=True)),
                ('status', models.CharField(choices=[('open', 'Open'), ('under review', 'Under Review'), ('resolved', 'Resolved')], default='open', max_length=50)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('grant', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='grants.grant')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical ticket',
                'verbose_name_plural': 'historical tickets',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
