from django.urls import path
from .views import TicketListView, TicketChatHistoryViewSet, TicketDetailView, TicketCreateView,TicketStatusUpdateViewSet,GrantListAPIView

urlpatterns = [
    path('v1/tickets/', TicketListView.as_view(), name='view-all-tickets'),
    path('v1/tickets/create/', TicketCreateView.as_view(), name='create-ticket'),
    path('v1/tickets/<int:pk>/', TicketDetailView.as_view(), name='view-detail-ticket'),
    path(
        'v1/tickets/<int:pk>/status/',
        TicketStatusUpdateViewSet.as_view({
            'patch': 'partial_update',
            'put': 'update',
        }),
        name='ticket-status-update'
    ),
    path('v1/ticket-updates/', TicketChatHistoryViewSet.as_view({'get': 'list', 'post': 'create'}), name='ticketupdate-list'),
    path('v1/grants-list/', GrantListAPIView.as_view(), name='grant-list'),

]
            
