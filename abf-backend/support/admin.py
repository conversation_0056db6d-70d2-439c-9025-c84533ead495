# from django.contrib import admin
# from simple_history.admin import SimpleHistoryAdmin
# from .models import Ticket, TicketChatHistory

# @admin.register(Ticket)
# class TicketAdmin(SimpleHistoryAdmin):
#     list_display = ("id", "title", "grant", "status", "priority", "created_at", "updated_at")
#     list_filter = ("status", "priority", "category", "grant")
#     search_fields = ("title", "description", "grant__grant_name", "email")
#     readonly_fields = ("created_at", "updated_at")
#     ordering = ("-created_at",)

# @admin.register(TicketChatHistory)
# class TicketChatHistoryAdmin(SimpleHistoryAdmin):
#     list_display = ("id", "ticket", "user", "updated_at")
#     search_fields = ("ticket__title", "user__email", "update_text")
#     readonly_fields = ("updated_at",)
#     ordering = ("-updated_at",)

