from rest_framework import serializers
from .models import Ticket, TicketChatHistory, Organization
from rest_framework import serializers
from .models import TicketChatHistory
from .utils import generate_presigned_url
from rest_framework import serializers
from .models import Ticket
from users.models import UserType,User
import logging
from rest_framework import serializers
from support.models import Ticket
from grants.models import Grant
from rest_framework import serializers
from .models import Ticket
from .utils import generate_presigned_url 

logger = logging.getLogger(__name__)

class TicketSerializer(serializers.ModelSerializer):
    grant_name = serializers.StringRelatedField(source="grant", read_only=True)
    grant = serializers.PrimaryKeyRelatedField(queryset=Grant.objects.all())
    attachments = serializers.SerializerMethodField()
    status_history = serializers.SerializerMethodField()
    extra_kwargs = {
        "file": {"required": False},
        "grant": {"required": False, "allow_null": True},
    }
    class Meta:
        model = Ticket
        fields = '__all__'
        extra_kwargs = {
            'grant': {'help_text': 'The grant that raised the ticket.'},
            'category': {'help_text': 'The category of the ticket, e.g., Financial, Billing, etc.'},
            'priority': {'help_text': 'Priority level of the ticket (Low, Medium, High, Urgent).'},
            'title': {'help_text': 'The title or subject of the ticket.'},
            'description': {'help_text': 'A detailed description of the issue.'},
            'email': {'help_text': 'The email address of the person raising the ticket.'},
            'phone': {'help_text': 'Phone number of the person raising the ticket.'},
            'status': {'help_text': 'Current status of the ticket (Open, Pending, Resolved, Resolved).'},
            'created_at': {'help_text': 'The timestamp when the ticket was created.'},
        }
        read_only_fields = ['grant_name', 'created_at', 'updated_at']

    def get_attachments(self, obj):
        try:
            if obj.file:
                url = generate_presigned_url(obj.file.name)
                return [url] if url else None
            return None
        except Exception as e:
            return None

    def get_status_history(self, obj):
        history = obj.history.order_by("history_date")
        status_changes = []

        for i in range(1, len(history)):
            prev = history[i - 1]
            curr = history[i]

            if prev.status != curr.status:
                status_changes.append({
                    "from_status": prev.status,
                    "to_status": curr.status,
                    "changed_at": curr.history_date,
                    "changed_by": {
                        "first_name": getattr(curr.history_user, 'first_name', ''),
                        "last_name": getattr(curr.history_user, 'last_name', ''),
                        "email": getattr(curr.history_user, 'email', ''),
                    },
                })

        return status_changes


class UserTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserType
        fields = ["code", "name"]

class UserShortSerializer(serializers.ModelSerializer):
    type = UserTypeSerializer()  # nested

    class Meta:
        model = User
        fields = ["id", "email", "type", 'first_name','last_name']

class TicketChatHistorySerializer(serializers.ModelSerializer):
    attachments = serializers.SerializerMethodField()
    file = serializers.FileField(write_only=True, required=False)
    user = UserShortSerializer(read_only=True)

    class Meta:
        model = TicketChatHistory
        fields = ['id', 'ticket', 'update_text', 'updated_at', 'file', 'attachments', 'user']
        extra_kwargs = {
            'ticket': {'help_text': 'The ID of the ticket being updated.'},
            'update_text': {'help_text': 'The message or update text related to the ticket.'},
            'updated_at': {'help_text': 'Timestamp when this update was created.'},
        }

    def validate(self, attrs):
        request = self.context.get("request")
        user = request.user if request else None
        ticket = attrs.get('ticket') or getattr(self.instance, 'ticket', None)

        update_text = attrs.get("update_text")
        file = attrs.get("file")

        # Must have text or file
        if not update_text and not file:
            raise serializers.ValidationError("Either update text or file must be provided.")

        if ticket:
            if ticket.status.lower() == "resolved":
                if update_text:
                    raise serializers.ValidationError("Cannot add text message to a resolved ticket.")
                if file:
                    raise serializers.ValidationError("Cannot upload file to a resolved ticket.")

            if (
                ticket.status.lower() == "open"
                and hasattr(user, "type")
                and getattr(user.type, "code", "").upper() == "GRANT_MAKER"
            ):
                ticket.status = "under review"
                ticket.save(update_fields=["status"])

        return attrs

    def get_attachments(self, obj):
        try:
            if obj.file:
                from .utils import generate_presigned_url
                url = generate_presigned_url(obj.file.name)
                return [url] if url else None
            return None
        except Exception as e:
            logger.exception("Error generating attachment URL:")
            return None

class TicketStatusUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Ticket
        fields = ['status']

    def validate_status(self, value):
        valid_choices = [choice[0] for choice in Ticket.STATUS_CHOICES]
        if value not in valid_choices:
            raise serializers.ValidationError("Invalid status value.")
        return value

    def validate(self, attrs):
        ticket = self.instance
        new_status = attrs.get("status")

        if ticket and ticket.status == "resolved" and new_status == "open":
            attrs["status"] = "under review"
        return attrs

class TicketStatusChangeSerializer(serializers.Serializer):
    from_status = serializers.CharField()
    to_status = serializers.CharField()
    changed_at = serializers.DateTimeField()
    changed_by = UserShortSerializer(allow_null=True)

class GrantSerializer(serializers.ModelSerializer):
    user_count = serializers.SerializerMethodField()

    class Meta:
        model = Grant
        fields = [
            'id', 'grant_name', 'start_date', 'end_date', 'grant_purpose',
            'annual_budget', 'funding_sources', 'organization',
            'grant_maker_organization', 'created_at', 'updated_at',
            'user_count'
        ]

    def get_user_count(self, obj):
        return User.objects.filter(organization=obj.organization).count()