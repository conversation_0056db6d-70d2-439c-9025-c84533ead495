from django.db import models
from phonenumber_field.modelfields import PhoneNumberField
from profiles.models import Organization
from django.core.exceptions import ValidationError
from users.models import User
from simple_history.models import HistoricalRecords
from grants.models import Grant
from .utils import generate_presigned_url

class Ticket(models.Model):

    STATUS_CHOICES = (
        ('open', 'Open'),
        ('under review', 'Under Review'),
        ('resolved', 'Resolved'),
    )
    CATEGORY_CHOICES = (
        ('financial', 'Financial'),
        ('reporting', 'Reporting'),
        ('documents', 'Documents'),
        ('technical support', 'Technical Support'),
        ('applications', 'Applications'),
        ('account management', 'Account Management'),
    )
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    grant = models.ForeignKey(
        Grant,
        on_delete=models.CASCADE,
        related_name='grants',
    )
    point_of_contact_name = models.CharField(max_length=255, null=False, blank=False)  # Added
    category = models.CharField(max_length=100, choices=CATEGORY_CHOICES, default='general')
    priority = models.CharField(max_length=50, choices=PRIORITY_CHOICES, default='low')
    title = models.CharField(max_length=255, default='')
    description = models.TextField()
    email = models.EmailField()
    phone = PhoneNumberField()
    file = models.FileField(upload_to='support-ticket-attachment/', blank=True, null=True)
    status = models.CharField(max_length=50, choices=STATUS_CHOICES, default='open')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    history = HistoricalRecords()

    def __str__(self):
        return f"Ticket #{self.id} - {self.title}"

    def clean(self):
        if self.status == 'resolved' and not self.description:
            raise ValidationError("Cannot resolve a ticket without a description.")

    def __str__(self):
        return f"{self.title} ({self.status})"


class TicketChatHistory(models.Model):
    ticket = models.ForeignKey(
        Ticket,
        on_delete=models.CASCADE,
        related_name='updates'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='ticket_updates',
    )
    update_text = models.TextField(blank=True)
    file = models.FileField(upload_to='support-update-attachment/', blank=True, null=True)
    updated_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        if self.user and hasattr(self.user, 'type') and self.user.type.code == 'GRANT_MAKER':
            if self.ticket.status != 'under review':
                self.ticket.status = 'under review'
                self.ticket.save(update_fields=["status"])
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Update #{self.id} for Ticket #{self.ticket.id} by {self.user.email if self.user else 'Unknown User'}"
    

