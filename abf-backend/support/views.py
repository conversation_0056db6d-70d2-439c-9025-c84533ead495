import logging
from django.shortcuts import get_object_or_404
from rest_framework import generics, status, viewsets
from rest_framework.response import Response
from rest_framework.exceptions import NotAuthenticated, PermissionDenied, ValidationError
from .models import Ticket, TicketChatHistory, Grant
from .serializers import (
    TicketSerializer,
    TicketStatusUpdateSerializer,
    TicketChatHistorySerializer,
    GrantSerializer,
)
from users.models import User

logger = logging.getLogger(__name__)

# Utility for user token decoding
def get_authenticated_user(request):
    decoded_token = getattr(request, 'decoded_token', None)
    if not decoded_token:
        raise NotAuthenticated("Token is missing or invalid.")
    try:
        return User.objects.select_related('organization').get(cognito_sub=decoded_token.get('sub'))
    except User.DoesNotExist:
        raise PermissionDenied("User not found or unauthorized.")
    except Exception as e:
        logger.error(f"Unexpected error during token decoding: {str(e)}")
        raise PermissionDenied("Authentication failed.")

        
# ----------------------------- Ticket Creation -----------------------------
class TicketCreateView(generics.CreateAPIView):
    serializer_class = TicketSerializer

    def post(self, request, *args, **kwargs):
        try:
            user = get_authenticated_user(request)
            if not user.organization:
                return Response({'detail': 'User is not assigned to any organization'}, status=status.HTTP_400_BAD_REQUEST)

            data = {
                "title": request.data.get("title"),
                "description": request.data.get("description"),
                "category": request.data.get("category"),
                "priority": request.data.get("priority"),
                "email": request.data.get("email"),
                "phone": request.data.get("phone"),
                "status": request.data.get("status", "open"),
                "organization": user.organization.id,
                "grant": request.data.get("grant"),
                "point_of_contact_name" : request.data.get('point_of_contact_name')
            }

            if Ticket.objects.filter(
                title=data["title"],
                description=data["description"],
                category=data["category"],
                email=data["email"],
                grant_id=data["grant"],
                point_of_contact_name = data['point_of_contact_name']
            ).exists():
                return Response({"detail": "A similar ticket already exists."}, status=status.HTTP_409_CONFLICT)

            serializer = self.get_serializer(data=data)
            serializer.is_valid(raise_exception=True)
            serializer.save(file=request.FILES.get("file"))
            return Response({"detail": "Ticket created successfully", "data": serializer.data}, status=status.HTTP_201_CREATED)

        except PermissionDenied as e:
            logger.warning(f"Permission denied: {str(e)}")
            return Response({"detail": str(e)}, status=status.HTTP_403_FORBIDDEN)
        
        except ValidationError as e:
            logger.warning(f"Validation error: {e}")
            return Response({"detail": "Validation failed", "errors": e.detail}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Ticket creation error: {e}")
            return Response({"detail": "Internal server error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# ----------------------------- Ticket List -----------------------------
class TicketListView(generics.ListAPIView):
    serializer_class = TicketSerializer

    def get_queryset(self):
        try:
            
            user = get_authenticated_user(self.request)
            if user.type.code == 'GRANT_MAKER':
                grant_ids = Grant.objects.filter(grant_maker_organization_id=user.organization.id).values_list('id', flat=True)
            elif user.type.code == 'GRANTEE':
                grant_ids = Grant.objects.filter(organization_id=user.organization.id).values_list('id', flat=True)

            return Ticket.objects.filter(grant_id__in=grant_ids).order_by("-created_at")
        except Exception as e:
            logger.error(f"TicketListView error: {str(e)}")
            return Ticket.objects.none()


# ----------------------------- Ticket Detail -----------------------------
class TicketDetailView(generics.RetrieveAPIView):
    serializer_class = TicketSerializer
    lookup_field = "pk"

    def get_object(self):
        try:
            user = get_authenticated_user(self.request)
            ticket = get_object_or_404(Ticket, id=self.kwargs["pk"])

            # Determine allowed grant IDs based on user type
            if user.type.code == 'GRANT_MAKER':
                allowed_grant_ids = Grant.objects.filter(
                    grant_maker_organization_id=user.organization.id
                ).values_list('id', flat=True)
            elif user.type.code == 'GRANTEE':
                allowed_grant_ids = Grant.objects.filter(
                    organization_id=user.organization.id
                ).values_list('id', flat=True)
            else:
                raise PermissionDenied("User type not authorized to view this ticket")

            if ticket.grant_id not in allowed_grant_ids:
                raise PermissionDenied("Access denied to this ticket")

            return ticket
        except PermissionDenied:
            raise
        except Exception as e:
            logger.error(f"Ticket detail retrieval failed: {e}")
            raise ValidationError("Could not retrieve ticket.")


# ----------------------------- Ticket Chat History -----------------------------
class TicketChatHistoryViewSet(viewsets.ModelViewSet):
    serializer_class = TicketChatHistorySerializer
    filterset_fields = ['ticket']
    ordering_fields = ['updated_at']

    def get_queryset(self):
        try:
            user = get_authenticated_user(self.request)
            ticket_id = self.request.query_params.get('ticket')
            if not ticket_id:
                raise PermissionDenied("Ticket ID is required.")

            # Get allowed grants based on user type
            if user.type.code == 'GRANT_MAKER':
                allowed_grants = Grant.objects.filter(
                    grant_maker_organization_id=user.organization.id
                ).values_list('id', flat=True)
            elif user.type.code == 'GRANTEE':
                allowed_grants = Grant.objects.filter(
                    organization_id=user.organization.id
                ).values_list('id', flat=True)
            else:
                raise PermissionDenied("Unauthorized user type.")

            # Check ticket access
            ticket = get_object_or_404(Ticket, id=ticket_id, grant_id__in=allowed_grants)
            return TicketChatHistory.objects.filter(ticket=ticket).order_by("updated_at")

        except PermissionDenied:
            raise
        except Exception as e:
            logger.error(f"Chat history fetch error: {e}")
            return TicketChatHistory.objects.none()

    def create(self, request, *args, **kwargs):
        try:
            user = get_authenticated_user(request)
            ticket_id = request.data.get("ticket")
            if not ticket_id:
                return Response({'detail': 'Ticket ID is required'}, status=status.HTTP_400_BAD_REQUEST)

            # Get allowed grants based on user type
            if user.type.code == 'GRANT_MAKER':
                allowed_grants = Grant.objects.filter(
                    grant_maker_organization_id=user.organization.id
                ).values_list('id', flat=True)
            elif user.type.code == 'GRANTEE':
                allowed_grants = Grant.objects.filter(
                    organization_id=user.organization.id
                ).values_list('id', flat=True)
            else:
                return Response({'detail': 'Unauthorized user type'}, status=status.HTTP_403_FORBIDDEN)

            # Validate ticket access
            ticket = get_object_or_404(Ticket, id=ticket_id, grant_id__in=allowed_grants)

            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            serializer.save(
                ticket=ticket,
                user=user,
                file=request.FILES.get('file')
            )
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except PermissionDenied as e:
            logger.warning(f"Permission denied: {str(e)}")
            return Response({"detail": str(e)}, status=status.HTTP_403_FORBIDDEN)
        except ValidationError as e:
            return Response({"detail": "Validation failed", "errors": e.detail}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Chat creation error: {e}")
            return Response({"detail": "Internal server error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



# ----------------------------- Ticket Status Update -----------------------------
class TicketStatusUpdateViewSet(viewsets.ModelViewSet):
    serializer_class = TicketStatusUpdateSerializer
    http_method_names = ['patch']

    def get_queryset(self):
        try:
            user = get_authenticated_user(self.request)

            # Determine allowed grants based on user type
            if user.type.code == 'GRANT_MAKER':
                allowed_grants = Grant.objects.filter(
                    grant_maker_organization_id=user.organization.id
                ).values_list('id', flat=True)
            elif user.type.code == 'GRANTEE':
                allowed_grants = Grant.objects.filter(
                    organization_id=user.organization.id
                ).values_list('id', flat=True)
            else:
                raise PermissionDenied("Unauthorized user type.")

            return Ticket.objects.filter(grant_id__in=allowed_grants)
        except PermissionDenied:
            logger.warning("Permission denied")
            raise
        except Exception as e:
            logger.error(f"Status update fetch failed: {e}")
            return Ticket.objects.none()



# ----------------------------- Grant List -----------------------------
class GrantListAPIView(generics.ListAPIView):
    serializer_class = GrantSerializer

    def get_queryset(self):
        try:
            user = get_authenticated_user(self.request)

            if user.type.code == 'GRANT_MAKER':
                return Grant.objects.filter(grant_maker_organization_id=user.organization.id)
            elif user.type.code == 'GRANTEE':
                return Grant.objects.filter(organization_id=user.organization.id)
            else:
                raise PermissionDenied("Unauthorized user type.")

        except PermissionDenied:
            logger.warning("Permission denied")
            return Grant.objects.none()
        except Exception as e:
            logger.error(f"Grant list fetch failed: {e}")
            return Grant.objects.none()
