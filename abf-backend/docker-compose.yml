services:
  backend:
    build: .
    container_name: abf-backend-container
    ports:
      - "8000:8000"
    env_file:
      - .env

    volumes:
      - .:/app
      - ./logs:/app/logs
    depends_on:
      - db
    networks:
      - abf-network
    command: >
        sh -c "python manage.py makemigrations --noinput &&
          python manage.py migrate &&
          python manage.py runserver 0.0.0.0:8000"
  db:
    image: postgres:17
    container_name: postgres-db
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: 123
      POSTGRES_DB: abf
    ports:
      - "5432:5432"
    volumes:
      - pg_data:/var/lib/postgresql/data

    networks:
      - abf-network

  pgadmin:
    image: dpage/pgadmin4
    container_name: my_pgadmin
    restart: always
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "5050:80"
    depends_on:
      - db
    networks:
      - abf-network
volumes:
  pg_data:

networks:
  abf-network:
