from grants.models import Grant, OrganizationGrantHistory
from rest_framework import serializers


class GrantSerializer(serializers.ModelSerializer):
    recipient_organization_name = serializers.CharField(source='organization.organization_name', read_only=True)

    class Meta:
        model = Grant
        fields = [
            'id', 'grant_name', 'start_date', 'end_date', 'grant_purpose',
            'annual_budget', 'funding_sources', 'organization',
            'grant_maker_organization', 'created_at', 'updated_at',
            'recipient_organization_name',
        ]

class OrganizationGrantHistorySerializer(serializers.ModelSerializer):
    # Optional: if you want to show the readable label (e.g. "Pending")
    status_display = serializers.SerializerMethodField()

    class Meta:
        model = OrganizationGrantHistory
        fields = [
            "id",
            "organization",
            "grant_name",
            "grant_purpose",
            "start_date",
            "end_date",
            "budget",
            "status",
            "status_display",
            "created_at",
            "updated_at"
        ]
        read_only_fields = ("organization", "created_at", "updated_at")

    def get_status_display(self, obj):
        return obj.get_status_display()

