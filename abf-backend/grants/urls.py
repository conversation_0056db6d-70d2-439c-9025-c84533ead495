from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from grants.views import GrantmakerGrantViewSet, OrganizationGrantHistoryViewSet, GranteeGrantViewSet

router = DefaultRouter()
router.register("grants", GrantmakerGrantViewSet, basename='grantmaker-grants')
router.register("organization-grant-history", OrganizationGrantHistoryViewSet, basename='organization-grant-history')
router.register('me/grants', GranteeGrantViewSet, basename='grantee-grants')

urlpatterns = [
    path('', include(router.urls))
]
