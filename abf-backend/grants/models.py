import datetime
from django.db import models
from django.db.models import ForeignKey

from common.mixins.s3 import S3PresignedURLMixin
from users.models import User

from profiles.models import Organization


class Grant(models.Model):
    """
    Each grant. If each grant belongs to exactly ONE organization,
    add organization as a ForeignKey. If you want many-to-many, see bridging model below.
    """
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='grants_given_to_organisation'
    )

    grant_maker_organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='grants_given_by_grant_maker',
    )

    grant_name = models.CharField(max_length=255)
    start_date = models.DateField(default=datetime.date.today)
    end_date = models.DateField(default=datetime.date.today)
    grant_purpose = models.TextField(blank=True, null=True)
    annual_budget = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    funding_sources = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def clean(self):
        pass

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.grant_name} (Org: {self.organization.organization_name})"


class GrantAttachment(S3PresignedURLMixin, models.Model):

    grant: Grant = ForeignKey(
        Grant,
        on_delete=models.CASCADE,
        related_name='attachments'
    )
    s3_key = models.CharField(max_length=512)  # S3 object key (e.g. "grant_attachments/abc.pdf")
    filename = models.CharField(max_length=255)  # Original uploaded filename
    file_type = models.CharField(max_length=50, blank=True, null=True)  # e.g., PDF, DOCX
    uploaded_at = models.DateTimeField(auto_now_add=True)
    description = models.TextField(blank=True, null=True)

    def get_download_url(self):
        return self.generate_presigned_url(s3_key=self.s3_key, method="get_object")

    def __str__(self):
        return f"{self.filename} for grant: {self.grant.grant_name}"


class OrganizationGrantHistoryStatus(models.TextChoices):
    ACTIVE = "ACTIVE", "Active"
    PENDING = "PENDING", "Pending"
    COMPLETED = "COMPLETED", "Completed"


class OrganizationGrantHistory(models.Model):
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="grant_histories"
    )
    grant_name = models.CharField(max_length=255)
    grant_purpose = models.TextField()
    start_date = models.DateField()
    end_date = models.DateField()
    budget = models.DecimalField(max_digits=12, decimal_places=2)
    status = models.CharField(
        max_length=20,
        choices=OrganizationGrantHistoryStatus.choices,
        default=OrganizationGrantHistoryStatus.PENDING
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"GrantHistory: {self.grant_name} ({self.organization.organization_name})"