from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
import logging

from typing_extensions import ReadOnly

from common.decorators import handle_org_exceptions
from common.exceptions import UserNotRegistered, UserOrganizationNotFound
from common.mixins.org_aware import OrgAwareMixin
from profiles.utils import log_attachment_history

from profiles.models import Organization, OrganizationKMP, OrganizationAttachment, OrganizationAttachmentStatus, \
    OrganizationFunctionType, OrganizationAttachmentType
from profiles.serializers import OrganizationSerializer, OrganizationAttachmentSerializer, FundingEntitySerializer, \
    OrganizationAttachmentTypeSerializer
from users.permissions import IsGrantee, IsGrantMaker
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet
from profiles.serializers import OrganizationKMPSerializer
from profiles.models import OrganizationKMP
from users.models import User
from rest_framework.response import Response
from rest_framework import status

class OrganizationViewSet(OrgAwareMixin, ModelViewSet):
    permission_classes = [IsGrantee]
    serializer_class = OrganizationSerializer
    queryset = Organization.objects.all()

    @handle_org_exceptions
    def list(self, request, *args, **kwargs):
        org = self.get_user_org(request)
        org.previous_grants = org.grant_histories.all()
        logging.debug(f"previous grants = {org.previous_grants}")
        serializer = self.get_serializer(org)
        return Response({'status': 'SUCCESS', 'data': serializer.data}, status=status.HTTP_200_OK)

    @handle_org_exceptions
    def create(self, request, *args, **kwargs):
        user: User = self.get_user(request)

        if user.organization:
            return Response({
                'status': 'ERROR',
                'message': 'User already has an organization. Use PATCH to update it.'
            }, status=status.HTTP_400_BAD_REQUEST)

        if user.type.code == 'GRANTEE':
            request.data['organization_function_type'] = 'GRANTEE_ORGANIZATION'
        elif user.type.code == 'GRANT_MAKER':
            request.data['organization_function_type'] = 'GRANT_MAKER_ORGANIZATION'

        logging.debug(f'User = {user}')
        logging.debug(f'Request data =  {request.data}')

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            org = serializer.save()
            user.organization = org
            user.save()
            return Response({'status': 'SUCCESS', 'data': serializer.data}, status=status.HTTP_201_CREATED)

        return Response({'status': 'ERROR', 'errors': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=["patch"], url_path="me")
    def update_user_organization(self, request, *args, **kwargs):
        """
        PATCH /api/profiles/v1/organization/me/
        """
        user: User = self.get_user(request)

        if not user.organization:
            return Response({
                'status': 'ERROR',
                'message': 'User has no associated organization.'
            }, status=status.HTTP_400_BAD_REQUEST)

        org_instance = user.organization
        serializer = self.get_serializer(org_instance, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response({'status': 'SUCCESS', 'data': serializer.data}, status=status.HTTP_200_OK)

        return Response({'status': 'ERROR', 'errors': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)


class FundingEntityViewSet(OrgAwareMixin, ModelViewSet):
    serializer_class = FundingEntitySerializer
    permission_classes = [IsGrantMaker]

    def get_queryset(self):
        org = self.get_user_org(self.request)
        return Organization.objects.filter(
            grant_maker_organization=org.id,
            organization_function_type__code='FUNDING_ENTITY'
        )

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'status': 'SUCCESS',
            'data': serializer.data,
            'message': 'Funding entities fetched successfully'
        }, status=status.HTTP_200_OK)

    def create(self, request, *args, **kwargs):
        user: User = self.get_user(request)

        if user.type.code != 'GRANT_MAKER':
            return Response({
                'status': 'ERROR',
                'message': 'Only grant maker users can create funding entities.'
            }, status=status.HTTP_403_FORBIDDEN)

        funding_entity_type = OrganizationFunctionType.objects.get(code='FUNDING_ENTITY')
        request.data['organization_function_type'] = funding_entity_type.id
        request.data['grant_maker_organization'] = user.organization.id

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({'status': 'SUCCESS', 'data': serializer.data}, status=status.HTTP_201_CREATED)

        return Response({'status': 'ERROR', 'errors': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({'status': 'SUCCESS', 'data': serializer.data, 'message': 'Funding entity updated successfully'})
        return Response({'status': 'ERROR', 'errors': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)


class KMPViewSet(OrgAwareMixin, ModelViewSet):
    serializer_class = OrganizationKMPSerializer
    permission_classes = [IsGrantee]

    def get_queryset(self):
        decoded_token = getattr(self.request, 'decoded_token', None)
        if not decoded_token:
            return OrganizationKMP.objects.none()

        try:
            user = User.objects.select_related('organization').get(cognito_sub=decoded_token.get('sub'))
        except User.DoesNotExist:
            return OrganizationKMP.objects.none()

        if not user.organization:
            return OrganizationKMP.objects.none()

        return OrganizationKMP.objects.filter(organization=user.organization)

    def create(self, request, *args, **kwargs):
        decoded_token = getattr(request, 'decoded_token', None)
        if not decoded_token:
            return Response({'status': 'ERROR', 'message': 'Unauthorized'}, status=status.HTTP_401_UNAUTHORIZED)

        try:
            user = User.objects.select_related('organization').get(cognito_sub=decoded_token.get('sub'))
        except User.DoesNotExist:
            return Response({'status': 'ERROR', 'message': 'User is not associated with the organization'}, status=400)

        if not user.organization:
            return Response({'status': 'ERROR', 'message': 'User must be part of an organization to add KMP'}, status=400)

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            serializer.save(organization=user.organization)
            return Response({'status': 'SUCCESS', 'message': 'KMP added successfully', 'data': serializer.data}, status=status.HTTP_201_CREATED)
        return Response({'status': 'ERROR', 'errors': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({'status': 'SUCCESS', 'data': serializer.data})
        return Response({'status': 'ERROR', 'errors': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)


class OrganizationAttachmentsViewSet(OrgAwareMixin, ModelViewSet):

    queryset = OrganizationAttachment.objects.all()
    serializer_class = OrganizationAttachmentSerializer
    permission_classes = [IsGrantee]

    def get_queryset(self):
        try:
            organization = self.get_user_org(self.request)
        except UserNotRegistered as e:
            logging.exception(str(e))
            return OrganizationAttachment.objects.none()
        except UserOrganizationNotFound as e:
            logging.exception(str(e))
            return OrganizationAttachment.objects.none()

        return OrganizationAttachment.objects.filter(organization=organization)

    def perform_create(self, serializer):
        try:
            user = getattr(self.request, 'db_user', None)
            organization = self.get_user_org(self.request)
        except (UserNotRegistered, UserOrganizationNotFound) as e:
            logging.exception(str(e))
            raise ValidationError(str(e))

        try:
            pending_status = OrganizationAttachmentStatus.objects.get(code='PENDING')
        except OrganizationAttachmentStatus.DoesNotExist:
            logging.exception("PENDING status not found")
            raise ValidationError("Default status PENDING does not exist")

        # Save the attachment with correct organization, uploader, and status
        attachment = serializer.save(
            uploaded_by=user,
            organization=organization,
            status=pending_status
        )
        # Also log history
        log_attachment_history(attachment, uploaded_by=user)

    def create(self, request, *args, **kwargs):
        response = super().create(request, *args, **kwargs)
        return Response({'status': 'SUCCESS', 'data': response.data}, status=response.status_code)

    @action(detail=False, methods=['get'], url_path='attachmentType/(?P<attachment_type>[^/.]+)')
    def retrieve_by_attachment_type(self, request, attachment_type=None):
        try:
            organization = self.get_user_org(request)
            attachment = OrganizationAttachment.objects.get(
                organization=organization,
                attachment_type__code=attachment_type
            )
        except OrganizationAttachment.DoesNotExist:
            return Response({'status': 'ERROR', 'message': 'Attachment not found'}, status=status.HTTP_404_NOT_FOUND)

        serializer = self.get_serializer(attachment)
        return Response({'status': 'SUCCESS', 'data': serializer.data})

    @action(detail=False, methods=['patch'], url_path='(?P<attachment_type>[^/.]+)')
    def update_by_attachment_type(self, request, attachment_type=None):
        try:
            organization = self.get_user_org(request)
            attachment = OrganizationAttachment.objects.get(
                organization=organization,
                attachment_type__code=attachment_type
            )
        except OrganizationAttachment.DoesNotExist:
            return Response({'status': 'ERROR', 'message': 'Attachment not found'}, status=status.HTTP_404_NOT_FOUND)

        serializer = self.get_serializer(attachment, data=request.data)
        if serializer.is_valid():
            updated_attachment = serializer.save()
            log_attachment_history(updated_attachment, uploaded_by=getattr(request, 'db_user', None))
            return Response({'status': 'SUCCESS', 'data': serializer.data})
        return Response({'status': 'ERROR', 'errors': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=False)
        if serializer.is_valid():
            attachment = serializer.save()
            log_attachment_history(attachment, uploaded_by=getattr(self.request, 'db_user', None))
            return Response({'status': 'SUCCESS', 'data': serializer.data})
        return Response({'status': 'ERROR', 'errors': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response({'status': 'SUCCESS', 'message': 'Attachment deleted successfully'})


class OrganizationAttachmentTypeViewSet(OrgAwareMixin, ReadOnlyModelViewSet):
    queryset = OrganizationAttachmentType.objects.all()
    serializer_class = OrganizationAttachmentTypeSerializer

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'status': 'SUCCESS',
            'message': 'Attachment types fetched successfully',
            'data': serializer.data
        }, status=status.HTTP_200_OK)
