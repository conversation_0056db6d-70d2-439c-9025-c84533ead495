from common.mixins.s3 import S3PresignedURLMixin
from grants.serializers import OrganizationGrantHistorySerializer, GrantSerializer
from users.models import User
from profiles.models import Organization, OrganizationFunctionType, OrganizationLegalType, OrganizationKMP, \
    OrganizationAttachment, OrganizationAttachmentType, OrganizationAttachmentStatus, Address
from rest_framework import serializers
from abf_backend import settings

class OrganizationAttachmentSerializer(S3PresignedURLMixin, serializers.ModelSerializer):
    attachment_type = serializers.SlugRelatedField(
        queryset=OrganizationAttachmentType.objects.all(),
        slug_field='code'
    )
    attachment_type_name = serializers.CharField(
        source='attachment_type.name',
        read_only=True
    )
    status = serializers.SlugRelatedField(
        queryset=OrganizationAttachmentStatus.objects.all(),
        slug_field='code',
        required=False
    )
    uploaded_by_email = serializers.CharField(source='uploaded_by.email', read_only=True)

    class Meta:
        model = OrganizationAttachment
        fields = ['id', 'object_key', 'attachment_type', 'original_filename', 'remarks', 'status', 'uploaded_at', 'uploaded_by', 'uploaded_by_email', 'attachment_type_name']
        read_only_fields = ['id', 'organization', 'uploaded_by', 'uploaded_by_email', 'attachment_type_name']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if self.context.get('request') and self.context['request'].method == 'GET':
            representation['object_key'] = self.generate_presigned_url(s3_key=instance.object_key, method="get_object")
        return representation

class AddressSerializer(serializers.ModelSerializer):

    class Meta:
        fields = '__all__'
        model = Address

class OrganizationKMPSerializer(serializers.ModelSerializer):

    class Meta:
        fields = '__all__'
        model = OrganizationKMP
        read_only_fields = ['organization']


class FundingEntitySerializer(serializers.ModelSerializer):
    grant_maker_organization = serializers.PrimaryKeyRelatedField(
        queryset=Organization.objects.all(),
        write_only=True
    )

    organization_function_type = serializers.PrimaryKeyRelatedField(
        queryset=OrganizationFunctionType.objects.all(),
        required=True
    )

    class Meta:
        model = Organization
        fields = ['id', 'organization_name', 'pan_number', 'grant_maker_organization', 'organization_function_type']


class OrganizationSerializer(serializers.ModelSerializer):

    # Accept codes in the request
    organization_function_type = serializers.SlugRelatedField(
        slug_field='code',
        queryset=OrganizationFunctionType.objects.all()
    )
    organization_legal_type = serializers.SlugRelatedField(
        slug_field='code',
        queryset=OrganizationLegalType.objects.all(),
        required=False,
        allow_null=True
    )

    # Add readable names to the response
    organization_function_type_name = serializers.CharField(
        source='organization_function_type.name',
        read_only=True
    )
    organization_legal_type_name = serializers.CharField(
        source='organization_legal_type.name',
        read_only=True
    )

    previous_grants = OrganizationGrantHistorySerializer(
        many=True,
        read_only=True,
        source='grant_histories'
    )

    attachments = OrganizationAttachmentSerializer(
        many=True,
        read_only=True,
        source='attachments_of_organization'
    )

    kmps = OrganizationKMPSerializer(
        many=True,
        read_only=True,
        source='key_management_personnel_in_organization'
    )

    grants = GrantSerializer(
        many=True,
        read_only=True,
        source='grants_given_to_organisation'
    )

    address = AddressSerializer()

    logo_key = serializers.SerializerMethodField()

    def get_logo_key(self, obj):
        if obj.logo_key:
            return f"{settings.CLOUDFRONT_DISTRIBUTION_DOMAIN}/{obj.logo_key}"

        return None

    def update(self, instance, validated_data):
        address_data = validated_data.pop('address', None)

        # Update the base Organization fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Handle nested address update if provided
        if address_data:
            address_instance = instance.address
            for attr, value in address_data.items():
                setattr(address_instance, attr, value)
            address_instance.save()

        return instance

    class Meta:
        model = Organization
        fields = '__all__'


class ProfileListSerializer(serializers.ModelSerializer):

    class Meta:
        model = Organization
        fields = '__all__'


class OrganizationAttachmentTypeSerializer(serializers.ModelSerializer):

    class Meta:
        model = OrganizationAttachmentType
        fields = '__all__'
