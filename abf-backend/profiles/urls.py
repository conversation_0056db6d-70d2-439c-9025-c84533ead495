from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>

from .views import OrganizationViewSet, KMPViewSet, OrganizationAttachmentsViewSet, FundingEntityViewSet, \
    OrganizationAttachmentTypeViewSet

router = DefaultRouter()
router.register('organization', OrganizationViewSet, basename='organization')
router.register('attachments', OrganizationAttachmentsViewSet, basename='attachments')
router.register('kmp', KMPViewSet, basename='kmp')
router.register('funding-entity', FundingEntityViewSet, basename='funding-entity')
router.register('attachment-types', OrganizationAttachmentTypeViewSet, basename='attachment-types')

urlpatterns = [
    path('v1/', include(router.urls)),
]

