from django.conf import settings
from django.db import models
from django.db.models import CASCADE
from phonenumber_field.modelfields import PhoneNumber<PERSON>ield
import re
from django_cryptography.fields import encrypt

from django.core.exceptions import ValidationError


class OrganizationFunctionType(models.Model):
    """It could be either grantee organisation or grant maker organisation"""

    code = models.CharField(max_length=128, blank=False, unique=True, null=False)  # 'GRANTEE', 'GRANT_MAKER'
    name = models.CharField(max_length=128, blank=False, unique=True, null=False)
    description = models.TextField(blank=True)

    def clean(self):

        if not re.fullmatch(r'[A-Z_]+', self.code):
            raise ValidationError({
                'code': 'Code must only contain uppercase letters and underscore'
            })

    @classmethod
    def seed_defaults(cls):
        defaults = [
            {"code": "GRANTEE_ORGANIZATION", "name": "Grantee Organization"},
            {"code": "GRANT_MAKER_ORGANIZATION", "name": "Grant Maker Organization"},
            {"code": "FUNDING_ENTITY", "name": "Funding Entity"},
        ]
        for entry in defaults:
            cls.objects.get_or_create(code=entry["code"], defaults={"name": entry["name"]})

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.code} - {self.name}"

class OrganizationLegalType(models.Model):

    code = models.CharField(max_length=128, blank=False, unique=True, null=False)  # 'NON_PROFIT', 'PRIVATE_LIMITED', etc.
    name = models.CharField(max_length=128, blank=False, unique=True, null=False)
    description = models.TextField(blank=True)

    @classmethod
    def seed_defaults(cls):
        defaults = [
            {"code": "NON_PROFIT", "name": "Non-Profit"},
            {"code": "TRUST", "name": "Trust"},
        ]
        for entry in defaults:
            cls.objects.get_or_create(code=entry["code"], defaults={"name": entry["name"]})

    def clean(self):

        if not re.fullmatch(r'[A-Z_]+', self.code):
            raise ValidationError({
                'code': 'Code must only contain uppercase letters and underscore'
            })

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.code} - {self.name}"

class Address(models.Model):
    """
    Stores the addresses of the organization.
    """
    address_line_1 = models.CharField(max_length=512)
    address_line_2 = models.CharField(null=True, blank=True)  # Optional for landmark or such
    locality = models.CharField(max_length=128)     # For eg. Dadar
    city = models.CharField(max_length=256)         # For eg. Mumbai
    state = models.CharField(max_length=128)        # For eg. Maharashtra
    postal_code = models.CharField(max_length=6)    # eg. 411001
    country = models.CharField(max_length=128)      # eg. India

class Organization(models.Model):
    """
    Stores the main grantee info: name, type, tax registration, contact, etc.
    """

    point_of_contact_name = models.CharField(max_length=255, null=True, blank=True)
    budget = models.IntegerField(null=True)  # Organization budget
    logo_key = models.CharField(max_length=256, null=True, blank=True)
    # Organization details
    organization_legal_type = models.ForeignKey(
        OrganizationLegalType,
        on_delete=models.CASCADE,
        related_name='organizations_by_legal_type',
        blank=True,
        null=True
    )
    organization_function_type = models.ForeignKey(
        OrganizationFunctionType,
        on_delete=models.CASCADE,
        related_name='organizations_by_function_type',
    )

    organization_name = models.CharField(max_length=255)

    grant_maker_organization = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='funding_entities',
        help_text="Only used if this organization is a funding entity"
    )

    pan_number = encrypt(models.CharField(max_length=50, blank=True, null=True))
    address = models.OneToOneField(
        Address,
        on_delete=models.CASCADE,
        null=True
    )

    phone_number = PhoneNumberField()
    email_address = models.EmailField(max_length=100, blank=True, null=True)
    website_url = models.URLField(max_length=255, blank=True, null=True)
    number_of_team_members = models.IntegerField(blank=True, null=True)
    mission = models.TextField(blank=True, null=True)
    vision = models.TextField(blank=True, null=True)
    background_history = models.TextField(blank=True, null=True)
    registered_year = models.CharField(max_length=4, null=True)

    # Tax details
    csr_registration_number = models.CharField(max_length=128, blank=True, null=True)
    tax_registration_number = models.CharField(max_length=100, blank=True, null=True)
    tax_registration_number_under_12_a = models.CharField(max_length=100, blank=True, null=True)
    fcra_registration_number = models.CharField(max_length=100, blank=True, null=True)
    trust_registration_number = models.CharField(max_length=100, blank=True, null=True)
    darpan_id = models.CharField(max_length=100, blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def clean(self):
        pass
        #if self.organization_function_type == 'GRANTEE':
        #    pass

        #if self.organization_function_type == 'GRANT_MAKER':
        #    # TODO: Need to add other things which aren't needed after discussion
        #    errors = []
        #    if self.organization_legal_type:
        #        errors.append({'organization_legal_type': 'Grant maker organisation does not need organization legal type'})
        #    if self.tax_registration_no:
        #        errors.append({'tax_registration_no': 'Grant maker organisation does not need a tax registration number'})
        #    if self.background_history:
        #        errors.append({'background_history': 'Grant maker organisation does not need background history'})
        #    if self.pan:
        #        errors.append({'pan': 'Grant maker organisation does not need a pan'})

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.organization_name} - {self.organization_function_type}"

class OrganizationKMP(models.Model):
    """
    Key Management Personnel for each organization (one-to-many).
    """
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='key_management_personnel_in_organization'
    )
    name = models.CharField(max_length=100)
    designation = models.CharField(max_length=100, blank=True, null=True)
    din = models.CharField(max_length=50, blank=True, null=True)  # Director Identification Number
    phone_number = models.CharField(max_length=10, blank=True, null=True)
    email = models.EmailField(blank=False, null=True, unique=True)

    def __str__(self):
        return f"{self.name} - {self.designation}"

class OrganizationAttachmentStatus(models.Model):
    code = models.CharField(max_length=32, blank=False, null=False, unique=True)
    name = models.CharField(max_length=128, blank=False, null=False)
    description = models.CharField(max_length=512, blank=True, null=True)

    @classmethod
    def seed_defaults(cls):
        default_statuses = [
            {"code": "PENDING", "name": "Pending Review", "description": "Waiting for verification"},
            {"code": "VERIFIED", "name": "Verified", "description": "Document is verified"},
            {"code": "REJECTED", "name": "Rejected", "description": "Rejected due to issues"}
        ]

        for status in default_statuses:
            cls.objects.get_or_create(code=status['code'], defaults={
                "name": status["name"],
                "description": status["description"]
            })

    def clean(self):
        if not re.fullmatch(r'[A-Z_]+', self.code):
            raise ValidationError({
                'code': 'Code must only contain uppercase letters and underscore'
            })

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):

        return f'{self.code} - {self.name}'

class OrganizationAttachmentType(models.Model):
    code = models.CharField(max_length=64, unique=True)  # e.g. "PAN_CARD"
    name = models.CharField(max_length=256)              # e.g. "PAN Card"
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    @classmethod
    def seed_defaults(cls):
        documents = [
            ("ORGANIZATION_REGISTRATION_CERTIFICATE", "Organization Registration Certificate"),
            ("TAX_EXEMPTION", "Tax Exemption Certificate"),
            ("FCRA_CERTIFICATE", "FCRA Certificate"),
            ("PAN_CARD", "PAN Card"),
            ("CSR_REGISTRATION", "CSR Registration Certificate"),
            ("TRUST_REGISTRATION", "Trust Registration Certificate"),
            ("TAX_80G", "Tax Exemption Certificate (80G)"),
            ("TAX_12A", "Tax Exemption Certificate (12A)"),
            ("INCORPORATION", "Certificate of Incorporation"),
            ("CONFLICT_DECLARATION", "Conflict of Interest Declaration for BOD"),
            ("THEORY_OF_CHANGE", "Theory of Change"),
            ("CSR_FUNDS_DECLARATION", "Self Declaration of CSR Funds"),
            ("POSH_POLICY", "POSH Policy"),
            ("CHILD_PROTECTION", "Child Protection Policy"),
            ("CANCELLED_CHEQUE", "Cancelled Cheque"),
            ("FINANCIAL_STATEMENT_LATEST", "Latest Financial Statement"),
            ("FINANCIAL_STATEMENT_PREVIOUS", "Previous Year Financial Statement"),
            ("FINANCIAL_STATEMENT_SECOND_PREVIOUS", "Second Previous Year Financial Statement"),
            ("ANNUAL_REPORTS", "Annual Reports"),
        ]

        for code, name in documents:
            cls.objects.get_or_create(
                code=code,
                defaults={"name": name}
            )

    def clean(self):
        if not re.fullmatch(r'[A-Z0-9_]+', self.code):
            raise ValidationError({
                'code': 'Code must only contain uppercase letters, numbers and underscore'
            })

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

class OrganizationAttachment(models.Model):
    """
    Attachments for each organization (registration docs, PAN copy, etc.).
    """
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['organization', 'attachment_type'],
                name='unique_attachment_per_org_per_attachment_type'
            )
        ]
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='attachments_of_organization'
    )
    status = models.ForeignKey(
        OrganizationAttachmentStatus,
        on_delete=models.PROTECT
    )

    attachment_type = models.ForeignKey(
        OrganizationAttachmentType,
        on_delete=models.PROTECT,
        related_name='organization_attachments'
    )
    uploaded_by = models.ForeignKey(
        settings.USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    original_filename = models.CharField(max_length=512, blank=True, null=True)
    remarks = models.TextField(blank=True, null=True)
    object_key = models.CharField(max_length=1024, blank=True, null=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def clean(self):
        if self.status and self.status.code not in ["PENDING", "VERIFIED"]:
            if not self.remarks or self.remarks.strip() == "":
                raise ValidationError("Remarks are required in this scenario")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.organization.organization_name} - {self.attachment_type}"

class OrganizationAttachmentHistory(models.Model):

    class Meta:
        ordering = ['-uploaded_at']

    attachment = models.ForeignKey(
        OrganizationAttachment,
        on_delete=models.CASCADE,
        related_name='history'
    )

    status = models.ForeignKey(
        OrganizationAttachmentStatus,
        on_delete=models.PROTECT
    )

    uploaded_by = models.ForeignKey(
        settings.USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )

    object_key = models.CharField(max_length=1024)
    remarks = models.TextField(blank=True, null=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.attachment} - {self.status.code} at {self.uploaded_at.strftime('%Y-%m-%d %H:%M')}"
