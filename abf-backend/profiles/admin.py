from django.contrib import admin
from .models import (
    OrganizationFunctionType,
    OrganizationLegalType,
    Organization,
    OrganizationKMP,
    OrganizationAttachmentStatus,
    OrganizationAttachmentType,
    OrganizationAttachment,
    OrganizationAttachmentHistory,
    Address,
)

@admin.register(OrganizationFunctionType)
class OrganizationFunctionTypeAdmin(admin.ModelAdmin):
    list_display = ("code", "name")
    search_fields = ("code", "name")

@admin.register(OrganizationLegalType)
class OrganizationLegalTypeAdmin(admin.ModelAdmin):
    list_display = ("code", "name")
    search_fields = ("code", "name")

@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ("organization_name", "organization_function_type", "organization_legal_type", "email_address", "phone_number")
    search_fields = ("organization_name", "email_address", "phone_number")
    list_filter = ("organization_function_type", "organization_legal_type")

@admin.register(OrganizationKMP)
class OrganizationKMPAdmin(admin.ModelAdmin):
    list_display = ("name", "designation", "email", "phone_number", "organization")
    search_fields = ("name", "email", "organization__organization_name")
    list_filter = ("organization",)

@admin.register(OrganizationAttachmentStatus)
class OrganizationAttachmentStatusAdmin(admin.ModelAdmin):
    list_display = ("code", "name")
    search_fields = ("code", "name")

@admin.register(OrganizationAttachmentType)
class OrganizationAttachmentTypeAdmin(admin.ModelAdmin):
    list_display = ("code", "name")
    search_fields = ("code", "name")

@admin.register(OrganizationAttachment)
class OrganizationAttachmentAdmin(admin.ModelAdmin):
    list_display = ("organization", "attachment_type", "status", "uploaded_by", "uploaded_at")
    search_fields = ("organization__organization_name", "attachment_type__code", "uploaded_by__email")
    list_filter = ("status", "attachment_type")

@admin.register(OrganizationAttachmentHistory)
class OrganizationAttachmentHistoryAdmin(admin.ModelAdmin):
    list_display = ("attachment", "status", "uploaded_by", "uploaded_at")
    search_fields = ("attachment__organization__organization_name", "status__code", "uploaded_by__email")
    list_filter = ("status", "uploaded_at")



@admin.register(Address)
class AddressyAdmin(admin.ModelAdmin):

    list_display = ("address_line_1", "address_line_2", "locality", "locality")


