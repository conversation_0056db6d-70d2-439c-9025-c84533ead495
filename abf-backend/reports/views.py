from rest_framework import permissions, status, viewsets
from rest_framework.exceptions import PermissionDenied, ValidationError
from rest_framework.response import Response

from common.decorators import handle_org_exceptions
from common.mixins.org_aware import OrgAwareMixin
from grants.models import Grant
from profiles.models import Organization
from reports.models import NarrativeReport, NarrativeReportQuestion, ReportsGallery
from reports.permissions import CanGranteeEditOwnUpload
from reports.serializers import NarrativeReportSerializer, ReportsGallerySerializer
from users.permissions import IsGrantee, IsGrantMaker


class GranteeNarrativeReportViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    permission_classes = [IsGrantee]
    serializer_class = NarrativeReportSerializer

    @handle_org_exceptions
    def get_queryset(self):
        user_org = self.get_user_org(self.request)
        queryset = NarrativeReport.objects.filter(organization=user_org)

        # Apply filters based on query parameters
        filters = {
            'year': self.request.query_params.get("year"),
            'quarter': self.request.query_params.get("quarter"),
            'grant_id': self.request.query_params.get("grant"),
            'status': self.request.query_params.get("status")
        }
        
        for field, value in filters.items():
            if value:
                queryset = queryset.filter(**{field: value})

        return queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['organization'] = self.get_user_org(self.request)
        return context

    @handle_org_exceptions
    def perform_create(self, serializer):
        user_org = self.get_user_org(self.request)
        grant_id = self.request.data.get("grant_id")
        questions_data = self.request.data.get("questions", [])

        try:
            grant = Grant.objects.get(pk=grant_id)
        except Grant.DoesNotExist:
            raise ValidationError("Grant not found.")

        serializer.save(organization=user_org, grant=grant)

        # Create questions for the report
        report = serializer.instance
        for q in questions_data:
            question_text = q.get("question", "").strip()
            answer_text = q.get("answer", "").strip()
            if question_text:
                NarrativeReportQuestion.objects.create(report=report, question=question_text, answer=answer_text)

    @handle_org_exceptions
    def perform_update(self, serializer):
        user_org = self.get_user_org(self.request)
        report = self.get_object()

        if report.organization != user_org:
            raise PermissionDenied("You do not have permission to update this report.")

        # Validate report status for editing
        if report.status == "PENDING":
            raise ValidationError("The report is currently under review")
        elif report.status == "APPROVED":
            raise ValidationError("This report has been approved and cannot be edited.")
        elif report.status != "REJECTED":
            raise ValidationError("Only rejected reports can be edited.")

        # Handle resubmission
        is_resubmission = self.request.data.get("status") == "PENDING" and report.status == "REJECTED"
        
        # Validate restricted fields
        if 'remarks' in self.request.data:
            raise ValidationError({"remarks": "You are not allowed to modify this field."})

        # Validate questions data
        questions_data = self.request.data.get("questions")
        if questions_data is None:
            raise ValidationError({"questions": "This field is required."})
        if not isinstance(questions_data, list):
            raise ValidationError({"questions": "Must be a list."})

        # Update existing questions or create new ones
        existing_answers = {qa.id: qa for qa in report.question_answers.all()}

        for q in questions_data:
            q_id = q.get("id")
            question_text = q.get("question", "").strip()
            answer_text = q.get("answer", "").strip()

            if q_id in existing_answers:
                qa = existing_answers[q_id]
                if question_text:
                    qa.question = question_text
                if answer_text:
                    qa.answer = answer_text
                qa.save()
            elif question_text:
                NarrativeReportQuestion.objects.create(report=report, question=question_text, answer=answer_text)

        # Handle resubmission status change
        if is_resubmission:
            report.status = "PENDING"
            report.save()

        serializer.instance = report


class GrantmakerNarrativeReportViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    permission_classes = [IsGrantMaker]
    serializer_class = NarrativeReportSerializer

    @handle_org_exceptions
    def get_queryset(self):
        queryset = NarrativeReport.objects.all()

        # Apply filters based on query parameters
        filters = {
            'organization_id': self.request.query_params.get("grantee_id"),
            'year': self.request.query_params.get("year"),
            'quarter': self.request.query_params.get("quarter"),
            'grant_id': self.request.query_params.get("grant"),
            'status': self.request.query_params.get("status")
        }
        
        for field, value in filters.items():
            if value:
                queryset = queryset.filter(**{field: value})

        return queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['organization'] = self.get_user_org(self.request)
        return context

    @handle_org_exceptions
    def perform_update(self, serializer):
        report = self.get_object()
        new_status = self.request.data.get("status")
        remarks = self.request.data.get("remarks")

        # Validate rejection requirements
        if new_status == "REJECTED" and not remarks:
            raise ValidationError({"remarks": "Remarks are required when rejecting a report."})

        serializer.save()

        # Update questions if provided
        questions_data = self.request.data.get("questions")
        if questions_data is not None:
            if not isinstance(questions_data, list):
                raise ValidationError({"questions": "Must be a list of answers."})

            report.question_answers.all().delete()
            for q in questions_data:
                NarrativeReportQuestion.objects.create(
                    report=report,
                    question=q.get("question", "").strip(),
                    answer=q.get("answer", "").strip()
                )


class GranteeReportsGalleryViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    serializer_class = ReportsGallerySerializer
    permission_classes = [IsGrantee, CanGranteeEditOwnUpload]

    def get_queryset(self):
        organization = self.get_user_org(self.request)
        return ReportsGallery.objects.filter(organization=organization)

    def perform_create(self, serializer):
        organization = self.get_user_org(self.request)
        serializer.save(organization=organization)

    def perform_destroy(self, instance):
        organization = self.get_user_org(self.request)
        if instance.organization != organization:
            raise PermissionDenied("You cannot delete this report.")

        instance.delete_s3_file()
        instance.delete()


class GrantmakerReportsGalleryViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    serializer_class = ReportsGallerySerializer
    permission_classes = [IsGrantMaker]

    def get_queryset(self):
        queryset = ReportsGallery.objects.all()

        # Apply filters based on query parameters
        org_id = self.request.query_params.get("organization")
        if org_id:
            try:
                Organization.objects.get(pk=org_id)  # Validate organization exists
                queryset = queryset.filter(organization_id=org_id)
            except Organization.DoesNotExist:
                raise ValidationError("Organization not found.")

        title = self.request.query_params.get("title")
        if title:
            queryset = queryset.filter(title__icontains=title)

        uploaded_after = self.request.query_params.get("uploaded_after")
        if uploaded_after:
            queryset = queryset.filter(uploaded_at__gte=uploaded_after)

        uploaded_before = self.request.query_params.get("uploaded_before")
        if uploaded_before:
            queryset = queryset.filter(uploaded_at__lte=uploaded_before)

        return queryset

    def perform_update(self, serializer):
        # Allow update of metadata only
        serializer.save()