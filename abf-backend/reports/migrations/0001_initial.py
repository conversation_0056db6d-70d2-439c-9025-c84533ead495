# Generated by Django 4.2.20 on 2025-06-17 07:33

import common.mixins.s3
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('profiles', '0001_initial'),
        ('grants', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='NarrativeReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quarter', models.IntegerField(default=1, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(4)])),
                ('year', models.IntegerField()),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected')], default='PENDING', max_length=20)),
                ('remarks', models.TextField(blank=True, help_text='Remarks for reviewers or admins (optional)', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('grant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='narrative_report_for_grant', to='grants.grant')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='narrative_reports_of_org', to='profiles.organization')),
            ],
        ),
        migrations.CreateModel(
            name='ReportsGallery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('s3_key', models.CharField(max_length=512)),
                ('file_name', models.CharField(max_length=255)),
                ('file_type', models.CharField(blank=True, max_length=255, null=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports_gallery_of_org', to='profiles.organization')),
            ],
            bases=(models.Model, common.mixins.s3.S3PresignedURLMixin),
        ),
        migrations.CreateModel(
            name='NarrativeReportQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question', models.TextField()),
                ('answer', models.TextField(blank=True)),
                ('report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='question_answers', to='reports.narrativereport')),
            ],
        ),
        migrations.AddConstraint(
            model_name='narrativereport',
            constraint=models.UniqueConstraint(fields=('organization', 'grant', 'year', 'quarter'), name='unique_report_per_org_grant_quarter'),
        ),
    ]
