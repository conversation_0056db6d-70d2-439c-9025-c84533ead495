from django.urls import include, path
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>

from aws_services.views import GeneratePresignedURL
from reports.views import (GranteeNarrativeReportViewSet,
                           GranteeReportsGalleryViewSet,
                           GrantmakerNarrativeReportViewSet,
                           GrantmakerReportsGalleryViewSet)

router = DefaultRouter()
router.register("narrative", GranteeNarrativeReportViewSet, basename="narrative-reports")
router.register("grantmaker/narrative", GrantmakerNarrativeReportViewSet, basename="grantmaker-narrative-reports")
router.register("gallery", GranteeReportsGalleryViewSet, basename="grantee-reports-gallery")
router.register("grantmaker/gallery", GrantmakerReportsGalleryViewSet, basename="grantmaker-reports-gallery")

urlpatterns = [
    path("gallery/upload/", GeneratePresignedURL.as_view(), name="reports-gallery-presign"),
    path("", include(router.urls)),
]
