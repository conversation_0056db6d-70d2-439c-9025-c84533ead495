from rest_framework import serializers

from grants.models import Grant
from grants.serializers import GrantSerializer
from profiles.serializers import OrganizationSerializer
from reports.models import (NarrativeReport, NarrativeReportQuestion,
                            ReportsGallery)


class NarrativeReportQuestionSerializer(serializers.ModelSerializer):
    class Meta:
        model = NarrativeReportQuestion
        fields = '__all__'

class NarrativeReportSerializer(serializers.ModelSerializer):
    grant_id = serializers.PrimaryKeyRelatedField(
        queryset=Grant.objects.all(),
        write_only=True,
        required=False  # Make it not required for updates
    )
    grant = GrantSerializer(read_only=True)
    organization = OrganizationSerializer(read_only=True)
    question_answers = NarrativeReportQuestionSerializer(many=True, read_only=True)

    class Meta:
        model = NarrativeReport
        fields = [
            'id', 'organization', 'grant_id', 'grant',
            'quarter', 'year', 'status', 'remarks',
            'question_answers', 'created_at', 'updated_at',
        ]
        read_only_fields = ['organization', 'grant', 'created_at', 'updated_at'] 

    def validate(self, attrs):
        organization = self.context.get('organization')
        grant = attrs.get('grant_id', getattr(self.instance, 'grant', None)) 
        year = attrs.get('year', getattr(self.instance, 'year', None))       
        quarter = attrs.get('quarter', getattr(self.instance, 'quarter', None)) 

        if not organization:
            raise serializers.ValidationError("User is not associated with an organization.")

        # Skip validation if required fields are missing during partial updates
        if self.instance and not any(attrs.keys()):
            return attrs

        if not all([grant, year, quarter]):
            return attrs

        # Skip uniqueness check when updating and no conflict
        if self.instance:
            if (
                self.instance.grant_id == grant.id and
                self.instance.year == year and
                self.instance.quarter == quarter
            ):
                return attrs

        exists = NarrativeReport.objects.filter(
            organization=organization,
            grant=grant,
            year=year,
            quarter=quarter,
        ).exclude(pk=self.instance.pk if self.instance else None).exists() # Exclude the current instance during update check
        if exists:
            raise serializers.ValidationError(
                "A report already exists for this grant, year, and quarter."
            )

        return attrs

    def create(self, validated_data):
        grant = validated_data.pop('grant_id')
        organization = self.context.get('organization')

        validated_data.pop('organization', None)
        validated_data.pop('grant', None)

        if not organization:
            raise serializers.ValidationError("Organization context missing.")

        return NarrativeReport.objects.create(
            organization=organization,
            grant=grant,
            **validated_data
        )

    def update(self, instance, validated_data):
        instance.status = validated_data.get('status', instance.status)
        instance.remarks = validated_data.get('remarks', instance.remarks)
        instance.save()
        return instance

class ReportsGallerySerializer(serializers.ModelSerializer):
    attachment_url = serializers.ReadOnlyField()
    organization = OrganizationSerializer(read_only=True)

    class Meta:
        model = ReportsGallery
        fields = [
            "id", "title", "s3_key", "file_name", "file_type",
            "uploaded_at", "description", "attachment_url", "organization",
        ]
        read_only_fields = ["uploaded_at", "attachment_url"]