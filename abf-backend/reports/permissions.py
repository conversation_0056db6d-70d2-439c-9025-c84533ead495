# reports/permissions.py
from rest_framework.permissions import SAFE_METHODS, BasePermission


class CanGranteeEditOwnUpload(BasePermission):
    """
    Grantees can only modify their own uploads.
    """

    def has_object_permission(self, request, view, obj):
        # Allow safe methods (GET, HEAD, OPTIONS) for own org
        if request.method in SAFE_METHODS:
            return obj.organization == view.get_user_org(request)

        # Allow edit/delete only if the object belongs to user's org
        return obj.organization == view.get_user_org(request)
