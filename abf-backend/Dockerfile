# ---------- Base Image ----------
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    DJANGO_SETTINGS_MODULE=abf_backend.settings

# Set working directory
WORKDIR /app

# Install OS-level dependencies
RUN apt-get update && apt-get install -y \
    libpq-dev gcc curl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip
RUN pip install -r requirements.txt

# Copy all project files

COPY .env .env
COPY . .
# Expose port for gunicorn
EXPOSE 8000

# Start server using gunicorn
CMD ["sh", "-c", "python manage.py migrate && gunicorn abf_backend.wsgi:application --bind 0.0.0.0:8000"]
