from rest_framework.views import exception_handler
import logging
from rest_framework.response import Response
from rest_framework import status
from common.exceptions import UserOrganizationNotFound

def custom_exception_handler(exc, context):
    response = exception_handler(exc, context)

    # DRF-handled exceptions (ValidationError, etc.)
    if response is not None:
        logging.error(f"Response data = {response.data}")
        return Response({
            "status": "ERROR",
            "message": str(response.data.get("detail", "Validation failed")),
            "data": response.data
        }, status=response.status_code)

    # Custom app-level exceptions
    if isinstance(exc, UserOrganizationNotFound):
        return Response({
            "status": "ERROR",
            "message": str(exc),
            "data": None
        }, status=status.HTTP_400_BAD_REQUEST)

    # Catch-all fallback
    return Response({
        "status": "ERROR",
        "message": str(exc),
        "data": None
    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)