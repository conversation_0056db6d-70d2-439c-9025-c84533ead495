from common.exceptions import UserOrganizationNotFound, UserNotRegistered
from users.models import User

class OrgAwareMixin:

    def get_user_org(self, request):
        """
            Retrieve the user's organization from request. Raises exception if not found.
        """
        user = getattr(request, 'db_user', None)
        if not user:
            raise UserNotRegistered("User is not registered.")
        if not user.organization:
            raise UserOrganizationNotFound("User must belong to an organization.")
        return user.organization

    def get_user(self, request) -> User:
        user = getattr(request, 'db_user', None)
        if not user:
            raise UserNotRegistered("User is not registered.")

        return user
