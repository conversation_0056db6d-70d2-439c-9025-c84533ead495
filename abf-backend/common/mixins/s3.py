import boto3
import botocore.exceptions
import logging

from s3transfer.compat import seekable

from abf_backend import settings


class S3PresignedURLMixin:
    """
    Reusable Mixin to generate presigned S3 URLs.
    """

    def get_s3_client(self):
        try:
            return boto3.client(
                "s3",
                region_name=settings.AWS_COGNITO_REGION,
                aws_access_key_id=settings.AWS_ACCESS_KEY,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            )

        except (botocore.exceptions.BotoCoreError, botocore.exceptions.InvalidRegionError) as e:
            logging.error(f"S3 client initialization failed: {e}")
            raise RuntimeError("Failed to initialize S3 client. Please check your AWS configuration.")

    def generate_presigned_url(self, s3_key: str, method: str = "get_object", expires_in: int = 3600, content_type=None, bucket=settings.S3_BUCKET_NAME):
        """
        Returns a presigned URL for either GET or PUT (upload/download)
        """
        try:
            s3 = self.get_s3_client()
            logging.debug(f"Bucket = {bucket}")

            params = {
                "Bucket": bucket,
                "Key": s3_key,
            }

            if content_type and method == "put_object":
                params["ContentType"] = content_type

            return s3.generate_presigned_url(
                ClientMethod=method,
                Params=params,
                ExpiresIn=expires_in
            )
        except botocore.exceptions.BotoCoreError as e:
            logging.error(f"S3 URL generation failed: {e}")
            raise RuntimeError("Could not generate S3 URL. Please try again.")
