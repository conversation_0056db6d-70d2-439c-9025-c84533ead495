from functools import wraps
from users.models import User
from rest_framework.response import Response
from rest_framework import status
from common.exceptions import UserNotRegistered, UserOrganizationNotFound


def attach_user(view_func):
    @wraps(view_func)
    def wrapper(self, request, *args, **kwargs):
        decoded_token = getattr(request, 'decoded_token', None)

        if not decoded_token:
            return Response({
                'status': 'ERROR',
                'message': 'Unauthorized.'
            }, status=status.HTTP_401_UNAUTHORIZED)

        try:
            user = User.objects.select_related('organization').get(cognito_sub=decoded_token.get('sub'))
        except User.DoesNotExist:
            return Response({
                'status': 'ERROR',
                'message': 'User not found'
            }, status=status.HTTP_404_NOT_FOUND)

        request.db_user = user
        return view_func(self, request, *args, **kwargs)

    return wrapper

def handle_org_exceptions(view_func):
    @wraps(view_func)
    def _wrapped_view(*args, **kwargs):
        try:
            return view_func(*args, **kwargs)
        except UserNotRegistered as e:
            return Response({'status': 'ERROR', 'data': [], 'message': str(e)}, status=status.HTTP_403_FORBIDDEN)
        except UserOrganizationNotFound as e:
            return Response({'status': 'ERROR', 'data': [], 'message': str(e)}, status=status.HTTP_403_FORBIDDEN)
    return _wrapped_view
