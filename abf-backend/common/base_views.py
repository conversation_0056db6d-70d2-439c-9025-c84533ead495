from typing import Optional, <PERSON><PERSON>

from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView

from profiles.models import Organization


class OrgAwareAPIView(APIView):

    def get_user_org_or_response(self, request: Request) -> Tuple[Optional[Organization], Optional[Response]]:
        user = getattr(request, 'db_user', None)

        if not user:
            return None, Response({
                "status": "ERROR",
                "message": "User not found.",
                "data": None
            }, status=status.HTTP_401_UNAUTHORIZED)

        if not user.organization:
            return None, Response({
                "status": "ERROR",
                "message": "User does not belong to any organization.",
                "data": None
            }, status=status.HTTP_400_BAD_REQUEST)

        return user.organization, None


