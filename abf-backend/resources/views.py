from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .models import Resource
from .serializers import ResourceSerializer
import logging
from django.db import transaction

logger = logging.getLogger(__name__)

# class ResourceListCreateView(APIView):

#     def get(self, request):
#         try:
#             resources = Resource.objects.all()
#             serializer = ResourceSerializer(resources, many=True)
#             return Response(serializer.data)
#         except Exception as e:
#             logger.error(f"Error in ResourceListCreateView.get: {str(e)}", exc_info=True)
#             return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

#     def post(self, request):
#         try:
#             logger.debug(f"Request POST data: {dict(request.POST)}")
#             logger.debug(f"Request FILES: {dict(request.FILES)}")
#             resources_data = []
#             index = 0
#             while f'resources[{index}][title]' in request.POST:
#                 title = request.POST.get(f'resources[{index}][title]')
#                 description = request.POST.get(f'resources[{index}][description]', '')
#                 file = request.FILES.get(f'resources[{index}][file]')
#                 if not title:
#                     logger.error(f"Missing title for resource {index + 1}")
#                     return Response(
#                         {"error": f"Title is required for resource {index + 1}"},
#                         status=status.HTTP_400_BAD_REQUEST
#                     )
#                 if not file:
#                     logger.error(f"Missing file for resource {index + 1}")
#                     return Response(
#                         {"error": f"File is required for resource {index + 1}"},
#                         status=status.HTTP_400_BAD_REQUEST
#                     )
#                 resources_data.append({
#                     'title': title,
#                     'file_description': description,
#                     'file_uploaded': file
#                 })
#                 index += 1

#             if not resources_data:
#                 logger.error("No valid resources provided")
#                 return Response(
#                     {"error": "No valid resources provided"},
#                     status=status.HTTP_400_BAD_REQUEST
#                 )
#             if len(resources_data) > 100:
#                 logger.error("Too many resources provided")
#                 return Response(
#                     {"error": "Cannot upload more than 100 resources"},
#                     status=status.HTTP_400_BAD_REQUEST
#                 )

#             resources = []
#             with transaction.atomic():
#                 for data in resources_data:
#                     serializer = ResourceSerializer(data=data)
#                     if serializer.is_valid():
#                         resource = serializer.save()
#                         resources.append(resource)
#                     else:
#                         logger.error(f"Serializer errors: {serializer.errors}")
#                         return Response(
#                             serializer.errors,
#                             status=status.HTTP_400_BAD_REQUEST
#                         )

#             serializer = ResourceSerializer(resources, many=True)
#             logger.debug(f"Created resources: {serializer.data}")
#             return Response(serializer.data, status=status.HTTP_201_CREATED)

#         except Exception as e:
#             logger.error(f"Error in ResourceListCreateView.post: {str(e)}", exc_info=True)
#             return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.exceptions import NotAuthenticated, PermissionDenied
from .models import Resource
from .serializers import ResourceSerializer
import logging
from django.db import transaction
from users.models import User
from django.core.exceptions import ObjectDoesNotExist

logger = logging.getLogger(__name__)

class ResourceListCreateView(APIView):

    def get_authenticated_user(self, request):
        decoded_token = getattr(request, 'decoded_token', None)
        if not decoded_token:
            raise NotAuthenticated("Token is missing or invalid.")
        try:
            return User.objects.select_related('organization').get(cognito_sub=decoded_token.get('sub'))
        except User.DoesNotExist:
            raise PermissionDenied("User not found or unauthorized.")
        except Exception as e:
            logger.error(f"Unexpected error during token decoding: {str(e)}")
            raise PermissionDenied("Authentication failed.")

    def get(self, request):
        try:
            user = self.get_authenticated_user(request)

            if user.type.code not in ['GRANT_MAKER', 'GRANTEE']:
                raise PermissionDenied("Only Grant Makers and Grantees are allowed to view this resource.")

            resources = Resource.objects.all()
            if not resources.exists():
                raise ObjectDoesNotExist("No resources found.")

            serializer = ResourceSerializer(resources, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except NotAuthenticated:
            return Response(
                {"error": "Authentication credentials were not provided or are invalid."},
                status=status.HTTP_401_UNAUTHORIZED
            )
        except PermissionDenied:
            return Response(
                {"error": "You do not have permission to access this resource."},
                status=status.HTTP_403_FORBIDDEN
            )
        except ObjectDoesNotExist:
            return Response(
                {"error": "No resources found."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Unhandled exception in ResourceListCreateView.get: {str(e)}", exc_info=True)
            return Response(
                {"error": "An internal server error occurred."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
    def post(self, request):
        try:
            user = self.get_authenticated_user(request)
            if user.type.code != 'GRANT_MAKER':
                logger.error(f"User attempted to create resources without GRANT_MAKER role")
                raise PermissionDenied("Only users with GRANT_MAKER role can create resources.")

            logger.debug(f"Request POST data: {dict(request.POST)}")
            logger.debug(f"Request FILES: {dict(request.FILES)}")
            resources_data = []
            index = 0
            while f'resources[{index}][title]' in request.POST:
                title = request.POST.get(f'resources[{index}][title]')
                description = request.POST.get(f'resources[{index}][description]', '')
                file = request.FILES.get(f'resources[{index}][file]')
                if not title:
                    logger.error(f"Missing title for resource {index + 1}")
                    return Response(
                        {"error": f"Title is required for resource {index + 1}"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                if not file:
                    logger.error(f"Missing file for resource {index + 1}")
                    return Response(
                        {"error": f"File is required for resource {index + 1}"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                resources_data.append({
                    'title': title,
                    'file_description': description,
                    'file_uploaded': file
                })
                index += 1

            if not resources_data:
                logger.error("No valid resources provided")
                return Response(
                    {"error": "No valid resources provided"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            if len(resources_data) > 100:
                logger.error("Too many resources provided")
                return Response(
                    {"error": "Cannot upload more than 100 resources"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            resources = []
            with transaction.atomic():
                for data in resources_data:
                    serializer = ResourceSerializer(data=data)
                    if serializer.is_valid():
                        resource = serializer.save()
                        resources.append(resource)
                    else:
                        return Response(
                            serializer.errors,
                            status=status.HTTP_400_BAD_REQUEST
                        )

            serializer = ResourceSerializer(resources, many=True)
            logger.debug(f"Created resources: {serializer.data}")
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except NotAuthenticated as e:
            logger.error(f"Authentication error: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_401_UNAUTHORIZED)
        except PermissionDenied as e:
            logger.error(f"Permission error: ")
            return Response({"error": str(e)}, status=status.HTTP_403_FORBIDDEN)
        except Exception as e:
            logger.error(f"Unexpected Error ", exc_info=True)
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
class ResourceDetailView(APIView):

    def get(self, request, pk):
        try:
            resource = Resource.objects.get(pk=pk)
            serializer = ResourceSerializer(resource)
            return Response(serializer.data)
        except Resource.DoesNotExist:
            return Response({"error": "Resource not found"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error in ResourceDetailView.get: {str(e)}", exc_info=True)
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk):
        try:
            resource = Resource.objects.get(pk=pk)
            
            serializer = ResourceSerializer(resource)
            resource_data = serializer.data
            
            resource.delete()
            
            return Response(
                {
                    "message": f"{resource_data.get('title', 'Resource')} deleted successfully",
                    "deleted_resource": resource_data
                }, 
                status=status.HTTP_200_OK
            )
            
        except Resource.DoesNotExist:
            return Response(
                {"error": "Resource not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error in ResourceDetailView.delete: {str(e)}", exc_info=True)
            return Response(
                {"error": "An error occurred while deleting the resource"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
