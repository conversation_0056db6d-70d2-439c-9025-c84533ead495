from django.db import models

class Resource(models.Model):
    title = models.CharField(max_length=255)
    file_name = models.CharField(max_length=255, blank=True, null=True)
    file_description = models.TextField(blank=True)
    file_uploaded = models.FileField(upload_to='Resources/', blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    uploaded_at = models.DateTimeField(auto_now_add=True, null=True)

    def __str__(self):
        return f"{self.title} ({self.file_name or 'No File'})"