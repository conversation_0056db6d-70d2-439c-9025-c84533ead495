# Generated by Django 4.2.20 on 2025-06-17 07:33

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Resource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=255)),
                ('file_name', models.CharField(blank=True, max_length=255, null=True)),
                ('file_description', models.TextField(blank=True)),
                ('file_uploaded', models.FileField(blank=True, null=True, upload_to='Resources/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, null=True)),
            ],
        ),
    ]
