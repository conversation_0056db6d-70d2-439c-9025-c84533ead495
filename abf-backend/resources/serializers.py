from rest_framework import serializers
from .models import Resource
from .utils import generate_presigned_url
import logging

logger = logging.getLogger(__name__)

class ResourceSerializer(serializers.ModelSerializer):
    attachments = serializers.SerializerMethodField(read_only=True)
    file_names = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Resource
        fields = ['id', 'title', 'file_name', 'file_description', 'file_uploaded', 'created_at', 'uploaded_at', 'attachments', 'file_names']
        read_only_fields = ['created_at', 'uploaded_at', 'file_name', 'attachments', 'file_names']

    def get_attachments(self, obj: Resource) -> list | None:
        if not obj.file_uploaded:
            return None
        try:
            url = generate_presigned_url(obj.file_uploaded.name)
            return [url] if url else []
        except Exception as e:
            logger.error(f"Error generating attachment URL for Resource {obj.id}: {str(e)}")
            return []

    def get_file_names(self, obj: Resource) -> list | None:
        return [obj.file_uploaded.name.split('/')[-1]] if obj.file_uploaded else []

    def create(self, validated_data: dict) -> Resource:
        try:
            file = validated_data.get('file_uploaded')
            if file:
                validated_data['file_name'] = file.name
            resource = Resource.objects.create(**validated_data)
            logger.debug(f"Created resource: {resource.title}, File: {resource.file_name}")
            return resource
        except Exception as e:
            logger.error(f"Error creating resource: {str(e)}")
            raise serializers.ValidationError(f"Failed to create resource: {str(e)}")

    def validate(self, data: dict) -> dict:
        if not data.get('title'):
            raise serializers.ValidationError({"title": "Title is required"})
        file = data.get('file_uploaded')
        if file and file.size > 50 * 1024 * 1024:  # 50MB limit
            raise serializers.ValidationError({"file_uploaded": f"File {file.name} is too large (max 50MB)"})
        return data

    def to_representation(self, instance):
        if isinstance(instance, list):
            return [super().to_representation(item) for item in instance]
        return super().to_representation(instance)