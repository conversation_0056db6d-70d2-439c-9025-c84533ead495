from rest_framework import viewsets

from users.permissions import IsGrantMaker
from .models import FundingRecord, FundingAllocation
from .serializers import FundingRecordSerializer, FundingAllocationSerializer
import logging
from django.db import transaction
from django.db.models import OuterRef

from rest_framework import generics, status
from rest_framework.exceptions import NotAuthenticated, PermissionDenied
from rest_framework.response import Response
from rest_framework.views import APIView

from common.decorators import handle_org_exceptions
from common.mixins.org_aware import OrgAwareMixin
from .models import Disbursement, Expense, Grant
from .serializers import (
    DisbursementSerializer,
    ExpenseSerializer,
    UnifiedExpenseSerializer,
    GrantSerializer
)

from users.models import User
from django.db import models


logger = logging.getLogger(__name__)

def get_authenticated_user(request):
    decoded_token = getattr(request, 'decoded_token', None)
    if not decoded_token:
        raise NotAuthenticated("Token is missing or invalid.")
    try:
        return User.objects.select_related('organization').get(cognito_sub=decoded_token.get('sub'))
    except User.DoesNotExist:
        raise PermissionDenied("User not found or unauthorized.")
    except Exception as e:
        logger.error(f"Unexpected error during token decoding: {str(e)}")
        raise PermissionDenied("Authentication failed.")

class DisbursementListView(generics.ListCreateAPIView):
    queryset = Disbursement.objects.all()
    serializer_class = DisbursementSerializer

class DisbursementDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Disbursement.objects.all()
    serializer_class = DisbursementSerializer

class ExpenseListView(generics.ListAPIView):
    queryset = Expense.objects.all()
    serializer_class = ExpenseSerializer

    def get_queryset(self):
        try:
            user = get_authenticated_user(self.request)
            grant_ids = Grant.objects.filter(
                organization_id=user.organization.id
            ).values_list('id', flat=True)

            queryset = Expense.objects.filter(grant_id__in = grant_ids )
            return queryset
        except Exception as e:
            logger.error(f"ExpenseListView error: {str(e)}")
            return Expense.objects.none()

class ExpenseDetailView(generics.RetrieveAPIView):
    serializer_class = ExpenseSerializer

    def get_queryset(self):
        try:
            user = get_authenticated_user(self.request)
            if not user:
                logger.error("User is not authenticated.")
                return Expense.objects.none()

            if not user.organization:
                logger.error("User does not belong to any organization.")
                return Expense.objects.none()

            grant_ids = Grant.objects.filter(
                organization_id=user.organization.id
            ).values_list('id', flat=True)

            if not grant_ids:
                logger.warning("No grants found for the user's organization.")
                return Expense.objects.none()

            queryset = Expense.objects.filter(grant_id__in=grant_ids)
            return queryset
        except Exception as e:
            logger.error(f"ExpenseDetailView error: {str(e)}")
            return Expense.objects.none()

    def get(self, request, *args, **kwargs):
        try:
            user = get_authenticated_user(request)
            if not user:
                return Response(
                    {"error": "Authentication required."},
                    status=status.HTTP_401_UNAUTHORIZED
                )

            if not user.organization:
                return Response(
                    {"error": "User does not belong to any organization."},
                    status=status.HTTP_403_FORBIDDEN
                )
            expense = self.get_object()
            grant_id = expense.grant_id

            if not Grant.objects.filter(id=grant_id, organization_id=user.organization.id).exists():
                return Response(
                    {"error": "You do not have permission to access this expense."},
                    status=status.HTTP_403_FORBIDDEN
                )
            serializer = self.get_serializer(expense)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Expense.DoesNotExist:
            logger.error("Expense not found.")
            return Response(
                {"error": "Expense not found."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"ExpenseDetailView error: {str(e)}")
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class GrantListAPIView(generics.ListAPIView):
    serializer_class = GrantSerializer

    def get_queryset(self):
        try:
            user = get_authenticated_user(self.request)

            # Exclude grants that have any associated expenses
            grants_without_expenses = Grant.objects.filter(
                organization_id=user.organization.id
            ).exclude(
                id__in=Expense.objects.filter(grant=OuterRef('pk')).values('grant')
            )
            return grants_without_expenses

        except PermissionDenied:
            logger.warning("Permission denied:")
            return Grant.objects.none()
        except Exception as e:
            logger.error(f"Grant list fetch failed: {e}")
            return Grant.objects.none()


class CreateExpenseView(APIView):
    def post(self, request):
        user = get_authenticated_user(request)
        if not user:
            return Response(
                {"error": "Authentication required."},
                status=status.HTTP_401_UNAUTHORIZED
            )
        if not user.organization:
            logger.error("User does not belong to any organization.")
            return Response(
                {"error": "User is not associated with any organization."},
                status=status.HTTP_403_FORBIDDEN
            )
        if not isinstance(request.data, list):
            logger.warning("Invalid request data: not a list")
            return Response(
                {"error": "Request data must be a list of expenses."},
                status=status.HTTP_400_BAD_REQUEST
            )
        grant_ids = Grant.objects.filter(
            organization_id=user.organization.id
        ).values_list('id', flat=True)

        if not grant_ids:
            logger.error("No grants found for the user's organization.")
            return Response(
                {"error": "No valid grants available for this organization."},
                status=status.HTTP_403_FORBIDDEN
            )
        errors = []
        valid_expenses = []
        for index, expense_data in enumerate(request.data):
            grant_id = expense_data.get("grant")
            if grant_id not in grant_ids:
                errors.append({
                    "row_index": index + 1,
                    "errors": {"grant": ["Invalid grant ID for your organization."]}
                })
                continue
            serializer = UnifiedExpenseSerializer(data=expense_data)
            if serializer.is_valid():
                valid_expenses.append(serializer)
            else:
                errors.append({
                    "row_index": index + 1,
                    "errors": serializer.errors
                })
        if errors:
            logger.warning(f"Validation errors in expense submission: {errors}")
            return Response(errors, status=status.HTTP_400_BAD_REQUEST)
        try:
            for serializer in valid_expenses:
                serializer.save()
            logger.info("Expenses created successfully")
            return Response(
                {"message": "Expenses created successfully."},
                status=status.HTTP_201_CREATED
            )
        except Exception as e:
            logger.error(f"Failed to save expenses: {str(e)}")
            return Response(
                {
                    "error": f"Failed to save expenses: {str(e)}",
                    "row_index": None
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ExpenseBulkUpdateView(generics.GenericAPIView):
    serializer_class = UnifiedExpenseSerializer
    queryset = Expense.objects.all()

    def put(self, request, *args, **kwargs):
        user = get_authenticated_user(request)
        if not user:
            return Response({"error": "Authentication required."}, status=status.HTTP_401_UNAUTHORIZED)

        if not user.organization:
            return Response({"error": "User is not associated with any organization."}, status=status.HTTP_403_FORBIDDEN)

        if user.type.code == 'GRANT_MAKER':
            grant_ids = Grant.objects.filter(grant_maker_organization_id=user.organization.id).values_list('id', flat=True)
        elif user.type.code == 'GRANTEE':
            grant_ids = Grant.objects.filter(organization_id=user.organization.id).values_list('id', flat=True)
        else:
            return Response({"error": "Unauthorized user type."}, status=status.HTTP_403_FORBIDDEN)

        if not isinstance(request.data, list):
            return Response({"error": "Request data must be a list of expense updates"}, status=status.HTTP_400_BAD_REQUEST)

        updated_instances = []
        errors = []

        try:
            with transaction.atomic():
                for index, item in enumerate(request.data):
                    expense_id = item.get('id')
                    if not expense_id:
                        errors.append({"row_index": index + 1, "errors": {"id": ["Expense ID is required."]}})
                        continue
                    try:
                        instance = Expense.objects.get(id=expense_id)
                    except Expense.DoesNotExist:
                        errors.append({"row_index": index + 1, "errors": {"id": [f"Expense with ID {expense_id} does not exist"]}})
                        continue
                    if instance.grant_id not in grant_ids:
                        errors.append({"row_index": index + 1, "errors": {"grant": ["You are not authorized to update this expense"]}})
                        continue
                    serializer = self.get_serializer(instance, data=item, partial=True)
                    if serializer.is_valid():
                        serializer.save()
                        updated_instances.append(serializer.data)
                    else:
                        errors.append({"row_index": index + 1, "errors": serializer.errors})

                if errors:
                    transaction.set_rollback(True)
                    return Response(errors, status=status.HTTP_400_BAD_REQUEST)

                return Response({"message": "Expenses updated successfully."}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Bulk update failed: {str(e)}")
            return Response({"error": f"Internal server error: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ExpensesByGrantView(OrgAwareMixin, APIView):
    @handle_org_exceptions
    def get(self, request, grant_id):
        user_org = self.get_user_org(request)

        if not Grant.objects.filter(
                id=grant_id,
        ).filter(
            models.Q(organization_id=user_org.id) |
            models.Q(grant_maker_organization_id=user_org.id)
        ).exists():
            return Response({
                "status": "FORBIDDEN",
                "message": "You do not have access to this grant's expenses."
            }, status=status.HTTP_403_FORBIDDEN)

        expenses = Expense.objects.filter(grant_id=grant_id)
        serializer = ExpenseSerializer(expenses, many=True)
        return Response({
            "status": "SUCCESS",
            "data": serializer.data,
            "message": "Data fetched successfully"
        }, status=status.HTTP_200_OK)


class FundingAllocationViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    serializer_class = FundingAllocationSerializer
    permission_classes = [IsGrantMaker]

    def get_queryset(self):
        user_org = self.get_user_org(self.request)
        return FundingAllocation.objects.filter(
            grant__grant_maker_organization=user_org
        )

    @handle_org_exceptions
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'status': 'SUCCESS',
            'data': serializer.data,
            'message': 'Funding allocations fetched successfully'
        }, status=status.HTTP_200_OK)

    @handle_org_exceptions
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'status': 'SUCCESS',
                'data': serializer.data,
                'message': 'Funding allocation created successfully'
            }, status=status.HTTP_201_CREATED)
        return Response({
            'status': 'ERROR',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    @handle_org_exceptions
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @handle_org_exceptions
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'status': 'SUCCESS',
                'data': serializer.data,
                'message': 'Funding allocation updated successfully'
            }, status=status.HTTP_200_OK)

        return Response({
            'status': 'ERROR',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

class FundingRecordViewSet(OrgAwareMixin, viewsets.ModelViewSet):
    serializer_class = FundingRecordSerializer
    permission_classes = [IsGrantMaker]

    def get_queryset(self):
        return FundingRecord.objects.all()

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'status': 'SUCCESS',
            'data': serializer.data,
            'message': 'Funding records fetched successfully'
        }, status=status.HTTP_200_OK)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'status': 'SUCCESS',
                'data': serializer.data,
                'message': 'Funding record created successfully'
            }, status=status.HTTP_201_CREATED)

        return Response({
            'status': 'ERROR',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'status': 'SUCCESS',
                'data': serializer.data,
                'message': 'Funding record updated successfully'
            }, status=status.HTTP_200_OK)

        return Response({
            'status': 'ERROR',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
class ExpenseStatusUpdateView(OrgAwareMixin, APIView):
    """
    Update expense status (approve/reject) - for grantmakers
    """
    permission_classes = [IsGrantMaker]

    @handle_org_exceptions
    def post(self, request, pk):
        user = get_authenticated_user(request)
        if not user:
            return Response(
                {"error": "Authentication required."},
                status=status.HTTP_401_UNAUTHORIZED
            )

        # Check if user is a grantmaker
        if user.type.code != 'GRANT_MAKER':
            return Response(
                {"error": "Only grantmakers can update expense status."},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            expense = Expense.objects.get(id=pk)
        except Expense.DoesNotExist:
            return Response(
                {"error": "Expense not found."},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if grantmaker has access to this expense
        user_org = self.get_user_org(request)
        if not Grant.objects.filter(
            id=expense.grant_id,
            grant_maker_organization_id=user_org.id
        ).exists():
            return Response(
                {"error": "You do not have permission to update this expense."},
                status=status.HTTP_403_FORBIDDEN
            )

        new_status = request.data.get('status')
        notes = request.data.get('notes', '')

        if new_status not in ['approved', 'rejected']:
            return Response(
                {"error": "Status must be 'approved' or 'rejected'."},
                status=status.HTTP_400_BAD_REQUEST
            )

        if new_status == 'rejected' and not notes.strip():
            return Response(
                {"error": "Notes are required when rejecting an expense."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update expense status
        expense.status = new_status
        if new_status == 'rejected':
            # Store rejection notes in the dedicated field
            expense.rejection_notes = notes
        else:
            # Clear rejection notes if approved
            expense.rejection_notes = ''

        expense.save()

        serializer = ExpenseSerializer(expense)
        return Response({
            "status": "SUCCESS",
            "data": serializer.data,
            "message": f"Expense {new_status} successfully"
        }, status=status.HTTP_200_OK)


class GranteeExpensesView(OrgAwareMixin, APIView):
    """
    Get all expenses for a specific grantee - for grantmakers
    """
    permission_classes = [IsGrantMaker]

    @handle_org_exceptions
    def get(self, request, grantee_id):
        user = get_authenticated_user(request)
        if not user:
            return Response(
                {"error": "Authentication required."},
                status=status.HTTP_401_UNAUTHORIZED
            )

        # Check if user is a grantmaker
        if user.type.code != 'GRANT_MAKER':
            return Response(
                {"error": "Only grantmakers can view grantee expenses."},
                status=status.HTTP_403_FORBIDDEN
            )

        user_org = self.get_user_org(request)

        # Get all grants for this grantee that belong to the grantmaker's organization
        grants = Grant.objects.filter(
            organization_id=grantee_id,
            grant_maker_organization_id=user_org.id
        )

        if not grants.exists():
            return Response(
                {"error": "No grants found for this grantee in your organization."},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get all expenses for these grants
        expenses = Expense.objects.filter(grant__in=grants).order_by('-expense_date')

        # Filter by status if provided
        status_filter = request.query_params.get('status')
        if status_filter:
            expenses = expenses.filter(status=status_filter)

        serializer = ExpenseSerializer(expenses, many=True)
        return Response({
            "status": "SUCCESS",
            "data": serializer.data,
            "message": "Expenses fetched successfully"
        }, status=status.HTTP_200_OK)


class QuarterlyTotalsView(OrgAwareMixin, APIView):

    permission_classes = [IsGrantMaker]

    @handle_org_exceptions
    def get(self, request):
        user = get_authenticated_user(request)
        if not user:
            return Response(
                {"error": "Authentication required."},
                status=status.HTTP_401_UNAUTHORIZED
            )

        user_org = self.get_user_org(request)

        grant_filter = request.query_params.get('grant')
        quarter_filter = request.query_params.get('quarter')
        year_filter = request.query_params.get('year', '2025')

        def normalize_quarter(quarter):
            quarter_map = {
                'Q1': 'Apr-Jun',
                'Q2': 'Jul-Sep',
                'Q3': 'Oct-Dec',
                'Q4': 'Jan-Mar'
            }
            return quarter_map.get(quarter, quarter)

        if quarter_filter:
            quarter_filter = normalize_quarter(quarter_filter)

        expenses_query = Expense.objects.filter(grant__organization_id=user_org.id)

        if grant_filter and grant_filter not in ['all', 'All']:
            expenses_query = expenses_query.filter(grant_id=grant_filter)

        expenses = expenses_query.all()

        quarterly_totals = {
            'Apr-Jun': {'budget': 0, 'actual': 0},
            'Jul-Sep': {'budget': 0, 'actual': 0},
            'Oct-Dec': {'budget': 0, 'actual': 0},
            'Jan-Mar': {'budget': 0, 'actual': 0}
        }

        for expense in expenses:
            if not quarter_filter or quarter_filter == 'All' or quarter_filter == 'Apr-Jun':
                quarterly_totals['Apr-Jun']['budget'] += float(expense.budget_q1 or 0)
                quarterly_totals['Apr-Jun']['actual'] += float(expense.actual_q1 or 0)

            if not quarter_filter or quarter_filter == 'All' or quarter_filter == 'Jul-Sep':
                quarterly_totals['Jul-Sep']['budget'] += float(expense.budget_q2 or 0)
                quarterly_totals['Jul-Sep']['actual'] += float(expense.actual_q2 or 0)

            if not quarter_filter or quarter_filter == 'All' or quarter_filter == 'Oct-Dec':
                quarterly_totals['Oct-Dec']['budget'] += float(expense.budget_q3 or 0)
                quarterly_totals['Oct-Dec']['actual'] += float(expense.actual_q3 or 0)

            if not quarter_filter or quarter_filter == 'All' or quarter_filter == 'Jan-Mar':
                quarterly_totals['Jan-Mar']['budget'] += float(expense.budget_q4 or 0)
                quarterly_totals['Jan-Mar']['actual'] += float(expense.actual_q4 or 0)

        total_budget = sum(quarter['budget'] for quarter in quarterly_totals.values())
        total_actual = sum(quarter['actual'] for quarter in quarterly_totals.values())
        remaining_balance = total_budget - total_actual
        disbursed_amount = total_budget * 0.8

        budget_overview = {
            'total_budget': total_budget,
            'total_actual': total_actual,
            'remaining_balance': remaining_balance,
            'disbursed_amount': disbursed_amount
        }

        return Response({
            "status": "SUCCESS",
            "data": {
                "quarterly_totals": quarterly_totals,
                "budget_overview": budget_overview,
                "chart_data": [
                    {"quarter": "Apr-Jun", "budget": quarterly_totals['Apr-Jun']['budget'], "actual": quarterly_totals['Apr-Jun']['actual']},
                    {"quarter": "Jul-Sep", "budget": quarterly_totals['Jul-Sep']['budget'], "actual": quarterly_totals['Jul-Sep']['actual']},
                    {"quarter": "Oct-Dec", "budget": quarterly_totals['Oct-Dec']['budget'], "actual": quarterly_totals['Oct-Dec']['actual']},
                    {"quarter": "Jan-Mar", "budget": quarterly_totals['Jan-Mar']['budget'], "actual": quarterly_totals['Jan-Mar']['actual']}
                ]
            },
            "message": "Quarterly totals fetched successfully"
        }, status=status.HTTP_200_OK)
