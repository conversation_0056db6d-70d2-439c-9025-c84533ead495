# Generated by Django 4.2.20 on 2025-06-17 07:33

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Disbursement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scheduled_payment_date', models.DateField()),
                ('scheduled_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('received_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('pending_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('payment_received_date', models.DateField(blank=True, null=True)),
            ],
            options={
                'verbose_name_plural': 'Disbursements',
            },
        ),
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('particulars', models.CharField(max_length=255)),
                ('main_header', models.CharField(blank=True, default='', max_length=255)),
                ('sub_header', models.CharField(blank=True, default='', max_length=255)),
                ('units', models.DecimalField(decimal_places=2, max_digits=15)),
                ('frequency', models.DecimalField(decimal_places=2, max_digits=15)),
                ('cost_per_unit', models.DecimalField(decimal_places=2, max_digits=15)),
                ('activity_description', models.TextField(blank=True, default='')),
                ('budget_q1', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('budget_q2', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('budget_q3', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('budget_q4', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('actual_q1', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('actual_q2', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('actual_q3', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('actual_q4', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('total_budget', models.DecimalField(decimal_places=2, max_digits=15)),
                ('total_grant_budget', models.DecimalField(decimal_places=2, max_digits=15)),
                ('total_actual', models.DecimalField(decimal_places=2, max_digits=15)),
                ('remarks', models.TextField(blank=True, default='')),
                ('rejection_notes', models.TextField(blank=True, default='')),
                ('expense_date', models.DateField(default=django.utils.timezone.now)),
                ('source_type', models.CharField(default='excel', max_length=50)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('is_frozen', models.BooleanField(default=False)),
                ('metadata', models.JSONField(default=dict)),
                ('file1', models.FileField(blank=True, null=True, upload_to='expense_files/')),
                ('file2', models.FileField(blank=True, null=True, upload_to='expense_files/')),
                ('file3', models.FileField(blank=True, null=True, upload_to='expense_files/')),
                ('file4', models.FileField(blank=True, null=True, upload_to='expense_files/')),
            ],
        ),
        migrations.CreateModel(
            name='FundingAllocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount_allocated', models.DecimalField(decimal_places=2, max_digits=15)),
                ('notes', models.TextField(blank=True, default='')),
            ],
        ),
        migrations.CreateModel(
            name='FundingRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15)),
                ('date_received', models.DateField()),
                ('notes', models.TextField(blank=True, default='')),
            ],
        ),
    ]
