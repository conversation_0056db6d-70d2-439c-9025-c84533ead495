# Generated by Django 4.2.20 on 2025-06-17 07:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('profiles', '0001_initial'),
        ('funding', '0001_initial'),
        ('grants', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='fundingrecord',
            name='funding_entity',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='profiles.organization'),
        ),
        migrations.AddField(
            model_name='fundingallocation',
            name='funding_entity',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='profiles.organization'),
        ),
        migrations.AddField(
            model_name='fundingallocation',
            name='grant',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='grants.grant'),
        ),
        migrations.AddField(
            model_name='expense',
            name='grant',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expenses', to='grants.grant'),
        ),
        migrations.AddField(
            model_name='disbursement',
            name='grant',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='grants.grant'),
        ),
        migrations.AddIndex(
            model_name='fundingrecord',
            index=models.Index(fields=['funding_entity', 'date_received'], name='funding_fun_funding_4055a3_idx'),
        ),
        migrations.AddIndex(
            model_name='fundingallocation',
            index=models.Index(fields=['grant', 'funding_entity'], name='funding_fun_grant_i_e3db3b_idx'),
        ),
        migrations.AddIndex(
            model_name='disbursement',
            index=models.Index(fields=['grant'], name='funding_dis_grant_i_4287c9_idx'),
        ),
        migrations.AddIndex(
            model_name='disbursement',
            index=models.Index(fields=['scheduled_payment_date'], name='funding_dis_schedul_012f7f_idx'),
        ),
        migrations.AddIndex(
            model_name='disbursement',
            index=models.Index(fields=['payment_received_date'], name='funding_dis_payment_7edb92_idx'),
        ),
    ]
