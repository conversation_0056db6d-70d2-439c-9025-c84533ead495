from datetime import datetime
from decimal import Decimal

from common.decorators import handle_org_exceptions
from common.mixins.org_aware import OrgAwareMixin
from profiles.models import Organization
from .utils import generate_presigned_url, get_available_funds
import logging
from datetime import date
from rest_framework import serializers
from .models import Expense, Grant, Disbursement, FundingRecord, FundingAllocation
from users.models import User
from django.db import models

logger = logging.getLogger(__name__)

class DisbursementSerializer(serializers.ModelSerializer):
    class Meta:
        model = Disbursement
        fields = '__all__'

    def validate(self, data):
        if data.get('received_amount') and data['received_amount'] > data['scheduled_amount']:
            raise serializers.ValidationError({
                'received_amount': "Received amount cannot be greater than scheduled amount."
            })
        return data

class ExpenseSerializer(serializers.ModelSerializer):
    grant_name = serializers.CharField(source='grant.grant_name', read_only=True)
    class Meta:
        model = Expense
        fields = '__all__'
        extra_fields = ['grant_name']


class UnifiedExpenseSerializer(serializers.ModelSerializer):
    grant = serializers.PrimaryKeyRelatedField(queryset=Grant.objects.all())
    units = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    cost_per_unit = serializers.DecimalField(max_digits=10, decimal_places=2, coerce_to_string=False)
    budget_q1 = serializers.DecimalField(max_digits=12, decimal_places=2, coerce_to_string=False, default=0)
    budget_q2 = serializers.DecimalField(max_digits=12, decimal_places=2, coerce_to_string=False, default=0)
    budget_q3 = serializers.DecimalField(max_digits=12, decimal_places=2, coerce_to_string=False, default=0)
    budget_q4 = serializers.DecimalField(max_digits=12, decimal_places=2, coerce_to_string=False, default=0)
    actual_q1 = serializers.DecimalField(max_digits=12, decimal_places=2, coerce_to_string=False, default=0)
    actual_q2 = serializers.DecimalField(max_digits=12, decimal_places=2, coerce_to_string=False, default=0)
    actual_q3 = serializers.DecimalField(max_digits=12, decimal_places=2, coerce_to_string=False, default=0)
    actual_q4 = serializers.DecimalField(max_digits=12, decimal_places=2, coerce_to_string=False, default=0)
    metadata = serializers.JSONField(default=dict, required=False)

    class Meta:
        model = Expense
        fields = [
            'id', 'particulars', 'main_header', 'sub_header', 'units', 'frequency',
            'cost_per_unit', 'activity_description', 'budget_q1', 'budget_q2',
            'budget_q3', 'budget_q4', 'actual_q1', 'actual_q2', 'actual_q3',
            'actual_q4', 'grant', 'remarks', 'rejection_notes', 'metadata', 'file1', 'file2', 'file3', 'file4'
        ]

    def get_attachments(self, obj):
        try:
            if obj.file:
                url = generate_presigned_url(obj.file.name)
                return [url] if url else None
            return None
        except Exception as e:
            return None
    def validate_metadata(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Metadata must be a dictionary.")

        edit_history = value.get('edit_history', [])
        if not isinstance(edit_history, list):
            raise serializers.ValidationError("Metadata.edit_history must be a list.")

        required_fields = ['row_id', 'field', 'old_value', 'new_value', 'timestamp']
        for log in edit_history:
            if not isinstance(log, dict):
                raise serializers.ValidationError("Each edit history log must be a dictionary.")
            if not all(field in log for field in required_fields):
                raise serializers.ValidationError(
                    f"Each edit history log must contain {', '.join(required_fields)}."
                )
            try:
                from datetime import datetime
                datetime.fromisoformat(log['timestamp'].replace('Z', '+00:00'))
            except ValueError:
                raise serializers.ValidationError(
                    f"Invalid timestamp format in edit history: {log['timestamp']}"
                )

        return value

    def validate_grant(self, value):

        if not Grant.objects.filter(id=value.id).exists():
            logger.error(f"Invalid grant ID: {value.id}")
            raise serializers.ValidationError("Selected grant does not exist.")
        return value

    def validate_particulars(self, value):
        if not value or value.strip() == '':
            raise serializers.ValidationError("Particulars is required and cannot be empty.")
        return value.strip()

    def validate_units(self, value):
        if value <= 0:
            raise serializers.ValidationError("Units must be a positive number.")
        return value

    def validate_cost_per_unit(self, value):
        if value < 0:
            raise serializers.ValidationError("Cost per unit cannot be negative.")
        return value

    def validate_budget_q1(self, value):
        if value < 0:
            raise serializers.ValidationError("Budget Q1 cannot be negative.")
        return value

    def validate_budget_q2(self, value):
        if value < 0:
            raise serializers.ValidationError("Budget Q2 cannot be negative.")
        return value

    def validate_budget_q3(self, value):
        if value < 0:
            raise serializers.ValidationError("Budget Q3 cannot be negative.")
        return value

    def validate_budget_q4(self, value):
        if value < 0:
            raise serializers.ValidationError("Budget Q4 cannot be negative.")
        return value

    def validate_actual_q1(self, value):
        if value < 0:
            raise serializers.ValidationError("Actual Q1 cannot be negative.")
        return value

    def validate_actual_q2(self, value):
        """Ensure actual_q2 is non-negative."""
        if value < 0:
            raise serializers.ValidationError("Actual Q2 cannot be negative.")
        return value

    def validate_actual_q3(self, value):
        if value < 0:
            raise serializers.ValidationError("Actual Q3 cannot be negative.")
        return value

    def validate_actual_q4(self, value):
        if value < 0:
            raise serializers.ValidationError("Actual Q4 cannot be negative.")
        return value

    def validate_remarks(self, value):
        if value is None:
            return ''
        return value.strip()

    def validate(self, data):
        errors = {}
        total_budget = (
            data.get('budget_q1', 0) +
            data.get('budget_q2', 0) +
            data.get('budget_q3', 0) +
            data.get('budget_q4', 0)
        )

        total_actual = (
                data.get('actual_q1', 0) +
                data.get('actual_q2', 0) +
                data.get('actual_q3', 0) +
                data.get('actual_q4', 0)
        )

        quarter_pairs = [
            ('budget_q1', 'actual_q1', 'Q1'),
            ('budget_q2', 'actual_q2', 'Q2'),
            ('budget_q3', 'actual_q3', 'Q3'),
            ('budget_q4', 'actual_q4', 'Q4'),
        ]

        def get_financial_quarter(date=None):
            if date is None:
                date = datetime.now()
            month = date.month

            # Financial year in India: April to March
            if 4 <= month <= 6:
                quarter = "Q1"
            elif 7 <= month <= 9:
                quarter = "Q2"
            elif 10 <= month <= 12:
                quarter = "Q3"
            else:
                quarter = "Q4"  # Jan to Mar

            return quarter

        current_quarter = get_financial_quarter()
        data['status'] = 'approved' # Default case
        for budget_quarter, actual_quarter, quarter in quarter_pairs:
            budget = data.get(budget_quarter, 0)
            actual = data.get(actual_quarter, 0)

            # Only check for overspent and underspent for current quarter, for future quarters make sure
            # only 0 is present as you cannot report actuals for future quarters
            if current_quarter == quarter:
                if actual > budget * Decimal("1.1"):
                    data['status'] = 'pending'

                    if not data.get('remarks'):
                        errors[actual_quarter] = f"Actuals exceed budget by more than 10%."
                        errors['remarks'] = "Remarks are required for overspending."

                elif actual < budget * Decimal("0.5"):
                    data['status'] = 'pending'

                    if not data.get('remarks'):
                        errors[actual_quarter] = f"Actuals are less than 50% of budget"
                        errors['remarks'] = "Remarks are required for underspending."

            elif quarter > current_quarter:
                if actual > 0:
                    errors[actual_quarter] = f"Actuals cannot be entered for future quarters."

        # Total grants budget = units x cost per unit
        total_grant_budget = data.get('units', 1) * data.get('cost_per_unit', 1) * data.get('frequency', 1)

        if total_budget != total_grant_budget:
            errors['remarks'] = "Total Budget should match the Grant Budget"

        if errors:
            raise serializers.ValidationError(errors)

        data['total_budget'] = total_budget
        data['total_grant_budget'] = total_grant_budget
        data['total_actual'] = total_actual
        data['expense_date'] = data.get('expense_date', date.today())
        data['source_type'] = 'excel'
        data['is_frozen'] = False

        return data

    def create(self, validated_data):
        """Create a new Expense instance."""
        metadata = validated_data.pop('metadata', {'edit_history': []})
        expense = Expense.objects.create(**validated_data, metadata=metadata)
        logger.info(f"Created expense {expense.id} with metadata: {metadata}")
        return expense

    def update(self, instance, validated_data):
        # Update all fields without validation
        for key, value in validated_data.items():
            setattr(instance, key, value)
        instance.save()
        logger.info(f"Updated expense {instance.id} with data: {validated_data}")
        return instance

class GrantSerializer(serializers.ModelSerializer):
    user_count = serializers.SerializerMethodField()

    class Meta:
        model = Grant
        fields = [
            'id', 'grant_name', 'start_date', 'end_date', 'grant_purpose',
            'annual_budget', 'funding_sources', 'organization',
            'grant_maker_organization', 'created_at', 'updated_at',
            'user_count'
        ]

    def get_user_count(self, obj):
        return User.objects.filter(organization=obj.organization).count()

class FundingRecordSerializer(OrgAwareMixin, serializers.ModelSerializer):
    funding_entity_name = serializers.CharField(source='funding_entity.organization_name', read_only=True)

    class Meta:
        model = FundingRecord
        fields = ['id', 'funding_entity', 'funding_entity_name', 'amount', 'date_received', 'notes']

    def validate_funding_entity(self, value):
        request = self.context.get('request')
        user_organization = self.get_user_org(request)

        if value.grant_maker_organization != user_organization:
            raise serializers.ValidationError("This funding entity does not belong to your organization.")
        return value


class FundingAllocationSerializer(OrgAwareMixin, serializers.ModelSerializer):
    funding_entity_name = serializers.CharField(source='funding_entity.organization_name', read_only=True)
    grant_name = serializers.CharField(source='grant.grant_name', read_only=True)
    grant_recipient_organization = serializers.CharField(source='grant.organization', read_only=True)
    grant_recipient_organization_name = serializers.CharField(source='grant.organization.organization_name', read_only=True)

    class Meta:
        model = FundingAllocation
        fields = [
            'id', 'grant', 'grant_name', 'grant_recipient_organization', 'grant_recipient_organization_name',
            'funding_entity', 'funding_entity_name', 'amount_allocated', 'notes'
        ]
        read_only_fields = ['amount_allocated']

    def validate_funding_entity(self, funding_entity):
        request = self.context.get('request')
        user_organization = self.get_user_org(request)

        if funding_entity.organization_function_type.code != 'FUNDING_ENTITY':
            raise serializers.ValidationError("Selected organization is not designated as a funding entity.")
        if funding_entity.grant_maker_organization != user_organization:
            raise serializers.ValidationError("This funding entity does not belong to your organization.")
        return funding_entity

    @handle_org_exceptions
    def validate(self, data):
        funding_entity = data.get("funding_entity")
        grant = data.get('grant')
        annual_budget = grant.annual_budget

        available_amount = get_available_funds(funding_entity.id)

        if annual_budget >= available_amount:
            raise serializers.ValidationError({
                'amount_allocated': 'Allocated amount exceeds available funds.'
            })
        # Temporarily store for use in create()
        self._validated_annual_budget = annual_budget

        return data

    def create(self, validated_data):
        # Use the annual budget stored during validation as the allocated amount
        validated_data["amount_allocated"] = getattr(self, "_validated_annual_budget", 0)
        return super().create(validated_data)
