from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import (
    ExpenseListView, ExpenseDetailView, CreateExpenseView,
    ExpenseBulkUpdateView, ExpenseStatusUpdateView, GrantListAPIView,
    ExpensesByGrantView, QuarterlyTotalsView, FundingAllocationViewSet,
    FundingRecordViewSet, GranteeExpensesView
)

router = DefaultRouter()

router.register(r'funding-records', FundingRecordViewSet, basename='funding-record')
router.register(r'funding-allocations', FundingAllocationViewSet, basename='funding-allocation')

urlpatterns = [
    path('v1/expenses/list/', ExpenseListView.as_view(), name='expense-list'),
    path('v1/expenses/<int:pk>/', ExpenseDetailView.as_view(), name='expense-detail'),
    path('v1/expenses/create/', CreateExpenseView.as_view(), name='expense-create'),
    path('v1/expenses/bulk_update/', ExpenseBulkUpdateView.as_view(), name='bulk_update_expenses'),
    path('v1/expenses/<int:pk>/update-status/', ExpenseStatusUpdateView.as_view(), name='expense-status-update'),
    path('v1/expenses/grants/', GrantListAPIView.as_view(), name='grants'),
    path('v1/expenses/quarterly-totals/', QuarterlyTotalsView.as_view(), name='quarterly-totals'),
    # TODO: Add trailing /
    path('v1/grants/<int:grant_id>/expenses', ExpensesByGrantView.as_view()),
    path('v1/', include(router.urls)),
    path('v1/grantee/<int:grantee_id>/expenses/', GranteeExpensesView.as_view(), name='grantee-expenses'),
]

urlpatterns += router.urls

