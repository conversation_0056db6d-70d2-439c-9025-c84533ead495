import boto3
from botocore.exceptions import NoCredentialsError, ClientError
from django.conf import settings
from django.db.models import Sum
import logging

from funding.models import FundingRecord, FundingAllocation

logger = logging.getLogger(__name__)

def generate_presigned_url(file=None, expires_in: int = 3600):
    if not file:
        logger.error("generate_presigned_url was called with no file")
        return None

    AWS_ACCESS_KEY_ID = settings.AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY = settings.AWS_SECRET_ACCESS_KEY
    AWS_REGION = getattr(settings, "AWS_S3_REGION_NAME", "ap-south-1")
    BUCKET_NAME = settings.AWS_STORAGE_BUCKET_NAME

    if not BUCKET_NAME:
        logger.error("AWS_STORAGE_BUCKET_NAME is not set in settings.")
        return None

    try:
        s3 = boto3.client(
            's3',
            region_name=AWS_REGION,
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY
        )

        presigned_url = s3.generate_presigned_url(
            'get_object',
            Params={'Bucket': BUCKET_NAME, 'Key': file},
            ExpiresIn=expires_in
        )
        return presigned_url
    except (NoCredentialsError, ClientError) as e:
        logger.exception("Error generating presigned URL for file: %s", file)
        return None

def get_available_funds(funding_entity_id):

    total_received = FundingRecord.objects.filter(
        funding_entity_id=funding_entity_id
    ).aggregate(total=Sum('amount'))['total'] or 0

    total_allocated = FundingAllocation.objects.filter(
        funding_entity_id=funding_entity_id
    ).aggregate(total=Sum('amount_allocated'))['total'] or 0

    return total_received - total_allocated
