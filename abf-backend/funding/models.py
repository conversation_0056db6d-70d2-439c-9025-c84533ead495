from django.db import models
from django.utils import timezone
from grants.models import Grant
from profiles.models import Organization

class Disbursement(models.Model):
    grant = models.ForeignKey(Grant, on_delete=models.CASCADE)
    scheduled_payment_date = models.DateField()
    scheduled_amount = models.DecimalField(max_digits=12, decimal_places=2)
    received_amount = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    pending_amount = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    payment_received_date = models.DateField(blank=True, null=True)

    def save(self, *args, **kwargs):
        if self.received_amount is not None:
            self.pending_amount = self.scheduled_amount - self.received_amount
        else:
            self.pending_amount = self.scheduled_amount
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Disbursement for {self.grant.grant_name} scheduled on {self.scheduled_payment_date}"

    class Meta:
        verbose_name_plural = "Disbursements"
        indexes = [
            models.Index(fields=['grant']),
            models.Index(fields=['scheduled_payment_date']),
            models.Index(fields=['payment_received_date']),
        ]

class Expense(models.Model):
    STATUS_PENDING = 'pending'
    STATUS_APPROVED = 'approved'
    STATUS_REJECTED = 'rejected'
    STATUS_CHOICES = [
        (STATUS_PENDING, 'Pending'),
        (STATUS_APPROVED, 'Approved'),
        (STATUS_REJECTED, 'Rejected'),
    ]

    particulars = models.CharField(max_length=255)
    main_header = models.CharField(max_length=255, blank=True, default='')
    sub_header = models.CharField(max_length=255, blank=True, default='')
    units = models.DecimalField(max_digits=15, decimal_places=2)
    frequency = models.DecimalField(max_digits=15, decimal_places=2)
    cost_per_unit = models.DecimalField(max_digits=15, decimal_places=2)
    activity_description = models.TextField(blank=True, default='')
    budget_q1 = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    budget_q2 = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    budget_q3 = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    budget_q4 = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    actual_q1 = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    actual_q2 = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    actual_q3 = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    actual_q4 = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_budget = models.DecimalField(max_digits=15, decimal_places=2)
    total_grant_budget = models.DecimalField(max_digits=15, decimal_places=2)
    total_actual = models.DecimalField(max_digits=15, decimal_places=2)
    grant = models.ForeignKey(Grant, on_delete=models.CASCADE, related_name='expenses')
    remarks = models.TextField(blank=True, default='')
    rejection_notes = models.TextField(blank=True, default='')
    expense_date = models.DateField(default=timezone.now)
    source_type = models.CharField(max_length=50, default='excel')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=STATUS_PENDING)
    is_frozen = models.BooleanField(default=False)
    metadata = models.JSONField(default=dict)

    file1 = models.FileField(upload_to='expense_files/', blank=True, null=True)
    file2 = models.FileField(upload_to='expense_files/', blank=True, null=True)
    file3 = models.FileField(upload_to='expense_files/', blank=True, null=True)
    file4 = models.FileField(upload_to='expense_files/', blank=True, null=True)

    def __str__(self):
        return f"{self.particulars} - {self.main_header} ({self.sub_header})"

class FundingRecord(models.Model):
    funding_entity = models.ForeignKey(Organization, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    date_received = models.DateField()
    notes = models.TextField(blank=True, default='')

    class Meta:
        indexes = [models.Index(fields=['funding_entity', 'date_received'])]

    def __str__(self):
        return f"{self.funding_entity.name} - {self.date_received}"


class FundingAllocation(models.Model):
    grant = models.ForeignKey(Grant, on_delete=models.CASCADE)
    funding_entity = models.ForeignKey(Organization, on_delete=models.CASCADE, null=True)

    # Amount allocated should always be == grant's total budget
    amount_allocated = models.DecimalField(max_digits=15, decimal_places=2)
    notes = models.TextField(blank=True, default='')

    class Meta:
        indexes = [models.Index(fields=['grant', 'funding_entity'])]

    def __str__(self):
        return f"{self.grant.grant_name} - {self.amount_allocated}"
