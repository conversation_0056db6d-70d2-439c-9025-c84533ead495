from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.status import HTTP_400_BAD_REQUEST

from profiles.models import Organization, OrganizationFunctionType, OrganizationAttachment, OrganizationAttachmentStatus
from profiles.serializers import OrganizationSerializer, OrganizationAttachmentSerializer
from grants.models import Grant, OrganizationGrantHistory
from grants.serializers import GrantSerializer, OrganizationGrantHistorySerializer
from funding.models import Expense, Disbursement
from funding.serializers import ExpenseSerializer, DisbursementSerializer
from users.permissions import IsGrantMaker
from common.mixins.org_aware import OrgAwareMixin
import logging

class GranteeViewSet(OrgAwareMixin, viewsets.ViewSet):
    """
    ViewSet for grantmakers to view and manage grantees
    """
    permission_classes = [IsGrantMaker]

    def list(self, request):
        """
        List all grantees for the current grantmaker
        """
        try:
            # Get the grantmaker's organization
            grantmaker_org = self.get_user_org(request)
            if not grantmaker_org:
                return Response({
                    'status': 'error',
                    'message': 'Grantmaker organization not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Get all grants given by this grantmaker
            grants = Grant.objects.filter(grant_maker_organization=grantmaker_org)

            # Get all grantee organizations that have received grants from this grantmaker
            grantee_orgs = Organization.objects.filter(
                grants_given_to_organisation__in=grants
            ).distinct()

            # Serialize the grantee organizations
            serializer = OrganizationSerializer(grantee_orgs, many=True)

            return Response({
                'status': 'success',
                'data': serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logging.error(f"Error in grantee list: {str(e)}")
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def retrieve(self, request, pk=None):
        """
        Retrieve a specific grantee's details
        """
        try:
            # Get the grantmaker's organization
            grantmaker_org = self.get_user_org(request)
            if not grantmaker_org:
                return Response({
                    'status': 'error',
                    'message': 'Grantmaker organization not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Get the grantee organization
            try:
                grantee_org = Organization.objects.get(pk=pk)
            except Organization.DoesNotExist:
                return Response({
                    'status': 'error',
                    'message': 'Grantee organization not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Check if this grantmaker has given grants to this grantee
            grants = Grant.objects.filter(
                grant_maker_organization=grantmaker_org,
                organization=grantee_org
            )

            if not grants.exists():
                return Response({
                    'status': 'error',
                    'message': 'You do not have permission to view this grantee'
                }, status=status.HTTP_403_FORBIDDEN)

            # Serialize the grantee organization
            org_serializer = OrganizationSerializer(grantee_org, context={'request': request})

            # Get grants for this grantee
            grants_serializer = GrantSerializer(grants, many=True)

            # Get expenses for this grantee's grants
            expenses = Expense.objects.filter(grant__in=grants)
            expenses_serializer = ExpenseSerializer(expenses, many=True)

            # Get disbursements for this grantee's grants
            disbursements = Disbursement.objects.filter(grant__in=grants)
            disbursements_serializer = DisbursementSerializer(disbursements, many=True)

            return Response({
                'status': 'success',
                'data': {
                    'organization': org_serializer.data,
                    'grants': grants_serializer.data,
                    'expenses': expenses_serializer.data,
                    'disbursements': disbursements_serializer.data
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logging.error(f"Error in grantee retrieve: {str(e)}")
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'], url_path='grant-history')
    def grant_history(self, request, pk=None):
        """
        Get grant history for a specific grantee organization
        """
        try:
            # Get the grantmaker's organization
            grantmaker_org = self.get_user_org(request)
            if not grantmaker_org:
                return Response({
                    'status': 'error',
                    'message': 'Grantmaker organization not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Get the grantee organization
            try:
                grantee_org = Organization.objects.get(pk=pk)
            except Organization.DoesNotExist:
                return Response({
                    'status': 'error',
                    'message': 'Grantee organization not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Check if this grantmaker has given grants to this grantee
            grants = Grant.objects.filter(
                grant_maker_organization=grantmaker_org,
                organization=grantee_org
            )

            if not grants.exists():
                return Response({
                    'status': 'error',
                    'message': 'You do not have permission to view this grantee\'s grant history'
                }, status=status.HTTP_403_FORBIDDEN)

            # Get grant history for this grantee
            grant_history = OrganizationGrantHistory.objects.filter(
                organization=grantee_org
            ).order_by('-created_at')

            # Serialize the grant history
            serializer = OrganizationGrantHistorySerializer(grant_history, many=True)

            return Response({
                'status': 'SUCCESS',
                'message': 'Grant history retrieved successfully',
                'data': serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logging.error(f"Error in grant history retrieve: {str(e)}")
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def all(self, request):
        """
        List all grantee organizations in the system
        """
        try:
            # Get all organizations regardless of type
            all_orgs = Organization.objects.all()

            # Serialize the organizations
            serializer = OrganizationSerializer(all_orgs, many=True)

            return Response({
                'status': 'success',
                'data': serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logging.error(f"Error in all grantees list: {str(e)}")
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def users(self, request):
        """
        List all users associated with organizations
        """
        try:
            from users.models import User
            from users.serializers import UserSerializer

            # Get all users
            all_users = User.objects.all()

            # Serialize the users
            serializer = UserSerializer(all_users, many=True)

            return Response({
                'status': 'success',
                'data': serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logging.error(f"Error in users list: {str(e)}")
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class OrganizationAttachmentViewSet(viewsets.ModelViewSet):
    queryset = OrganizationAttachment.objects.all()
    serializer_class = OrganizationAttachmentSerializer
    permission_classes = [IsGrantMaker]

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        status_code = request.data.get("status")
        remarks = request.data.get("remarks", "")

        if status_code not in ["VERIFIED", "REJECTED", "PENDING"]:
            return Response({"error": "Invalid status"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            new_status = OrganizationAttachmentStatus.objects.get(code=status_code)
            instance.status = new_status
            if status_code == "REJECTED":
                if not remarks.strip():
                    return Response({"error": "Remarks required for rejection."}, status=status.HTTP_400_BAD_REQUEST)
                instance.remarks = remarks
            instance.save()
            serializer = self.get_serializer(instance)
            return Response({"status": "SUCCESS", "data": serializer.data}, status=status.HTTP_200_OK)
        except OrganizationAttachmentStatus.DoesNotExist:
            return Response({"error": "Invalid status code."}, status=HTTP_400_BAD_REQUEST)

