from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON>er
from .views import GranteeViewSet, OrganizationAttachmentViewSet

router = DefaultRouter()
router.register(r'v1/grantees', GranteeViewSet, basename='grantees')
router.register('v1/documents', OrganizationAttachmentViewSet, basename='documents')

urlpatterns = [
    path('', include(router.urls)),
    path('v1/grantees/all/', GranteeViewSet.as_view({'get': 'all'}), name='all-grantees'),
    path('v1/users/', GranteeViewSet.as_view({'get': 'users'}), name='all-users'),
]
