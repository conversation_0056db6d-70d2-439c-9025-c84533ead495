import json
import logging
import requests
import jwt
from jwt.algorithms import RSAAlgorithm
from django.http import JsonResponse
from rest_framework import status
from users.models import User
from abf_backend import settings


class CognitoAuthMiddleware:
    """
    Middleware to validate AWS Cognito JWT tokens, decode user info,
    attach the user object to the request, and ensure the user is registered in the system.
    """

    def __init__(self, get_response):
        self.get_response = get_response

        # Paths excluded from authentication (like login, file uploads, admin panel)
        self.excluded_paths = [
            "/api/auth/",
            "/admin"
            # "/api/"
        ]

        # If needed later: role-based group access control from Cognito
        self.groups_required = {
            "grant_maker": {},
            "grantee": {}
        }

    def __call__(self, request):
        # Skip token auth for excluded paths
        if any(request.path.startswith(path) for path in self.excluded_paths):
            return self.get_response(request)

        # Read the Authorization header
        token: str = request.headers.get('Authorization')

        if not token:
            logging.warning("Authorization header is missing")
            return JsonResponse({
                "status": "ERROR",
                "message": "Authorization token is missing."
            }, status=status.HTTP_400_BAD_REQUEST)

        # Extract token from "Bearer <token>" format
        if token.startswith("Bearer "):
            token = token.split(' ')[1]
        else:
            logging.warning("Invalid Authorization header format")
            return JsonResponse({
                "status": "ERROR",
                "message": "Invalid Authorization header format. Expected 'Bearer <token>'."
            }, status=status.HTTP_400_BAD_REQUEST)

        logging.debug(f"Processing token: {token}")

        try:
            # Decode the JWT using Cognito public keys
            decoded_token = self.decode_jwt_token(token)
            logging.debug(f"Decoded token: {decoded_token}")

            # Attach Cognito user ID and token info to request for use in views
            request.cognito_id = decoded_token.get('sub')
            request.decoded_token = decoded_token

            # Look up the user in the database based on Cognito 'sub'
            try:
                user = User.objects.select_related('role', 'type', 'organization').get(
                    cognito_sub=decoded_token.get('sub'))
            except User.DoesNotExist:
                logging.warning(f'User with cognito id: {request.cognito_id} not registered')
                return JsonResponse({
                    'status': 'ERROR',
                    'message': 'User not registered.'
                }, status=status.HTTP_401_UNAUTHORIZED)

            except Exception as e:
                logging.error(f"Unexpected error during user lookup: {str(e)}")
                return JsonResponse({
                    'status': 'ERROR',
                    'message': 'Unexpected error during user authentication.'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Attach the user object to the request (custom attribute, not request.user)
            request.db_user = user

            # Continue processing the request
            return self.get_response(request)

        except jwt.exceptions.ExpiredSignatureError:
            logging.error("JWT token has expired.")
            return JsonResponse({
                "status": "ERROR",
                "message": "Token has expired. Please refresh the token."
            }, status=status.HTTP_401_UNAUTHORIZED)

        except jwt.exceptions.InvalidTokenError as e:
            logging.error(f"Invalid JWT token: {e}")
            return JsonResponse({
                "status": "ERROR",
                "message": f"Invalid token: {str(e)}"
            }, status=status.HTTP_401_UNAUTHORIZED)

        except Exception as e:
            logging.error(f"Unexpected error during token processing: {e}")
            return JsonResponse({
                "status": "ERROR",
                "message": "Authentication error."
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_cognito_public_keys(self):
        """
        Fetch and return AWS Cognito public keys used to verify JWT tokens.
        These are fetched dynamically from the Cognito issuer's well-known endpoint.
        """
        try:
            response = requests.get(settings.AWS_COGNITO_PUBLIC_KEYS_URL)
            response.raise_for_status()
            keys = response.json().get("keys", [])
            return {key["kid"]: key for key in keys}
        except requests.RequestException as e:
            logging.error(f"Error fetching Cognito public keys: {e}")
            return {}

    def decode_jwt_token(self, token):
        """
        Decode and validate an AWS Cognito JWT using the correct public key.
        This also checks the audience (client ID).
        """
        # Get unverified JWT headers to find which public key (kid) was used to sign it
        headers = jwt.get_unverified_header(token)
        kid = headers.get('kid')

        if not kid:
            logging.error("JWT token missing 'kid' in header.")
            raise jwt.InvalidTokenError("Invalid token: missing key ID (kid).")

        # Load Cognito public keys
        cognito_keys = self.get_cognito_public_keys()

        if kid not in cognito_keys:
            logging.error("Public key not found in Cognito.")
            raise jwt.InvalidTokenError("Invalid token: public key not found.")

        # Parse the JWK into a usable RSA public key
        public_key = RSAAlgorithm.from_jwk(json.dumps(cognito_keys[kid]))

        # Finally decode and verify the JWT
        return jwt.decode(
            token,
            public_key,
            algorithms=["RS256"],
            audience=settings.AWS_COGNITO_CLIENT_ID  # Ensures token was meant for your app
        )
