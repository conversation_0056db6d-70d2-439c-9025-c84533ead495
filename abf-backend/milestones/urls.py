# urls.py
from django.urls import path
from .views import QuestionerViewSet, QuestionViewSet, MilestoneViewSet

urlpatterns = [
    path('v1/questioners/', QuestionerViewSet.as_view({'get': 'list', 'post': 'create'}), name='questioner-list'),
    path('v1/questioners/<int:pk>/', QuestionerViewSet.as_view({'get': 'retrieve', 'put': 'update', 'delete': 'destroy'}), name='questioner-detail'),
    path('v1/questions/', QuestionViewSet.as_view({'get': 'list', 'post': 'create'}), name='question-list'),
    path('v1/questions/<int:pk>/', QuestionViewSet.as_view({'get': 'retrieve', 'put': 'update', 'delete': 'destroy'}), name='question-detail'),
    path('v1/milestones/', MilestoneViewSet.as_view({'get': 'list', 'post': 'create'}), name='milestone-list'),
    path('v1/milestones/<int:pk>/', MilestoneViewSet.as_view({'get': 'retrieve', 'put': 'update', 'delete': 'destroy'}), name='milestone-detail'),
]
