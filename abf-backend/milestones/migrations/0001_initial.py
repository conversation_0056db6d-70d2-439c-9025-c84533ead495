# Generated by Django 4.2.20 on 2025-06-17 07:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('grants', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Milestone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True)),
                ('activity', models.CharField(blank=True, max_length=255, null=True)),
                ('target_group', models.CharField(blank=True, max_length=255, null=True)),
                ('target_q1', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('target_q2', models.CharField(blank=True, max_length=255, null=True)),
                ('target_q3', models.CharField(blank=True, max_length=255, null=True)),
                ('target_q4', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ('achieved_q1', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('achieved_q2', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('achieved_q3', models.CharField(blank=True, max_length=255, null=True)),
                ('achieved_q4', models.CharField(blank=True, max_length=255, null=True)),
                ('cost_per_unit', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_planned_budget', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('balance', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('grant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='grants.grant')),
            ],
        ),
        migrations.CreateModel(
            name='Questioner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('grant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='grants.grant')),
            ],
            options={
                'db_table': 'questioners',
            },
        ),
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('questioner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='questions', to='milestones.questioner')),
            ],
        ),
        migrations.CreateModel(
            name='MilestonesAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attachment_type', models.CharField(blank=True, max_length=100, null=True)),
                ('file_path', models.CharField(blank=True, max_length=500, null=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('milestone', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='milestones.milestone')),
            ],
        ),
    ]
