from django.db import models
from grants.models import Grant

class Questioner(models.Model):
    
    grant = models.Foreign<PERSON>ey(Grant, on_delete=models.CASCADE, related_name='questions')
    title = models.CharField(max_length=255)
    description = models.TextField(max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    class Meta:
        db_table = 'questioners'


class Question(models.Model):

    questioner = models.ForeignKey(Questioner, on_delete=models.SET_NULL, null=True, blank=True, related_name='questions')
    title = models.Char<PERSON>ield(max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)




class Milestone(models.Model):

    grant = models.ForeignKey(Grant, on_delete=models.CASCADE)
    description = models.TextField(blank=True)

    activity = models.Char<PERSON>ield(max_length=255, blank=True, null=True)
    target_group = models.CharField(max_length=255, blank=True, null=True)
    target_q1 = models.CharField(max_length=255, blank=True, null=True)
    target_q2 = models.CharField(max_length=255, blank=True, null=True)
    target_q3 = models.CharField(max_length=255, blank=True, null=True)
    target_q4 = models.CharField(max_length=255, blank=True, null=True)
    achieved_q1 = models.CharField(max_length=255, blank=True, null=True)
    achieved_q2 = models.CharField(max_length=255, blank=True, null=True)
    achieved_q3 = models.CharField(max_length=255, blank=True, null=True)
    achieved_q4 = models.CharField(max_length=255, blank=True, null=True)
    cost_per_unit = models.DecimalField(max_digits=10, decimal_places=2)
    total_planned_budget = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    

class MilestonesAttachment(models.Model):
 
    milestone = models.ForeignKey( Milestone, on_delete=models.CASCADE, related_name='attachments')
    attachment_type = models.CharField(max_length=100, blank=True, null=True)
    # If you want Django to handle file uploads, use FileField or ImageField:
    # file = models.FileField(upload_to='org_attachments/')
    file_path = models.CharField(max_length=500, blank=True, null=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.milestone} - {self.attachment_type}"


