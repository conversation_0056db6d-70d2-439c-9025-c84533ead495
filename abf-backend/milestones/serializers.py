from rest_framework import serializers
from .models import Questioner, Question, Milestone

class QuestionerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Questioner
        fields = '__all__'


class QuestionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Question
        fields = '__all__'


class MilestoneSerializer(serializers.ModelSerializer):
    class Meta:
        model = Milestone
        fields = '__all__'
