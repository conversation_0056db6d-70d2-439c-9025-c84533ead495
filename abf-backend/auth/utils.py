import base64
import hashlib
import hmac
import json
import logging

import boto3
import jwt
from botocore.exceptions import ClientError
from jwt.algorithms import RSAAlgorithm
from rest_framework import status

from rest_framework.response import Response
from abf_backend import settings
from mypy_boto3_cognito_idp import CognitoIdentityProviderClient
import requests
from users.models import User, UserRole, UserType

cognito_client: CognitoIdentityProviderClient = boto3.client('cognito-idp',
                                                             region_name=settings.AWS_COGNITO_REGION,
                                                             aws_access_key_id=settings.AWS_ACCESS_KEY,
                                                             aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
                                                             )

TEMPORARY_USER_ROLE = 'ADMIN'
def get_secret_hash(username):
    message = username + settings.AWS_COGNITO_CLIENT_ID
    dig = hmac.new(
        str(settings.AWS_COGNITO_CLIENT_SECRET).encode('utf-8'),
        msg=str(message).encode('utf-8'),
        digestmod=hashlib.sha256
    ).digest()
    return base64.b64encode(dig).decode()

def cognito_login(username, password):
    logging.info("Trying Cognito Login")
    logging.debug(f'hash = {get_secret_hash(username)}')
    try:
        response = cognito_client.initiate_auth(
            AuthFlow='USER_PASSWORD_AUTH',
            AuthParameters={
                'USERNAME': username,
                'PASSWORD': password,
                'SECRET_HASH': get_secret_hash(username)
            },
            ClientId=settings.AWS_COGNITO_CLIENT_ID
        )

        logging.info(f'Normal user password auth = {response}')
        if response.get('ChallengeName') == 'NEW_PASSWORD_REQUIRED':
            logging.error('New password required but not provided')

            return Response({
                'status': 'NEW_PASSWORD_REQUIRED',
                'session': response.get('Session'),
                'message': 'User must set a new password'
            }, status=status.HTTP_403_FORBIDDEN)

        auth_result = response.get('AuthenticationResult')
        if not isinstance(auth_result, dict) or 'IdToken' not in auth_result:
            return Response({
                'status': 'ERROR',
                'message': 'Authentication token missing from response.'
            }, status=status.HTTP_401_UNAUTHORIZED)

        decoded_token = decode_jwt_token(auth_result['IdToken'])

        groups = decoded_token.get('cognito:groups')

        if not groups:
            return Response({
                'status': 'ERROR',
                'message': 'User type is not assigned. Please contact your administrator.'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not isinstance(groups, list):
            return Response({
                'status': 'ERROR',
                'message': 'Invalid user type format. Please contact your administrator.'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        auth_result['User'] = {
            'Type': groups[0].upper(),
            'Role': 'ADMIN',  # Static role for now, change when dynamic role assignment is implemented
            'FirstName': decoded_token.get('name'),
            'LastName': decoded_token.get('family_name'),
            'Email': decoded_token.get('email')
        }

        return Response({
            'status': 'SUCCESS',
            'AuthenticationResult': auth_result
        }, status=status.HTTP_200_OK)

    except cognito_client.exceptions.NotAuthorizedException as e:
        logging.error(f"Not authorized exception: {e}")
        return Response({
            'status': 'UNAUTHORIZED',
            'message': e.response['Error']['Message']
        }, status=status.HTTP_401_UNAUTHORIZED)

    except cognito_client.exceptions.TooManyRequestsException as e:
        logging.error(f"Too may requests exception: {e}")
        return Response({
            'status': 'RATE_LIMIT_EXCEEDED',
            'message': e.response['Error']['Message']
        }, status=status.HTTP_429_TOO_MANY_REQUESTS)

    except ClientError as e:
        logging.error(f"Client error: {e}")
        return Response({
            'status': 'CLIENT_ERROR',
            'message': e.response['Error']['Message']
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        logging.error(f"Error during Cognito login: {e}")
        return Response({
            'status': 'INTERNAL_SERVER_ERROR',
            'message': 'An unexpected error occurred. Please try again later.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def validate_authentication_result(auth_result):
    id_token = auth_result.get('IdToken')
    if not id_token:
        raise ValueError('Authentication token missing from response.')
    return id_token

def validate_user_groups(decoded_token):
    groups = decoded_token.get('cognito:groups')
    if not isinstance(groups, list) or not groups:
        raise ValueError('Invalid or missing user group information.')
    return groups

def create_or_update_user(decoded_token, groups):
    try:
        role_obj = UserRole.objects.get(code=TEMPORARY_USER_ROLE)
        type_obj = UserType.objects.get(code=groups[0].upper())
        User.objects.update_or_create(
            cognito_sub=decoded_token.get('sub'),
            defaults={
                'first_name': decoded_token.get('name', ''),
                'last_name': decoded_token.get('family_name', ''),
                'email': decoded_token.get('email'),
                'role': role_obj,
                'type': type_obj,
            }
        )
        logging.debug(f"User created or updated for email: {decoded_token.get('email')}")
    except (UserRole.DoesNotExist, UserType.DoesNotExist) as e:
        logging.error(f"Role or Type mapping failed: {e}")
        raise ValueError(f"User group mapping failed: {str(e)}")

def confirm_new_password(username, new_password, session):
    logging.info("User is in FORCE_CHANGE_PASSWORD state, changing password...")

    try:
        response = cognito_client.respond_to_auth_challenge(
            ClientId=settings.AWS_COGNITO_CLIENT_ID,
            ChallengeName='NEW_PASSWORD_REQUIRED',
            ChallengeResponses={
                'USERNAME': username,
                'NEW_PASSWORD': new_password,
                'SECRET_HASH': get_secret_hash(username)
            },
            Session=session
        )

        auth_result = response.get('AuthenticationResult', {})
        try:
            id_token = validate_authentication_result(auth_result)
            decoded_token = decode_jwt_token(id_token)
            groups = validate_user_groups(decoded_token)
            create_or_update_user(decoded_token, groups)
        except ValueError as e:
            logging.error(f"Error when confirming new password: {str(e)}")
            return Response({
                'status': 'ERROR',
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

        auth_result['User'] = {
            'Type': groups[0].upper(),
            'Role': TEMPORARY_USER_ROLE  # Static role for now
        }

        return Response({
            'status': 'SUCCESS',
            'AuthenticationResult': auth_result,
        }, status=status.HTTP_200_OK)

    except cognito_client.exceptions.NotAuthorizedException:
        return Response({
            'status': 'ERROR',
            'message': 'Session timed out, please re-login'
        }, status=status.HTTP_400_BAD_REQUEST)

    except cognito_client.exceptions.InvalidParameterException:
        return Response({
            'status': 'ERROR',
            'message': 'Name for your account is not set, please get in touch with administrator.'
        }, status=status.HTTP_400_BAD_REQUEST)

    except cognito_client.exceptions.ExpiredCodeException:
        return Response({
            'status': 'UNAUTHORIZED',
            'message': 'Session token expired'
        }, status=status.HTTP_401_UNAUTHORIZED)

    except cognito_client.exceptions.InvalidPasswordException:
        return Response({
            'status': 'ERROR',
            'message': 'Password does not conform to the policy.'
        }, status=status.HTTP_400_BAD_REQUEST)

    except ClientError as e:
        logging.error(f'Client error: {e.response}')
        return Response({
            'status': 'ERROR',
            'message': str(e.response['Error']['Message'])
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logging.error(f"Unable to FORCE_CHANGE_PASSWORD: {e}")

        return Response({
            'status': 'ERROR',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def get_cognito_public_keys():
    """Fetch AWS Cognito public keys dynamically."""
    try:
        response = requests.get(settings.AWS_COGNITO_PUBLIC_KEYS_URL)
        response.raise_for_status()
        keys = response.json().get("keys", [])
        return {key["kid"]: key for key in keys}
    except requests.RequestException as e:
        logging.error(f"Error fetching Cognito public keys: {e}")
        return {}

def decode_jwt_token(token):
    """Decode and validate AWS Cognito JWT token."""
    headers = jwt.get_unverified_header(token)
    kid = headers.get('kid')

    if not kid:
        logging.error("JWT token missing 'kid' in header.")
        raise jwt.InvalidTokenError("Invalid token: missing key ID (kid).")

    cognito_keys = get_cognito_public_keys()

    if kid not in cognito_keys:
        logging.error("Public key not found in Cognito.")
        raise jwt.InvalidTokenError("Invalid token: public key not found.")

    public_key = RSAAlgorithm.from_jwk(json.dumps(cognito_keys[kid]))

    return jwt.decode(
        token,
        public_key,
        algorithms=["RS256"],
        audience=settings.AWS_COGNITO_CLIENT_ID
    )
