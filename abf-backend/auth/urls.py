from django.urls import path

from auth.views import CognitoLoginView, CognitoLogoutView, CognitoSignUpView, CognitoConfirmNewPasswordView, \
    CognitoVerifyTokenAPIView

urlpatterns = [
    path('v1/login/', CognitoLoginView.as_view(), name='login'),
    path('v1/confirm-new-password/', CognitoConfirmNewPasswordView.as_view(), name='confirm-new-password'),
    path('v1/refresh/', CognitoVerifyTokenAPIView.as_view(), name='refresh-token'),
    path('v1/logout/', CognitoLogoutView.as_view(), name='logout'),
    path('v1/signup/', CognitoSignUpView.as_view(), name='signup')
]
