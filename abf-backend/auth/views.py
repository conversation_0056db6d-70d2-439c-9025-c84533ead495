import logging
import urllib.parse

import requests
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK
from rest_framework.views import APIView

from abf_backend import settings
from auth.utils import cognito_login, confirm_new_password, cognito_client, get_secret_hash, decode_jwt_token
import json
from users.models import User

COGNITO_URL = f"https://cognito-idp.{settings.AWS_COGNITO_REGION}.amazonaws.com/{settings.AWS_COGNITO_USER_POOL_ID}"


class CognitoLoginView(APIView):

    def post(self, request):
        logging.info(f'data = {request.data}')
        username = request.data.get('username')
        password = request.data.get('password')

        if not username or not password:
            return Response({"error": "Username and password required"}, status=status.HTTP_400_BAD_REQUEST)

        auth_result = cognito_login(username, password)
        # TODO: Remove this log line
        logging.debug(f'auth_result = {auth_result}')

        return auth_result


class CognitoConfirmNewPasswordView(APIView):
    def post(self, request):
        body = request.data
        username = body.get('username')
        new_password = body.get('new_password')
        session = body.get('session')

        if not username or not new_password or not session:
            return Response({
                'status': 'ERROR',
                'message': 'Username, new password, and session are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        logging.debug(f'CognitoConfirmNewPasswordView: username={username}')
        return confirm_new_password(username, new_password, session)


class CognitoVerifyTokenAPIView(APIView):

    def post(self, request):
        refresh_token = request.data.get('refresh_token')
        id_token = request.data.get('id_token')

        if not refresh_token:
            return Response({
                'status': 'ERROR',
                'message': 'Refresh token is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        auth_params = {
            'REFRESH_TOKEN':  refresh_token
        }

        try:
            decoded_token = decode_jwt_token(id_token)
            cognito_id = decoded_token.get('sub')
            if settings.AWS_COGNITO_CLIENT_SECRET:
                auth_params['SECRET_HASH'] = get_secret_hash(cognito_id)

            logging.debug(f"auth params = {auth_params}")

            response = cognito_client.initiate_auth(
                AuthFlow="REFRESH_TOKEN_AUTH",
                AuthParameters=auth_params,
                ClientId=settings.AWS_COGNITO_CLIENT_ID,
            )

            auth_result = response.get('AuthenticationResult', {})

            return Response({
                'status': 'SUCCESS',
                'AuthenticationResult': auth_result
            }, status=HTTP_200_OK)

        except cognito_client.exceptions.NotAuthorizedException as e:
            logging.error(f'{str(e)}')
            return Response({
                'status': 'UNAUTHORIZED',
                'message': f'{str(e).split(":")[1]}'
            }, status=status.HTTP_401_UNAUTHORIZED)

        except cognito_client.exceptions.InvalidParameterException as e:
            logging.error(f'{str(e)}')
            return Response({
                'status': 'UNAUTHORIZED',
                'message': f'{str(e)}'
            }, status=status.HTTP_401_UNAUTHORIZED)

        except Exception as e:
            logging.error(f'Error when updating access token: {str(e)}')
            return Response()

class CognitoLogoutView(APIView):

    def post(self, request):
        pass


class CognitoSignUpView(APIView):

    def post(self, request):
        pass
